/**
 * Agent-Enhanced Workflow Artifacts Page
 * Display artifacts generated by the agent-enhanced workflow system
 */

'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

interface Artifact {
  id: string;
  type: string;
  title: string;
  content: any;
  status: string;
  createdAt: string;
  updatedAt?: string;
  createdBy?: string;
  version?: number;
  metadata?: Record<string, any>;
}

interface ArtifactsResponse {
  success: boolean;
  sessionId: string;
  artifacts: Record<string, Artifact>;
  error?: string;
}

function AgentEnhancedArtifactsContent() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;

  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && sessionId) {
      loadArtifacts();
    }
  }, [sessionId, mounted]);

  const loadArtifacts = async () => {
    if (!sessionId) {
      setError('No session ID provided');
      setLoading(false);
      return;
    }

    try {
      console.log(`🔍 Loading artifacts for session: ${sessionId}`);

      // For agent-enhanced workflow system, try the correct API endpoints
      const apiEndpoints = [
        // Primary: Old workflow results API (for exec-* IDs from agent-enhanced system)
        `/api/workflow/results/${sessionId}`,
        // Secondary: General artifact API
        `/api/artifact?executionId=${sessionId}`,
        // Fallback: Check if there are any content items
        `/api/workflow/execution/${sessionId}`
      ];

      let artifactsFound = false;
      let lastError = '';

      for (const endpoint of apiEndpoints) {
        try {
          console.log(`🔍 Trying endpoint: ${endpoint}`);
          const response = await fetch(endpoint);

          if (response.ok) {
            const result = await response.json();
            console.log(`🎯 Response from ${endpoint}:`, result);

            // Handle different response formats
            if (result.success) {
              let artifactsArray: Artifact[] = [];

              // Handle workflow results format
              if (result.data?.artifacts) {
                artifactsArray = result.data.artifacts.map((artifact: any) => ({
                  ...artifact,
                  id: artifact.id || `artifact-${Date.now()}`,
                  title: artifact.title || 'Untitled Artifact',
                  content: artifact.content || '',
                  status: artifact.status || 'draft',
                  createdAt: artifact.createdAt || new Date().toISOString()
                }));
              }
              // Handle dynamic collaboration format
              else if (result.artifacts) {
                if (Array.isArray(result.artifacts)) {
                  artifactsArray = result.artifacts;
                } else {
                  artifactsArray = Object.values(result.artifacts);
                }
                artifactsArray = artifactsArray.map((artifact: any) => ({
                  ...artifact,
                  id: artifact.id || `artifact-${Date.now()}`,
                  title: artifact.title || artifact.name || 'Untitled Artifact',
                  content: artifact.content || '',
                  status: artifact.status || 'draft',
                  createdAt: artifact.createdAt || artifact.timestamp || new Date().toISOString()
                }));
              }
              // Handle content format (old workflow system)
              else if (result.data?.content) {
                artifactsArray = result.data.content.map((content: any) => ({
                  id: content.id || `content-${Date.now()}`,
                  title: content.title || 'Generated Content',
                  content: content.content || '',
                  type: content.type || 'content',
                  status: content.status || 'completed',
                  createdAt: content.createdAt || new Date().toISOString()
                }));
              }

              if (artifactsArray.length > 0) {
                console.log(`✅ Found ${artifactsArray.length} artifacts from ${endpoint}`);
                setArtifacts(artifactsArray);
                artifactsFound = true;
                break;
              }
            }
          } else {
            lastError = `${endpoint}: HTTP ${response.status}`;
          }
        } catch (endpointError) {
          lastError = `${endpoint}: ${endpointError instanceof Error ? endpointError.message : 'Unknown error'}`;
          console.log(`❌ Failed to fetch from ${endpoint}:`, endpointError);
        }
      }

      if (!artifactsFound) {
        // For agent-enhanced system, this is expected if no artifacts were generated
        console.log(`ℹ️ No artifacts found for agent-enhanced execution ${sessionId}`);
        setArtifacts([]); // Set empty array instead of error
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load artifacts';
      setError(errorMessage);
      console.error('Load artifacts error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatContent = (content: any): string => {
    if (!content) return 'No content available';
    if (typeof content === 'string') {
      return content;
    }
    try {
      return JSON.stringify(content, null, 2);
    } catch (err) {
      return 'Error formatting content';
    }
  };

  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleString();
    } catch (err) {
      return 'Invalid date';
    }
  };

  const downloadArtifact = (artifact: Artifact) => {
    const content = typeof artifact.content === 'string'
      ? artifact.content
      : JSON.stringify(artifact.content, null, 2);

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-700';
      case 'approved': return 'bg-green-100 text-green-700';
      case 'rejected': return 'bg-red-100 text-red-700';
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      case 'review': return 'bg-blue-100 text-blue-700';
      case 'draft': return 'bg-gray-100 text-gray-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Initializing...</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading artifacts...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <h3 className="font-bold">Error Loading Artifacts</h3>
          <p>{error}</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            🔄 Retry
          </button>
          <button
            onClick={() => router.push('/workflow/agent-enhanced')}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            ← Back to Agent-Enhanced Workflow
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Agent-Enhanced Workflow Artifacts</h1>
            <p className="text-gray-600 mb-3">Generated content from your agent collaboration session</p>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>Session ID: {sessionId}</span>
              <span>ID Type: {sessionId.startsWith('exec-') ? 'Workflow Execution' : 'Session UUID'}</span>
              <span>Total Artifacts: {artifacts.length}</span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => router.push('/workflow/agent-enhanced')}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ← Back to Workflow
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Artifacts Summary */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h3 className="font-medium mb-3">Artifacts Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Total Artifacts:</span>
            <p className="font-medium">{artifacts.length}</p>
          </div>
          <div>
            <span className="text-gray-500">Completed:</span>
            <p className="text-green-600 font-medium">
              {artifacts.filter(a => a.status?.toLowerCase() === 'completed').length}
            </p>
          </div>
          <div>
            <span className="text-gray-500">In Review:</span>
            <p className="text-blue-600 font-medium">
              {artifacts.filter(a => a.status?.toLowerCase() === 'review').length}
            </p>
          </div>
          <div>
            <span className="text-gray-500">Draft:</span>
            <p className="text-gray-600 font-medium">
              {artifacts.filter(a => a.status?.toLowerCase() === 'draft').length}
            </p>
          </div>
        </div>
      </div>

      {/* Generated Artifacts */}
      {artifacts.length > 0 ? (
        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-4">Generated Artifacts</h3>
          <div className="space-y-4">
            {artifacts.map(artifact => (
              <div key={artifact.id} className="bg-white border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">{artifact.title}</h4>
                  <div className="flex gap-2 text-xs">
                    <span className="bg-gray-100 px-2 py-1 rounded">{artifact.type || 'Unknown'}</span>
                    <span className={`px-2 py-1 rounded ${getStatusColor(artifact.status)}`}>
                      {artifact.status || 'Unknown'}
                    </span>
                    {artifact.version && (
                      <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded">
                        v{artifact.version}
                      </span>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded border mb-3">
                  <pre className="whitespace-pre-wrap text-sm max-h-64 overflow-y-auto">
                    {formatContent(artifact.content)}
                  </pre>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 mb-3">
                  <button
                    onClick={() => downloadArtifact(artifact)}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                  >
                    📥 Download
                  </button>
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(formatContent(artifact.content));
                      alert('Content copied to clipboard!');
                    }}
                    className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                  >
                    📋 Copy
                  </button>
                </div>

                {/* Artifact Metadata */}
                <div className="text-xs text-gray-500">
                  <div className="flex gap-4 flex-wrap">
                    <span>Created: {formatDate(artifact.createdAt)}</span>
                    {artifact.updatedAt && (
                      <span>Updated: {formatDate(artifact.updatedAt)}</span>
                    )}
                    {artifact.createdBy && (
                      <span>Creator: {artifact.createdBy}</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-600 mb-4">No artifacts found for this session.</p>

          {/* Quick Test Section */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-2xl mx-auto mb-4">
            <h4 className="font-semibold text-yellow-900 mb-2">🧪 Quick Test</h4>
            <p className="text-sm text-yellow-800 mb-3">Try one of the available execution IDs from the backend:</p>
            <div className="space-y-2">
              {['762962b0-de02-4505-a3fb-6b016d29abca', 'ceef6bd0-a842-47bb-aade-0d36efa08c90', '74fa83e9-cd6d-49b7-8b8f-6cacf998d042'].map(id => (
                <button
                  key={id}
                  onClick={() => {
                    window.open(`/workflow/agent-enhanced/artifacts/${id}`, '_blank');
                  }}
                  className="block w-full px-3 py-2 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700"
                >
                  Try: {id.slice(0, 8)}...
                </button>
              ))}
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-2xl mx-auto">
            <h4 className="font-semibold text-blue-900 mb-2">🔍 Troubleshooting</h4>
            <div className="text-sm text-blue-800 space-y-2">
              <p><strong>Agent-Enhanced Workflow System:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Current ID: <code>{sessionId}</code></li>
                <li>Format: {sessionId.startsWith('exec-') ? 'WorkflowExecution (exec-*)' : 'Workflow Engine (UUID)'}</li>
                <li>Artifacts stored in workflow results system</li>
                <li>Generated during agent consultations and workflow steps</li>
              </ul>
              <p className="mt-3"><strong>Possible Issues:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>ID mismatch: UI shows exec-* but backend uses UUIDs</li>
                <li>Workflow may still be running</li>
                <li>No artifacts generated yet</li>
                <li>Agent consultations didn't produce content</li>
              </ul>
              <p className="mt-3"><strong>Backend logs show available execution IDs:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4 text-xs">
                <li><code>762962b0-de02-4505-a3fb-6b016d29abca</code></li>
                <li><code>ceef6bd0-a842-47bb-aade-0d36efa08c90</code></li>
                <li><code>74fa83e9-cd6d-49b7-8b8f-6cacf998d042</code></li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Error Boundary Component
function ErrorBoundary({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Error caught by boundary:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Main export with Error Boundary
export default function AgentEnhancedArtifactsPage() {
  return (
    <ErrorBoundary
      fallback={
        <div className="max-w-6xl mx-auto p-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <h3 className="font-bold">Application Error</h3>
            <p>Something went wrong while loading the artifacts.</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              🔄 Reload Page
            </button>
          </div>
        </div>
      }
    >
      <AgentEnhancedArtifactsContent />
    </ErrorBoundary>
  );
}
