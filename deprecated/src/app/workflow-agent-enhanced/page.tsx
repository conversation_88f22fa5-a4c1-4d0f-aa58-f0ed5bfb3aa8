/**
 * Agent-Enhanced Workflow Dashboard
 *
 * Comprehensive dashboard for workflow creation with dynamic agent consultation
 */

'use client';

import { useState, useEffect } from 'react';
import TemplateSelector from '../../../components/Workflow/TemplateSelector';
import WorkflowBuilder from '../../../components/Workflow/WorkflowBuilder';
import AgentConsultationConfig from '../../../components/Agents/AgentConsultationConfig';
import WorkflowExecution from '../../../components/Workflow/WorkflowExecution';
import AgentActivityMonitor from '../../../components/Agents/AgentActivityMonitor';
import HumanFeedbackInterface from '../../../components/Workflow/HumanFeedbackInterface';

// Import new UI components from UIIntegrationExample
import { AgentCollaborationViewer, HumanInteraction } from '../../../components/Workflow/AgentCollaborationViewer';
import { RealTimeCollaborationMonitor, CollaborationUpdate, AgentActivity } from '../../../components/Workflow/RealTimeCollaborationMonitor';
import { HumanInterventionPanel, HumanFeedback, InterventionAction } from '../../../components/Workflow/HumanInterventionPanel';

// Import backend services
import { AgentCollaborationEngine, CollaborationResult } from '../../../core/agents/AgentCollaborationEngine';
import { DynamicWorkflowExecution } from '../../../components/Workflow/DynamicWorkflowExecution';
import { SeoKeywordAgent } from '../../../core/agents/seo-keyword-agent';
import { MarketResearchAgent } from '../../../core/agents/market-research-agent';
import { ContentStrategyAgent } from '../../../core/agents/content-strategy-agent';

type TabType = 'templates' | 'builder' | 'agents' | 'execution' | 'monitor' | 'feedback' | 'collaboration';

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  difficulty: string;
  estimatedTime: number;
  featured: boolean;
  consultationEnabled: boolean;
  agentCount: number;
}

interface AgentMetrics {
  totalConsultations: number;
  successfulConsultations: number;
  failedConsultations: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
  agentUtilization: Record<string, number>;
  lastUpdated: string;
}

interface WorkflowExecutionState {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed';
  currentStep?: string;
  progress: number;
  artifacts: any[];
}

export default function AgentEnhancedWorkflowPage() {
  const [activeTab, setActiveTab] = useState<TabType>('templates');
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [currentWorkflow, setCurrentWorkflow] = useState<any>(null);
  const [currentExecution, setCurrentExecution] = useState<string | null>(null);
  const [agentMetrics, setAgentMetrics] = useState<AgentMetrics | null>(null);
  const [notifications, setNotifications] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentArtifact, setCurrentArtifact] = useState<any>(null);
  const [workflowPaused, setWorkflowPaused] = useState(false);

  // New state for collaboration features
  const [collaborationResult, setCollaborationResult] = useState<CollaborationResult | null>(null);
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([]);
  const [isCollaborationActive, setIsCollaborationActive] = useState(false);
  const [selectedRound, setSelectedRound] = useState(0);
  const [feedbackHistory, setFeedbackHistory] = useState<HumanFeedback[]>([]);
  const [executionState, setExecutionState] = useState<WorkflowExecutionState | null>(null);

  // Initialize collaboration engine
  const [collaborationEngine] = useState(() => {
    const engine = new AgentCollaborationEngine();
    engine.registerAgent(new SeoKeywordAgent());
    engine.registerAgent(new MarketResearchAgent());
    engine.registerAgent(new ContentStrategyAgent());
    return engine;
  });

  const [dynamicExecution] = useState(() => new DynamicWorkflowExecution(collaborationEngine));

  useEffect(() => {
    loadAgentMetrics();
    loadExecutionState();

    // Auto-refresh metrics every 30 seconds
    const interval = setInterval(() => {
      loadAgentMetrics();
      if (currentExecution) {
        loadExecutionState();
      }
    }, 30000);
    return () => clearInterval(interval);
  }, [currentExecution]);

  const loadAgentMetrics = async () => {
    try {
      const response = await fetch('/api/agents/consultation?type=metrics');
      const result = await response.json();

      if (result.success) {
        setAgentMetrics(result.data.metrics);

        // Update agent activities based on metrics
        const activities: AgentActivity[] = [
          { agentId: 'seo-keyword', status: 'idle', lastSeen: new Date().toISOString() },
          { agentId: 'market-research', status: 'idle', lastSeen: new Date().toISOString() },
          { agentId: 'content-strategy', status: 'idle', lastSeen: new Date().toISOString() }
        ];
        setAgentActivities(activities);
      }
    } catch (error) {
      console.error('Failed to load agent metrics:', error);
    }
  };

  const loadExecutionState = async () => {
    if (!currentExecution) return;

    try {
      const response = await fetch(`/api/workflow/execution/${currentExecution}`);
      const result = await response.json();

      if (result.success) {
        setExecutionState(result.data);

        // Update collaboration state if available
        if (result.data.collaboration) {
          setCollaborationResult(result.data.collaboration);
        }
      }
    } catch (error) {
      console.error('Failed to load execution state:', error);
    }
  };

  const addNotification = (message: string) => {
    setNotifications(prev => [message, ...prev.slice(0, 4)]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n !== message));
    }, 5000);
  };

  const handleTemplateSelect = (template: WorkflowTemplate) => {
    setSelectedTemplate(template);
    setActiveTab('builder');
    addNotification(`Selected template: ${template.name}`);
  };

  const handleWorkflowCreate = (workflow: any) => {
    setCurrentWorkflow(workflow);
    setActiveTab('execution');
    addNotification('Workflow created successfully');
  };

  const handleExecutionStart = (executionId: string) => {
    setCurrentExecution(executionId);
    setActiveTab('monitor');
    addNotification(`Workflow execution started: ${executionId.slice(-8)}`);

    // Initialize agent activities for new execution
    setAgentActivities([
      { agentId: 'seo-keyword', status: 'waiting', lastSeen: new Date().toISOString() },
      { agentId: 'market-research', status: 'waiting', lastSeen: new Date().toISOString() },
      { agentId: 'content-strategy', status: 'waiting', lastSeen: new Date().toISOString() }
    ]);
  };

  // Handle collaboration viewer interactions
  const handleCollaborationInteraction = (interaction: HumanInteraction) => {
    console.log('🎯 Collaboration interaction:', interaction);

    switch (interaction.type) {
      case 'round-select':
        if (interaction.roundNumber !== undefined) {
          setSelectedRound(interaction.roundNumber - 1); // Convert to 0-based index
        }
        break;
      case 'add-requirement':
        addNotification('📋 Requirement added to collaboration');
        break;
      case 'provide-feedback':
        addNotification('💬 Feedback provided to agents');
        break;
      case 'resolve-conflict':
        addNotification('⚠️ Conflict resolution initiated');
        break;
    }
  };

  // Handle real-time collaboration updates
  const handleSessionUpdate = (update: CollaborationUpdate) => {
    console.log('📡 Session update:', update);

    switch (update.type) {
      case 'round-started':
        setIsCollaborationActive(true);
        setAgentActivities(prev => prev.map(activity => ({
          ...activity,
          status: update.data.participatingAgents?.includes(activity.agentId) ? 'analyzing' : 'waiting'
        })));
        addNotification(`Round ${update.data.roundNumber} started`);
        break;
      case 'round-completed':
        addNotification(`Round ${update.data.roundNumber} completed`);
        break;
      case 'consensus-updated':
        addNotification(`Consensus updated: ${Math.round(update.data.confidence * 100)}%`);
        break;
    }
  };

  // Handle human intervention actions
  const handleIntervention = (action: InterventionAction) => {
    console.log('👤 Human intervention:', action);

    switch (action.type) {
      case 'pause-collaboration':
        setIsCollaborationActive(false);
        setWorkflowPaused(true);
        setAgentActivities(prev => prev.map(activity => ({
          ...activity,
          status: 'waiting'
        })));
        addNotification('Collaboration paused');
        break;
      case 'resume-collaboration':
        setIsCollaborationActive(true);
        setWorkflowPaused(false);
        setAgentActivities(prev => prev.map(activity => ({
          ...activity,
          status: 'analyzing'
        })));
        addNotification('Collaboration resumed');
        break;
      case 'add-requirement':
        addNotification(`Requirement added: ${action.data}`);
        break;
      case 'resolve-conflict':
        addNotification(`Conflict resolved: ${action.data}`);
        break;
    }
  };

  // Handle feedback submission
  const handleFeedbackSubmit = (feedback: HumanFeedback) => {
    console.log('💬 Feedback submitted:', feedback);
    setFeedbackHistory(prev => [...prev, feedback]);

    // Simulate agent response to feedback
    setTimeout(() => {
      setAgentActivities(prev => prev.map(activity => ({
        ...activity,
        status: feedback.targetAgents?.includes(activity.agentId) ? 'responding' : activity.status,
        lastSeen: new Date().toISOString()
      })));
    }, 1000);

    addNotification('Feedback submitted to agents');
  };

  const getTabIcon = (tab: TabType) => {
    switch (tab) {
      case 'templates': return '📋';
      case 'builder': return '🔧';
      case 'agents': return '🤖';
      case 'execution': return '▶️';
      case 'monitor': return '📊';
      case 'feedback': return '💬';
      case 'collaboration': return '🤝';
      default: return '📋';
    }
  };

  const getTabBadge = (tab: TabType) => {
    switch (tab) {
      case 'agents':
        if (agentMetrics?.totalConsultations) {
          return agentMetrics.totalConsultations;
        }
        break;
      case 'monitor':
        if (currentExecution) {
          return '●';
        }
        break;
      case 'feedback':
        if (currentArtifact) {
          return '●';
        }
        break;
      case 'collaboration':
        if (isCollaborationActive) {
          return '●';
        }
        if (collaborationResult) {
          return collaborationResult.rounds.length;
        }
        break;
    }
    return null;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Agent-Enhanced Workflows</h1>
              <p className="text-sm text-gray-600">
                Create intelligent workflows with dynamic agent consultation
              </p>
            </div>

            <div className="flex items-center space-x-4">
              {/* Agent Status Indicator */}
              {agentMetrics && (
                <div className="flex items-center space-x-2 text-sm">
                  <div className={`w-3 h-3 rounded-full ${
                    agentMetrics.successRate > 0.9 ? 'bg-green-500' :
                    agentMetrics.successRate > 0.7 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-gray-700">
                    Agents: {Math.round(agentMetrics.successRate * 100)}% success
                  </span>
                </div>
              )}

              {/* Current Execution */}
              {currentExecution && (
                <div className="text-sm text-blue-600">
                  Active: <span className="font-medium">{currentExecution.slice(-8)}</span>
                </div>
              )}

              {/* Quick Actions */}
              <button
                onClick={() => setActiveTab('agents')}
                className="flex items-center px-3 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4" />
                </svg>
                Agent Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="space-y-2">
            {notifications.map((notification, index) => (
              <div
                key={index}
                className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 animate-fade-in"
              >
                🔔 {notification}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {(['templates', 'builder', 'agents', 'execution', 'monitor', 'feedback', 'collaboration'] as TabType[]).map((tab) => {
              const badge = getTabBadge(tab);
              return (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span>{getTabIcon(tab)} {tab.charAt(0).toUpperCase() + tab.slice(1)}</span>
                  {badge && (
                    <span className={`inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full ${
                      tab === 'monitor' ? 'text-green-100 bg-green-600' : 'text-blue-100 bg-blue-600'
                    }`}>
                      {badge}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'templates' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Choose a Workflow Template</h2>
                <p className="text-gray-600">
                  Select from our collection of pre-built templates with intelligent agent consultation
                </p>
              </div>
              
              <TemplateSelector
                onTemplateSelect={handleTemplateSelect}
                showAgentFeatures={true}
                filterByAgentSupport={false}
              />
            </div>

            {/* Agent-Enhanced Templates Showcase */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-xl">🤖</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Agent-Enhanced Templates</h3>
                  <p className="text-sm text-gray-600">Templates with built-in intelligent agent consultation</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">🔍</span>
                    <h4 className="font-medium text-gray-900">SEO Blog Post</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Automatic keyword research and SEO optimization with agent consultation
                  </p>
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">SEO Agent</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded">Strategy Agent</span>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">📊</span>
                    <h4 className="font-medium text-gray-900">Market Research</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Content creation with market insights and competitive analysis
                  </p>
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">Market Agent</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded">Strategy Agent</span>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">📝</span>
                    <h4 className="font-medium text-gray-900">Content Strategy</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Strategic content planning with multi-agent collaboration
                  </p>
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">SEO Agent</span>
                    <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">Market Agent</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded">Strategy Agent</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'builder' && (
          <div className="bg-white rounded-lg shadow-lg">
            <WorkflowBuilder
              selectedTemplate={selectedTemplate}
              onWorkflowCreate={handleWorkflowCreate}
              enableAgentConsultation={true}
              onNotification={addNotification}
            />
          </div>
        )}

        {activeTab === 'agents' && (
          <div className="space-y-6">
            <AgentConsultationConfig
              onConfigUpdate={(config) => {
                addNotification('Agent consultation configuration updated');
              }}
              onNotification={addNotification}
            />
          </div>
        )}

        {activeTab === 'execution' && (
          <div className="bg-white rounded-lg shadow-lg">
            <WorkflowExecution
              workflow={currentWorkflow}
              onExecutionStart={handleExecutionStart}
              onNotification={addNotification}
              enableAgentConsultation={true}
            />
          </div>
        )}

        {activeTab === 'monitor' && (
          <div className="space-y-6">
            <AgentActivityMonitor
              executionId={currentExecution}
              metrics={agentMetrics}
              onNotification={addNotification}
            />

            {/* Quick Artifacts Access */}
            {currentExecution && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Generated Artifacts</h3>
                  <button
                    onClick={() => {
                      window.open(`/workflow/agent-enhanced/artifacts/${currentExecution}`, '_blank');
                    }}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    📄 View All Artifacts
                  </button>
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">🎯 Execution Information</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p><strong>Current Execution ID:</strong> <code>{currentExecution}</code></p>
                    <p><strong>ID Format:</strong> {currentExecution.startsWith('exec-') ? 'WorkflowExecution (exec-*)' : 'Workflow Engine (UUID)'}</p>
                    <p><strong>System:</strong> Agent-Enhanced Workflow</p>
                  </div>
                  <div className="mt-3 text-sm text-blue-800">
                    <p><strong>Available Actions:</strong></p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>Click "View All Artifacts" to see generated content</li>
                      <li>Monitor agent consultations in the activity log above</li>
                      <li>Use "Feedback Tab" to review and approve artifacts</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'feedback' && (
          <div className="space-y-6">
            {/* Debug Controls */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-yellow-900 mb-3">🔧 Debug Controls</h3>
              <div className="flex space-x-3">
                <button
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/agents/consultation', { method: 'DELETE' });
                      const result = await response.json();
                      if (result.success) {
                        addNotification('Agent consultation counters cleared - infinite loop fixed!');
                      }
                    } catch (error) {
                      addNotification('Failed to clear consultation counters');
                    }
                  }}
                  className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                >
                  🧹 Clear Agent Consultation Counters
                </button>
                <button
                  onClick={() => {
                    window.open(`/workflow/agent-enhanced/artifacts/${currentExecution}`, '_blank');
                  }}
                  disabled={!currentExecution}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  📄 View Generated Artifacts
                </button>
              </div>
              <p className="text-sm text-yellow-800 mt-2">
                If you're experiencing infinite agent consultation loops, click "Clear Agent Consultation Counters" to reset the system.
              </p>
            </div>

            <HumanFeedbackInterface
              workflowExecutionId={currentExecution || ''}
              stepId="content-creation"
              artifact={currentArtifact}
              onFeedbackSubmit={(feedback) => {
                addNotification('Feedback submitted and agents consulted');
              }}
              onArtifactApprove={(artifactId) => {
                addNotification('Artifact approved');
                setCurrentArtifact(null);
              }}
              onArtifactReject={(artifactId, feedback) => {
                addNotification('Artifact rejected with feedback');
                // Trigger regeneration workflow
              }}
              onNotification={addNotification}
            />
          </div>
        )}

        {activeTab === 'collaboration' && (
          <div className="space-y-6">
            {/* Collaboration Dashboard Header */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    🤝 Agent Collaboration Dashboard
                  </h2>
                  <p className="text-gray-600">
                    Real-time monitoring and interaction with multi-agent collaboration sessions
                  </p>
                </div>
                <button
                  onClick={async () => {
                    if (!currentExecution) {
                      addNotification('Please start a workflow execution first');
                      return;
                    }

                    setIsCollaborationActive(true);
                    addNotification('🚀 Starting agent collaboration...');

                    try {
                      // Trigger agent collaboration through the consultation API
                      const response = await fetch('/api/agents/consultation', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                          agentId: 'seo-keyword',
                          workflowExecutionId: currentExecution,
                          stepId: 'content-creation',
                          context: {
                            topic: 'AI Startups 2025',
                            targetAudience: 'entrepreneurs and investors',
                            contentType: 'blog-article'
                          },
                          consultationConfig: {
                            enabled: true,
                            triggers: [{ type: 'always', agents: ['seo-keyword', 'market-research', 'content-strategy'], priority: 'high' }],
                            maxConsultations: 3,
                            timeoutMs: 30000,
                            fallbackBehavior: 'continue'
                          }
                        })
                      });

                      const result = await response.json();
                      if (result.success) {
                        addNotification('✅ Agent collaboration completed');
                        setIsCollaborationActive(false);
                      }
                    } catch (error) {
                      console.error('Collaboration failed:', error);
                      addNotification('❌ Agent collaboration failed');
                      setIsCollaborationActive(false);
                    }
                  }}
                  disabled={isCollaborationActive || !currentExecution}
                  className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
                >
                  {isCollaborationActive ? '🔄 Collaborating...' : '🚀 Start Collaboration'}
                </button>
              </div>
            </div>

            {/* Main Collaboration Grid */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              {/* Left Column - Real-time Monitor */}
              <div className="space-y-6">
                <RealTimeCollaborationMonitor
                  sessionId={currentExecution || 'demo-session'}
                  activeSessions={currentExecution ? [{
                    id: currentExecution,
                    task: { type: 'artifact-refinement', stepId: 'content-creation', stepType: 'content-creation', objective: 'Create high-quality content' },
                    agents: ['seo-keyword', 'market-research', 'content-strategy'],
                    startedAt: new Date().toISOString(),
                    status: isCollaborationActive ? 'running' : 'completed'
                  }] : []}
                  agentActivities={agentActivities}
                  metrics={{
                    averageResponseTime: agentMetrics?.averageResponseTime || 2.3,
                    consensusVelocity: 0.15,
                    agentParticipation: 0.95,
                    qualityScore: 85
                  }}
                  progressData={{
                    currentRound: selectedRound + 1,
                    totalRounds: collaborationResult?.rounds.length || 2,
                    consensusLevel: collaborationResult?.consensus.confidence || 0.85,
                    participatingAgents: 3
                  }}
                  alerts={[]}
                  onSessionUpdate={handleSessionUpdate}
                />

                <HumanInterventionPanel
                  collaborationResult={collaborationResult}
                  isCollaborationActive={isCollaborationActive}
                  onIntervention={handleIntervention}
                  onFeedbackSubmit={handleFeedbackSubmit}
                />
              </div>

              {/* Right Column - Collaboration Viewer */}
              <div className="space-y-6">
                {collaborationResult ? (
                  <AgentCollaborationViewer
                    collaborationResult={collaborationResult}
                    activeRound={selectedRound}
                    isLive={isCollaborationActive}
                    onInteraction={handleCollaborationInteraction}
                    onRoundSelect={handleCollaborationInteraction}
                  />
                ) : (
                  <div className="bg-white rounded-lg shadow-lg p-6">
                    <div className="text-center py-8">
                      <div className="text-gray-400 mb-4">
                        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Collaboration</h3>
                      <p className="text-gray-600 mb-4">
                        Start a workflow execution and then initiate agent collaboration to see real-time collaboration data.
                      </p>
                    </div>
                  </div>
                )}

                {/* Feedback History */}
                {feedbackHistory.length > 0 && (
                  <div className="bg-white rounded-lg shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      📝 Feedback History
                    </h3>
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {feedbackHistory.map((feedback, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-800">
                                {feedback.type === 'requirement' ? '📋' : '💬'} {feedback.type}
                              </div>
                              <div className="text-sm text-gray-600 mt-1">
                                {feedback.content}
                              </div>
                              {feedback.targetAgents && (
                                <div className="text-xs text-gray-500 mt-1">
                                  Target: {feedback.targetAgents.join(', ')}
                                </div>
                              )}
                            </div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              feedback.priority === 'high' ? 'bg-red-100 text-red-800' :
                              feedback.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {feedback.priority}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
