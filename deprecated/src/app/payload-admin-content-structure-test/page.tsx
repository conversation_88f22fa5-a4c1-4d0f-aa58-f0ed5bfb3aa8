'use client'

import React, { useState, useEffect } from 'react'

// Define the content types
type ContentType = 'product-page' | 'blog-article' | 'buying-guide'

// Define the content structure interfaces
interface ContentSection {
  id: string;
  type: string;
  title: string;
  purpose: string;
  keyPoints?: string[];
  keywordTargeting?: string[];
  estimatedLength?: string;
  children?: ContentSection[];
}

interface ContentStructure {
  contentType: ContentType;
  contentObjective: string;
  topicFocus: string;
  targetAudience: {
    primary: string;
    secondary?: string;
    needs: string[];
  };
  sections: ContentSection[];
  keywordStrategy: {
    primary: string;
    secondary: string;
    distribution: string;
  };
  toneAndStyle: string;
  contentDifferentiators: string[];
  reasoning: string;
}

interface ContentStructureProposal {
  agentId: string;
  agentName: string;
  structure: ContentStructure;
  reasoning: string;
  strengths: string[];
  considerations: string[];
}

interface ContentStructureFeedback {
  fromAgentId: string;
  toAgentId: string;
  proposalId: string;
  feedback: string;
  suggestions: {
    section: string;
    suggestion: string;
    reasoning: string;
  }[];
  agreement: {
    section: string;
    reasoning: string;
  }[];
}

interface ContentStructureNegotiation {
  proposals: ContentStructureProposal[];
  feedback: ContentStructureFeedback[];
  finalStructure?: ContentStructure;
  negotiationStatus: 'in-progress' | 'completed';
}

// Define the form state
interface FormState {
  contentType: ContentType;
  topicFocus: string;
  targetAudience: string;
  primaryKeywords: string[];
  tonePreference: string;
}

// Main component
const ContentStructureTestPage: React.FC = () => {
  // State management
  const [formState, setFormState] = useState<FormState>({
    contentType: 'blog-article',
    topicFocus: '',
    targetAudience: 'General audience',
    primaryKeywords: [],
    tonePreference: 'Informative'
  })
  
  const [keywordsInput, setKeywordsInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [taskId, setTaskId] = useState<string | null>(null)
  const [contentStructure, setContentStructure] = useState<ContentStructure | null>(null)
  const [negotiation, setNegotiation] = useState<ContentStructureNegotiation | null>(null)
  const [activeTab, setActiveTab] = useState<'generate' | 'negotiate' | 'result'>('generate')
  
  // Handle form input changes
  const handleInputChange = (name: string, value: any) => {
    setFormState(prev => ({ ...prev, [name]: value }))
  }
  
  // Handle keywords input
  const handleKeywordsChange = (value: string) => {
    setKeywordsInput(value)
    
    // Parse keywords from comma-separated list
    const keywords = value
      .split(',')
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0)
    
    handleInputChange('primaryKeywords', keywords)
  }
  
  // Generate content structure
  const handleGenerateStructure = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/content-structure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-action': 'generate'
        },
        body: JSON.stringify(formState),
      })
      
      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || 'Content structure generation failed')
      }
      
      setContentStructure(data.contentStructure)
      setTaskId(data.taskId)
      setActiveTab('result')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setIsLoading(false)
    }
  }
  
  // Start content structure negotiation
  const handleStartNegotiation = async () => {
    if (!taskId) return
    
    setIsLoading(true)
    setError(null)
    
    try {
      // Generate proposals from different agents
      const marketResearchProposal = await generateProposal('marketResearch', 'Market Research Agent', 'market research')
      const seoProposal = await generateProposal('seoKeyword', 'SEO Keyword Agent', 'SEO optimization')
      const contentStrategyProposal = await generateProposal('contentStrategy', 'Content Strategy Agent', 'content strategy')
      
      // Collect feedback on proposals
      await collectFeedback('marketResearch', 'Market Research Agent', 'seoKeyword', 'seo-proposal')
      await collectFeedback('seoKeyword', 'SEO Keyword Agent', 'contentStrategy', 'content-strategy-proposal')
      await collectFeedback('contentStrategy', 'Content Strategy Agent', 'marketResearch', 'market-research-proposal')
      
      // Negotiate final structure
      const negotiationResponse = await fetch('/api/content-structure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-action': 'negotiate'
        },
        body: JSON.stringify({ taskId }),
      })
      
      const negotiationData = await negotiationResponse.json()
      
      if (!negotiationData.success) {
        throw new Error(negotiationData.error || 'Content structure negotiation failed')
      }
      
      setNegotiation(negotiationData.negotiation)
      setContentStructure(negotiationData.finalStructure)
      setActiveTab('result')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setIsLoading(false)
    }
  }
  
  // Helper function to generate a proposal
  const generateProposal = async (agentId: string, agentName: string, agentSpecialty: string) => {
    const response = await fetch('/api/content-structure', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-action': 'propose'
      },
      body: JSON.stringify({
        taskId,
        agentId,
        agentName,
        agentSpecialty,
        ...formState
      }),
    })
    
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || `Proposal generation failed for ${agentName}`)
    }
    
    return data.proposal
  }
  
  // Helper function to collect feedback
  const collectFeedback = async (fromAgentId: string, fromAgentName: string, toAgentId: string, proposalId: string) => {
    const response = await fetch('/api/content-structure', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-action': 'feedback'
      },
      body: JSON.stringify({
        taskId,
        fromAgentId,
        fromAgentName,
        toAgentId,
        proposalId,
        agentSpecialty: fromAgentId === 'marketResearch' ? 'market research' : 
                        fromAgentId === 'seoKeyword' ? 'SEO optimization' : 'content strategy'
      }),
    })
    
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || `Feedback collection failed from ${fromAgentName}`)
    }
    
    return data.feedback
  }
  
  // Get task status
  const getTaskStatus = async () => {
    if (!taskId) return
    
    try {
      const response = await fetch(`/api/content-structure?taskId=${taskId}`)
      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to get task status')
      }
      
      // Process task artifacts to extract content structure
      const task = data.task
      
      // Find the final content structure artifact
      const finalStructureArtifact = task.artifacts?.find(artifact => 
        artifact.name === 'Final Content Structure'
      )
      
      if (finalStructureArtifact) {
        const textPart = finalStructureArtifact.parts.find(part => part.type === 'text')
        if (textPart && textPart.text) {
          setContentStructure(JSON.parse(textPart.text))
        }
      }
    } catch (err) {
      console.error('Error getting task status:', err)
    }
  }
  
  // Render content structure
  const renderContentStructure = (structure: ContentStructure) => {
    return (
      <div className="content-structure">
        <h3>Content Structure</h3>
        
        <div className="structure-details">
          <div className="detail-item">
            <strong>Content Type:</strong> {structure.contentType}
          </div>
          <div className="detail-item">
            <strong>Content Objective:</strong> {structure.contentObjective}
          </div>
          <div className="detail-item">
            <strong>Topic Focus:</strong> {structure.topicFocus}
          </div>
          <div className="detail-item">
            <strong>Target Audience:</strong> {structure.targetAudience.primary}
            {structure.targetAudience.secondary && `, ${structure.targetAudience.secondary}`}
          </div>
          <div className="detail-item">
            <strong>Audience Needs:</strong>
            <ul>
              {structure.targetAudience.needs.map((need, index) => (
                <li key={index}>{need}</li>
              ))}
            </ul>
          </div>
          <div className="detail-item">
            <strong>Tone and Style:</strong> {structure.toneAndStyle}
          </div>
          <div className="detail-item">
            <strong>Keyword Strategy:</strong>
            <ul>
              <li><strong>Primary:</strong> {structure.keywordStrategy.primary}</li>
              <li><strong>Secondary:</strong> {structure.keywordStrategy.secondary}</li>
              <li><strong>Distribution:</strong> {structure.keywordStrategy.distribution}</li>
            </ul>
          </div>
        </div>
        
        <h4>Content Sections</h4>
        <div className="content-sections">
          {structure.sections.map((section, index) => (
            <div key={index} className="section-item">
              <h5>{section.title}</h5>
              <div className="section-details">
                <div><strong>Type:</strong> {section.type}</div>
                <div><strong>Purpose:</strong> {section.purpose}</div>
                {section.keyPoints && section.keyPoints.length > 0 && (
                  <div>
                    <strong>Key Points:</strong>
                    <ul>
                      {section.keyPoints.map((point, i) => (
                        <li key={i}>{point}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {section.keywordTargeting && section.keywordTargeting.length > 0 && (
                  <div>
                    <strong>Keyword Targeting:</strong>
                    <ul>
                      {section.keywordTargeting.map((keyword, i) => (
                        <li key={i}>{keyword}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {section.estimatedLength && (
                  <div><strong>Estimated Length:</strong> {section.estimatedLength}</div>
                )}
                {section.children && section.children.length > 0 && (
                  <div>
                    <strong>Subsections:</strong>
                    <ul>
                      {section.children.map((child, i) => (
                        <li key={i}>
                          <h6>{child.title}</h6>
                          <div>{child.purpose}</div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <h4>Content Differentiators</h4>
        <ul>
          {structure.contentDifferentiators.map((differentiator, index) => (
            <li key={index}>{differentiator}</li>
          ))}
        </ul>
        
        <div className="reasoning">
          <h4>Reasoning</h4>
          <p>{structure.reasoning}</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="content-structure-test-page">
      <header className="page-header">
        <h1>Dynamic Content Structure Test</h1>
        <p className="description">
          Test the dynamic content structure generation and negotiation API.
        </p>
      </header>
      
      <div className="tabs">
        <button 
          className={`tab-button ${activeTab === 'generate' ? 'active' : ''}`}
          onClick={() => setActiveTab('generate')}
        >
          Generate Structure
        </button>
        <button 
          className={`tab-button ${activeTab === 'negotiate' ? 'active' : ''}`}
          onClick={() => setActiveTab('negotiate')}
          disabled={!taskId}
        >
          Negotiate Structure
        </button>
        <button 
          className={`tab-button ${activeTab === 'result' ? 'active' : ''}`}
          onClick={() => setActiveTab('result')}
          disabled={!contentStructure}
        >
          View Result
        </button>
      </div>
      
      <div className="tab-content">
        {activeTab === 'generate' && (
          <div className="generate-tab">
            <h2>Generate Content Structure</h2>
            
            <div className="form-section">
              <div className="form-group">
                <label>Content Type</label>
                <select
                  value={formState.contentType}
                  onChange={(e) => handleInputChange('contentType', e.target.value)}
                  disabled={isLoading}
                >
                  <option value="blog-article">Blog Article</option>
                  <option value="product-page">Product Page</option>
                  <option value="buying-guide">Buying Guide</option>
                </select>
              </div>
              
              <div className="form-group">
                <label>Topic Focus</label>
                <input
                  type="text"
                  value={formState.topicFocus}
                  onChange={(e) => handleInputChange('topicFocus', e.target.value)}
                  placeholder="e.g., Benefits of AI in Content Marketing"
                  disabled={isLoading}
                />
              </div>
              
              <div className="form-group">
                <label>Target Audience</label>
                <input
                  type="text"
                  value={formState.targetAudience}
                  onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                  placeholder="e.g., Marketing Professionals"
                  disabled={isLoading}
                />
              </div>
              
              <div className="form-group">
                <label>Primary Keywords (comma-separated)</label>
                <textarea
                  value={keywordsInput}
                  onChange={(e) => handleKeywordsChange(e.target.value)}
                  placeholder="e.g., AI content, content marketing, AI tools"
                  disabled={isLoading}
                />
              </div>
              
              <div className="form-group">
                <label>Tone Preference</label>
                <select
                  value={formState.tonePreference}
                  onChange={(e) => handleInputChange('tonePreference', e.target.value)}
                  disabled={isLoading}
                >
                  <option value="Informative">Informative</option>
                  <option value="Conversational">Conversational</option>
                  <option value="Professional">Professional</option>
                  <option value="Authoritative">Authoritative</option>
                  <option value="Friendly">Friendly</option>
                </select>
              </div>
              
              <button
                className="primary-button"
                onClick={handleGenerateStructure}
                disabled={isLoading || !formState.topicFocus}
              >
                {isLoading ? 'Generating...' : 'Generate Content Structure'}
              </button>
              
              {error && <div className="error-message">{error}</div>}
            </div>
          </div>
        )}
        
        {activeTab === 'negotiate' && (
          <div className="negotiate-tab">
            <h2>Negotiate Content Structure</h2>
            
            <p>
              This will start a negotiation process between different agent personas to create an optimal content structure.
            </p>
            
            <button
              className="primary-button"
              onClick={handleStartNegotiation}
              disabled={isLoading || !taskId}
            >
              {isLoading ? 'Negotiating...' : 'Start Negotiation Process'}
            </button>
            
            {error && <div className="error-message">{error}</div>}
          </div>
        )}
        
        {activeTab === 'result' && contentStructure && (
          <div className="result-tab">
            <h2>Content Structure Result</h2>
            
            {renderContentStructure(contentStructure)}
            
            <div className="action-buttons">
              <button
                className="secondary-button"
                onClick={() => {
                  // Create a content generation request using the structure
                  window.location.href = '/admin/content-generation';
                }}
              >
                Use This Structure for Content Generation
              </button>
            </div>
          </div>
        )}
      </div>
      
      <style jsx>{`
        .content-structure-test-page {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }
        
        .page-header {
          margin-bottom: 2rem;
          border-bottom: 1px solid #eaeaea;
          padding-bottom: 1rem;
        }
        
        h1 {
          font-size: 2rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }
        
        .description {
          color: #666;
          font-size: 1.1rem;
          line-height: 1.5;
        }
        
        .tabs {
          display: flex;
          margin-bottom: 2rem;
          border-bottom: 1px solid #eaeaea;
        }
        
        .tab-button {
          padding: 0.75rem 1.5rem;
          background: none;
          border: none;
          cursor: pointer;
          font-size: 1rem;
          font-weight: 500;
          color: #666;
          border-bottom: 2px solid transparent;
        }
        
        .tab-button.active {
          color: #0070f3;
          border-bottom-color: #0070f3;
        }
        
        .tab-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        
        .form-section {
          background: #f9f9f9;
          padding: 1.5rem;
          border-radius: 8px;
          margin-bottom: 2rem;
        }
        
        .form-group {
          margin-bottom: 1.5rem;
        }
        
        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 500;
        }
        
        input, select, textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 1rem;
        }
        
        textarea {
          min-height: 100px;
          resize: vertical;
        }
        
        .primary-button {
          background-color: #0070f3;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 4px;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
        }
        
        .primary-button:hover {
          background-color: #0060df;
        }
        
        .primary-button:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
        
        .secondary-button {
          background-color: #f3f3f3;
          color: #333;
          border: 1px solid #ddd;
          padding: 0.75rem 1.5rem;
          border-radius: 4px;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
        }
        
        .secondary-button:hover {
          background-color: #e9e9e9;
        }
        
        .error-message {
          color: #d32f2f;
          margin-top: 1rem;
          padding: 0.75rem;
          background-color: #ffebee;
          border-radius: 4px;
        }
        
        .content-structure {
          background: #f9f9f9;
          padding: 1.5rem;
          border-radius: 8px;
        }
        
        .structure-details {
          margin-bottom: 2rem;
        }
        
        .detail-item {
          margin-bottom: 1rem;
        }
        
        .content-sections {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 1.5rem;
          margin-bottom: 2rem;
        }
        
        .section-item {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 1px solid #eaeaea;
        }
        
        .section-details {
          margin-top: 1rem;
        }
        
        .section-details > div {
          margin-bottom: 0.75rem;
        }
        
        .reasoning {
          background: white;
          padding: 1.5rem;
          border-radius: 8px;
          border: 1px solid #eaeaea;
        }
        
        .action-buttons {
          margin-top: 2rem;
          display: flex;
          justify-content: flex-end;
        }
      `}</style>
    </div>
  )
}

export default ContentStructureTestPage