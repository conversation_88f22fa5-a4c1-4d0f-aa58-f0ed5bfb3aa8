'use client';

import React from 'react';
import { Box, Typography, CircularProgress, Paper } from '@mui/material';
import GoalBasedDashboardSession from '../../../components/GoalBasedCollaboration/GoalBasedDashboardSession';

interface SessionPageProps {
  params: {
    id: string;
  };
}

/**
 * Session page component that displays a specific collaboration session
 */
export default function SessionPage({ params }: SessionPageProps) {
  const { id } = params;

  if (!id) {
    return (
      <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>
        <CircularProgress size={40} sx={{ mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Loading Session...
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please wait while we retrieve your session.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Goal-Based Collaboration Session
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Session ID: {id}
        </Typography>
      </Paper>

      <GoalBasedDashboardSession initialSessionId={id} />
    </Box>
  );
}
