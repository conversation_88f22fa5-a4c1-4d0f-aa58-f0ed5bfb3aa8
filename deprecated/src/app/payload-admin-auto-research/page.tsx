'use client'

import React from 'react'
import { Metadata } from 'next'
import AutomatedResearchForm from '@/components/Research/AutomatedResearchForm'

// // Declare the metadata for this page
// export const metadata: Metadata = {
//   title: 'Product Research & Generation',
//   description: 'Automated content generation for product data',
// }

// Custom admin page for the automated research tool
export default function AutomatedResearchPage() {
  return (
    <div className="custom-admin-page">
      <header className="page-header">
        <h1>Product Research & Content Generation</h1>
        <p className="description">
          Generate comprehensive product content automatically with AI. Fill in the form below
          to research a product and create content across multiple collections.
        </p>
      </header>

      {/* Render the research form component */}
      <AutomatedResearchForm />

      <footer className="page-footer">
        <p>
          This tool uses AI to generate content. Always review the generated content before publishing.
        </p>
      </footer>

      <style jsx>{`
        .custom-admin-page {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .page-header {
          margin-bottom: 2rem;
          border-bottom: 1px solid var(--theme-elevation-100);
          padding-bottom: 1rem;
        }

        h1 {
          font-size: 2rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .description {
          color: var(--theme-elevation-500);
          font-size: 1.1rem;
          line-height: 1.5;
        }

        .page-footer {
          margin-top: 3rem;
          padding-top: 1rem;
          border-top: 1px solid var(--theme-elevation-100);
          color: var(--theme-elevation-500);
          font-size: 0.9rem;
        }
      `}</style>
    </div>
  )
}