'use client'

import React, { useState, useEffect } from 'react'
import { v4 as uuidv4 } from 'uuid'

// Define the A2A task interface
interface A2ATask {
  id: string;
  status: {
    state: 'pending' | 'running' | 'completed' | 'failed';
    timestamp: string;
    message?: {
      role: string;
      parts: Array<{
        type: string;
        text?: string;
        data?: any;
      }>;
    };
  };
  history?: Array<{
    role: string;
    parts: Array<{
      type: string;
      text?: string;
      data?: any;
    }>;
  }>;
  artifacts?: Array<{
    name: string;
    parts: Array<{
      type: string;
      text?: string;
      data?: any;
    }>;
    index: number;
    lastChunk: boolean;
  }>;
  metadata?: any;
}

// Define the form state
interface FormState {
  agentEndpoint: string;
  userMessage: string;
  streamingEnabled: boolean;
}

// Define the available agents
const availableAgents = [
  { name: 'Market Research Agent', endpoint: '/api/agents/market-research' },
  { name: 'SEO Keyword Agent', endpoint: '/api/agents/seo-keyword' },
  { name: 'Content Strategy Agent', endpoint: '/api/agents/content-strategy' },
  { name: 'Content Generation Agent', endpoint: '/api/agents/content-generation' },
  { name: 'SEO Optimization Agent', endpoint: '/api/agents/seo-optimization' }
];

// Main component
const A2AProtocolTestPage: React.FC = () => {
  // State management
  const [formState, setFormState] = useState<FormState>({
    agentEndpoint: availableAgents[0].endpoint,
    userMessage: '',
    streamingEnabled: true
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [task, setTask] = useState<A2ATask | null>(null);
  const [activeTab, setActiveTab] = useState<'send' | 'task' | 'artifacts'>('send');
  const [streamingData, setStreamingData] = useState<any[]>([]);
  
  // Handle form input changes
  const handleInputChange = (name: string, value: any) => {
    setFormState(prev => ({ ...prev, [name]: value }));
  };
  
  // Send task to agent
  const handleSendTask = async () => {
    setIsLoading(true);
    setError(null);
    setStreamingData([]);
    
    try {
      // Generate a new task ID
      const newTaskId = uuidv4();
      setTaskId(newTaskId);
      
      // Create the JSON-RPC request
      const jsonRpcRequest = {
        jsonrpc: '2.0',
        id: 1,
        method: 'tasks/send',
        params: {
          id: newTaskId,
          message: {
            role: 'user',
            parts: [
              {
                type: 'text',
                text: formState.userMessage
              }
            ]
          },
          metadata: {
            streamingEnabled: formState.streamingEnabled
          }
        }
      };
      
      // Send the request to the agent
      const response = await fetch(formState.agentEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(jsonRpcRequest)
      });
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error.message || 'Task sending failed');
      }
      
      // If streaming is enabled, start polling for updates
      if (formState.streamingEnabled) {
        startPollingForUpdates(newTaskId);
      } else {
        // Otherwise, just get the task once
        await getTask(newTaskId);
      }
      
      setActiveTab('task');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Poll for task updates
  const startPollingForUpdates = async (taskId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const updatedTask = await getTask(taskId);
        
        // If task is completed or failed, stop polling
        if (updatedTask.status.state === 'completed' || updatedTask.status.state === 'failed') {
          clearInterval(pollInterval);
        }
      } catch (error) {
        console.error('Error polling for updates:', error);
        clearInterval(pollInterval);
      }
    }, 2000); // Poll every 2 seconds
    
    // Clean up interval on component unmount
    return () => clearInterval(pollInterval);
  };
  
  // Get task by ID
  const getTask = async (taskId: string): Promise<A2ATask> => {
    // Create the JSON-RPC request
    const jsonRpcRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tasks/get',
      params: {
        id: taskId
      }
    };
    
    // Send the request to the agent
    const response = await fetch(formState.agentEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(jsonRpcRequest)
    });
    
    const data = await response.json();
    
    if (data.error) {
      throw new Error(data.error.message || 'Failed to get task');
    }
    
    const updatedTask = data.result.task;
    setTask(updatedTask);
    
    // Update streaming data if there are artifacts
    if (updatedTask.artifacts && updatedTask.artifacts.length > 0) {
      setStreamingData(prev => {
        const newData = [...prev];
        updatedTask.artifacts.forEach(artifact => {
          // Check if we already have this artifact chunk
          const existingIndex = newData.findIndex(item => 
            item.name === artifact.name && item.index === artifact.index
          );
          
          if (existingIndex >= 0) {
            // Update existing chunk
            newData[existingIndex] = artifact;
          } else {
            // Add new chunk
            newData.push(artifact);
          }
        });
        return newData;
      });
    }
    
    return updatedTask;
  };
  
  // Cancel task
  const handleCancelTask = async () => {
    if (!taskId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Create the JSON-RPC request
      const jsonRpcRequest = {
        jsonrpc: '2.0',
        id: 3,
        method: 'tasks/cancel',
        params: {
          id: taskId
        }
      };
      
      // Send the request to the agent
      const response = await fetch(formState.agentEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(jsonRpcRequest)
      });
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error.message || 'Task cancellation failed');
      }
      
      // Update the task
      await getTask(taskId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Render JSON data in a readable format
  const renderJsonData = (data: any) => {
    return (
      <pre style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '10px', 
        borderRadius: '4px',
        overflow: 'auto',
        maxHeight: '400px'
      }}>
        {JSON.stringify(data, null, 2)}
      </pre>
    );
  };
  
  // Render artifacts
  const renderArtifacts = () => {
    if (!task || !task.artifacts || task.artifacts.length === 0) {
      return <p>No artifacts available.</p>;
    }
    
    return task.artifacts.map((artifact, index) => (
      <div key={`${artifact.name}-${index}`} className="card" style={{ marginBottom: '16px' }}>
        <div className="card-header">
          <h3>{artifact.name}</h3>
          <p>Chunk {artifact.index + 1}{artifact.lastChunk ? ' (Final)' : ''}</p>
        </div>
        <div className="card-content">
          {artifact.parts.map((part, partIndex) => (
            <div key={partIndex} style={{ marginBottom: '16px' }}>
              <h4>
                {part.type === 'text' ? 'Text Content:' : 'Data Content:'}
              </h4>
              {part.type === 'text' ? (
                <p>{part.text}</p>
              ) : (
                renderJsonData(part.data)
              )}
            </div>
          ))}
        </div>
      </div>
    ));
  };
  
  // Render streaming data
  const renderStreamingData = () => {
    if (streamingData.length === 0) {
      return <p>No streaming data available.</p>;
    }
    
    return streamingData.map((chunk, index) => (
      <div key={`stream-${index}`} className="card" style={{ marginBottom: '16px' }}>
        <div className="card-header">
          <h3>{chunk.name}</h3>
          <p>Chunk {chunk.index + 1}{chunk.lastChunk ? ' (Final)' : ''}</p>
        </div>
        <div className="card-content">
          {chunk.parts.map((part: any, partIndex: number) => (
            <div key={partIndex} style={{ marginBottom: '16px' }}>
              <h4>
                {part.type === 'text' ? 'Text Content:' : 'Data Content:'}
              </h4>
              {part.type === 'text' ? (
                <p>{part.text}</p>
              ) : (
                renderJsonData(part.data)
              )}
            </div>
          ))}
        </div>
      </div>
    ));
  };
  
  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ marginBottom: '16px' }}>A2A Protocol Test</h1>
      
      <div className="tabs" style={{ marginBottom: '24px' }}>
        <button 
          className={activeTab === 'send' ? 'active' : ''} 
          onClick={() => setActiveTab('send')}
          style={{
            padding: '8px 16px',
            marginRight: '8px',
            backgroundColor: activeTab === 'send' ? '#007bff' : '#f0f0f0',
            color: activeTab === 'send' ? 'white' : 'black',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Send Task
        </button>
        <button 
          className={activeTab === 'task' ? 'active' : ''} 
          onClick={() => setActiveTab('task')}
          style={{
            padding: '8px 16px',
            marginRight: '8px',
            backgroundColor: activeTab === 'task' ? '#007bff' : '#f0f0f0',
            color: activeTab === 'task' ? 'white' : 'black',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Task Status
        </button>
        <button 
          className={activeTab === 'artifacts' ? 'active' : ''} 
          onClick={() => setActiveTab('artifacts')}
          style={{
            padding: '8px 16px',
            backgroundColor: activeTab === 'artifacts' ? '#007bff' : '#f0f0f0',
            color: activeTab === 'artifacts' ? 'white' : 'black',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Artifacts
        </button>
      </div>
      
      {activeTab === 'send' && (
        <div className="card" style={{ 
          border: '1px solid #ddd', 
          borderRadius: '8px', 
          padding: '16px',
          marginBottom: '16px'
        }}>
          <h2 style={{ marginBottom: '16px' }}>Send Task to Agent</h2>
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px' }}>Agent</label>
            <select
              value={formState.agentEndpoint}
              onChange={(e) => handleInputChange('agentEndpoint', e.target.value)}
              style={{ 
                width: '100%', 
                padding: '8px', 
                borderRadius: '4px', 
                border: '1px solid #ddd' 
              }}
            >
              {availableAgents.map((agent) => (
                <option key={agent.endpoint} value={agent.endpoint}>
                  {agent.name}
                </option>
              ))}
            </select>
          </div>
          
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px' }}>User Message</label>
            <textarea
              rows={4}
              value={formState.userMessage}
              onChange={(e) => handleInputChange('userMessage', e.target.value)}
              style={{ 
                width: '100%', 
                padding: '8px', 
                borderRadius: '4px', 
                border: '1px solid #ddd' 
              }}
            />
          </div>
          
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px' }}>Streaming</label>
            <select
              value={formState.streamingEnabled.toString()}
              onChange={(e) => handleInputChange('streamingEnabled', e.target.value === 'true')}
              style={{ 
                width: '100%', 
                padding: '8px', 
                borderRadius: '4px', 
                border: '1px solid #ddd' 
              }}
            >
              <option value="true">Enabled</option>
              <option value="false">Disabled</option>
            </select>
          </div>
          
          <div style={{ display: 'flex', marginBottom: '16px' }}>
            <button
              onClick={handleSendTask}
              disabled={isLoading || !formState.userMessage}
              style={{
                padding: '8px 16px',
                backgroundColor: isLoading || !formState.userMessage ? '#cccccc' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                marginRight: '8px',
                cursor: isLoading || !formState.userMessage ? 'not-allowed' : 'pointer'
              }}
            >
              {isLoading ? 'Sending...' : 'Send Task'}
            </button>
            
            {taskId && (
              <button
                onClick={handleCancelTask}
                disabled={isLoading || !task || task.status.state === 'completed' || task.status.state === 'failed'}
                style={{
                  padding: '8px 16px',
                  backgroundColor: isLoading || !task || task.status.state === 'completed' || task.status.state === 'failed' ? '#cccccc' : '#dc3545',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: isLoading || !task || task.status.state === 'completed' || task.status.state === 'failed' ? 'not-allowed' : 'pointer'
                }}
              >
                Cancel Task
              </button>
            )}
          </div>
          
          {error && (
            <div style={{ 
              padding: '8px 16px', 
              backgroundColor: '#f8d7da', 
              color: '#721c24', 
              borderRadius: '4px', 
              marginTop: '16px' 
            }}>
              Error: {error}
            </div>
          )}
        </div>
      )}
      
      {activeTab === 'task' && (
        <div className="card" style={{ 
          border: '1px solid #ddd', 
          borderRadius: '8px', 
          padding: '16px',
          marginBottom: '16px'
        }}>
          <h2 style={{ marginBottom: '8px' }}>Task Status</h2>
          <p style={{ marginBottom: '16px' }}>{taskId ? `Task ID: ${taskId}` : 'No task sent yet'}</p>
          
          {isLoading && !task && (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <p>Loading...</p>
            </div>
          )}
          
          {task && (
            <>
              <div style={{ marginBottom: '16px' }}>
                <h3 style={{ marginBottom: '8px' }}>Status</h3>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                  <span style={{ marginRight: '8px' }}>State:</span>
                  <span style={{ 
                    padding: '4px 8px', 
                    borderRadius: '4px', 
                    backgroundColor: 
                      task.status.state === 'completed' ? '#28a745' : 
                      task.status.state === 'failed' ? '#dc3545' : 
                      task.status.state === 'running' ? '#007bff' : '#6c757d',
                    color: 'white',
                    fontSize: '14px'
                  }}>
                    {task.status.state}
                  </span>
                </div>
                <p style={{ fontSize: '14px' }}>
                  Timestamp: {new Date(task.status.timestamp).toLocaleString()}
                </p>
              </div>
              
              {task.status.message && (
                <div style={{ marginBottom: '16px' }}>
                  <h3 style={{ marginBottom: '8px' }}>Status Message</h3>
                  <p style={{ fontSize: '14px' }}>
                    Role: {task.status.message.role}
                  </p>
                  {task.status.message.parts.map((part, index) => (
                    <div key={index} style={{ marginTop: '8px' }}>
                      {part.type === 'text' ? (
                        <p>{part.text}</p>
                      ) : (
                        renderJsonData(part.data)
                      )}
                    </div>
                  ))}
                </div>
              )}
              
              {task.history && task.history.length > 0 && (
                <div style={{ marginBottom: '16px' }}>
                  <h3 style={{ marginBottom: '8px' }}>History</h3>
                  {task.history.map((message, index) => (
                    <div 
                      key={index} 
                      style={{ 
                        marginBottom: '16px', 
                        padding: '16px', 
                        backgroundColor: message.role === 'user' ? '#e3f2fd' : '#f5f5f5',
                        borderRadius: '8px'
                      }}
                    >
                      <h4 style={{ marginBottom: '8px' }}>
                        {message.role === 'user' ? 'User' : 'Agent'} (Message {index + 1})
                      </h4>
                      <div>
                        {message.parts.map((part, partIndex) => (
                          <div key={partIndex}>
                            {part.type === 'text' ? (
                              <p>{part.text}</p>
                            ) : (
                              renderJsonData(part.data)
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {formState.streamingEnabled && (
                <div style={{ marginBottom: '16px' }}>
                  <h3 style={{ marginBottom: '8px' }}>Streaming Data</h3>
                  {renderStreamingData()}
                </div>
              )}
            </>
          )}
          
          {!isLoading && !task && (
            <p>No task data available. Send a task first.</p>
          )}
        </div>
      )}
      
      {activeTab === 'artifacts' && (
        <div className="card" style={{ 
          border: '1px solid #ddd', 
          borderRadius: '8px', 
          padding: '16px',
          marginBottom: '16px'
        }}>
          <h2 style={{ marginBottom: '16px' }}>Task Artifacts</h2>
          
          {isLoading && !task && (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <p>Loading...</p>
            </div>
          )}
          
          {task ? (
            renderArtifacts()
          ) : (
            <p>No task data available. Send a task first.</p>
          )}
        </div>
      )}
      
      <style jsx>{`
        .card {
          border: 1px solid #ddd;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
          padding: 16px;
          border-bottom: 1px solid #ddd;
          background-color: #f8f9fa;
        }
        
        .card-content {
          padding: 16px;
        }
        
        .card-header h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
        }
        
        .card-header p {
          margin: 0;
          color: #6c757d;
          font-size: 14px;
        }
      `}</style>
    </div>
  );
};

export default A2AProtocolTestPage;