'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  CircularProgress,
  Divider,
  Chip,
  Grid,
  Card,
  CardContent,
  Container,
  Alert
} from '@mui/material';
import { dynamicCollaborationClient } from '../../../../lib/dynamic-collaboration-client';
import { DynamicWorkflowPhase } from '../../../../app/(payload)/api/agents/dynamic-collaboration-v2/types';

/**
 * Test page for the dynamic collaboration JSONRPC API
 */
export default function TestPage() {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string>('');
  const [sessions, setSessions] = useState<any[]>([]);
  const [sessionDetails, setSessionDetails] = useState<any>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [artifacts, setArtifacts] = useState<any[]>([]);
  const [userMessage, setUserMessage] = useState<string>('');
  const [artifactName, setArtifactName] = useState<string>('');
  const [artifactContent, setArtifactContent] = useState<string>('');
  const [publishTitle, setPublishTitle] = useState<string>('');
  const [articleContent, setArticleContent] = useState<string>('');

  // Load sessions on mount
  useEffect(() => {
    loadSessions();
  }, []);

  // Load sessions
  const loadSessions = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await dynamicCollaborationClient.listSessions();
      setSessions(result.sessions || []);
      setSuccess('Sessions loaded successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Error loading sessions: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Create a new session
  const createSession = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await dynamicCollaborationClient.createSession({
        topic: 'Test Session',
        contentType: 'blog-article',
        targetAudience: 'developers',
        tone: 'professional',
        keywords: ['test', 'api', 'jsonrpc']
      });

      setSessionId(result.sessionId);
      setSuccess(`Session created successfully: ${result.sessionId}`);
      loadSessions();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Error creating session: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Load session details
  const loadSessionDetails = async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await dynamicCollaborationClient.getSession(id);
      setSessionDetails(result);
      setSessionId(id);
      setSuccess(`Session details loaded successfully`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Error loading session details: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Load messages
  const loadMessages = async () => {
    if (!sessionId) {
      setError('No session selected');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await dynamicCollaborationClient.listMessages(sessionId);
      setMessages(result.messages || []);
      setSuccess('Messages loaded successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Error loading messages: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Load artifacts
  const loadArtifacts = async () => {
    if (!sessionId) {
      setError('No session selected');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await dynamicCollaborationClient.listArtifacts(sessionId);
      setArtifacts(result.artifacts || []);
      setSuccess('Artifacts loaded successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Error loading artifacts: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Send a message
  const sendMessage = async () => {
    if (!sessionId) {
      setError('No session selected');
      return;
    }

    if (!userMessage) {
      setError('Message cannot be empty');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await dynamicCollaborationClient.sendMessage(sessionId, userMessage);
      setUserMessage('');
      setSuccess('Message sent successfully');
      loadMessages();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Error sending message: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Create an artifact
  const createArtifact = async () => {
    if (!sessionId) {
      setError('No session selected');
      return;
    }

    if (!artifactName) {
      setError('Artifact name cannot be empty');
      return;
    }

    if (!artifactContent) {
      setError('Artifact content cannot be empty');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await dynamicCollaborationClient.createArtifact(
        sessionId,
        'test-artifact',
        artifactName,
        artifactContent
      );
      setArtifactName('');
      setArtifactContent('');
      setSuccess('Artifact created successfully');
      loadArtifacts();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Error creating artifact: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Publish content
  const handlePublishContent = async () => {
    if (!sessionId) {
      setError('No session selected');
      return;
    }

    if (!publishTitle) {
      setError('Title cannot be empty');
      return;
    }

    if (!articleContent) {
      setError('Content cannot be empty');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await dynamicCollaborationClient.publishContent(
        sessionId,
        publishTitle,
        articleContent
      );
      setPublishTitle('');
      setArticleContent('');
      setSuccess('Content published successfully');
      loadArtifacts();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Error publishing content: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Dynamic Collaboration JSONRPC API Test
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Sessions
            </Typography>
            <Button
              variant="contained"
              onClick={createSession}
              disabled={loading}
              sx={{ mb: 2 }}
            >
              Create New Session
            </Button>
            <Button
              variant="outlined"
              onClick={loadSessions}
              disabled={loading}
              sx={{ ml: 1, mb: 2 }}
            >
              Refresh
            </Button>

            {loading && sessions.length === 0 ? (
              <CircularProgress />
            ) : sessions.length === 0 ? (
              <Typography>No sessions found</Typography>
            ) : (
              <Grid container spacing={2}>
                {sessions.map((session) => (
                  <Grid item xs={12} key={session.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6">{session.topic}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          ID: {session.id}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Phase: {session.currentPhase}
                        </Typography>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => loadSessionDetails(session.id)}
                          sx={{ mt: 1 }}
                        >
                          Load Details
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Session Details
            </Typography>
            {sessionId ? (
              <Typography variant="body1" gutterBottom>
                Current Session: {sessionId}
              </Typography>
            ) : (
              <Typography variant="body1" gutterBottom>
                No session selected
              </Typography>
            )}

            {sessionDetails && (
              <Box>
                <Typography variant="body1">
                  Topic: {sessionDetails.topic}
                </Typography>
                <Typography variant="body1">
                  Phase: {sessionDetails.currentPhase}
                </Typography>
                <Typography variant="body1">
                  Status: {sessionDetails.status}
                </Typography>

                <Box sx={{ mt: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={loadMessages}
                    disabled={loading}
                    sx={{ mr: 1 }}
                  >
                    Load Messages
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={loadArtifacts}
                    disabled={loading}
                  >
                    Load Artifacts
                  </Button>
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Messages
            </Typography>

            <Box sx={{ mb: 2 }}>
              <TextField
                fullWidth
                label="Message"
                value={userMessage}
                onChange={(e) => setUserMessage(e.target.value)}
                disabled={!sessionId || loading}
                sx={{ mb: 1 }}
              />
              <Button
                variant="contained"
                onClick={sendMessage}
                disabled={!sessionId || !userMessage || loading}
              >
                Send Message
              </Button>
            </Box>

            <Divider sx={{ my: 2 }} />

            {messages.length === 0 ? (
              <Typography>No messages</Typography>
            ) : (
              <Box>
                {messages.map((message) => (
                  <Card key={message.id} variant="outlined" sx={{ mb: 1 }}>
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        From: {message.from} | To: {message.to}
                      </Typography>
                      <Typography variant="body1">
                        {typeof message.content === 'string'
                          ? message.content
                          : JSON.stringify(message.content)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(message.timestamp).toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Artifacts
            </Typography>

            <Box sx={{ mb: 2 }}>
              <TextField
                fullWidth
                label="Artifact Name"
                value={artifactName}
                onChange={(e) => setArtifactName(e.target.value)}
                disabled={!sessionId || loading}
                sx={{ mb: 1 }}
              />
              <TextField
                fullWidth
                label="Artifact Content"
                value={artifactContent}
                onChange={(e) => setArtifactContent(e.target.value)}
                disabled={!sessionId || loading}
                multiline
                rows={3}
                sx={{ mb: 1 }}
              />
              <Button
                variant="contained"
                onClick={createArtifact}
                disabled={!sessionId || !artifactName || !artifactContent || loading}
              >
                Create Artifact
              </Button>
            </Box>

            <Divider sx={{ my: 2 }} />

            {artifacts.length === 0 ? (
              <Typography>No artifacts</Typography>
            ) : (
              <Box>
                {artifacts.map((artifact) => (
                  <Card key={artifact.id} variant="outlined" sx={{ mb: 1 }}>
                    <CardContent>
                      <Typography variant="h6">
                        {artifact.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Type: {artifact.type} | Creator: {artifact.creator}
                      </Typography>
                      <Typography variant="body1" sx={{ mt: 1 }}>
                        {typeof artifact.content === 'string'
                          ? artifact.content.substring(0, 100) + (artifact.content.length > 100 ? '...' : '')
                          : JSON.stringify(artifact.content).substring(0, 100) + '...'}
                      </Typography>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Publish Content
            </Typography>

            <Box>
              <TextField
                fullWidth
                label="Title"
                value={publishTitle}
                onChange={(e) => setPublishTitle(e.target.value)}
                disabled={!sessionId || loading}
                sx={{ mb: 1 }}
              />
              <TextField
                fullWidth
                label="Content"
                value={articleContent}
                onChange={(e) => setArticleContent(e.target.value)}
                disabled={!sessionId || loading}
                multiline
                rows={5}
                sx={{ mb: 1 }}
              />
              <Button
                variant="contained"
                color="secondary"
                onClick={handlePublishContent}
                disabled={!sessionId || !publishTitle || !articleContent || loading}
              >
                Publish Content
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}
