'use client';

import React from 'react';
import { useSearchParams } from 'next/navigation';
import dynamic from 'next/dynamic';
import { Box, CircularProgress, Typography } from '@mui/material';

// Dynamically import the dashboard component with no SSR to avoid window is not defined errors
// This uses the updated dashboard component that works with the V2 API
const DynamicCollaborationDashboard = dynamic(
  () => import('@/components/DynamicCollaboration/DynamicCollaborationDashboard'),
  {
    ssr: false,
    loading: () => (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
        <CircularProgress size={60} sx={{ mb: 4 }} />
        <Typography variant="h5" gutterBottom>
          Loading Dynamic Collaboration Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Please wait while we initialize the collaborative agent system...
        </Typography>
      </Box>
    )
  }
);

/**
 * Dynamic Collaboration Dashboard Page
 *
 * This page integrates the dynamic collaboration dashboard component
 * and handles URL parameters for session ID.
 *
 * The dashboard now uses the V2 API with the new state management system,
 * providing improved type safety, performance, and maintainability.
 */
export default function DynamicCollaborationPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('sessionId');

  return (
    <div className="dynamic-collaboration-page">
      <DynamicCollaborationDashboard initialSessionId={sessionId || undefined} />
    </div>
  );
}
