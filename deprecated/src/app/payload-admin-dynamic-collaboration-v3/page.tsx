'use client';

import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import Link from 'next/link';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

/**
 * Admin page for Dynamic Collaboration V3
 */
export default function DynamicCollaborationV3AdminPage() {
  return (
    <Box sx={{ p: 4, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h3" gutterBottom>
        Dynamic Collaboration V3
      </Typography>
      
      <Typography variant="body1" paragraph>
        The Dynamic Collaboration V3 system is an advanced agent collaboration platform that enables 
        multiple specialized AI agents to work together to create high-quality content. This version 
        includes improved state management, robust error handling, and a more intuitive user interface.
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', mt: 4 }}>
        <Paper sx={{ p: 3, flex: '1 1 300px' }}>
          <Typography variant="h5" gutterBottom>
            Key Features
          </Typography>
          <ul>
            <li>Transactional state management with optimistic concurrency control</li>
            <li>State machine-based workflow orchestration</li>
            <li>Robust error handling and recovery mechanisms</li>
            <li>Improved artifact management and visualization</li>
            <li>Real-time collaboration network visualization</li>
            <li>Phase transition tracking and visualization</li>
            <li>Article preview with editing capabilities</li>
          </ul>
        </Paper>
        
        <Paper sx={{ p: 3, flex: '1 1 300px' }}>
          <Typography variant="h5" gutterBottom>
            Workflow Phases
          </Typography>
          <ol>
            <li><strong>Planning:</strong> Define goals and plan the workflow</li>
            <li><strong>Research:</strong> Conduct market research and keyword analysis</li>
            <li><strong>Creation:</strong> Develop content strategy and create content</li>
            <li><strong>Review:</strong> Optimize content for SEO and assess quality</li>
            <li><strong>Finalization:</strong> Finalize and publish the content</li>
          </ol>
        </Paper>
      </Box>
      
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Link href="/dynamic-collaboration-v3" passHref>
          <Button 
            variant="contained" 
            size="large" 
            endIcon={<ArrowForwardIcon />}
            sx={{ px: 4, py: 1.5 }}
          >
            Launch Dynamic Collaboration V3 Dashboard
          </Button>
        </Link>
      </Box>
      
      <Box sx={{ mt: 6 }}>
        <Typography variant="h5" gutterBottom>
          Technical Documentation
        </Typography>
        <Typography variant="body1" paragraph>
          The Dynamic Collaboration V3 system is built on a robust architecture that includes:
        </Typography>
        <ul>
          <li><strong>State Schema:</strong> Comprehensive schema definitions with runtime validation using Zod</li>
          <li><strong>Transactional State Store:</strong> Atomic operations with optimistic concurrency control</li>
          <li><strong>State Manager:</strong> High-level operations for state manipulation</li>
          <li><strong>Workflow State Machine:</strong> Explicit states, transitions, and guards</li>
          <li><strong>Workflow Orchestrator:</strong> Coordinates the state machine and agent interactions</li>
          <li><strong>API Layer:</strong> RESTful API for client-server communication</li>
          <li><strong>UI Components:</strong> React components for visualization and interaction</li>
        </ul>
      </Box>
    </Box>
  );
}
