'use client';

import { useState, useEffect } from 'react';
import { Box, Button, Container, Typography, Paper, TextField, MenuItem, Select, FormControl, InputLabel, CircularProgress, Alert } from '@mui/material';

export default function PipelinePage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [pipelineType, setPipelineType] = useState('dynamic-collaboration');
  const [formData, setFormData] = useState({
    topic: 'AI in Healthcare',
    targetAudience: 'Medical Professionals',
    contentType: 'Blog Post',
    tone: 'Professional'
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle pipeline type change
  const handlePipelineTypeChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    setPipelineType(e.target.value as string);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/pipeline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pipelineType,
          params: formData
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'An error occurred');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Test ping
  const handlePing = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/pipeline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pipelineType: 'ping',
          params: { message: 'Hello, Pipeline API!' }
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'An error occurred');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Pipeline API Test
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Quick Test
        </Typography>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={handlePing}
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Ping Pipeline API'}
        </Button>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Pipeline Request Form
        </Typography>
        <form onSubmit={handleSubmit}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="pipeline-type-label">Pipeline Type</InputLabel>
            <Select
              labelId="pipeline-type-label"
              id="pipeline-type"
              value={pipelineType}
              label="Pipeline Type"
              onChange={handlePipelineTypeChange}
            >
              <MenuItem value="dynamic-collaboration">Dynamic Collaboration</MenuItem>
              <MenuItem value="research">Research</MenuItem>
              <MenuItem value="content-generation">Content Generation</MenuItem>
              <MenuItem value="dynamic-structure">Dynamic Structure</MenuItem>
            </Select>
          </FormControl>

          <TextField
            fullWidth
            margin="normal"
            label="Topic"
            name="topic"
            value={formData.topic}
            onChange={handleInputChange}
          />

          <TextField
            fullWidth
            margin="normal"
            label="Target Audience"
            name="targetAudience"
            value={formData.targetAudience}
            onChange={handleInputChange}
          />

          <TextField
            fullWidth
            margin="normal"
            label="Content Type"
            name="contentType"
            value={formData.contentType}
            onChange={handleInputChange}
          />

          <TextField
            fullWidth
            margin="normal"
            label="Tone"
            name="tone"
            value={formData.tone}
            onChange={handleInputChange}
          />

          <Button 
            type="submit" 
            variant="contained" 
            color="primary" 
            sx={{ mt: 2 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Submit'}
          </Button>
        </form>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {result && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Result
          </Typography>
          <pre style={{ overflow: 'auto', maxHeight: '300px' }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </Paper>
      )}
    </Container>
  );
}
