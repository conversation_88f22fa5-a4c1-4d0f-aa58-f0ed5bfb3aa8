import { NextRequest, NextResponse } from 'next/server';

/**
 * Pipeline API endpoint redirector
 *
 * This endpoint redirects requests from /pipeline to /api/pipeline
 * to maintain compatibility with existing code.
 */
export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();

    console.log('Pipeline request received:', body);

    // Special case for ping command with no pipelineType
    if (Array.isArray(body) && body.length > 0 && Array.isArray(body[0]) && body[0][0] === 'ping') {
      console.log('Received ping command, handling directly');
      return NextResponse.json({
        status: 'success',
        message: 'Pipeline API is working!',
        received: body
      });
    }

    // Forward the request to the real pipeline endpoint
    const response = await fetch(`${req.nextUrl.origin}/api/pipeline`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    // Get the response data
    const data = await response.json();

    console.log('Pipeline response:', data);

    // Return the response from the real endpoint
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error in pipeline redirector:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error in pipeline redirector' },
      { status: 500 }
    );
  }
}
