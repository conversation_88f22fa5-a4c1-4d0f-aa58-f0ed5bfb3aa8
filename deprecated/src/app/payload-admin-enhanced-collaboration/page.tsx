'use client';

import React from 'react';
import IntegratedCollaborativeDashboard from '../../../../components/EnhancedCollaboration/IntegratedCollaborativeDashboard';

export default function EnhancedCollaborationPage() {
  return (
    <div className="custom-admin-page">
      <header className="page-header">
        <div className="header-main">
          <h1>Enhanced AI Content Collaboration</h1>
          <p className="description">
            Create high-quality, SEO-optimized content using our collaborative multi-agent system with dynamic workflows, 
            transparent reasoning, and a human-like content creation process.
          </p>
        </div>
        
        <div className="agent-workflow-overview">
          <h2>Collaborative Agent Workflow</h2>
          <div className="workflow-steps">
            <div className="workflow-step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h3>Planning Phase</h3>
                <p>Initial research and content strategy development</p>
              </div>
            </div>
            <div className="workflow-step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h3>Multi-Turn Discussion</h3>
                <p>Agents collaborate with feedback loops to refine ideas</p>
              </div>
            </div>
            <div className="workflow-step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h3>Execution Phase</h3>
                <p>Content generation with iterative improvements</p>
              </div>
            </div>
            <div className="workflow-step">
              <div className="step-number">4</div>
              <div className="step-content">
                <h3>Review Phase</h3>
                <p>SEO optimization and final content refinement</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="agent-team-intro">
        <h2>Your AI Content Team</h2>
        <div className="agent-cards">
          <div className="agent-card content-generation">
            <h3>Content Generation Agent</h3>
            <p>Creates engaging, high-quality content based on strategic direction</p>
          </div>
          <div className="agent-card market-research">
            <h3>Market Research Agent</h3>
            <p>Provides market insights and content trend analysis</p>
          </div>
          <div className="agent-card seo-keyword">
            <h3>SEO Keyword Agent</h3>
            <p>Identifies and optimizes content for search performance</p>
          </div>
          <div className="agent-card content-strategy">
            <h3>Content Strategy Agent</h3>
            <p>Develops content structure and strategic approach</p>
          </div>
          <div className="agent-card seo-optimization">
            <h3>SEO Optimization Agent</h3>
            <p>Enhances content for maximum search visibility</p>
          </div>
        </div>
      </div>

      <main className="dashboard-container">
        <IntegratedCollaborativeDashboard />
      </main>

      <footer className="page-footer">
        <div className="footer-content">
          <div className="footer-section">
            <h3>How It Works</h3>
            <p>
              Our system simulates a human content team with specialized AI agents that communicate and collaborate 
              on your content creation tasks. Each agent brings unique expertise to the process, resulting in 
              higher quality content than any single agent could produce.
            </p>
          </div>
          <div className="footer-section">
            <h3>Best Practices</h3>
            <ul>
              <li>Provide clear, specific topic descriptions for best results</li>
              <li>Include target keywords to improve SEO optimization</li>
              <li>Review and edit the generated content before publishing</li>
              <li>Use the agent logs to understand the creation process</li>
            </ul>
          </div>
        </div>
        <p className="footer-disclaimer">
          This tool uses a collaborative network of specialized AI agents with enhanced reasoning capabilities and chain-of-thought processing.
          Always review the generated content before publishing.  
        </p>
      </footer>

      <style jsx>{`
        .custom-admin-page {
          padding: 2rem;
          max-width: 1600px;
          margin: 0 auto;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Header Styles */
        .page-header {
          margin-bottom: 2.5rem;
          padding-bottom: 2rem;
          border-bottom: 1px solid #eee;
        }

        .header-main {
          margin-bottom: 2rem;
        }

        h1 {
          font-size: 2.5rem;
          margin-bottom: 0.5rem;
          font-weight: 600;
          background: linear-gradient(90deg, #3a7bd5, #00d2ff);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          display: inline-block;
        }

        h2 {
          font-size: 1.8rem;
          margin-bottom: 1.5rem;
          color: #333;
        }

        h3 {
          font-size: 1.2rem;
          margin-bottom: 0.5rem;
          font-weight: 600;
          color: #444;
        }

        .description {
          color: #555;
          font-size: 1.1rem;
          line-height: 1.6;
          max-width: 800px;
        }

        /* Workflow Steps Styles */
        .agent-workflow-overview {
          background-color: #f8f9fa;
          padding: 2rem;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .workflow-steps {
          display: flex;
          flex-wrap: wrap;
          gap: 1.5rem;
          margin-top: 1.5rem;
          justify-content: space-between;
        }

        .workflow-step {
          flex: 1;
          min-width: 200px;
          display: flex;
          align-items: flex-start;
          padding: 1rem;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
          transition: transform 0.2s, box-shadow 0.2s;
        }

        .workflow-step:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .step-number {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          background: linear-gradient(135deg, #3a7bd5, #00d2ff);
          color: white;
          border-radius: 50%;
          font-weight: bold;
          margin-right: 1rem;
          flex-shrink: 0;
        }

        .step-content p {
          margin-top: 0.5rem;
          color: #666;
          font-size: 0.9rem;
        }

        /* Agent Team Introduction */
        .agent-team-intro {
          margin: 3rem 0;
        }

        .agent-cards {
          display: flex;
          flex-wrap: wrap;
          gap: 1.5rem;
          margin-top: 1.5rem;
        }

        .agent-card {
          flex: 1;
          min-width: 200px;
          padding: 1.5rem;
          border-radius: 8px;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
          transition: transform 0.2s;
          position: relative;
          overflow: hidden;
        }

        .agent-card:hover {
          transform: translateY(-5px);
        }

        .agent-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          height: 5px;
          width: 100%;
          background: linear-gradient(90deg, #3a7bd5, #00d2ff);
        }

        .agent-card h3 {
          margin-bottom: 0.75rem;
        }

        .agent-card p {
          color: #666;
          font-size: 0.9rem;
          line-height: 1.5;
        }

        .content-generation::before { background: linear-gradient(90deg, #3a7bd5, #00d2ff); }
        .market-research::before { background: linear-gradient(90deg, #11998e, #38ef7d); }
        .seo-keyword::before { background: linear-gradient(90deg, #f12711, #f5af19); }
        .content-strategy::before { background: linear-gradient(90deg, #8e2de2, #4a00e0); }
        .seo-optimization::before { background: linear-gradient(90deg, #eb3349, #f45c43); }

        /* Dashboard Container */
        .dashboard-container {
          margin: 3rem 0;
          padding: 2rem;
          background-color: #f8f9fa;
          border-radius: 8px;
          box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        /* Footer Styles */
        .page-footer {
          margin-top: 4rem;
          padding-top: 2rem;
          border-top: 1px solid #eee;
          color: #555;
        }

        .footer-content {
          display: flex;
          flex-wrap: wrap;
          gap: 2rem;
          margin-bottom: 2rem;
        }

        .footer-section {
          flex: 1;
          min-width: 250px;
        }

        .footer-section h3 {
          margin-bottom: 1rem;
          font-size: 1.2rem;
          color: #444;
        }

        .footer-section p, .footer-section li {
          color: #666;
          line-height: 1.6;
          font-size: 0.9rem;
        }

        .footer-section ul {
          padding-left: 1.5rem;
        }

        .footer-section li {
          margin-bottom: 0.5rem;
        }

        .footer-disclaimer {
          font-size: 0.85rem;
          color: #888;
          padding-top: 1.5rem;
          border-top: 1px solid #eee;
          text-align: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .workflow-steps, .agent-cards, .footer-content {
            flex-direction: column;
          }
          
          .workflow-step, .agent-card {
            min-width: 100%;
          }
          
          .custom-admin-page {
            padding: 1rem;
          }
          
          h1 {
            font-size: 2rem;
          }
        }
      `}</style>
    </div>
  );
}


