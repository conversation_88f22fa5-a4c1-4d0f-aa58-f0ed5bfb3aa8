'use client';

import React from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import dynamic from 'next/dynamic';
import { useSearchParams } from 'next/navigation';

// Dynamically import the dashboard component with no SSR to avoid window is not defined errors
const GoalBasedDashboard = dynamic(
  () => import('@/components/GoalBasedCollaboration/GoalBasedDashboard'),
  {
    ssr: false,
    loading: () => (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh' }}>
        <CircularProgress size={60} sx={{ mb: 4 }} />
        <Typography variant="h5" gutterBottom>
          Loading Goal-Based Collaboration Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Please wait while we initialize the goal-based collaborative agent system...
        </Typography>
      </Box>
    )
  }
);

export default function GoalBasedCollaborationPage() {
  // Get the session ID from the URL if available
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('sessionId');

  return <GoalBasedDashboard initialSessionId={sessionId || undefined} />;
}
