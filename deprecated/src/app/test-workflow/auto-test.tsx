'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  Button,
  Container,
  Typography,
  Paper,
  CircularProgress,
  <PERSON><PERSON>,
  Stepper,
  Step,
  StepLabel,
  Divider,
  List,
  ListItem,
  ListItemText,
  Chip
} from '@mui/material';
import {
  DynamicWorkflowPhase,
  dynamicStateStore
} from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state/index';
import { generateMockArtifact } from './mock-artifact-generator';

export default function AutoTestWorkflow() {
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [state, setState] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [currentPhase, setCurrentPhase] = useState<DynamicWorkflowPhase>(DynamicWorkflowPhase.PLANNING);
  const [completedGoals, setCompletedGoals] = useState<string[]>([]);
  const [artifacts, setArtifacts] = useState<any[]>([]);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const [autoRunning, setAutoRunning] = useState(false);
  const [testStatus, setTestStatus] = useState<'idle' | 'running' | 'completed' | 'failed'>('idle');

  // Add a log entry
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString()} - ${message}`]);
  };

  // Initialize a new session
  const initializeSession = async () => {
    setLoading(true);
    setError(null);
    addLog('Initializing new session...');

    try {
      const response = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'initiate',
          params: {
            topic: 'CRM Systems',
            contentType: 'blog-article',
            targetAudience: 'business professionals',
            tone: 'informative',
            keywords: ['CRM', 'customer relationship management', 'sales']
          },
          id: Date.now().toString()
        })
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error.message || 'Failed to initialize session');
      }

      setSessionId(data.result.sessionId);
      addLog(`Session initialized with ID: ${data.result.sessionId}`);

      // Start polling for state updates
      startPolling(data.result.sessionId);

      return data.result.sessionId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error: ${errorMessage}`);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Start polling for state updates
  const startPolling = (id: string) => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    const interval = setInterval(() => {
      fetchState(id);
    }, 3000);

    setPollingInterval(interval);
  };

  // Stop polling
  const stopPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  };

  // Fetch the current state
  const fetchState = async (id: string) => {
    try {
      const response = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'getState',
          params: { sessionId: id },
          id: Date.now().toString()
        })
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error.message || 'Failed to fetch state');
      }

      setState(data.result.state);

      // Update current phase and completed goals
      if (data.result.state.currentPhase !== currentPhase) {
        setCurrentPhase(data.result.state.currentPhase);
        addLog(`Phase transitioned to: ${data.result.state.currentPhase}`);
      }

      if (data.result.state.completedGoals) {
        const newCompletedGoals = data.result.state.completedGoals.filter(
          (goal: string) => !completedGoals.includes(goal)
        );

        if (newCompletedGoals.length > 0) {
          setCompletedGoals(data.result.state.completedGoals);
          newCompletedGoals.forEach((goal: string) => {
            addLog(`Goal completed: ${goal}`);
          });
        }
      }

      // Update artifacts
      if (data.result.state.artifacts) {
        const artifactArray = Object.values(data.result.state.artifacts);
        if (artifactArray.length !== artifacts.length) {
          setArtifacts(artifactArray);
          addLog(`Artifacts updated: ${artifactArray.length} artifacts available`);
        }
      }

      return data.result.state;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      addLog(`Error fetching state: ${errorMessage}`);
      throw err;
    }
  };

  // Complete a goal
  const completeGoal = async (goalType: string) => {
    if (!sessionId) return;

    setLoading(true);
    addLog(`Attempting to complete goal: ${goalType}...`);

    try {
      // First, create a mock artifact for this goal
      const mockArtifact = generateMockArtifact(goalType, sessionId);
      addLog(`Generated mock artifact for ${goalType}`);

      // Add the artifact to the session using the JSON-RPC endpoint
      const addArtifactResponse = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'addArtifact',
          params: {
            sessionId,
            artifact: mockArtifact
          },
          id: Date.now().toString()
        })
      });

      const artifactData = await addArtifactResponse.json();

      if (!artifactData.success) {
        addLog(`Warning: Failed to add artifact: ${artifactData.error || 'Unknown error'}`);
      } else {
        addLog(`Added artifact ${mockArtifact.id} to session. Artifacts count: ${artifactData.result.artifactsCount}`);
      }

      // Get the current state
      const currentState = await dynamicStateStore.getState(sessionId);
      if (!currentState) {
        throw new Error(`Session ${sessionId} not found`);
      }

      // Find the goal with the specified type
      const goalsOfType = Object.entries(currentState.goals).filter(([_, g]) => g.type === goalType);

      if (goalsOfType.length === 0) {
        throw new Error(`No goals of type ${goalType} found`);
      }

      // Get the first goal of this type
      const [goalId] = goalsOfType[0];

      // Now complete the goal using the JSON-RPC endpoint
      addLog(`Completing goal ${goalType} with ID ${goalId} and artifact ${mockArtifact.id}`);

      const response = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'completeGoal',
          params: {
            sessionId,
            goalType,
            artifactId: mockArtifact.id,
            goalId
          },
          id: Date.now().toString()
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to complete goal');
      }

      // Log the response data
      addLog(`API response: ${JSON.stringify(data.result)}`);

      // Verify the goal was completed
      const updatedState = await dynamicStateStore.getState(sessionId);
      const isGoalCompleted = updatedState?.completedGoals?.includes(goalType) || false;
      const isArtifactAdded = Object.keys(updatedState?.artifacts || {}).includes(mockArtifact.id);

      addLog(`Goal ${goalType} completed successfully with artifact ${mockArtifact.id}`);
      addLog(`Goal in completedGoals: ${isGoalCompleted}, Artifact in state: ${isArtifactAdded}`);

      // If the artifact is not in the state, try to add it directly
      if (!isArtifactAdded) {
        addLog(`Artifact ${mockArtifact.id} not in state, adding it directly`);

        // Use the API to add the artifact
        const addArtifactResponse = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            jsonrpc: '2.0',
            method: 'addArtifact',
            params: {
              sessionId,
              artifact: mockArtifact
            },
            id: Date.now().toString()
          })
        });

        const artifactData = await addArtifactResponse.json();

        if (artifactData.error) {
          addLog(`Warning: Failed to add artifact: ${artifactData.error.message}`);
        } else {
          addLog(`Added artifact ${mockArtifact.id} to session`);
        }
      }

      // Refresh state
      await fetchState(sessionId);

      // Try to transition to the next phase if needed
      await checkPhaseTransition(sessionId, goalType);

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error completing goal: ${errorMessage}`);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Check if we need to transition to the next phase
  const checkPhaseTransition = async (sessionId: string, completedGoalType: string) => {
    try {
      // Get the current state
      const currentState = await dynamicStateStore.getState(sessionId);
      if (!currentState) return;

      const { currentPhase, completedGoals } = currentState;

      // Determine if we should transition based on completed goals
      let shouldTransition = false;
      let nextPhase: DynamicWorkflowPhase = currentPhase;

      if (currentPhase === DynamicWorkflowPhase.RESEARCH) {
        // If both market_research and keyword_analysis are completed, transition to CREATION
        if (completedGoals.includes('market_research') && completedGoals.includes('keyword_analysis')) {
          shouldTransition = true;
          nextPhase = DynamicWorkflowPhase.CREATION;
        }
      } else if (currentPhase === DynamicWorkflowPhase.CREATION) {
        // If content_strategy and content_creation are completed, transition to REVIEW
        if (completedGoals.includes('content_strategy') && completedGoals.includes('content_creation')) {
          shouldTransition = true;
          nextPhase = DynamicWorkflowPhase.REVIEW;
        }
      } else if (currentPhase === DynamicWorkflowPhase.REVIEW) {
        // If seo_optimization and quality_assessment are completed, transition to FINALIZATION
        if (completedGoals.includes('seo_optimization') && completedGoals.includes('quality_assessment')) {
          shouldTransition = true;
          nextPhase = DynamicWorkflowPhase.FINALIZATION;
        }
      }

      if (shouldTransition && nextPhase !== currentPhase) {
        addLog(`All goals for phase ${currentPhase} completed. Transitioning to ${nextPhase}...`);
        await transitionPhase(nextPhase);
      } else {
        addLog(`Staying in phase ${currentPhase} after completing ${completedGoalType}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      addLog(`Error checking phase transition: ${errorMessage}`);
    }
  };

  // Transition to next phase
  const transitionPhase = async (phase: DynamicWorkflowPhase) => {
    if (!sessionId) return false;

    setLoading(true);
    addLog(`Attempting to transition to phase: ${phase}...`);

    try {
      // Use the API to transition the phase
      const response = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'transitionPhase',
          params: {
            sessionId,
            phase
          },
          id: Date.now().toString()
        })
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error.message || 'Failed to transition phase');
      }

      addLog(`Transitioned to phase: ${phase}`);

      // Refresh state
      await fetchState(sessionId);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error transitioning phase: ${errorMessage}`);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Run the automated test
  const runAutomatedTest = async () => {
    setAutoRunning(true);
    setTestStatus('running');
    addLog('Starting automated test...');

    try {
      // Step 1: Initialize session
      addLog('Step 1: Initialize session');
      const id = await initializeSession();

      // Wait for initial state to be ready
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Step 2: Complete the entire workflow using the complete-workflow endpoint
      addLog('Step 2: Complete the entire workflow');

      const completeResponse = await fetch('/api/agents/dynamic-collaboration-v2/complete-workflow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: id
        })
      });

      const completeData = await completeResponse.json();

      if (!completeData.success) {
        throw new Error(completeData.error || 'Failed to complete workflow');
      }

      addLog(`Workflow completed successfully`);
      addLog(`Current phase: ${completeData.currentPhase}`);
      addLog(`Completed goals: ${JSON.stringify(completeData.completedGoals)}`);
      addLog(`Artifacts count: ${completeData.artifactsCount}`);
      addLog(`Generated artifacts count: ${completeData.generatedArtifactsCount}`);

      // Wait for 3 seconds
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Step 3: Verify final state
      addLog('Step 3: Verify final state');
      const finalState = await fetchState(id);

      // Log the current state for debugging
      addLog(`Current phase: ${finalState.currentPhase}`);
      addLog(`Completed goals: ${JSON.stringify(finalState.completedGoals)}`);
      addLog(`Artifacts: ${Object.keys(finalState.artifacts || {}).length}`);
      addLog(`Generated artifacts: ${JSON.stringify(finalState.generatedArtifacts)}`);

      // Check if all goals are completed
      const expectedGoals = [
        'market_research',
        'keyword_analysis',
        'content_strategy',
        'content_creation',
        'seo_optimization',
        'quality_assessment'
      ];

      const completedGoals = finalState.completedGoals || [];
      const missingGoals = expectedGoals.filter(goal => !completedGoals.includes(goal));

      if (missingGoals.length > 0) {
        addLog(`Missing goals: ${JSON.stringify(missingGoals)}`);
      }

      const allGoalsCompleted = missingGoals.length === 0;

      // Check if artifacts are generated
      const artifactCount = Object.keys(finalState.artifacts || {}).length;
      const hasArtifacts = artifactCount > 0;

      if (!hasArtifacts) {
        addLog('No artifacts found in the state');
      } else {
        addLog(`Found ${artifactCount} artifacts`);
      }

      if (allGoalsCompleted && hasArtifacts) {
        addLog('Test completed successfully! All goals completed and artifacts generated.');
        setTestStatus('completed');
      } else {
        addLog(`Test completed with issues. All goals completed: ${allGoalsCompleted}, Has artifacts: ${hasArtifacts}`);
        setTestStatus('failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      addLog(`Test failed: ${errorMessage}`);
      setTestStatus('failed');
    } finally {
      setAutoRunning(false);
    }
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  // Get phase label
  const getPhaseLabel = (phase: DynamicWorkflowPhase): string => {
    switch (phase) {
      case DynamicWorkflowPhase.PLANNING:
        return 'Planning';
      case DynamicWorkflowPhase.RESEARCH:
        return 'Research';
      case DynamicWorkflowPhase.CREATION:
        return 'Content Creation';
      case DynamicWorkflowPhase.REVIEW:
        return 'Review';
      case DynamicWorkflowPhase.FINALIZATION:
        return 'Finalization';
      default:
        return 'Unknown';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Automated Workflow Test
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Test Control
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={runAutomatedTest}
            disabled={autoRunning || testStatus === 'running'}
          >
            {autoRunning ? <CircularProgress size={24} /> : 'Run Automated Test'}
          </Button>

          <Button
            variant="outlined"
            color="secondary"
            onClick={() => {
              stopPolling();
              setSessionId(null);
              setState(null);
              setCurrentPhase(DynamicWorkflowPhase.PLANNING);
              setCompletedGoals([]);
              setArtifacts([]);
              setTestStatus('idle');
              addLog('Test reset');
            }}
            disabled={autoRunning}
          >
            Reset
          </Button>
        </Box>

        <Typography variant="body2">
          Test Status: <Chip
            label={testStatus}
            color={
              testStatus === 'completed' ? 'success' :
              testStatus === 'failed' ? 'error' :
              testStatus === 'running' ? 'primary' :
              'default'
            }
            size="small"
          />
        </Typography>

        {sessionId && (
          <Typography variant="body2" sx={{ mt: 1 }}>
            Session ID: <Chip label={sessionId} size="small" />
          </Typography>
        )}
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3, maxHeight: '400px', overflow: 'auto' }}>
        <Typography variant="h6" gutterBottom>
          Test Logs
        </Typography>
        <List dense>
          {logs.map((log, index) => (
            <ListItem key={index} divider>
              <ListItemText primary={log} />
            </ListItem>
          ))}
        </List>
      </Paper>

      {sessionId && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Current State
          </Typography>
          <Typography variant="body2" gutterBottom>
            Phase: <Chip label={getPhaseLabel(currentPhase)} color="primary" size="small" />
          </Typography>
          <Typography variant="body2" gutterBottom>
            Completed Goals: {completedGoals.length}
          </Typography>
          <Typography variant="body2" gutterBottom>
            Artifacts: {artifacts.length}
          </Typography>

          <Divider sx={{ my: 2 }} />

          <Typography variant="subtitle2" gutterBottom>
            Raw State:
          </Typography>
          <Box sx={{ maxHeight: '200px', overflow: 'auto' }}>
            <pre>{JSON.stringify(state, null, 2)}</pre>
          </Box>
        </Paper>
      )}
    </Container>
  );
}
