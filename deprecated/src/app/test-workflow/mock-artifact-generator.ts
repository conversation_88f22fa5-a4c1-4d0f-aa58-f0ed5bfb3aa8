import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a mock artifact for a specific goal type
 * @param goalType The type of goal to generate an artifact for
 * @param sessionId The session ID
 * @returns The generated artifact
 */
export function generateMockArtifact(goalType: string, sessionId: string) {
  const artifactId = uuidv4();
  const timestamp = new Date().toISOString();

  switch (goalType) {
    case 'market_research':
      return {
        id: artifactId,
        type: 'market_research_report',
        name: 'Market Research Report',
        createdBy: 'market-research-agent',
        createdAt: timestamp,
        status: 'completed',
        content: {
          targetAudience: {
            demographics: {
              age: '25-45',
              profession: 'Business professionals, IT managers, Sales teams',
              industry: 'Various industries with customer relationship needs'
            },
            psychographics: {
              painPoints: [
                'Difficulty managing customer data across departments',
                'Inefficient sales processes',
                'Poor customer retention',
                'Lack of actionable insights from customer data'
              ],
              goals: [
                'Improve customer relationships',
                'Increase sales efficiency',
                'Better data management',
                'Enhanced customer insights'
              ]
            }
          },
          marketTrends: [
            'Increasing adoption of cloud-based CRM solutions',
            'Integration of AI and machine learning in CRM systems',
            'Mobile CRM applications gaining popularity',
            'Focus on customer experience and personalization'
          ],
          competitorAnalysis: [
            {
              name: 'Salesforce',
              strengths: ['Market leader', 'Comprehensive features', 'Strong ecosystem'],
              weaknesses: ['Higher cost', 'Complex implementation', 'Steep learning curve']
            },
            {
              name: 'HubSpot',
              strengths: ['User-friendly', 'Good for SMBs', 'Free tier available'],
              weaknesses: ['Limited customization', 'Less robust for enterprise']
            },
            {
              name: 'Microsoft Dynamics',
              strengths: ['Integration with Microsoft products', 'Strong analytics'],
              weaknesses: ['Complex interface', 'Higher implementation costs']
            }
          ],
          contentGaps: [
            'Practical implementation guides for small businesses',
            'Cost-benefit analysis of different CRM systems',
            'Integration strategies with existing systems',
            'ROI measurement techniques'
          ]
        },
        metadata: {
          sessionId,
          goalType,
          version: 1
        }
      };

    case 'keyword_analysis':
      return {
        id: artifactId,
        type: 'keyword_analysis_report',
        name: 'SEO Keyword Analysis',
        createdBy: 'seo-agent',
        createdAt: timestamp,
        status: 'completed',
        content: {
          primaryKeywords: [
            { keyword: 'CRM systems', volume: 'High', competition: 'High', difficulty: 8 },
            { keyword: 'Customer relationship management', volume: 'High', competition: 'High', difficulty: 7 },
            { keyword: 'CRM software', volume: 'High', competition: 'High', difficulty: 9 }
          ],
          secondaryKeywords: [
            { keyword: 'CRM for small business', volume: 'Medium', competition: 'Medium', difficulty: 6 },
            { keyword: 'Best CRM tools', volume: 'Medium', competition: 'High', difficulty: 8 },
            { keyword: 'CRM implementation', volume: 'Medium', competition: 'Medium', difficulty: 5 },
            { keyword: 'CRM benefits', volume: 'Medium', competition: 'Medium', difficulty: 4 }
          ],
          longTailKeywords: [
            { keyword: 'How to choose the right CRM system', volume: 'Low', competition: 'Low', difficulty: 3 },
            { keyword: 'CRM implementation best practices', volume: 'Low', competition: 'Medium', difficulty: 4 },
            { keyword: 'CRM ROI calculation', volume: 'Low', competition: 'Low', difficulty: 2 }
          ],
          searchIntent: {
            informational: [
              'What is a CRM system',
              'Benefits of CRM software',
              'How CRM improves customer relationships'
            ],
            commercial: [
              'Best CRM for small business',
              'CRM software comparison',
              'CRM pricing plans'
            ],
            transactional: [
              'Buy CRM software',
              'CRM free trial',
              'CRM subscription'
            ]
          }
        },
        metadata: {
          sessionId,
          goalType,
          version: 1
        }
      };

    case 'content_strategy':
      return {
        id: artifactId,
        type: 'content_strategy_plan',
        name: 'Content Strategy Plan',
        createdBy: 'content-strategy-agent',
        createdAt: timestamp,
        status: 'completed',
        content: {
          title: 'The Ultimate Guide to CRM Systems: Boosting Business Relationships and Revenue',
          outline: [
            {
              section: 'Introduction',
              subsections: [
                'What is a CRM system and why it matters',
                'The evolution of customer relationship management',
                'How CRM transforms business operations'
              ]
            },
            {
              section: 'Types of CRM Systems',
              subsections: [
                'Operational CRM',
                'Analytical CRM',
                'Collaborative CRM',
                'Strategic CRM'
              ]
            },
            {
              section: 'Key Benefits of Implementing a CRM',
              subsections: [
                'Improved customer relationships',
                'Enhanced sales efficiency',
                'Better data management and insights',
                'Increased team collaboration'
              ]
            },
            {
              section: 'Choosing the Right CRM for Your Business',
              subsections: [
                'Assessing your business needs',
                'Key features to look for',
                'Cloud vs. On-premise solutions',
                'Budget considerations'
              ]
            },
            {
              section: 'Implementation Best Practices',
              subsections: [
                'Planning your CRM implementation',
                'Data migration strategies',
                'Training and adoption',
                'Common pitfalls to avoid'
              ]
            },
            {
              section: 'Measuring CRM Success',
              subsections: [
                'Key performance indicators',
                'ROI calculation',
                'Continuous improvement strategies'
              ]
            },
            {
              section: 'Future Trends in CRM',
              subsections: [
                'AI and machine learning integration',
                'Mobile CRM advancements',
                'Social CRM evolution',
                'Predictive analytics'
              ]
            },
            {
              section: 'Conclusion',
              subsections: [
                'Recap of CRM benefits',
                'Next steps for implementation',
                'Resources for further learning'
              ]
            }
          ],
          contentApproach: {
            tone: 'Informative and professional with practical insights',
            style: 'Comprehensive guide with actionable advice',
            format: 'Long-form article with sections, subsections, and visual elements',
            targetWordCount: '2500-3000 words'
          },
          keywordStrategy: {
            primaryKeywordDensity: '1-2%',
            secondaryKeywordPlacement: 'Headers, subheaders, and naturally throughout content',
            semanticKeywords: 'Customer management, business relationships, sales automation, data analytics'
          }
        },
        metadata: {
          sessionId,
          goalType,
          version: 1
        }
      };

    case 'content_creation':
      return {
        id: artifactId,
        type: 'article_draft',
        name: 'CRM Systems Article Draft',
        createdBy: 'content-creation-agent',
        createdAt: timestamp,
        status: 'completed',
        content: {
          title: 'The Ultimate Guide to CRM Systems: Boosting Business Relationships and Revenue',
          body: `# The Ultimate Guide to CRM Systems: Boosting Business Relationships and Revenue

## Introduction

In today's competitive business landscape, managing customer relationships effectively is no longer optional—it's essential for survival and growth. Customer Relationship Management (CRM) systems have emerged as powerful tools that help businesses organize, automate, and synchronize their sales, marketing, customer service, and technical support processes.

A CRM system serves as a central repository for customer data, interactions, and transactions, enabling businesses to better understand their customers, streamline processes, and ultimately drive revenue growth. Whether you're a small business owner or part of a large enterprise, implementing the right CRM system can transform how you engage with customers and manage your business operations.

## Types of CRM Systems

CRM systems come in various forms, each designed to address specific business needs:

### Operational CRM

Operational CRM focuses on automating and improving customer-facing processes such as sales, marketing, and service. It helps streamline day-to-day operations by managing leads, contacts, and customer interactions. Key features include:

- Contact and lead management
- Sales automation
- Marketing automation
- Service automation

### Analytical CRM

Analytical CRM systems analyze customer data to identify patterns and trends. This type of CRM helps businesses make data-driven decisions by providing insights into customer behavior, preferences, and needs. Key components include:

- Data warehousing
- Data mining
- OLAP (Online Analytical Processing)
- Reporting and dashboards

### Collaborative CRM

Collaborative CRM facilitates communication and collaboration between different departments within an organization, as well as with external stakeholders like suppliers and distributors. It ensures that customer information is shared across all touchpoints, creating a unified customer experience. Features include:

- Interaction management
- Channel management
- Document sharing
- Customer portals

### Strategic CRM

Strategic CRM focuses on collecting, integrating, and analyzing customer data to develop better customer-focused business strategies. It helps businesses understand customer needs and behaviors to create long-term, profitable relationships. Elements include:

- Customer segmentation
- Lifetime value analysis
- Personalization strategies
- Loyalty programs

## Key Benefits of Implementing a CRM

Implementing a CRM system offers numerous benefits that can significantly impact your business performance:

### Improved Customer Relationships

A CRM system provides a 360-degree view of your customers, including their purchase history, preferences, and interactions with your company. This comprehensive view enables you to:

- Personalize communications based on customer preferences
- Anticipate customer needs before they arise
- Resolve issues quickly and efficiently
- Build stronger, more meaningful relationships

### Enhanced Sales Efficiency

CRM systems streamline the sales process, making your sales team more productive and effective:

- Automate routine tasks like data entry and follow-up reminders
- Prioritize leads based on their likelihood to convert
- Track the sales pipeline from initial contact to closed deal
- Identify cross-selling and upselling opportunities

### Better Data Management and Insights

With a CRM system, customer data is centralized, organized, and accessible:

- Eliminate data silos between departments
- Ensure data accuracy and consistency
- Generate reports and analytics to inform business decisions
- Identify trends and patterns in customer behavior

### Increased Team Collaboration

CRM systems facilitate collaboration across departments:

- Share customer information seamlessly between sales, marketing, and customer service
- Ensure consistent messaging and approach
- Track customer interactions across all touchpoints
- Improve internal communication and coordination

## Choosing the Right CRM for Your Business

Selecting the right CRM system is crucial for successful implementation and adoption. Consider these factors when making your decision:

### Assessing Your Business Needs

Before evaluating CRM options, clearly define what you want to achieve:

- Identify pain points in your current customer management processes
- Determine which features are must-haves vs. nice-to-haves
- Consider your business size, industry, and specific requirements
- Involve stakeholders from different departments in the assessment process

### Key Features to Look For

While features vary across CRM systems, some essential capabilities include:

- Contact and lead management
- Sales pipeline tracking
- Email integration
- Reporting and analytics
- Mobile accessibility
- Integration capabilities with other business systems
- Customization options
- Automation features

### Cloud vs. On-Premise Solutions

CRM systems are typically available in two deployment models:

**Cloud-based CRM:**
- Lower upfront costs (subscription-based)
- Faster implementation
- Automatic updates and maintenance
- Accessibility from anywhere with internet connection
- Scalability as your business grows

**On-premise CRM:**
- One-time licensing fee (higher upfront cost)
- Greater control over data and security
- Customization flexibility
- No ongoing subscription fees
- Requires IT infrastructure and maintenance

### Budget Considerations

When budgeting for a CRM system, factor in:

- Initial purchase or subscription costs
- Implementation and customization expenses
- Training costs
- Ongoing maintenance and support
- Potential ROI and time to value

## Implementation Best Practices

A successful CRM implementation requires careful planning and execution:

### Planning Your CRM Implementation

- Set clear objectives and success metrics
- Create a detailed implementation timeline
- Assign roles and responsibilities
- Develop a change management strategy
- Plan for data migration and integration

### Data Migration Strategies

- Clean and deduplicate existing customer data
- Standardize data formats
- Prioritize which data to migrate
- Test migration processes before full implementation
- Validate data accuracy after migration

### Training and Adoption

- Provide comprehensive training for all users
- Create role-specific training materials
- Designate CRM champions within each department
- Offer ongoing support and resources
- Celebrate early wins to build momentum

### Common Pitfalls to Avoid

- Lack of executive sponsorship
- Insufficient user training
- Poor data quality
- Overly complex customization
- Failure to align CRM with business processes
- Neglecting change management

## Measuring CRM Success

To ensure your CRM investment delivers value, establish metrics to track success:

### Key Performance Indicators

- Customer acquisition cost
- Customer retention rate
- Sales cycle length
- Conversion rates
- Customer satisfaction scores
- Revenue growth
- User adoption rates

### ROI Calculation

Calculate the return on your CRM investment by:

- Quantifying time savings from automation
- Measuring increased sales productivity
- Tracking improved conversion rates
- Assessing customer retention improvements
- Comparing costs against revenue gains

### Continuous Improvement Strategies

- Regularly review CRM usage and performance
- Solicit feedback from users
- Stay updated on new features and capabilities
- Refine processes based on data and insights
- Periodically reassess your CRM strategy

## Future Trends in CRM

The CRM landscape continues to evolve with emerging technologies:

### AI and Machine Learning Integration

- Predictive lead scoring
- Automated data entry
- Intelligent customer insights
- Chatbots and virtual assistants
- Personalized recommendations

### Mobile CRM Advancements

- Enhanced mobile interfaces
- Location-based services
- Offline capabilities
- Voice commands and dictation
- Mobile-specific features for field sales

### Social CRM Evolution

- Social media integration
- Sentiment analysis
- Social listening capabilities
- Influencer identification
- Omnichannel engagement

### Predictive Analytics

- Customer behavior forecasting
- Churn prediction and prevention
- Next-best-action recommendations
- Sales forecasting
- Resource allocation optimization

## Conclusion

A well-implemented CRM system is more than just a technology solution—it's a strategic asset that can transform how your business acquires, serves, and retains customers. By centralizing customer data, automating processes, and providing valuable insights, CRM systems enable businesses of all sizes to build stronger customer relationships, increase operational efficiency, and drive sustainable growth.

As you consider implementing or upgrading your CRM system, remember that success depends not just on choosing the right technology, but also on aligning it with your business processes, ensuring user adoption, and continuously refining your approach based on results and feedback.

With the right CRM strategy in place, your business will be well-positioned to meet the evolving expectations of today's customers and stay ahead in an increasingly competitive marketplace.`,
          sections: [
            'Introduction',
            'Types of CRM Systems',
            'Key Benefits of Implementing a CRM',
            'Choosing the Right CRM for Your Business',
            'Implementation Best Practices',
            'Measuring CRM Success',
            'Future Trends in CRM',
            'Conclusion'
          ],
          wordCount: 2850,
          readingTime: '14 minutes'
        },
        metadata: {
          sessionId,
          goalType,
          version: 1
        }
      };

    case 'seo_optimization':
      return {
        id: artifactId,
        type: 'seo_optimization_report',
        name: 'SEO Optimization Report',
        createdBy: 'seo-agent',
        createdAt: timestamp,
        status: 'completed',
        content: {
          keywordAnalysis: {
            primaryKeywordUsage: {
              keyword: 'CRM systems',
              count: 24,
              density: '0.84%',
              recommendation: 'Good density, well-distributed throughout the content'
            },
            secondaryKeywordUsage: [
              {
                keyword: 'Customer relationship management',
                count: 12,
                density: '0.42%',
                recommendation: 'Well-optimized'
              },
              {
                keyword: 'CRM implementation',
                count: 8,
                density: '0.28%',
                recommendation: 'Well-optimized'
              }
            ],
            missingKeywords: [
              {
                keyword: 'CRM ROI calculation',
                recommendation: 'Add more specific content about calculating ROI'
              }
            ]
          },
          onPageOptimization: {
            title: {
              original: 'The Ultimate Guide to CRM Systems: Boosting Business Relationships and Revenue',
              optimized: 'The Ultimate Guide to CRM Systems: Boost Relationships & Revenue',
              recommendation: 'Title is well-optimized but slightly long. Consider the shorter version.'
            },
            headings: {
              h1: {
                count: 1,
                containsKeyword: true,
                recommendation: 'Good H1 usage'
              },
              h2: {
                count: 8,
                containsKeyword: true,
                recommendation: 'Good H2 structure and keyword usage'
              },
              h3: {
                count: 16,
                containsKeyword: true,
                recommendation: 'Good H3 usage throughout the content'
              }
            },
            contentLength: {
              wordCount: 2850,
              recommendation: 'Excellent length for comprehensive coverage of the topic'
            },
            readability: {
              score: 'Good',
              recommendation: 'Content is well-structured and easy to read'
            }
          },
          technicalSEO: {
            metaDescription: {
              original: '',
              optimized: 'Discover how CRM systems can transform your business relationships and boost revenue. Our comprehensive guide covers types, benefits, implementation, and future trends.',
              recommendation: 'Add the optimized meta description'
            },
            urlStructure: {
              recommended: 'ultimate-guide-to-crm-systems',
              recommendation: 'Use the recommended URL structure'
            },
            internalLinking: {
              opportunities: [
                'Link to related content about sales automation',
                'Link to CRM comparison guides',
                'Link to implementation case studies'
              ]
            },
            imageOptimization: {
              recommendation: 'Add relevant images with descriptive alt text containing target keywords'
            }
          },
          contentEnhancements: {
            suggestedAdditions: [
              'Add a FAQ section addressing common CRM questions',
              'Include customer success stories or case studies',
              'Add a comparison table of popular CRM solutions',
              'Include statistics on CRM ROI and business impact'
            ],
            callToAction: {
              recommendation: 'Add a strong call-to-action encouraging readers to assess their CRM needs or request a consultation'
            }
          }
        },
        metadata: {
          sessionId,
          goalType,
          version: 1
        }
      };

    case 'quality_assessment':
      return {
        id: artifactId,
        type: 'quality_assessment_report',
        name: 'Content Quality Assessment',
        createdBy: 'quality-assessment-agent',
        createdAt: timestamp,
        status: 'completed',
        content: {
          overallScore: 92,
          accuracyScore: 95,
          comprehensivenessScore: 90,
          engagementScore: 88,
          seoScore: 94,
          readabilityScore: 91,
          summary: 'The article provides a comprehensive and well-structured guide to CRM systems. It covers all essential aspects from types and benefits to implementation and future trends. The content is accurate, informative, and optimized for SEO with good keyword usage and structure.',
          strengths: [
            'Comprehensive coverage of the topic',
            'Well-structured with clear sections and subsections',
            'Good balance of introductory and advanced information',
            'Effective use of keywords without keyword stuffing',
            'Practical advice and actionable insights'
          ],
          areasForImprovement: [
            'Could include more specific examples or case studies',
            'Some sections could benefit from visual elements',
            'Consider adding more industry-specific insights',
            'Could expand the section on ROI calculation with specific formulas'
          ],
          recommendations: [
            'Add 2-3 customer success stories to illustrate CRM benefits',
            'Include a comparison table of top CRM solutions',
            'Add more statistics to support key points',
            'Consider breaking up longer paragraphs for better readability',
            'Add a downloadable CRM assessment checklist as a lead magnet'
          ],
          finalVerdict: 'The article is of high quality and ready for publication with minor enhancements. It effectively addresses the target audience needs and is well-optimized for search engines.'
        },
        metadata: {
          sessionId,
          goalType,
          version: 1
        }
      };

    default:
      return {
        id: artifactId,
        type: 'generic_artifact',
        name: 'Generic Artifact',
        createdBy: 'system',
        createdAt: timestamp,
        status: 'completed',
        content: {
          message: `Mock artifact for ${goalType}`,
          details: 'This is a placeholder artifact generated for testing purposes.'
        },
        metadata: {
          sessionId,
          goalType,
          version: 1
        }
      };
  }
}
