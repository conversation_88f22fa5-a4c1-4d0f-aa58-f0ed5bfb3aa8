'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Typography,
  Paper,
  CircularProgress,
  <PERSON><PERSON>,
  Stepper,
  Step,
  StepLabel,
  Divider,
  List,
  ListItem,
  ListItemText,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import { DynamicWorkflowPhase, dynamicStateStore } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state/index';
import AutoTestWorkflow from './auto-test';
import { generateMockArtifact } from './mock-artifact-generator';

export default function TestWorkflowPage() {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Dynamic Collaboration Workflow Testing
      </Typography>

      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} centered>
          <Tab label="Manual Testing" />
          <Tab label="Automated Testing" />
        </Tabs>
      </Paper>

      {activeTab === 0 ? (
        <ManualTestWorkflow />
      ) : (
        <AutoTestWorkflow />
      )}
    </Container>
  );
}

function ManualTestWorkflow() {
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [state, setState] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [currentPhase, setCurrentPhase] = useState<DynamicWorkflowPhase>(DynamicWorkflowPhase.PLANNING);
  const [completedGoals, setCompletedGoals] = useState<string[]>([]);
  const [artifacts, setArtifacts] = useState<any[]>([]);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // Add a log entry
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString()} - ${message}`]);
  };

  // Initialize a new session
  const initializeSession = async () => {
    setLoading(true);
    setError(null);
    addLog('Initializing new session...');

    try {
      const response = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'initiate',
          params: {
            topic: 'CRM Systems',
            contentType: 'blog-article',
            targetAudience: 'business professionals',
            tone: 'informative',
            keywords: ['CRM', 'customer relationship management', 'sales']
          },
          id: Date.now().toString()
        })
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error.message || 'Failed to initialize session');
      }

      setSessionId(data.result.sessionId);
      addLog(`Session initialized with ID: ${data.result.sessionId}`);

      // Start polling for state updates
      startPolling(data.result.sessionId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Start polling for state updates
  const startPolling = (id: string) => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    const interval = setInterval(() => {
      fetchState(id);
    }, 3000);

    setPollingInterval(interval);
  };

  // Stop polling
  const stopPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  };

  // Fetch the current state
  const fetchState = async (id: string) => {
    try {
      const response = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'getState',
          params: { sessionId: id },
          id: Date.now().toString()
        })
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error.message || 'Failed to fetch state');
      }

      setState(data.result.state);

      // Update current phase and completed goals
      if (data.result.state.currentPhase !== currentPhase) {
        setCurrentPhase(data.result.state.currentPhase);
        addLog(`Phase transitioned to: ${data.result.state.currentPhase}`);
      }

      if (data.result.state.completedGoals) {
        const newCompletedGoals = data.result.state.completedGoals.filter(
          (goal: string) => !completedGoals.includes(goal)
        );

        if (newCompletedGoals.length > 0) {
          setCompletedGoals(data.result.state.completedGoals);
          newCompletedGoals.forEach((goal: string) => {
            addLog(`Goal completed: ${goal}`);
          });
        }
      }

      // Update artifacts
      if (data.result.state.artifacts) {
        const artifactArray = Object.values(data.result.state.artifacts);
        if (artifactArray.length !== artifacts.length) {
          setArtifacts(artifactArray);
          addLog(`Artifacts updated: ${artifactArray.length} artifacts available`);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      addLog(`Error fetching state: ${errorMessage}`);
    }
  };

  // Complete a goal
  const completeGoal = async (goalType: string) => {
    if (!sessionId) return;

    setLoading(true);
    addLog(`Attempting to complete goal: ${goalType}...`);

    try {
      // First, create a mock artifact for this goal
      const mockArtifact = generateMockArtifact(goalType, sessionId);
      addLog(`Generated mock artifact for ${goalType}`);

      // Add the artifact to the session using the JSON-RPC endpoint
      const addArtifactResponse = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'addArtifact',
          params: {
            sessionId,
            artifact: mockArtifact
          },
          id: Date.now().toString()
        })
      });

      const artifactData = await addArtifactResponse.json();

      if (!artifactData.success) {
        addLog(`Warning: Failed to add artifact: ${artifactData.error || 'Unknown error'}`);
      } else {
        addLog(`Added artifact ${mockArtifact.id} to session. Artifacts count: ${artifactData.result.artifactsCount}`);
      }

      // Now complete the goal using the JSON-RPC endpoint
      const response = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'completeGoal',
          params: {
            sessionId,
            goalType,
            artifactId: mockArtifact.id
          },
          id: Date.now().toString()
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to complete goal');
      }

      addLog(`Goal ${goalType} completed successfully with artifact ${mockArtifact.id}`);

      // Refresh state
      fetchState(sessionId);

      // Check if we need to transition to the next phase
      const state = await fetch('/api/agents/dynamic-collaboration-v2/test-utils', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'getState',
          sessionId
        })
      }).then(res => res.json());

      if (state.success) {
        const { currentPhase, completedGoals } = state.result.state;

        // Determine if we should transition based on completed goals
        let shouldTransition = false;
        let nextPhase = currentPhase;

        if (currentPhase === DynamicWorkflowPhase.RESEARCH) {
          // If both market_research and keyword_analysis are completed, transition to CREATION
          if (completedGoals.includes('market_research') && completedGoals.includes('keyword_analysis')) {
            shouldTransition = true;
            nextPhase = DynamicWorkflowPhase.CREATION;
          }
        } else if (currentPhase === DynamicWorkflowPhase.CREATION) {
          // If content_strategy and content_creation are completed, transition to REVIEW
          if (completedGoals.includes('content_strategy') && completedGoals.includes('content_creation')) {
            shouldTransition = true;
            nextPhase = DynamicWorkflowPhase.REVIEW;
          }
        } else if (currentPhase === DynamicWorkflowPhase.REVIEW) {
          // If seo_optimization and quality_assessment are completed, transition to FINALIZATION
          if (completedGoals.includes('seo_optimization') && completedGoals.includes('quality_assessment')) {
            shouldTransition = true;
            nextPhase = DynamicWorkflowPhase.FINALIZATION;
          }
        }

        if (shouldTransition && nextPhase !== currentPhase) {
          addLog(`All goals for phase ${currentPhase} completed. Transitioning to ${nextPhase}...`);
          await transitionPhase(nextPhase);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error completing goal: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Transition to next phase
  const transitionPhase = async (phase: DynamicWorkflowPhase) => {
    if (!sessionId) return;

    setLoading(true);
    addLog(`Attempting to transition to phase: ${phase}...`);

    try {
      // Use the JSON-RPC endpoint for phase transition
      const response = await fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'transitionPhase',
          params: {
            sessionId,
            phase
          },
          id: Date.now().toString()
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to transition phase');
      }

      addLog(`Transitioned to phase: ${phase}`);

      // Refresh state
      fetchState(sessionId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error transitioning phase: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  // Get phase label
  const getPhaseLabel = (phase: DynamicWorkflowPhase): string => {
    switch (phase) {
      case DynamicWorkflowPhase.PLANNING:
        return 'Planning';
      case DynamicWorkflowPhase.RESEARCH:
        return 'Research';
      case DynamicWorkflowPhase.CREATION:
        return 'Content Creation';
      case DynamicWorkflowPhase.REVIEW:
        return 'Review';
      case DynamicWorkflowPhase.FINALIZATION:
        return 'Finalization';
      default:
        return 'Unknown';
    }
  };

  // Get current phase index
  const getCurrentPhaseIndex = (): number => {
    const phases = [
      DynamicWorkflowPhase.PLANNING,
      DynamicWorkflowPhase.RESEARCH,
      DynamicWorkflowPhase.CREATION,
      DynamicWorkflowPhase.REVIEW,
      DynamicWorkflowPhase.FINALIZATION
    ];

    const index = phases.indexOf(currentPhase);
    return index >= 0 ? index : 0;
  };

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Session Control
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={initializeSession}
            disabled={loading || !!sessionId}
          >
            {loading ? <CircularProgress size={24} /> : 'Initialize New Session'}
          </Button>

          {sessionId && (
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => {
                stopPolling();
                setSessionId(null);
                setState(null);
                setCurrentPhase(DynamicWorkflowPhase.PLANNING);
                setCompletedGoals([]);
                setArtifacts([]);
                addLog('Session reset');
              }}
            >
              Reset
            </Button>
          )}
        </Box>

        {sessionId && (
          <Typography variant="body2">
            Session ID: <Chip label={sessionId} size="small" />
          </Typography>
        )}
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {sessionId && (
        <>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Workflow Progress
            </Typography>
            <Stepper activeStep={getCurrentPhaseIndex()} alternativeLabel sx={{ mb: 2 }}>
              {[
                DynamicWorkflowPhase.PLANNING,
                DynamicWorkflowPhase.RESEARCH,
                DynamicWorkflowPhase.CREATION,
                DynamicWorkflowPhase.REVIEW,
                DynamicWorkflowPhase.FINALIZATION
              ].map((phase) => (
                <Step key={phase} completed={getCurrentPhaseIndex() > [
                  DynamicWorkflowPhase.PLANNING,
                  DynamicWorkflowPhase.RESEARCH,
                  DynamicWorkflowPhase.CREATION,
                  DynamicWorkflowPhase.REVIEW,
                  DynamicWorkflowPhase.FINALIZATION
                ].indexOf(phase)}>
                  <StepLabel>{getPhaseLabel(phase)}</StepLabel>
                </Step>
              ))}
            </Stepper>

            <Typography variant="body2" sx={{ mb: 2 }}>
              Current Phase: <Chip label={getPhaseLabel(currentPhase)} color="primary" size="small" />
            </Typography>

            <Typography variant="h6" gutterBottom>
              Phase Transition
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              <Button
                size="small"
                variant="outlined"
                onClick={() => transitionPhase(DynamicWorkflowPhase.RESEARCH)}
                disabled={loading || currentPhase !== DynamicWorkflowPhase.PLANNING}
              >
                To Research
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => transitionPhase(DynamicWorkflowPhase.CREATION)}
                disabled={loading || currentPhase !== DynamicWorkflowPhase.RESEARCH}
              >
                To Creation
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => transitionPhase(DynamicWorkflowPhase.REVIEW)}
                disabled={loading || currentPhase !== DynamicWorkflowPhase.CREATION}
              >
                To Review
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => transitionPhase(DynamicWorkflowPhase.FINALIZATION)}
                disabled={loading || currentPhase !== DynamicWorkflowPhase.REVIEW}
              >
                To Finalization
              </Button>
            </Box>
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Goals
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              <Button
                size="small"
                variant="outlined"
                onClick={() => completeGoal('market_research')}
                disabled={loading || completedGoals.includes('market_research')}
                color={completedGoals.includes('market_research') ? 'success' : 'primary'}
              >
                Complete Market Research
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => completeGoal('keyword_analysis')}
                disabled={loading || completedGoals.includes('keyword_analysis')}
                color={completedGoals.includes('keyword_analysis') ? 'success' : 'primary'}
              >
                Complete Keyword Analysis
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => completeGoal('content_strategy')}
                disabled={loading || completedGoals.includes('content_strategy')}
                color={completedGoals.includes('content_strategy') ? 'success' : 'primary'}
              >
                Complete Content Strategy
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => completeGoal('content_creation')}
                disabled={loading || completedGoals.includes('content_creation')}
                color={completedGoals.includes('content_creation') ? 'success' : 'primary'}
              >
                Complete Content Creation
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => completeGoal('seo_optimization')}
                disabled={loading || completedGoals.includes('seo_optimization')}
                color={completedGoals.includes('seo_optimization') ? 'success' : 'primary'}
              >
                Complete SEO Optimization
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => completeGoal('quality_assessment')}
                disabled={loading || completedGoals.includes('quality_assessment')}
                color={completedGoals.includes('quality_assessment') ? 'success' : 'primary'}
              >
                Complete Quality Assessment
              </Button>
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              Completed Goals:
            </Typography>
            {completedGoals.length > 0 ? (
              <List dense>
                {completedGoals.map((goal) => (
                  <ListItem key={goal}>
                    <ListItemText primary={goal} />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No goals completed yet
              </Typography>
            )}
          </Paper>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Artifacts ({artifacts.length})
            </Typography>
            {artifacts.length > 0 ? (
              <List>
                {artifacts.map((artifact: any) => (
                  <ListItem key={artifact.id} divider>
                    <ListItemText
                      primary={artifact.name || artifact.type}
                      secondary={
                        <>
                          <Typography variant="body2" component="span">
                            Type: {artifact.type}
                          </Typography>
                          <br />
                          <Typography variant="body2" component="span">
                            Created by: {artifact.createdBy}
                          </Typography>
                          <br />
                          <Typography variant="body2" component="span">
                            Status: <Chip label={artifact.status} size="small" />
                          </Typography>
                        </>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No artifacts generated yet
              </Typography>
            )}
          </Paper>
        </>
      )}

      <Paper sx={{ p: 3, mb: 3, maxHeight: '400px', overflow: 'auto' }}>
        <Typography variant="h6" gutterBottom>
          Logs
        </Typography>
        <List dense>
          {logs.map((log, index) => (
            <ListItem key={index} divider>
              <ListItemText primary={log} />
            </ListItem>
          ))}
        </List>
      </Paper>
    </Box>
  );
}
