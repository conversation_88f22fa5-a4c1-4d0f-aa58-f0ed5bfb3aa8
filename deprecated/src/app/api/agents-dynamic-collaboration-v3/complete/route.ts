/**
 * Dynamic Collaboration V3 Complete API Route
 *
 * This file implements the API route for completing a dynamic collaboration session.
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../../../(payload)/api/agents/collaborative-iteration/utils/logger';
import { WorkflowOrchestrator } from '../../../../../(payload)/api/agents/dynamic-collaboration-v3';

/**
 * POST handler for completing a dynamic collaboration session
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    
    // Extract parameters
    const { sessionId } = body;
    
    // Validate required parameters
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }
    
    // Create orchestrator
    const orchestrator = new WorkflowOrchestrator(sessionId);
    
    // Complete session
    const success = await orchestrator.completeSession();
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to complete session' },
        { status: 500 }
      );
    }
    
    logger.info(`Dynamic collaboration session completed`, {
      sessionId
    });
    
    return NextResponse.json({
      success: true,
      sessionId,
      message: 'Dynamic collaboration session completed successfully'
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error completing dynamic collaboration session`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}
