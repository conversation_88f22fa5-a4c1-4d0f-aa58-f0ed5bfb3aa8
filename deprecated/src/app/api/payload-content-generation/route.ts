// src/app/(payload)/api/content-generation/route.ts
import { NextResponse } from 'next/server';
import configPromise from '@payload-config';
import { getPayload } from 'payload';
import { executeContentGeneration } from '../agents/contentGeneration';

// Request interfaces
interface ContentGenerationRequest {
  contentType: 'product-page' | 'blog-article' | 'buying-guide';
  topicFocus: string;
  category: string;
  targetAudience: string;
  primaryKeywords: string[];
  tonePreference: string;
  competitorUrls?: string[];
  internalData?: any;
  generalInstructions?: string;
  feedback?: string;
  isRetry?: boolean;
}

// Main API handler
export async function POST(request: Request) {
  try {
    // Parse request
    const data: ContentGenerationRequest = await request.json();
    
    // Validate request
    if (!data.topicFocus) {
      return NextResponse.json({ success: false, error: 'Topic focus is required' }, { status: 400 });
    }

    // Get Payload CMS instance
    const payload = await getPayload({
      config: configPromise,
    });

    // Execute generation using collaborative agent system
    const generationResults = await executeContentGeneration(data);

    // Handle any errors from the generation process
    if (!generationResults.success) {
      return NextResponse.json({ 
        success: false, 
        error: generationResults.error || 'Content generation process failed'
      }, { status: 500 });
    }

    // Return the generated content
    return NextResponse.json({
      success: true,
      message: 'Content generated successfully',
      content: generationResults.content
    });

  } catch (error) {
    console.error('Error in content generation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}