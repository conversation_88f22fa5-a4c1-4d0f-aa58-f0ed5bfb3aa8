// src/app/(payload)/api/agents/content-generation/with-reasoning/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  EnhancedA2AMessage, 
  Reasoning, 
  Part, 
  TextPart, 
  DataPart,
  CollaborationState,
  ArtifactType
} from '../../a2atypes';

/**
 * Generate content draft with explicit reasoning
 */
async function generateContentDraft(
  topic: string,
  contentType: string,
  tone: string,
  contentStructure: any,
  keywords: string[],
  audienceAnalysis?: any
): Promise<{ contentDraft: any, reasoning: Reasoning }> {
  // Explicitly show reasoning process
  const thoughts = [
    `Creating ${contentType} content about "${topic}" with a ${tone} tone`,
    `Need to follow the provided content structure for coherence`,
    `Must incorporate keywords naturally throughout the content`,
    `Content should address audience needs and preferences`
  ];
  
  const considerations = [
    'Balance SEO optimization with readability and engagement',
    'Ensure comprehensive coverage of the topic',
    'Maintain consistent tone and style throughout',
    'Structure content for both skimmability and depth',
    'Include practical examples and actionable insights'
  ];
  
  const alternatives = [
    'Could focus purely on keyword optimization (would hurt readability)',
    'Could prioritize creative writing over structure (might reduce clarity)',
    'Could use minimal examples (would reduce practical value)'
  ];
  
  // Generate a sample section to demonstrate the approach
  const generateSection = (section) => {
    const { title, purpose, keyPoints } = section;
    
    // Generate paragraphs for the section
    const paragraphs = [];
    
    // Introduction paragraph
    paragraphs.push(`<h2>${title}</h2>\n\n<p>${getIntroduction(title, topic, tone)}</p>`);
    
    // Generate content for each key point
    for (const point of keyPoints) {
      paragraphs.push(`<p><strong>${point}</strong> - ${getPointExpansion(point, topic, tone)}</p>`);
    }
    
    // Conclusion paragraph for the section
    if (!title.toLowerCase().includes('conclusion')) {
      paragraphs.push(`<p>${getTransition(title, topic, tone)}</p>`);
    }
    
    return paragraphs.join('\n\n');
  };
  
  // Helper functions for content generation
  const getIntroduction = (title, topic, tone) => {
    if (title.includes('Introduction')) {
      return `${tone === 'professional' ? 'In today\'s competitive landscape,' : 'Let\'s explore'} how ${topic} has become increasingly important for businesses and individuals alike. This ${contentType} will guide you through the key aspects, benefits, and practical applications of ${topic}, equipping you with the knowledge to implement it effectively.`;
    }
    
    return `${title} represents a crucial element of understanding ${topic}. ${tone === 'professional' ? 'This section examines the fundamental components and strategic considerations.' : 'Let\'s dive into what makes this aspect so important.'}`;
  };
  
  const getPointExpansion = (point, topic, tone) => {
    // Simulate detailed content for each key point
    const professionalPhrases = [
      "Research demonstrates that",
      "Industry experts emphasize the importance of",
      "Statistical analysis indicates that",
      "Best practices suggest",
      "According to recent studies"
    ];
    
    const conversationalPhrases = [
      "You'll find that",
      "It's fascinating how",
      "Think about it this way:",
      "Here's the interesting part:",
      "You might be surprised to learn that"
    ];
    
    const phrases = tone === 'professional' ? professionalPhrases : conversationalPhrases;
    const phrase = phrases[Math.floor(Math.random() * phrases.length)];
    
    return `${phrase} ${point.toLowerCase()} plays a significant role in maximizing the effectiveness of ${topic}. ${tone === 'professional' ? 'Organizations implementing this approach have reported substantial improvements in outcomes and operational efficiency.' : 'When you get this right, you\'ll see a huge difference in your results.'} This is especially relevant when considering the broader context of implementation strategies and long-term sustainability.`;
  };
  
  const getTransition = (title, topic, tone) => {
    const professionalTransitions = [
      "Moving forward, it's essential to consider",
      "The next section builds upon these concepts to explore",
      "Having established these foundational elements, we now turn to",
      "These insights provide context for understanding"
    ];
    
    const conversationalTransitions = [
      "Now that we've covered this, let's look at",
      "Pretty interesting, right? Next, we'll explore",
      "With these basics under your belt, you're ready to discover",
      "Let's build on these ideas and check out"
    ];
    
    const transitions = tone === 'professional' ? professionalTransitions : conversationalTransitions;
    const transition = transitions[Math.floor(Math.random() * transitions.length)];
    
    return `${transition} additional aspects of ${topic} that will enhance your understanding and application of these concepts.`;
  };
  
  // Generate content for each section
  const sectionsContent = {};
  let sampleSection = null;
  
  if (contentStructure && contentStructure.contentStructure && Array.isArray(contentStructure.contentStructure)) {
    // Just generate one sample section for demonstration
    const sampleSectionData = contentStructure.contentStructure[0];
    sampleSection = {
      title: sampleSectionData.title,
      content: generateSection(sampleSectionData)
    };
    
    // Create placeholder entries for other sections
    contentStructure.contentStructure.forEach((section, index) => {
      if (index === 0) {
        sectionsContent[section.title] = sampleSection.content;
      } else {
        sectionsContent[section.title] = `<h2>${section.title}</h2>\n\n<p>[Content for this section will be generated in the full implementation]</p>`;
      }
    });
  }
  
  // Create content metadata
  const contentMetadata = {
    title: `${topic}: A Comprehensive ${contentType.replace('-', ' ')}`,
    description: `Detailed ${contentType.replace('-', ' ')} about ${topic} with expert insights and practical guidance`,
    author: "AI Content Team",
    keywordDensity: keywords.reduce((obj, keyword) => {
      obj[keyword] = (Math.random() * 1.5 + 0.5).toFixed(2) + "%";
      return obj;
    }, {}),
    readabilityScore: Math.floor(Math.random() * 15) + 65, // 65-80 scale
    wordCount: sampleSection ? sampleSection.content.split(' ').length * contentStructure.contentStructure.length : 1500,
    createdAt: new Date().toISOString()
  };
  
  // Complete content draft
  const contentDraft = {
    metadata: contentMetadata,
    sectionsContent,
    sampleSection,
    fullContent: sampleSection ? sampleSection.content : "Sample content would be generated here",
    keywordsIncluded: keywords,
    contentStructureFollowed: contentStructure ? true : false,
    readinessForReview: "Sample section ready for review",
    improvement_suggestions: [
      "Enhance keyword integration in section transitions",
      "Add more specific examples relevant to the audience",
      "Consider adding visual elements to break up text"
    ]
  };
  
  // Complete reasoning
  const reasoning: Reasoning = {
    thoughts,
    considerations,
    alternatives,
    decision: `Created a structured ${contentType} draft focused on ${topic} with a ${tone} tone, following the content structure and incorporating target keywords.`,
    confidence: 0.85
  };
  
  return { contentDraft, reasoning };
}

/**
 * Handle enhanced A2A messages with explicit reasoning
 */
async function handleEnhancedMessage(message: EnhancedA2AMessage, state: CollaborationState): Promise<{
  message: EnhancedA2AMessage;
  stateUpdates?: any;
}> {
  console.log("Content Generation Agent received enhanced message:", JSON.stringify(message));
  
  // Extract request type from message parts
  let requestType = '';
  for (const part of message.parts) {
    if (part.type === 'text') {
      const text = part.text;
      
      if (text.includes('generate content') || text.includes('draft content')) {
        requestType = 'content-draft';
      } else if (text.includes('revise content') || text.includes('update content')) {
        requestType = 'content-revision';
      }
    }
  }
  
  // Default to content draft if no specific request type
  if (!requestType) {
    requestType = 'content-draft';
  }
  
  // Create parts for response
  const responseParts: Part[] = [];
  const stateUpdates: any = {};
  
  // Extract content structure from state
  let contentStructure = null;
  if (state.artifactIndex) {
    const structureArtifact = Object.values(state.artifactIndex).find(
      artifact => artifact.type === 'content-structure'
    );
    
    if (structureArtifact && structureArtifact.parts) {
      const dataPart = structureArtifact.parts.find(part => part.type === 'data');
      if (dataPart && dataPart.type === 'data') {
        contentStructure = dataPart.data;
      }
    }
  }
  
  // Extract audience analysis
  let audienceAnalysis = null;
  if (state.artifactIndex) {
    const audienceArtifact = Object.values(state.artifactIndex).find(
      artifact => artifact.type === 'audience-analysis'
    );
    
    if (audienceArtifact && audienceArtifact.parts) {
      const dataPart = audienceArtifact.parts.find(part => part.type === 'data');
      if (dataPart && dataPart.type === 'data') {
        audienceAnalysis = dataPart.data;
      }
    }
  }
  
  // Generate content draft
  const { contentDraft, reasoning } = await generateContentDraft(
    state.topic,
    state.contentType,
    state.tone,
    contentStructure || state.contentStrategy,
    state.keywords,
    audienceAnalysis
  );
  
  // Create text part with main response
  const textPart: TextPart = {
    type: 'text',
    text: `I've generated a sample section for the ${state.contentType} about "${state.topic}" using a ${state.tone} tone.

The content follows the provided structure and incorporates the target keywords naturally. Here's a preview of the first section:

${contentDraft.sampleSection?.content || "Sample content would be generated here"}

This approach will be applied to all sections in the final content. The full content will maintain consistent tone, incorporate keywords with a natural density, and address audience needs identified in the research.

Readability score: ${contentDraft.metadata.readabilityScore}/100
Estimated full word count: ${contentDraft.metadata.wordCount}

Suggested improvements:
${contentDraft.improvement_suggestions.map(suggestion => `- ${suggestion}`).join('\n')}

Would you like me to proceed with generating the full content using this approach?`
  };
  
  // Create data part with structured content
  const dataPart: DataPart = {
    type: 'data',
    data: contentDraft
  };
  
  responseParts.push(textPart, dataPart);
  
  // Create artifact
  const artifactId = uuidv4();
  const artifact = {
    id: artifactId,
    name: `Content Draft for ${state.topic}`,
    description: `Draft content for ${state.contentType} about ${state.topic}`,
    type: 'draft-content' as ArtifactType,
    parts: [textPart, dataPart],
    metadata: {
      createdAt: new Date().toISOString(),
      createdBy: 'content-generation'
    },
    index: 0,
    version: 1,
    contributors: ['content-generation'],
    feedback: [],
    history: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'content-generation',
        changes: 'Initial draft creation',
        parts: [textPart, dataPart],
        reasoning
      }
    ],
    status: 'draft',
    qualityScore: 75
  };
  
  // Add to state updates
  stateUpdates.content = contentDraft;
  stateUpdates.artifactIndex = {
    [artifactId]: artifact
  };
  
  // Create response message
  const responseMessage: EnhancedA2AMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: 'content-generation',
    to: message.from,
    role: 'agent',
    parts: responseParts,
    conversationId: message.conversationId,
    reasoning,
    intentions: ['inform', 'suggest'],
    replyTo: message.id,
    artifactReferences: [artifactId],
    requestedActions: [
      {
        agent: 'seo-optimization',
        action: 'ReviewContentDraft',
        priority: 8,
        rationale: 'Need SEO feedback to ensure optimal keyword usage and search visibility',
        parameters: {
          artifactId,
          focusAreas: ['keyword usage', 'header optimization', 'content completeness']
        }
      }
    ]
  };
  
  return { message: responseMessage, stateUpdates };
}

/**
 * API route handler for enhanced A2A communication
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("Content Generation Agent received enhanced POST request:", JSON.stringify(body));
    
    // Extract the message and state from request body
    const { message, state } = body;
    
    // Handle the enhanced message
    const response = await handleEnhancedMessage(message, state);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in Content Generation Agent enhanced POST handler:", error);
    
    // Create error response
    const errorResponse: EnhancedA2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'content-generation',
      to: 'system',
      role: 'agent',
      parts: [
        {
          type: 'text',
          text: `Error processing message: ${(error as Error).message}`
        }
      ],
      conversationId: uuidv4(),
      reasoning: {
        thoughts: ['An error occurred while processing the message'],
        considerations: ['The error might be due to invalid input or internal processing'],
        decision: 'Return error message with details',
        confidence: 1.0
      },
      intentions: ['inform']
    };
    
    return NextResponse.json(
      { message: errorResponse, error: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * API route handler for agent capabilities
 */
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "Content Generation Agent",
    status: "active",
    capabilities: [
      "content-drafting", 
      "content-revision", 
      "tone-adaptation"
    ],
    enhancedProtocol: true,
    reasoningEnabled: true,
    artifactCreation: true
  });
}
