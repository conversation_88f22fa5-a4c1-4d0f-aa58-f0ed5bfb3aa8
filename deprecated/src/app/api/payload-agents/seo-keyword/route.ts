// src/app/(payload)/api/agents/seo-keyword/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { A2AMessage } from '../a2atypes';

// Define the SEO keyword state interface
export interface SeoKeywordState {
  topic: string;
  primaryKeywords?: string[];
  secondaryKeywords?: string[];
  keywordInsights?: string;
  seoRecommendations?: string;
  targetAudience?: string;
}

// Helper function to generate SEO keyword information based on a query
async function generateSeoKeywordInformation(query: string, targetAudience: string): Promise<any> {
  console.log(`Generating SEO keyword information for query: ${query}`);
  
  // Extract topic from query if possible
  const topicMatch = query.match(/for "(.*?)"/i);
  const topic = topicMatch ? topicMatch[1] : "the requested topic";
  
  // Generate SEO keyword information
  const prompt = `\nYou are an SEO specialist.\nFor the topic "${topic}", provide:\n- 5-10 high-traffic, relevant keywords\n- 3-5 long-tail keywords/questions people search for\n- Briefly explain why each keyword is relevant\nBase your suggestions on current search trends and the provided target audience: "${targetAudience}".\n`;
  
  return {
    primaryKeywords: [`${topic} guide`, `${topic} best practices`, `${topic} examples`],
    secondaryKeywords: [`how to use ${topic}`, `${topic} benefits`, `${topic} implementation`],
    keywordInsights: `These keywords balance search volume with relevance to ${topic}, focusing on informational intent.`,
    seoRecommendations: `Focus on long-form content that thoroughly covers ${topic} with proper heading structure and keyword placement.`
  };
}

// Function to handle agent messages
async function handleAgentMessage(message: A2AMessage): Promise<A2AMessage> {
  console.log("SEO Keyword Agent received message:", JSON.stringify(message));
  
  // Process the message based on its task type
  const task = message.task;
  
  if (task.type === "REQUEST_INFORMATION") {
    // Handle information request
    console.log("Handling information request");
    const query = task.query || "SEO keywords";
    const information = await generateSeoKeywordInformation(query);
    
    // Create response message
    return {
      task: {
        type: "PROVIDE_INFORMATION",
        information
      },
      source: "seo-keyword-agent",
      target: message.source
    };
  } else if (task.type === "REQUEST_FEEDBACK") {
    // Handle feedback request
    console.log("Handling feedback request");
    
    // Create response message with feedback
    return {
      task: {
        type: "PROVIDE_FEEDBACK",
        feedback: "The keyword selection looks good. Consider adding more long-tail keywords to capture specific search intent."
      },
      source: "seo-keyword-agent",
      target: message.source
    };
  } else if (task.type === "COLLABORATE") {
    // Handle collaboration request
    console.log("Handling collaboration request");
    const query = task.context || "SEO keywords";
    const information = await generateSeoKeywordInformation(query);
    
    // Create response message with collaboration results
    return {
      task: {
        type: "COLLABORATION_RESULT",
        result: information
      },
      source: "seo-keyword-agent",
      target: message.source
    };
  } else {
    // Handle unknown task type
    console.log("Unknown task type:", task.type);
    
    // Create error response message
    return {
      task: {
        type: "ERROR",
        error: `Unsupported task type: ${task.type}`
      },
      source: "seo-keyword-agent",
      target: message.source
    };
  }
}

// API route handlers
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "SEO Keyword Agent", 
    status: "active",
    capabilities: ["keyword-research", "seo-recommendations", "content-optimization"]
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("SEO Keyword Agent received POST request:", JSON.stringify(body));
    
    // Extract the message from the request body
    const { message, state } = body;
    console.log("SEO Keyword Agent received message:", JSON.stringify(message));
    
    // Adapt the message for the legacy handler
    // The legacy handler expects the message to be directly passed, not nested
    const adaptedMessage = {
      ...message,
      task: message.task || {
        type: "REQUEST_INFORMATION",
        query: typeof message.content === 'object' ? message.content.query : message.content
      }
    };
    
    // Handle the message
    const response = await handleAgentMessage(adaptedMessage);
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in SEO Keyword Agent POST handler:", error);
    return NextResponse.json(
      { error: "Internal server error", details: (error as Error).message },
      { status: 500 }
    );
  }
}
