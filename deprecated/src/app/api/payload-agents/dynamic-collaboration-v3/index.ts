/**
 * Dynamic Collaboration V3
 *
 * This file exports the main components of the dynamic collaboration system.
 */

// Export state components
export * from './state/schema';
export * from './state/store';
export * from './state/manager';

// Export workflow components
export * from './workflow/state-machine';
export * from './workflow/orchestrator';

// Re-export the main orchestrator for convenience
import { WorkflowOrchestrator } from './workflow/orchestrator';
export { WorkflowOrchestrator };

/**
 * Initialize a new dynamic collaboration session
 * @param sessionId The session ID
 * @param params Content generation parameters
 * @returns Promise<boolean> indicating success
 */
export async function initiateDynamicCollaboration(
  sessionId: string,
  params: any
): Promise<boolean> {
  return await WorkflowOrchestrator.initiate(sessionId, params);
}
