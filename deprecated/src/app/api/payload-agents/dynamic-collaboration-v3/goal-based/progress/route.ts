/**
 * Dynamic Collaboration V3 Goal-Based Progress API Route
 *
 * This file implements the API route for progressing a goal-based dynamic collaboration session.
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../../utils/logger';
import { GoalOrchestrator } from '../../workflow/goal-orchestrator';
import { z } from 'zod';

/**
 * POST /api/agents/dynamic-collaboration-v3/goal-based/progress
 *
 * Progress a goal-based dynamic collaboration session
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await request.json();

    // Validate request body
    const schema = z.object({
      sessionId: z.string().uuid()
    });

    const result = schema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }

    // Create orchestrator
    const orchestrator = new GoalOrchestrator(body.sessionId);

    // Process goals
    const success = await orchestrator.processGoals();

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to progress session' },
        { status: 500 }
      );
    }

    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error progressing goal-based dynamic collaboration session`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
