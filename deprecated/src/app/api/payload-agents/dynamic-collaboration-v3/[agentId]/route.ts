/**
 * Dynamic Collaboration V3 Agent API
 *
 * This file implements the API endpoints for agent communication in the dynamic collaboration v3 system.
 * It provides endpoints for sending messages to agents and processing agent responses.
 */

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { enhancedMessageBus } from '../../../agents/collaborative-iteration/utils/enhanced-message-bus';
import { IterativeMessage, IterativeMessageType } from '../../../agents/collaborative-iteration/types';

/**
 * POST /api/agents/dynamic-collaboration-v3/[agentId]
 * Send a message to an agent
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ agentId: string }> }
): Promise<NextResponse> {
  try {
    // Get agent ID from path
    const { agentId } = await params;

    // Parse request body
    const body = await req.json();
    const { message, sessionId } = body;

    // Validate required fields
    if (!message) {
      return NextResponse.json(
        { error: 'Missing required field: message' },
        { status: 400 }
      );
    }

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required field: sessionId' },
        { status: 400 }
      );
    }

    // Ensure message has required fields
    if (!message.from || !message.type || !message.content) {
      return NextResponse.json(
        { error: 'Message missing required fields: from, type, content' },
        { status: 400 }
      );
    }

    // Create session-specific message bus
    const messageBus = enhancedMessageBus.createSessionBus(sessionId);

    // Create a properly formatted message
    const fullMessage: IterativeMessage = {
      id: message.id || uuidv4(),
      timestamp: message.timestamp || new Date().toISOString(),
      from: message.from,
      to: agentId,
      type: message.type as IterativeMessageType,
      content: message.content,
      conversationId: message.conversationId || sessionId,
      sessionId: sessionId,
      inReplyTo: message.inReplyTo
    };

    // Send message via the message bus
    await messageBus.sendMessage(fullMessage);

    // Get the message history to find the response
    const messageHistory = messageBus.getHistory();
    const response = messageHistory
      .filter(msg => msg.from === agentId && msg.to === message.from && msg.inReplyTo === fullMessage.id)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];

    // Return response
    return NextResponse.json({
      success: true,
      response
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error processing agent message`, {
      agentId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
