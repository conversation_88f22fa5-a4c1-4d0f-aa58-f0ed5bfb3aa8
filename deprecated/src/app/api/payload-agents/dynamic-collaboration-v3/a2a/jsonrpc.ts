/**
 * A2A JSONRPC Implementation for Goal Orchestrator Backend
 *
 * This file implements the JSON-RPC API for agent-to-agent communication
 * in the goal orchestrator backend.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { GoalOrchestrator } from '../workflow/goal-orchestrator';
import { MessageType, GoalType, WorkflowPhase } from '../state/unified-schema';

// JSONRPC interface types
export interface JsonRpcRequest {
  jsonrpc: string;
  method: string;
  params: any;
  id: string | number;
}

export interface JsonRpcSuccessResponse {
  jsonrpc: string;
  result: any;
  id: string | number;
}

export interface JsonRpcErrorResponse {
  jsonrpc: string;
  error: {
    code: number;
    message: string;
    data?: any;
  };
  id: string | number | null;
}

export type JsonRpcResponse = JsonRpcSuccessResponse | JsonRpcErrorResponse;

/**
 * Process a JSONRPC request
 */
export async function processJsonRpcRequest(request: JsonRpcRequest): Promise<JsonRpcResponse> {
  const { jsonrpc, method, params, id } = request;

  // Validate JSONRPC request
  if (jsonrpc !== '2.0' || !method) {
    return {
      jsonrpc: '2.0',
      error: { code: -32600, message: 'Invalid Request' },
      id: id || null
    };
  }

  try {
    let result;

    // Session methods
    if (method === 'session.create') {
      result = await handleSessionCreate(params);
    } else if (method === 'session.get') {
      result = await handleSessionGet(params);
    } else if (method === 'session.list') {
      result = await handleSessionList();
    }
    // Goal methods
    else if (method === 'goal.define') {
      result = await handleGoalDefine(params);
    } else if (method === 'goal.activate') {
      result = await handleGoalActivate(params);
    } else if (method === 'goal.assign') {
      result = await handleGoalAssign(params);
    } else if (method === 'goal.complete') {
      result = await handleGoalComplete(params);
    }
    // Message methods
    else if (method === 'message.send') {
      result = await handleMessageSend(params);
    } else if (method === 'message.list') {
      result = await handleMessageList(params);
    }
    // Artifact methods
    else if (method === 'artifact.create') {
      result = await handleArtifactCreate(params);
    } else if (method === 'artifact.list') {
      result = await handleArtifactList(params);
    }
    // Feedback methods
    else if (method === 'feedback.request') {
      result = await handleFeedbackRequest(params);
    } else if (method === 'feedback.provide') {
      result = await handleFeedbackProvide(params);
    }
    // Workflow methods
    else if (method === 'workflow.transition') {
      result = await handleWorkflowTransition(params);
    }
    // Unknown method
    else {
      return {
        jsonrpc: '2.0',
        error: { code: -32601, message: 'Method not found' },
        id
      };
    }

    return {
      jsonrpc: '2.0',
      result,
      id
    };
  } catch (error) {
    const err = error as Error;
    logger.error(`Error processing JSON-RPC request`, {
      method,
      error: err.message || String(error),
      stack: err.stack
    });

    return {
      jsonrpc: '2.0',
      error: {
        code: -32603,
        message: err.message || 'Internal error',
        data: { stack: err.stack }
      },
      id
    };
  }
}

/**
 * Process a batch of JSONRPC requests
 */
export async function processJsonRpcBatch(requests: JsonRpcRequest[]): Promise<JsonRpcResponse[]> {
  const responses: JsonRpcResponse[] = [];

  for (const request of requests) {
    responses.push(await processJsonRpcRequest(request));
  }

  return responses;
}

// Handler implementations

/**
 * Create a new session
 */
async function handleSessionCreate(params: any) {
  const { topic, contentType, targetAudience, tone, keywords, additionalInstructions } = params;

  // Validate required parameters
  if (!topic) {
    throw new Error('Missing required parameter: topic');
  }

  // Generate session ID
  const sessionId = params.sessionId || uuidv4();

  // Initialize session
  const success = await GoalOrchestrator.initiate(sessionId, {
    topic,
    contentType: contentType || 'blog-article',
    targetAudience: targetAudience || 'general audience',
    tone: tone || 'informative',
    keywords: keywords || [],
    additionalInstructions
  });

  if (!success) {
    throw new Error('Failed to initialize session');
  }

  return {
    success: true,
    sessionId,
    message: 'Goal-based collaboration session created successfully'
  };
}

/**
 * Get a session by ID
 */
async function handleSessionGet(params: any) {
  const { sessionId } = params;

  // Validate session ID
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Get the session state
  const sessionState = await stateManager.getState();

  if (!sessionState) {
    throw new Error('Session not found');
  }

  return sessionState;
}

/**
 * List all sessions
 */
async function handleSessionList() {
  // This would require access to the TypedStateStore directly
  // For now, we'll return a placeholder
  return {
    message: 'Session listing not implemented yet'
  };
}

/**
 * Define a new goal
 */
async function handleGoalDefine(params: any) {
  const { sessionId, type, description, criteria, dependencies } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!type) {
    throw new Error('Missing required parameter: type');
  }
  if (!description) {
    throw new Error('Missing required parameter: description');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Define the goal
  const goalId = await stateManager.defineGoal({
    type: type as GoalType,
    description,
    criteria: criteria || [],
    dependencies: dependencies || []
  });

  return {
    success: true,
    goalId,
    message: 'Goal defined successfully'
  };
}

/**
 * Activate a goal
 */
async function handleGoalActivate(params: any) {
  const { sessionId, goalId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!goalId) {
    throw new Error('Missing required parameter: goalId');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Activate the goal
  const success = await stateManager.activateGoal(goalId);

  if (!success) {
    throw new Error('Failed to activate goal');
  }

  return {
    success: true,
    message: 'Goal activated successfully'
  };
}

/**
 * Assign a goal to an agent
 */
async function handleGoalAssign(params: any) {
  const { sessionId, goalId, agentId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!goalId) {
    throw new Error('Missing required parameter: goalId');
  }
  if (!agentId) {
    throw new Error('Missing required parameter: agentId');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Assign the goal
  const success = await stateManager.assignGoal(goalId, agentId);

  if (!success) {
    throw new Error('Failed to assign goal');
  }

  return {
    success: true,
    message: 'Goal assigned successfully'
  };
}

/**
 * Complete a goal
 */
async function handleGoalComplete(params: any) {
  const { sessionId, goalId, artifactId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!goalId) {
    throw new Error('Missing required parameter: goalId');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Complete the goal
  const success = await stateManager.completeGoal(goalId, artifactId);

  if (!success) {
    throw new Error('Failed to complete goal');
  }

  return {
    success: true,
    message: 'Goal completed successfully'
  };
}

/**
 * Send a message
 */
async function handleMessageSend(params: any) {
  const { sessionId, from, to, content, type, conversationId, replyTo } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!from) {
    throw new Error('Missing required parameter: from');
  }
  if (!to) {
    throw new Error('Missing required parameter: to');
  }
  if (!content) {
    throw new Error('Missing required parameter: content');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Create message
  const messageId = await stateManager.addMessage({
    from,
    to,
    content,
    type: type || MessageType.AGENT,
    conversationId: conversationId || uuidv4(),
    replyTo
  });

  return {
    success: true,
    messageId,
    message: 'Message sent successfully'
  };
}

/**
 * List messages for a session
 */
async function handleMessageList(params: any) {
  const { sessionId, conversationId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Get state
  const state = await stateManager.getState();
  if (!state) {
    throw new Error('Session not found');
  }

  // Filter messages by conversation if specified
  let messages;
  if (conversationId) {
    const messageIds = state.messages.byConversation[conversationId] || [];
    messages = messageIds.map(id => state.messages.byId[id]).filter(Boolean);
  } else {
    messages = Object.values(state.messages.byId);
  }

  return {
    success: true,
    messages
  };
}

/**
 * Create an artifact
 */
async function handleArtifactCreate(params: any) {
  const { sessionId, createdBy, title, content, type, goalId, previousVersionId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!createdBy) {
    throw new Error('Missing required parameter: createdBy');
  }
  if (!title) {
    throw new Error('Missing required parameter: title');
  }
  if (!content) {
    throw new Error('Missing required parameter: content');
  }
  if (!type) {
    throw new Error('Missing required parameter: type');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Create artifact
  const artifactId = await stateManager.createArtifact(
    createdBy,
    title,
    content,
    type,
    goalId,
    previousVersionId
  );

  return {
    success: true,
    artifactId,
    message: 'Artifact created successfully'
  };
}

/**
 * List artifacts for a session
 */
async function handleArtifactList(params: any) {
  const { sessionId, type, goalId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Get state
  const state = await stateManager.getState();
  if (!state) {
    throw new Error('Session not found');
  }

  // Filter artifacts
  let artifacts = Object.values(state.artifacts);

  if (type) {
    artifacts = artifacts.filter(artifact => artifact.type === type);
  }

  if (goalId) {
    artifacts = artifacts.filter(artifact => artifact.goalId === goalId);
  }

  return {
    success: true,
    artifacts
  };
}

/**
 * Request feedback on an artifact
 */
async function handleFeedbackRequest(params: any) {
  const { sessionId, fromAgent, toAgent, artifactId, specificAreas } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!fromAgent) {
    throw new Error('Missing required parameter: fromAgent');
  }
  if (!toAgent) {
    throw new Error('Missing required parameter: toAgent');
  }
  if (!artifactId) {
    throw new Error('Missing required parameter: artifactId');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Create feedback request
  const requestId = await stateManager.createFeedbackRequest(
    fromAgent,
    toAgent,
    artifactId,
    specificAreas || []
  );

  return {
    success: true,
    requestId,
    message: 'Feedback request created successfully'
  };
}

/**
 * Provide feedback on an artifact
 */
async function handleFeedbackProvide(params: any) {
  const { sessionId, requestId, fromAgent, toAgent, feedback } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!requestId) {
    throw new Error('Missing required parameter: requestId');
  }
  if (!fromAgent) {
    throw new Error('Missing required parameter: fromAgent');
  }
  if (!toAgent) {
    throw new Error('Missing required parameter: toAgent');
  }
  if (!feedback) {
    throw new Error('Missing required parameter: feedback');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Provide feedback
  const responseId = await stateManager.provideFeedbackResponse(
    requestId,
    fromAgent,
    toAgent,
    feedback
  );

  return {
    success: true,
    responseId,
    message: 'Feedback provided successfully'
  };
}

/**
 * Transition workflow phase
 */
async function handleWorkflowTransition(params: any) {
  const { sessionId, phase } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!phase) {
    throw new Error('Missing required parameter: phase');
  }

  // Get state manager
  const stateManager = new StateManager(sessionId);

  // Update phase
  const success = await stateManager.updatePhase(phase as WorkflowPhase);

  if (!success) {
    throw new Error('Failed to transition workflow phase');
  }

  return {
    success: true,
    message: `Workflow transitioned to ${phase} phase successfully`
  };
}
