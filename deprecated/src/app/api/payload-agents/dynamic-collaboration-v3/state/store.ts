/**
 * Transactional State Store
 *
 * This file implements a transactional state store for the dynamic collaboration system.
 * It provides atomic operations, optimistic concurrency control, and robust error handling.
 */

import { Redis } from '@upstash/redis';
import { CollaborationState, CollaborationStateSchema } from './unified-schema';
import logger from '../../../utils/logger';

/**
 * Redis client configuration
 */
const REDIS_KEY_PREFIX = 'dynamic-collab-v3:';
const REDIS_EXPIRY = 60 * 60 * 24 * 7; // 7 days in seconds
const MAX_RETRIES = 3;
const RETRY_DELAY_MS = 100;

/**
 * Error types
 */
export class StateNotFoundError extends Error {
  constructor(sessionId: string) {
    super(`State not found for session ${sessionId}`);
    this.name = 'StateNotFoundError';
  }
}

export class ConcurrencyError extends Error {
  constructor(sessionId: string) {
    super(`Concurrency conflict for session ${sessionId}`);
    this.name = 'ConcurrencyError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public details: any) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * Transactional State Store
 *
 * This class provides methods for managing dynamic collaboration state
 * with transactional guarantees, optimistic concurrency control, and validation.
 */
export class TransactionalStateStore {
  private redis: Redis | null = null;
  private inMemoryCache: Map<string, CollaborationState> = new Map();
  private static instance: TransactionalStateStore;

  /**
   * Get the singleton instance of the state store
   */
  public static getInstance(): TransactionalStateStore {
    if (!TransactionalStateStore.instance) {
      TransactionalStateStore.instance = new TransactionalStateStore();
    }
    return TransactionalStateStore.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    this.initializeRedis();
  }

  /**
   * Initialize Redis connection
   */
  private initializeRedis(): void {
    try {
      this.redis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL || '',
        token: process.env.UPSTASH_REDIS_REST_TOKEN || ''
      });
      logger.info('Transactional State Store initialized with Redis');

      // Test Redis connection
      this.redis.ping().then((result) => {
        logger.info(`Redis connection test: ${result}`);
      });
    } catch (error) {
      logger.error('Failed to initialize Redis connection', { error });
      this.redis = null;
    }
  }

  /**
   * Validate state against schema
   * @param state The state to validate
   * @throws ValidationError if validation fails
   */
  private validateState(state: CollaborationState): void {
    try {
      CollaborationStateSchema.parse(state);
    } catch (error) {
      throw new ValidationError('State validation failed', error);
    }
  }

  /**
   * Get state for a session
   * @param sessionId The session ID
   * @returns The session state
   * @throws StateNotFoundError if state not found
   */
  public async getState(sessionId: string): Promise<CollaborationState> {
    try {
      // Check in-memory cache first
      if (this.inMemoryCache.has(sessionId)) {
        logger.debug(`Retrieved state from in-memory cache for session ${sessionId}`, { sessionId });
        const cachedState = this.inMemoryCache.get(sessionId);
        if (cachedState) return cachedState;
      }

      // If not in cache, try to get from Redis
      if (this.redis) {
        const redisKey = `${REDIS_KEY_PREFIX}${sessionId}`;
        const rawState = await this.redis.json.get<any>(redisKey, "$");

        if (rawState) {
          // Handle the nested array structure from Redis
          let state: CollaborationState;

          // If the result is an array with one element (common with Upstash Redis JSON)
          if (Array.isArray(rawState) && rawState.length === 1) {
            state = rawState[0] as CollaborationState;
          } else {
            state = rawState as CollaborationState;
          }

          // Validate state
          this.validateState(state);

          // Update in-memory cache
          this.inMemoryCache.set(sessionId, state);
          logger.debug(`Retrieved state from Redis for session ${sessionId}`, { sessionId });
          return state;
        }
      }

      logger.debug(`No state found for session ${sessionId}`, { sessionId });
      throw new StateNotFoundError(sessionId);
    } catch (error) {
      if (error instanceof StateNotFoundError || error instanceof ValidationError) {
        throw error;
      }

      const err = error as Error;
      logger.error(`Error getting state for session ${sessionId}`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      throw new Error(`Failed to get state for session ${sessionId}: ${err.message}`);
    }
  }

  /**
   * Set state for a session
   * @param sessionId The session ID
   * @param state The state to set
   * @throws ValidationError if validation fails
   */
  public async setState(sessionId: string, state: CollaborationState): Promise<void> {
    try {
      // Validate state
      this.validateState(state);

      // Update in-memory cache
      this.inMemoryCache.set(sessionId, state);
      logger.debug(`Saved state to in-memory cache for session ${sessionId}`, { sessionId });

      // Persist to Redis if available
      if (this.redis) {
        const redisKey = `${REDIS_KEY_PREFIX}${sessionId}`;

        // Store the state in Redis using JSON.SET
        const result = await this.redis.json.set(redisKey, "$", state as unknown as Record<string, unknown>);
        logger.debug('Redis save result status', { status: result });

        // Set expiration
        const expireResult = await this.redis.expire(redisKey, REDIS_EXPIRY);
        logger.debug('Redis expire result status', { status: expireResult });
      }

      logger.debug(`Successfully saved state for session ${sessionId}`, {
        sessionId,
        version: state.version
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      const err = error as Error;
      logger.error(`Error setting state for session ${sessionId}`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      throw new Error(`Failed to set state for session ${sessionId}: ${err.message}`);
    }
  }

  /**
   * Update state for a session using an update function with optimistic concurrency control
   * @param sessionId The session ID
   * @param updateFn Function that takes the current state and returns the updated state
   * @throws StateNotFoundError if state not found
   * @throws ConcurrencyError if version conflict
   * @throws ValidationError if validation fails
   */
  public async updateState(
    sessionId: string,
    updateFn: (state: CollaborationState) => CollaborationState
  ): Promise<CollaborationState> {
    let retries = 0;

    while (retries < MAX_RETRIES) {
      try {
        // Get current state
        const currentState = await this.getState(sessionId);
        const currentVersion = currentState.version;

        // Apply update function
        const updatedState = updateFn(currentState);

        // Update version and timestamp
        updatedState.version = currentVersion + 1;
        updatedState.lastUpdated = new Date().toISOString();

        // Validate updated state
        this.validateState(updatedState);

        // Save updated state with version check
        if (this.redis) {
          const redisKey = `${REDIS_KEY_PREFIX}${sessionId}`;

          // Use a Lua script for atomic update with version check
          const script = `
            local current = redis.call('JSON.GET', KEYS[1])
            if not current then return {err="State not found"} end

            local currentObj = cjson.decode(current)
            if type(currentObj) == 'table' and currentObj[1] then
              currentObj = currentObj[1]
            end

            if currentObj.version ~= tonumber(ARGV[1]) then
              return {err="Version mismatch"}
            end

            redis.call('JSON.SET', KEYS[1], '$', ARGV[2])
            redis.call('EXPIRE', KEYS[1], ARGV[3])
            return {ok=1}
          `;

          const result = await this.redis.eval(
            script,
            [redisKey],
            [currentVersion.toString(), JSON.stringify(updatedState), REDIS_EXPIRY.toString()]
          );

          if (result.err === "Version mismatch") {
            logger.warn(`Concurrency conflict for session ${sessionId}, retrying`, {
              sessionId,
              attempt: retries + 1,
              maxRetries: MAX_RETRIES
            });
            retries++;
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
            continue;
          } else if (result.err) {
            throw new Error(result.err);
          }
        }

        // Update in-memory cache
        this.inMemoryCache.set(sessionId, updatedState);

        logger.debug(`Successfully updated state for session ${sessionId}`, {
          sessionId,
          oldVersion: currentVersion,
          newVersion: updatedState.version
        });

        return updatedState;
      } catch (error) {
        if (error instanceof StateNotFoundError || error instanceof ValidationError) {
          throw error;
        }

        if (retries >= MAX_RETRIES - 1) {
          const err = error as Error;
          logger.error(`Failed to update state after ${MAX_RETRIES} attempts`, {
            sessionId,
            error: err.message || String(error),
            stack: err.stack
          });
          throw new ConcurrencyError(sessionId);
        }

        retries++;
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
      }
    }

    throw new Error('Unexpected error in updateState');
  }

  /**
   * Delete state for a session
   * @param sessionId The session ID
   */
  public async deleteState(sessionId: string): Promise<void> {
    try {
      // Remove from in-memory cache
      this.inMemoryCache.delete(sessionId);

      // Remove from Redis if available
      if (this.redis) {
        const redisKey = `${REDIS_KEY_PREFIX}${sessionId}`;
        await this.redis.del(redisKey);
      }

      logger.info(`Deleted state for session ${sessionId}`, { sessionId });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error deleting state for session ${sessionId}`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      throw new Error(`Failed to delete state for session ${sessionId}: ${err.message}`);
    }
  }

  /**
   * List all session IDs
   * @returns Array of session IDs
   */
  public async listSessions(): Promise<string[]> {
    try {
      if (this.redis) {
        // Use scan instead of keys for production environments
        const keys = await this.redis.keys(`${REDIS_KEY_PREFIX}*`);
        return keys.map(key => key.replace(REDIS_KEY_PREFIX, ''));
      }

      // If Redis is not available, return in-memory cache keys
      return Array.from(this.inMemoryCache.keys());
    } catch (error) {
      const err = error as Error;
      logger.error('Error listing sessions', {
        error: err.message || String(error),
        stack: err.stack
      });
      return [];
    }
  }
}

// Export singleton instance
export const transactionalStateStore = TransactionalStateStore.getInstance();
