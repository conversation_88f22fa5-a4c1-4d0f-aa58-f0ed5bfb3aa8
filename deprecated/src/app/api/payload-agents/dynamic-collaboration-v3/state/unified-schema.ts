// src/app/(payload)/api/agents/dynamic-collaboration-v3/state/unified-schema.ts

import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';

/**
 * Workflow phases
 */
export enum WorkflowPhase {
  PLANNING = 'planning',
  RESEARCH = 'research',
  CREATION = 'creation',
  REVIEW = 'review',
  FINALIZATION = 'finalization'
}

/**
 * Milestone status
 */
export enum MilestoneStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped'
}

/**
 * Goal types
 */
export enum GoalType {
  // High-level goals
  RESEARCH = 'research',
  CONTENT = 'content',
  QUALITY = 'quality',

  // Specific goals
  MARKET_RESEARCH = 'market_research',
  KEYWORD_ANALYSIS = 'keyword_analysis',
  CONTENT_STRATEGY = 'content_strategy',
  CONTENT_CREATION = 'content_creation',
  SEO_OPTIMIZATION = 'seo_optimization',
  QUALITY_ASSESSMENT = 'quality_assessment'
}

/**
 * Goal status
 */
export enum GoalStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  IN_PROGRESS = 'in_progress',
  BLOCKED = 'blocked',
  COMPLETED = 'completed',
  FAILED = 'failed',
  PAUSED = 'paused'
}

/**
 * Message types
 */
export enum MessageType {
  SYSTEM = 'system',
  SYSTEM_MESSAGE = 'system_message',
  SYSTEM_ERROR = 'system_error',
  USER = 'user',
  AGENT = 'agent',
  GOAL_UPDATE = 'goal_update',
  ARTIFACT_UPDATE = 'artifact_update',
  WORKFLOW_TRANSITION = 'workflow_transition',
  FEEDBACK_REQUEST = 'feedback_request',
  FEEDBACK_RESPONSE = 'feedback_response',
  PROGRESS_UPDATE = 'progress_update'
}

/**
 * Session status
 */
export enum SessionStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

/**
 * Artifact status
 */
export enum ArtifactStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PUBLISHED = 'published'
}

/**
 * Reasoning schema
 */
export const ReasoningSchema = z.object({
  thoughts: z.array(z.string()),
  considerations: z.array(z.string()).optional(),
  decision: z.string(),
  confidence: z.number().min(0).max(1).optional()
});

/**
 * Goal schema
 */
export const GoalSchema = z.object({
  id: z.string().uuid(),
  type: z.nativeEnum(GoalType),
  description: z.string(),
  criteria: z.array(z.string()),
  status: z.nativeEnum(GoalStatus),
  progress: z.number().min(0).max(100),
  dependencies: z.array(z.string().uuid()),
  assignedTo: z.union([z.string(), z.array(z.string())]).optional(),
  createdAt: z.string().datetime(),
  startTime: z.string().datetime().optional(),
  completedAt: z.string().datetime().optional(),
  artifactIds: z.array(z.string().uuid()).optional(),
  reasoning: ReasoningSchema.optional(),
  version: z.number().int().positive()
});

/**
 * Message schema
 */
export const MessageSchema = z.object({
  id: z.string().uuid(),
  timestamp: z.string().datetime(),
  from: z.string(),
  to: z.union([z.string(), z.array(z.string())]),
  type: z.nativeEnum(MessageType),
  content: z.any(),
  replyTo: z.string().uuid().optional(),
  conversationId: z.string().uuid(),
  reasoning: ReasoningSchema.optional(),
  metadata: z.record(z.string(), z.any()).optional()
});

/**
 * Artifact schema
 */
export const ArtifactSchema = z.object({
  id: z.string().uuid(),
  type: z.string(),
  title: z.string(),
  content: z.any(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  createdBy: z.string(),
  status: z.nativeEnum(ArtifactStatus),
  version: z.number().int().positive(),
  metadata: z.record(z.string(), z.any()).optional(),
  reasoning: ReasoningSchema.optional(),
  goalId: z.string().uuid().optional(),
  previousVersionId: z.string().uuid().optional()
});

/**
 * Feedback data schema
 */
export const FeedbackDataSchema = z.object({
  overallRating: z.number().min(0).max(100),
  strengths: z.array(z.string()),
  areasForImprovement: z.array(z.string()),
  specificFeedback: z.array(z.object({
    section: z.string(),
    feedback: z.string(),
    suggestions: z.string()
  })),
  summary: z.string()
});

/**
 * Feedback request schema
 */
export const FeedbackRequestSchema = z.object({
  id: z.string().uuid(),
  artifactId: z.string().uuid(),
  fromAgent: z.string(),
  toAgent: z.string(),
  timestamp: z.string().datetime(),
  specificAreas: z.array(z.string()),
  status: z.enum(['pending', 'completed'])
});

/**
 * Feedback response schema
 */
export const FeedbackResponseSchema = z.object({
  id: z.string().uuid(),
  requestId: z.string().uuid(),
  artifactId: z.string().uuid(),
  fromAgent: z.string(),
  toAgent: z.string(),
  timestamp: z.string().datetime(),
  feedback: FeedbackDataSchema
});

/**
 * Feedback cycle schema
 */
export const FeedbackCycleSchema = z.object({
  id: z.string().uuid(),
  artifactId: z.string().uuid(),
  cycles: z.array(z.object({
    requestId: z.string().uuid(),
    feedbackId: z.string().uuid().optional(),
    requestTimestamp: z.string().datetime(),
    feedbackTimestamp: z.string().datetime().optional(),
    fromAgent: z.string(),
    toAgent: z.string(),
    rating: z.number().min(0).max(100).optional(),
    incorporated: z.boolean().default(false),
    incorporatedTimestamp: z.string().datetime().optional()
  }))
});

/**
 * Error record schema
 */
export const ErrorRecordSchema = z.object({
  id: z.string().uuid(),
  timestamp: z.string().datetime(),
  component: z.string(),
  operation: z.string(),
  message: z.string(),
  details: z.any().optional(),
  severity: z.enum(['low', 'medium', 'high', 'fatal']),
  goalId: z.string().uuid().optional(),
  artifactId: z.string().uuid().optional(),
  agentId: z.string().optional(),
  retryCount: z.number().int(),
  maxRetries: z.number().int(),
  resolved: z.boolean(),
  resolvedAt: z.string().datetime().optional(),
  resolution: z.string().optional()
});

/**
 * Workflow progress schema
 */
export const WorkflowProgressSchema = z.object({
  status: z.enum(['initializing', 'in_progress', 'paused', 'completed', 'error']).default('initializing'),
  currentPhase: z.nativeEnum(WorkflowPhase).default(WorkflowPhase.PLANNING),
  phaseProgress: z.record(z.nativeEnum(WorkflowPhase), z.number().min(0).max(100)).default({}),
  overallProgress: z.number().min(0).max(100).default(0),
  estimatedTimeRemaining: z.number().int().optional().default(1800), // 30 minutes as default
  startTime: z.string().datetime().default(() => new Date().toISOString()),
  lastUpdated: z.string().datetime().default(() => new Date().toISOString()),
  milestones: z.array(z.object({
    id: z.string(),
    description: z.string(),
    status: z.nativeEnum(MilestoneStatus).default(MilestoneStatus.PENDING),
    completedAt: z.string().datetime().optional(),
    dependsOn: z.array(z.string()).optional().default([])
  })).default([]),
  articleProgress: z.object({
    researchComplete: z.boolean().default(false),
    outlineComplete: z.boolean().default(false),
    draftComplete: z.boolean().default(false),
    seoOptimizationComplete: z.boolean().default(false),
    finalReviewComplete: z.boolean().default(false),
    articleComplete: z.boolean().default(false)
  }).default({
    researchComplete: false,
    outlineComplete: false,
    draftComplete: false,
    seoOptimizationComplete: false,
    finalReviewComplete: false,
    articleComplete: false
  }),
  lastError: z.object({
    errorId: z.string().uuid(),
    timestamp: z.string().datetime(),
    message: z.string(),
    severity: z.string()
  }).optional()
});

/**
 * Content generation parameters schema
 */
export const ContentGenerationParamsSchema = z.object({
  topic: z.string(),
  contentType: z.enum(['blog-article', 'product-page', 'buying-guide']),
  targetAudience: z.string(),
  tone: z.string(),
  keywords: z.array(z.string()).optional(),
  additionalInstructions: z.string().optional(),
  referenceUrls: z.array(z.string()).optional(),
  comparisonSessionId: z.string().uuid().optional()
});

/**
 * Unified collaboration state schema
 */
export const CollaborationStateSchema = z.object({
  // Common properties
  id: z.string().uuid(),
  topic: z.string(),
  contentType: z.enum(['blog-article', 'product-page', 'buying-guide']),
  targetAudience: z.string(),
  tone: z.string(),
  status: z.nativeEnum(SessionStatus),
  startTime: z.string().datetime(),
  endTime: z.string().datetime().optional(),

  // Workflow tracking
  currentPhase: z.nativeEnum(WorkflowPhase),
  phaseHistory: z.array(z.object({
    phase: z.nativeEnum(WorkflowPhase),
    timestamp: z.string().datetime()
  })),

  // Flexible storage for artifacts
  artifacts: z.record(z.string().uuid(), ArtifactSchema),
  generatedArtifactIds: z.array(z.string().uuid()),

  // Support for both record and array-based goals
  goals: z.object({
    byId: z.record(z.string().uuid(), GoalSchema),
    allIds: z.array(z.string().uuid()),
    activeIds: z.array(z.string().uuid()),
    completedIds: z.array(z.string().uuid())
  }),

  // Flexible message system
  messages: z.object({
    byId: z.record(z.string().uuid(), MessageSchema),
    allIds: z.array(z.string().uuid()),
    byConversation: z.record(z.string().uuid(), z.array(z.string().uuid()))
  }),

  // Feedback system
  feedbackRequests: z.record(z.string().uuid(), FeedbackRequestSchema),
  feedbackResponses: z.record(z.string().uuid(), FeedbackResponseSchema),
  feedbackCycles: z.record(z.string().uuid(), FeedbackCycleSchema).optional(),

  // Enhanced tracking
  workflowProgress: WorkflowProgressSchema.default({
    status: 'initializing',
    currentPhase: WorkflowPhase.PLANNING,
    phaseProgress: {
      [WorkflowPhase.PLANNING]: 0,
      [WorkflowPhase.RESEARCH]: 0,
      [WorkflowPhase.CREATION]: 0,
      [WorkflowPhase.REVIEW]: 0,
      [WorkflowPhase.FINALIZATION]: 0
    },
    overallProgress: 0,
    estimatedTimeRemaining: 1800,
    startTime: new Date().toISOString(),
    lastUpdated: new Date().toISOString(),
    milestones: [],
    articleProgress: {
      researchComplete: false,
      outlineComplete: false,
      draftComplete: false,
      seoOptimizationComplete: false,
      finalReviewComplete: false,
      articleComplete: false
    }
  }),
  errors: z.record(z.string().uuid(), ErrorRecordSchema).default({}),

  // Performance optimization: paginated collections
  messagePages: z.record(z.string(), z.array(z.string().uuid())).optional(),
  artifactPages: z.record(z.string(), z.array(z.string().uuid())).optional(),

  // Additional data
  keywords: z.array(z.string()),
  metadata: z.record(z.string(), z.any()).optional(),
  comparisonSessionId: z.string().uuid().optional(),

  // Version tracking
  version: z.number().int().positive(),
  lastUpdated: z.string().datetime()
});

/**
 * Type definitions derived from schemas
 */
export type Reasoning = z.infer<typeof ReasoningSchema>;
export type Goal = z.infer<typeof GoalSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type Artifact = z.infer<typeof ArtifactSchema>;
export type FeedbackData = z.infer<typeof FeedbackDataSchema>;
export type FeedbackRequest = z.infer<typeof FeedbackRequestSchema>;
export type FeedbackResponse = z.infer<typeof FeedbackResponseSchema>;
export type FeedbackCycle = z.infer<typeof FeedbackCycleSchema>;
export type WorkflowProgress = z.infer<typeof WorkflowProgressSchema>;
export type ErrorRecord = z.infer<typeof ErrorRecordSchema>;
export type ContentGenerationParams = z.infer<typeof ContentGenerationParamsSchema>;
export type CollaborationState = z.infer<typeof CollaborationStateSchema>;

/**
 * Create default workflow progress
 */
function createDefaultWorkflowProgress(startTime: string): WorkflowProgress {
  return {
    status: 'initializing',
    currentPhase: WorkflowPhase.PLANNING,
    phaseProgress: {
      [WorkflowPhase.PLANNING]: 0,
      [WorkflowPhase.RESEARCH]: 0,
      [WorkflowPhase.CREATION]: 0,
      [WorkflowPhase.REVIEW]: 0,
      [WorkflowPhase.FINALIZATION]: 0
    },
    overallProgress: 0,
    estimatedTimeRemaining: 1800, // 30 minutes as default
    startTime,
    lastUpdated: startTime,
    milestones: [
      {
        id: 'research-started',
        description: 'Research phase started',
        status: MilestoneStatus.PENDING,
        dependsOn: []
      },
      {
        id: 'research-completed',
        description: 'Research phase completed',
        status: MilestoneStatus.PENDING,
        dependsOn: ['research-started']
      },
      {
        id: 'outline-created',
        description: 'Content outline created',
        status: MilestoneStatus.PENDING,
        dependsOn: ['research-completed']
      },
      {
        id: 'draft-created',
        description: 'Initial draft created',
        status: MilestoneStatus.PENDING,
        dependsOn: ['outline-created']
      },
      {
        id: 'seo-optimized',
        description: 'Content SEO optimized',
        status: MilestoneStatus.PENDING,
        dependsOn: ['draft-created']
      },
      {
        id: 'final-review',
        description: 'Final content review completed',
        status: MilestoneStatus.PENDING,
        dependsOn: ['seo-optimized']
      },
      {
        id: 'article-completed',
        description: 'Article completed and ready for publishing',
        status: MilestoneStatus.PENDING,
        dependsOn: ['final-review']
      }
    ],
    articleProgress: {
      researchComplete: false,
      outlineComplete: false,
      draftComplete: false,
      seoOptimizationComplete: false,
      finalReviewComplete: false,
      articleComplete: false
    }
  };
}

/**
 * Create a new collaboration state
 */
export function createCollaborationState(
  sessionId: string,
  params: ContentGenerationParams
): CollaborationState {
  const now = new Date().toISOString();

  return {
    id: sessionId,
    topic: params.topic,
    contentType: params.contentType,
    targetAudience: params.targetAudience,
    tone: params.tone,
    status: SessionStatus.ACTIVE,
    startTime: now,
    currentPhase: WorkflowPhase.PLANNING,
    phaseHistory: [
      {
        phase: WorkflowPhase.PLANNING,
        timestamp: now
      }
    ],
    artifacts: {},
    generatedArtifactIds: [],
    goals: {
      byId: {},
      allIds: [],
      activeIds: [],
      completedIds: []
    },
    messages: {
      byId: {},
      allIds: [],
      byConversation: {}
    },
    feedbackRequests: {},
    feedbackResponses: {},
    feedbackCycles: {},
    // Enhanced tracking
    workflowProgress: createDefaultWorkflowProgress(now),
    errors: {},
    // Performance optimization
    messagePages: { '1': [] },
    artifactPages: { '1': [] },
    keywords: params.keywords || [],
    metadata: {
      additionalInstructions: params.additionalInstructions,
      referenceUrls: params.referenceUrls
    },
    comparisonSessionId: params.comparisonSessionId,
    version: 1,
    lastUpdated: now
  };
}