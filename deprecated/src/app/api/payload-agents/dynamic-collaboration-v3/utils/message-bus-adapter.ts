/**
 * Message Bus Adapter
 * 
 * This file provides a compatibility layer between different message bus implementations
 * to ensure consistent behavior regardless of which implementation is used.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { MessageType, Message } from '../state/unified-schema';
import { enhancedMessageBus } from '../../../agents/collaborative-iteration/utils/enhanced-message-bus';
import { IterativeMessageType } from '../../../agents/collaborative-iteration/types';

/**
 * Map MessageType to IterativeMessageType
 * @param type MessageType to map
 * @returns IterativeMessageType
 */
function mapMessageType(type: MessageType | string): IterativeMessageType {
  // Map MessageType to IterativeMessageType
  switch (type) {
    case MessageType.SYSTEM:
      return IterativeMessageType.SYSTEM_MESSAGE;
    case MessageType.USER:
      return IterativeMessageType.USER_MESSAGE;
    case MessageType.GOAL_UPDATE:
      return IterativeMessageType.GOAL_UPDATE;
    case MessageType.ARTIFACT_UPDATE:
      return IterativeMessageType.ARTIFACT_UPDATE;
    case MessageType.FEEDBACK_REQUEST:
      return IterativeMessageType.FEEDBACK_REQUEST;
    case MessageType.FEEDBACK_RESPONSE:
      return IterativeMessageType.FEEDBACK_RESPONSE;
    case MessageType.CONSULTATION_REQUEST:
      return IterativeMessageType.CONSULTATION_REQUEST;
    case MessageType.CONSULTATION_RESPONSE:
      return IterativeMessageType.CONSULTATION_RESPONSE;
    case MessageType.SYSTEM_MESSAGE:
      return IterativeMessageType.SYSTEM_MESSAGE;
    case MessageType.SYSTEM_ERROR:
      return IterativeMessageType.SYSTEM_ERROR;
    default:
      logger.warn(`Unknown message type: ${type}, defaulting to SYSTEM_MESSAGE`);
      return IterativeMessageType.SYSTEM_MESSAGE;
  }
}

/**
 * Create a message bus adapter
 * @param sessionId Session ID
 * @returns Message bus adapter
 */
export async function createMessageBusAdapter(sessionId: string): Promise<{
  sendMessage: (message: Message) => Promise<any>;
  getHistory: () => any[];
  createBroadcastMessage: (from: string, type: string, content: any) => any;
  createDirectMessage: (from: string, to: string | string[], type: string, content: any, inReplyTo?: string) => any;
}> {
  try {
    // Try to create a session bus using the enhanced message bus
    const sessionBus = enhancedMessageBus.createSessionBus(sessionId);
    
    // Check if the result is a Promise
    if (sessionBus instanceof Promise) {
      // Handle the async case
      const resolvedBus = await sessionBus;
      
      return {
        sendMessage: async (message: Message) => {
          return await resolvedBus.queueMessage(message);
        },
        getHistory: () => resolvedBus.getHistory(),
        createBroadcastMessage: (from: string, type: string, content: any) => {
          return resolvedBus.createBroadcastMessage(from, mapMessageType(type), content);
        },
        createDirectMessage: (from: string, to: string | string[], type: string, content: any, inReplyTo?: string) => {
          return resolvedBus.createDirectMessage(from, to, mapMessageType(type), content, inReplyTo);
        }
      };
    } else {
      // Handle the synchronous case
      return {
        sendMessage: async (message: Message) => {
          return await sessionBus.sendMessage(message);
        },
        getHistory: () => sessionBus.getHistory(),
        createBroadcastMessage: (from: string, type: string, content: any) => {
          return sessionBus.createBroadcastMessage(from, mapMessageType(type), content);
        },
        createDirectMessage: (from: string, to: string | string[], type: string, content: any, inReplyTo?: string) => {
          return sessionBus.createDirectMessage(from, to, mapMessageType(type), content, inReplyTo);
        }
      };
    }
  } catch (error) {
    // If there's an error, create a fallback message bus
    logger.error(`Error creating message bus adapter, using fallback implementation`, {
      sessionId,
      error: (error as Error).message,
      stack: (error as Error).stack
    });
    
    return createFallbackMessageBus(sessionId);
  }
}

/**
 * Create a fallback message bus
 * @param sessionId Session ID
 * @returns Fallback message bus
 */
function createFallbackMessageBus(sessionId: string): {
  sendMessage: (message: Message) => Promise<any>;
  getHistory: () => any[];
  createBroadcastMessage: (from: string, type: string, content: any) => any;
  createDirectMessage: (from: string, to: string | string[], type: string, content: any, inReplyTo?: string) => any;
} {
  // Create a simple in-memory message history
  const messageHistory: Message[] = [];
  
  return {
    sendMessage: async (message: Message) => {
      // Add message to history
      messageHistory.push(message);
      
      // Log the message
      logger.info(`[FALLBACK_MESSAGE_BUS] Message sent`, {
        sessionId,
        messageId: message.id,
        from: message.from,
        to: message.to,
        type: message.type
      });
      
      return { success: true };
    },
    getHistory: () => messageHistory,
    createBroadcastMessage: (from: string, type: string, content: any) => {
      const messageId = uuidv4();
      
      return {
        id: messageId,
        timestamp: new Date().toISOString(),
        from,
        to: 'all',
        type,
        content,
        conversationId: messageId,
        sessionId,
        metadata: {
          sessionId,
          fallbackImplementation: true
        }
      };
    },
    createDirectMessage: (from: string, to: string | string[], type: string, content: any, inReplyTo?: string) => {
      const messageId = uuidv4();
      
      return {
        id: messageId,
        timestamp: new Date().toISOString(),
        from,
        to,
        type,
        content,
        conversationId: sessionId,
        sessionId,
        inReplyTo,
        metadata: {
          sessionId,
          fallbackImplementation: true
        }
      };
    }
  };
}
