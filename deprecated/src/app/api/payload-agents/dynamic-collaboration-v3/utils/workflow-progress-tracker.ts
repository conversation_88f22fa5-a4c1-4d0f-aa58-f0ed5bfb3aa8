/**
 * Workflow Progress Tracker
 *
 * This utility manages workflow progress tracking, milestone updates,
 * and estimated time calculations for the goal-based orchestration system.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import {
  WorkflowPhase,
  MilestoneStatus,
  MessageType,
  GoalType,
  GoalStatus
} from '../state/unified-schema';

/**
 * Phase weight configuration for overall progress calculation
 */
const PHASE_WEIGHTS = {
  [WorkflowPhase.PLANNING]: 0.05,
  [WorkflowPhase.RESEARCH]: 0.25,
  [WorkflowPhase.CREATION]: 0.40,
  [WorkflowPhase.REVIEW]: 0.20,
  [WorkflowPhase.FINALIZATION]: 0.10
};

/**
 * Goal type to milestone mapping
 */
const GOAL_TO_MILESTONE_MAP: Record<GoalType, string | null> = {
  [GoalType.RESEARCH]: null, // High-level goal
  [GoalType.CONTENT]: null, // High-level goal
  [GoalType.QUALITY]: null, // High-level goal
  [GoalType.MARKET_RESEARCH]: 'research-started',
  [GoalType.KEYWORD_ANALYSIS]: 'research-completed',
  [GoalType.CONTENT_STRATEGY]: 'outline-created',
  [GoalType.CONTENT_CREATION]: 'draft-created',
  [GoalType.SEO_OPTIMIZATION]: 'seo-optimized',
  [GoalType.QUALITY_ASSESSMENT]: 'final-review'
};

/**
 * Goal type to article progress mapping
 */
const GOAL_TO_ARTICLE_PROGRESS_MAP: Partial<Record<GoalType, keyof {
  researchComplete: boolean,
  outlineComplete: boolean,
  draftComplete: boolean,
  seoOptimizationComplete: boolean,
  finalReviewComplete: boolean,
  articleComplete: boolean
}>> = {
  [GoalType.KEYWORD_ANALYSIS]: 'researchComplete',
  [GoalType.CONTENT_STRATEGY]: 'outlineComplete',
  [GoalType.CONTENT_CREATION]: 'draftComplete',
  [GoalType.SEO_OPTIMIZATION]: 'seoOptimizationComplete',
  [GoalType.QUALITY_ASSESSMENT]: 'finalReviewComplete'
};

/**
 * Workflow Progress Tracker
 */
export class WorkflowProgressTracker {
  private sessionId: string;
  private stateManager: StateManager;

  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
  }

  /**
   * Update phase progress
   * @param phase Workflow phase
   * @param progress Progress percentage (0-100)
   * @returns Success indicator
   */
  public async updatePhaseProgress(phase: WorkflowPhase, progress: number): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Update phase progress
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const workflowProgress = { ...currentState.workflowProgress };

        // Update phase progress
        workflowProgress.phaseProgress = {
          ...workflowProgress.phaseProgress,
          [phase]: Math.max(0, Math.min(100, progress))
        };

        // Calculate overall progress
        workflowProgress.overallProgress = this.calculateOverallProgress(workflowProgress.phaseProgress);

        // Update status if needed
        if (workflowProgress.status === 'initializing' && workflowProgress.overallProgress > 0) {
          workflowProgress.status = 'in_progress';
        }

        // Update last updated timestamp
        workflowProgress.lastUpdated = new Date().toISOString();

        return {
          ...currentState,
          workflowProgress
        };
      });

      // Create progress update message
      await this.createProgressUpdateMessage(phase, progress);

      return true;
    } catch (error) {
      logger.error(`Error updating phase progress`, {
        sessionId: this.sessionId,
        phase,
        progress,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Update milestone status
   * @param milestoneId Milestone ID
   * @param status New status
   * @returns Success indicator
   */
  public async updateMilestoneStatus(milestoneId: string, status: MilestoneStatus): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Find the milestone
      const milestone = state.workflowProgress.milestones.find(m => m.id === milestoneId);
      if (!milestone) {
        throw new Error(`Milestone ${milestoneId} not found`);
      }

      // Update milestone status
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const workflowProgress = { ...currentState.workflowProgress };

        // Update milestone status
        workflowProgress.milestones = workflowProgress.milestones.map(m => {
          if (m.id === milestoneId) {
            return {
              ...m,
              status,
              completedAt: status === MilestoneStatus.COMPLETED ? new Date().toISOString() : m.completedAt
            };
          }
          return m;
        });

        // Update last updated timestamp
        workflowProgress.lastUpdated = new Date().toISOString();

        return {
          ...currentState,
          workflowProgress
        };
      });

      // If milestone is completed, update dependent milestones
      if (status === MilestoneStatus.COMPLETED) {
        await this.updateDependentMilestones(milestoneId);

        // Update article progress if applicable
        await this.updateArticleProgressFromMilestone(milestoneId);
      }

      return true;
    } catch (error) {
      logger.error(`Error updating milestone status`, {
        sessionId: this.sessionId,
        milestoneId,
        status,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Update milestone status based on goal completion
   * @param goalId Goal ID
   * @param goalType Goal type
   * @returns Success indicator
   */
  public async updateMilestoneFromGoal(goalId: string, goalType: GoalType): Promise<boolean> {
    try {
      // Get milestone ID from goal type
      const milestoneId = GOAL_TO_MILESTONE_MAP[goalType];
      if (!milestoneId) {
        return true; // No milestone associated with this goal type
      }

      // Update milestone status
      await this.updateMilestoneStatus(milestoneId, MilestoneStatus.COMPLETED);

      // Update article progress if applicable
      const articleProgressKey = GOAL_TO_ARTICLE_PROGRESS_MAP[goalType];
      if (articleProgressKey) {
        await this.updateArticleProgress(articleProgressKey, true);
      }

      return true;
    } catch (error) {
      logger.error(`Error updating milestone from goal`, {
        sessionId: this.sessionId,
        goalId,
        goalType,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Update article progress
   * @param key Article progress key
   * @param value New value
   * @returns Success indicator
   */
  public async updateArticleProgress(
    key: keyof {
      researchComplete: boolean,
      outlineComplete: boolean,
      draftComplete: boolean,
      seoOptimizationComplete: boolean,
      finalReviewComplete: boolean,
      articleComplete: boolean
    },
    value: boolean
  ): Promise<boolean> {
    try {
      // Update article progress
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const workflowProgress = { ...currentState.workflowProgress };

        // Update article progress
        workflowProgress.articleProgress = {
          ...workflowProgress.articleProgress,
          [key]: value
        };

        // Check if article is complete
        if (key === 'finalReviewComplete' && value === true) {
          workflowProgress.articleProgress.articleComplete = true;

          // Update article-completed milestone
          workflowProgress.milestones = workflowProgress.milestones.map(m => {
            if (m.id === 'article-completed') {
              return {
                ...m,
                status: MilestoneStatus.COMPLETED,
                completedAt: new Date().toISOString()
              };
            }
            return m;
          });

          // Update workflow status
          workflowProgress.status = 'completed';
        }

        // Update last updated timestamp
        workflowProgress.lastUpdated = new Date().toISOString();

        return {
          ...currentState,
          workflowProgress
        };
      });

      return true;
    } catch (error) {
      logger.error(`Error updating article progress`, {
        sessionId: this.sessionId,
        key,
        value,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Update estimated time remaining
   * @param estimatedTimeRemaining Estimated time remaining in seconds
   * @returns Success indicator
   */
  public async updateEstimatedTimeRemaining(estimatedTimeRemaining: number): Promise<boolean> {
    try {
      // Update estimated time remaining
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const workflowProgress = { ...currentState.workflowProgress };

        // Update estimated time remaining
        workflowProgress.estimatedTimeRemaining = Math.max(0, estimatedTimeRemaining);

        // Update last updated timestamp
        workflowProgress.lastUpdated = new Date().toISOString();

        return {
          ...currentState,
          workflowProgress
        };
      });

      return true;
    } catch (error) {
      logger.error(`Error updating estimated time remaining`, {
        sessionId: this.sessionId,
        estimatedTimeRemaining,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Calculate overall progress based on phase progress
   * @param phaseProgress Phase progress record
   * @returns Overall progress percentage (0-100)
   */
  private calculateOverallProgress(phaseProgress: Record<WorkflowPhase, number>): number {
    let weightedSum = 0;
    let totalWeight = 0;

    for (const phase of Object.values(WorkflowPhase)) {
      const weight = PHASE_WEIGHTS[phase];
      const progress = phaseProgress[phase] || 0;

      weightedSum += weight * progress;
      totalWeight += weight;
    }

    return Math.round(weightedSum / totalWeight);
  }

  /**
   * Update dependent milestones
   * @param completedMilestoneId Completed milestone ID
   */
  private async updateDependentMilestones(completedMilestoneId: string): Promise<void> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) return;

      // Find milestones that depend on the completed milestone
      const dependentMilestones = state.workflowProgress.milestones.filter(
        m => m.dependsOn?.includes(completedMilestoneId) && m.status === MilestoneStatus.PENDING
      );

      // Update dependent milestones to in_progress if all dependencies are completed
      for (const milestone of dependentMilestones) {
        const allDependenciesCompleted = milestone.dependsOn?.every(depId => {
          const depMilestone = state.workflowProgress.milestones.find(m => m.id === depId);
          return depMilestone && depMilestone.status === MilestoneStatus.COMPLETED;
        });

        if (allDependenciesCompleted) {
          await this.updateMilestoneStatus(milestone.id, MilestoneStatus.IN_PROGRESS);
        }
      }
    } catch (error) {
      logger.error(`Error updating dependent milestones`, {
        sessionId: this.sessionId,
        completedMilestoneId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Update article progress based on milestone
   * @param milestoneId Milestone ID
   */
  private async updateArticleProgressFromMilestone(milestoneId: string): Promise<void> {
    try {
      // Map milestone to article progress
      const articleProgressMap: Record<string, keyof {
        researchComplete: boolean,
        outlineComplete: boolean,
        draftComplete: boolean,
        seoOptimizationComplete: boolean,
        finalReviewComplete: boolean,
        articleComplete: boolean
      }> = {
        'research-completed': 'researchComplete',
        'outline-created': 'outlineComplete',
        'draft-created': 'draftComplete',
        'seo-optimized': 'seoOptimizationComplete',
        'final-review': 'finalReviewComplete',
        'article-completed': 'articleComplete'
      };

      // Update article progress if applicable
      const articleProgressKey = articleProgressMap[milestoneId];
      if (articleProgressKey) {
        await this.updateArticleProgress(articleProgressKey, true);
      }
    } catch (error) {
      logger.error(`Error updating article progress from milestone`, {
        sessionId: this.sessionId,
        milestoneId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Create progress update message
   * @param phase Workflow phase
   * @param progress Progress percentage
   */
  private async createProgressUpdateMessage(phase: WorkflowPhase, progress: number): Promise<void> {
    try {
      // Create message ID
      const messageId = uuidv4();

      // Update state with the message
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        // Create conversation ID
        const conversationId = uuidv4();

        // Create message
        const newMessage = {
          id: messageId,
          timestamp: new Date().toISOString(),
          from: 'system',
          to: 'all',
          type: MessageType.PROGRESS_UPDATE,
          content: {
            phase,
            progress,
            overallProgress: currentState.workflowProgress.overallProgress
          },
          conversationId
        };

        // Add message to messages.byId
        const messages = {
          byId: {
            ...currentState.messages.byId,
            [messageId]: newMessage
          },
          allIds: [...currentState.messages.allIds, messageId],
          byConversation: {
            ...currentState.messages.byConversation,
            [conversationId]: [messageId]
          }
        };

        // Add to first message page
        const messagePages = { ...currentState.messagePages };
        if (messagePages['1']) {
          messagePages['1'] = [...messagePages['1'], messageId];
        } else {
          messagePages['1'] = [messageId];
        }

        return {
          ...currentState,
          messages,
          messagePages
        };
      });
    } catch (error) {
      logger.error(`Error creating progress update message`, {
        sessionId: this.sessionId,
        phase,
        progress,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}
