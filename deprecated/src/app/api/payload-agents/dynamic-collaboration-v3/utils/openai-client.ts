/**
 * OpenAI Client
 * 
 * This file provides a singleton instance of the OpenAI client for use throughout the application.
 */

import OpenAI from 'openai';
import logger from '../../../utils/logger';

// Initialize OpenAI client
let openaiInstance: OpenAI | null = null;

try {
  if (process.env.OPENAI_API_KEY) {
    openaiInstance = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    logger.info('OpenAI client initialized successfully');
  } else {
    logger.warn('OPENAI_API_KEY not found in environment variables');
  }
} catch (error) {
  logger.error('Error initializing OpenAI client', {
    error: (error as Error).message,
    stack: (error as Error).stack
  });
}

// Export the OpenAI client instance
export const openai = openaiInstance || new OpenAI({ apiKey: 'dummy-key' });

// Export a function to check if the client is properly initialized
export function isOpenAIClientInitialized(): boolean {
  return openaiInstance !== null;
}
