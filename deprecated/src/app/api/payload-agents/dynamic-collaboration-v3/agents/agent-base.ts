/**
 * Agent Base Class
 *
 * This file defines the base class for all specialized agents in the goal-based orchestration system.
 * It provides common functionality for message handling, state management, and goal processing.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import {
  MessageType,
  Message,
  Goal,
  GoalStatus,
  GoalType,
  CollaborationState
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { ArtifactDecisionFramework, ArtifactDecisionType } from '../services/artifact-decision-framework';
import { FeedbackLoopSystem } from '../utils/feedback-loop-system';
import { enhancedMessageBus, EnhancedMessageBus } from '../../../agents/collaborative-iteration/utils/enhanced-message-bus';

/**
 * Message handler function type
 */
export type MessageHandler = (
  message: Message,
  state: CollaborationState,
  stateManager: StateManager
) => Promise<Message | null>;

/**
 * Agent base class
 */
export abstract class AgentBase {
  protected agentId: string;
  protected sessionId: string;
  protected stateManager: StateManager;
  protected decisionFramework: ArtifactDecisionFramework;
  protected handlers: Map<MessageType, MessageHandler> = new Map();
  protected messageBus: {
    sendMessage: (message: any) => Promise<any>;
    getHistory: () => any[];
    createBroadcastMessage: (from: string, type: string, content: any) => any;
    createDirectMessage: (from: string, to: string | string[], type: string, content: any, inReplyTo?: string) => any;
  };

  /**
   * Constructor
   * @param agentId Agent ID
   * @param sessionId Session ID
   */
  constructor(agentId: string, sessionId: string) {
    this.agentId = agentId;
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
    this.decisionFramework = new ArtifactDecisionFramework(sessionId);

    // Create session-specific message bus
    this.messageBus = enhancedMessageBus.createSessionBus(sessionId);

    // Register handlers
    this.registerHandlers();

    // Register with the message bus
    enhancedMessageBus.registerHandler(
      sessionId,
      agentId,
      this.processMessage.bind(this)
    );
  }

  /**
   * Register message handlers
   * This method should be implemented by derived classes
   */
  protected abstract registerHandlers(): void;

  /**
   * Register a handler for a message type
   * @param type Message type
   * @param handler Handler function
   */
  protected registerHandler(type: MessageType | string, handler: MessageHandler): void {
    this.handlers.set(type as MessageType, handler);
  }

  /**
   * Process a message
   * @param message Message to process
   * @returns Promise<Message | null> Response message or null
   */
  public async processMessage(message: any): Promise<any> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Convert message to our format if needed
      const normalizedMessage: Message = {
        id: message.id,
        timestamp: message.timestamp,
        from: message.from,
        to: message.to,
        type: message.type as MessageType,
        content: message.content,
        conversationId: message.conversationId || message.id,
        replyTo: message.replyTo,
        metadata: message.metadata
      };

      // Get handler for message type
      const handler = this.handlers.get(normalizedMessage.type);
      if (!handler) {
        logger.warn(`No handler registered for message type ${normalizedMessage.type}`, {
          agentId: this.agentId,
          messageType: normalizedMessage.type
        });
        return null;
      }

      // Process message with handler
      const response = await handler(normalizedMessage, state, this.stateManager);

      // If there's a response, send it via the message bus
      if (response) {
        await this.messageBus.sendMessage(response);
      }

      return response;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing message`, {
        agentId: this.agentId,
        messageType: message.type,
        error: err.message || String(error),
        stack: err.stack
      });

      // Send error message
      const errorMessage = this.messageBus.createDirectMessage(
        this.agentId,
        message.from,
        'ERROR',
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.id
      );

      await this.messageBus.sendMessage(errorMessage);

      return null;
    }
  }

  /**
   * Process a goal
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  public abstract processGoal(goalId: string): Promise<boolean>;

  /**
   * Create a direct message
   * @param to Recipient
   * @param type Message type
   * @param content Message content
   * @param conversationId Conversation ID
   * @param inReplyTo Message ID being replied to
   * @returns Message object
   */
  protected createMessage(
    to: string | string[],
    type: MessageType | string,
    content: any,
    conversationId?: string,
    inReplyTo?: string
  ): Message {
    const message = this.messageBus.createDirectMessage(
      this.agentId,
      to,
      type,
      content,
      inReplyTo
    );

    // Ensure it has the correct format
    return {
      id: message.id,
      timestamp: message.timestamp,
      from: message.from,
      to: message.to,
      type: type as MessageType,
      content: message.content,
      conversationId: conversationId || message.id,
      replyTo: inReplyTo,
      metadata: message.metadata
    };
  }

  /**
   * Create a broadcast message
   * @param type Message type
   * @param content Message content
   * @returns Message object
   */
  protected createBroadcastMessage(
    type: MessageType | string,
    content: any
  ): Message {
    const message = this.messageBus.createBroadcastMessage(
      this.agentId,
      type,
      content
    );

    // Ensure it has the correct format
    return {
      id: message.id,
      timestamp: message.timestamp,
      from: message.from,
      to: message.to,
      type: type as MessageType,
      content: message.content,
      conversationId: message.id,
      metadata: message.metadata
    };
  }

  /**
   * Send a message
   * @param message Message to send
   * @returns Promise<any> Result of sending the message
   */
  protected async sendMessage(message: Message): Promise<any> {
    return await this.messageBus.sendMessage(message);
  }

  /**
   * Get session ID
   * @returns Session ID
   */
  protected getSessionId(): string {
    return this.sessionId;
  }
}
