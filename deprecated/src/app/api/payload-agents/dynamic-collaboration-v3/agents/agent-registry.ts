/**
 * Agent Registry
 *
 * This file implements a registry for all specialized agents in the goal-based orchestration system.
 * It provides methods for getting agent instances and routing messages to the appropriate agents.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { IterativeMessage } from '../../../agents/collaborative-iteration/types';
import { AgentBase } from './agent-base';
import { MarketResearchAgent } from './market-research-agent';
import { KeywordAnalysisAgent } from './keyword-analysis-agent';
import { ContentStrategyAgent } from './content-strategy-agent';
import { ContentCreationAgent } from './content-creation-agent';
import { SEOOptimizationAgent } from './seo-optimization-agent';
import { QualityAssessmentAgent } from './quality-assessment-agent';

/**
 * Agent registry
 */
export class AgentRegistry {
  private static instances: Map<string, Map<string, AgentBase>> = new Map();

  /**
   * Get an agent instance
   * @param agentId Agent ID
   * @param sessionId Session ID
   * @returns Agent instance
   */
  public static getAgent(agentId: string, sessionId: string): AgentBase {
    // Get or create session map
    if (!this.instances.has(sessionId)) {
      this.instances.set(sessionId, new Map());
    }

    const sessionAgents = this.instances.get(sessionId)!;

    // Get or create agent instance
    if (!sessionAgents.has(agentId)) {
      sessionAgents.set(agentId, this.createAgent(agentId, sessionId));
    }

    return sessionAgents.get(agentId)!;
  }

  /**
   * Create a new agent instance
   * @param agentId Agent ID
   * @param sessionId Session ID
   * @returns Agent instance
   */
  private static createAgent(agentId: string, sessionId: string): AgentBase {
    switch (agentId) {
      case 'market-research':
        return new MarketResearchAgent(sessionId);
      case 'keyword-analysis':
        return new KeywordAnalysisAgent(sessionId);
      case 'content-strategy':
        return new ContentStrategyAgent(sessionId);
      case 'content-creation':
        return new ContentCreationAgent(sessionId);
      case 'seo-optimization':
        return new SEOOptimizationAgent(sessionId);
      case 'quality-assessment':
        return new QualityAssessmentAgent(sessionId);
      default:
        throw new Error(`Unknown agent type: ${agentId}`);
    }
  }

  /**
   * Route a message to the appropriate agent
   * @param message Message to route
   * @param sessionId Session ID
   * @returns Promise<IterativeMessage | null> Response message or null
   */
  public static async routeMessage(message: IterativeMessage, sessionId: string): Promise<IterativeMessage | null> {
    try {
      // Get the target agent
      const agent = this.getAgent(message.to as string, sessionId);

      // Process the message
      return await agent.processMessage(message);
    } catch (error) {
      const err = error as Error;
      logger.error(`Error routing message`, {
        sessionId,
        messageId: message.id,
        to: message.to,
        error: err.message || String(error),
        stack: err.stack
      });
      return null;
    }
  }
}
