/**
 * Market Research Agent
 *
 * This file implements the specialized market research agent for the goal-based orchestration system.
 * It handles market research goals and produces market research artifacts.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import {
  MessageType,
  Message,
  GoalStatus,
  GoalType,
  CollaborationState,
  Artifact
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { ArtifactDecisionFramework, ArtifactDecisionType } from '../services/artifact-decision-framework';
import { FeedbackLoopSystem } from '../utils/feedback-loop-system-new';
import { AgentBase } from './agent-base';

/**
 * Market Research Agent
 */
export class MarketResearchAgent extends AgentBase {
  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    super('market-research', sessionId);
    logger.info(`Market Research Agent initialized for session ${sessionId}`);
  }

  /**
   * Register message handlers
   */
  protected registerHandlers(): void {
    this.registerHandler(MessageType.GOAL_ASSIGNMENT, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.ARTIFACT_REQUEST, this.handleArtifactRequest.bind(this));
    this.registerHandler(MessageType.FEEDBACK, this.handleFeedback.bind(this));
    this.registerHandler(MessageType.CONSULTATION_REQUEST, this.handleConsultationRequest.bind(this));
    this.registerHandler(MessageType.REQUEST, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.INITIAL_REQUEST, this.handleGoalAssignment.bind(this));
  }

  /**
   * Process a goal
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  public async processGoal(goalId: string): Promise<boolean> {
    try {
      logger.info(`Market Research Agent processing goal ${goalId}`, {
        sessionId: this.getSessionId(),
        goalId
      });

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Get the goal from the byId object
      const goal = state.goals.byId[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, this.agentId);

      // Make a decision about artifact creation
      const decision = await this.decisionFramework.decideArtifactCreation('market-research', goalId);

      // If we should use an existing artifact
      if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
        // Complete the goal with the existing artifact
        await this.stateManager.completeGoal(goalId, decision.existingArtifactId);
        return true;
      }

      // If we should improve an existing artifact
      if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
        // Get the existing artifact
        const existingArtifact = state.artifacts[decision.existingArtifactId];
        if (!existingArtifact) {
          throw new Error(`Artifact ${decision.existingArtifactId} not found`);
        }

        // Import the OpenAI integration
        const { generateMarketResearch } = await import('../utils/content-generation');

        // Generate improved market research content using OpenAI
        const marketResearchContent = await generateMarketResearch(
          state.topic,
          state.contentType,
          state.targetAudience
        );

        // Create the improved artifact
        const artifactId = await this.stateManager.createArtifact(
          'market-research',
          'Market Research Report (Improved)',
          marketResearchContent,
          'market-research',
          goalId,
          decision.existingArtifactId
        );

        // Initialize feedback loop system
        const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

        // Generate feedback on the market research
        const feedback = await feedbackSystem.generateFeedback(
          artifactId,
          'content-strategy',
          ['audience analysis', 'market trends', 'competitor analysis']
        );

        // Create a feedback request
        const requestId = await feedbackSystem.requestFeedback(
          'market-research',
          'content-strategy',
          artifactId,
          ['audience analysis', 'market trends', 'competitor analysis']
        );

        // Provide feedback
        await feedbackSystem.provideFeedback(
          'content-strategy',
          'market-research',
          requestId,
          feedback
        );

        // Complete the goal
        await this.stateManager.completeGoal(goalId, artifactId);
        return true;
      }

      // Otherwise, create a new artifact
      // Import the OpenAI integration
      const { generateMarketResearch } = await import('../utils/content-generation');

      // Generate market research content using OpenAI
      const marketResearchContent = await generateMarketResearch(
        state.topic,
        state.contentType,
        state.targetAudience
      );

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'market-research',
        'Market Research Report',
        marketResearchContent,
        'market-research',
        goalId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // Generate feedback on the market research
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'content-strategy',
        ['audience analysis', 'market trends', 'competitor analysis']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'market-research',
        'content-strategy',
        artifactId,
        ['audience analysis', 'market trends', 'competitor analysis']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'content-strategy',
        'market-research',
        requestId,
        feedback
      );

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing market research goal`, {
        sessionId: this.getSessionId(),
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Handle goal assignment message
   * @param message Goal assignment message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleGoalAssignment(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      // Extract goal ID from message content
      let goalId = message.content?.goalId;

      // If no goalId in content, check if there's a goal in the state that needs processing
      if (!goalId && message.type === MessageType.INITIAL_REQUEST) {
        // Find an active goal assigned to this agent
        const activeGoals = Object.values(state.goals.byId).filter(
          goal => goal.status === GoalStatus.ACTIVE && goal.assignedTo === this.agentId
        );

        if (activeGoals.length > 0) {
          goalId = activeGoals[0].id;
        } else {
          // Create a new goal if none exists
          const newGoal = {
            description: `Conduct market research for ${state.topic}`,
            type: GoalType.MARKET_RESEARCH,
            dependencies: [],
            criteria: [
              'Identify target audience demographics',
              'Analyze market trends',
              'Identify key competitors',
              'Determine market gaps and opportunities'
            ]
          };

          const goals = await stateManager.defineGoals([newGoal]);
          goalId = goals[0];
        }
      }

      if (!goalId) {
        throw new Error('Goal ID not provided and no active goals found');
      }

      // Process the goal
      await this.processGoal(goalId);

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.ACKNOWLEDGMENT,
        {
          goalId,
          status: 'processing',
          message: `Market Research Agent is processing goal ${goalId}`
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling goal assignment`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      // Send error message
      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle artifact request message
   * @param message Artifact request message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleArtifactRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      // Find the most recent market research artifact
      const marketResearchArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'market-research'
      );

      if (marketResearchArtifacts.length === 0) {
        // No artifact found, create one
        const newGoal = {
          description: `Conduct market research for ${state.topic}`,
          type: GoalType.MARKET_RESEARCH,
          dependencies: [],
          criteria: [
            'Identify target audience demographics',
            'Analyze market trends',
            'Identify key competitors',
            'Determine market gaps and opportunities'
          ]
        };

        const goals = await stateManager.defineGoals([newGoal]);
        await this.processGoal(goals[0]);

        // Get the artifact we just created
        const updatedState = await stateManager.getState();
        const newArtifacts = Object.values(updatedState.artifacts).filter(
          artifact => artifact.type === 'market-research'
        );

        if (newArtifacts.length === 0) {
          throw new Error('Failed to create market research artifact');
        }

        // Return the artifact
        return this.createMessage(
          message.from,
          MessageType.ARTIFACT_DELIVERY,
          {
            artifactId: newArtifacts[0].id,
            artifactType: 'market-research',
            content: newArtifacts[0].content
          },
          message.conversationId,
          message.id
        );
      }

      // Get the most recent artifact
      const artifact = marketResearchArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // Return the artifact
      return this.createMessage(
        message.from,
        MessageType.ARTIFACT_DELIVERY,
        {
          artifactId: artifact.id,
          artifactType: 'market-research',
          content: artifact.content
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling artifact request`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle feedback message
   * @param message Feedback message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleFeedback(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { feedback, artifactId } = message.content;

      if (!feedback || !artifactId) {
        throw new Error('Missing required fields: feedback, artifactId');
      }

      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // Process the feedback
      await feedbackSystem.processFeedback(artifactId, feedback);

      // Check if we need to improve the artifact
      if (feedback.improvementNeeded) {
        // Create a new goal to improve the artifact
        const newGoal = {
          description: `Improve market research for ${state.topic} based on feedback`,
          type: GoalType.MARKET_RESEARCH,
          dependencies: [],
          criteria: [
            'Address feedback points',
            'Improve quality of market research',
            'Provide more detailed analysis'
          ]
        };

        const goals = await stateManager.defineGoals([newGoal]);
        await this.processGoal(goals[0]);
      }

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.ACKNOWLEDGMENT,
        {
          message: `Feedback received and processed for artifact ${artifactId}`,
          status: 'success'
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling feedback`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle consultation request message
   * @param message Consultation request message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleConsultationRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { question, context } = message.content;

      if (!question) {
        throw new Error('Missing required field: question');
      }

      // Generate a response based on the question and context
      const response = `Market Research Agent consultation response to: "${question}"\n\n` +
        `Based on our market research for ${state.topic}, we can provide the following insights:\n\n` +
        `1. The target audience for this content is primarily ${state.targetAudience}\n` +
        `2. Key market trends include increasing interest in ${state.topic} among ${state.targetAudience}\n` +
        `3. Main competitors in this space are focusing on similar topics but with less depth\n` +
        `4. There's a market opportunity to provide more detailed, actionable content`;

      // Send consultation response
      return this.createMessage(
        message.from,
        MessageType.CONSULTATION_RESPONSE,
        {
          response,
          originalQuestion: question
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling consultation request`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }
}
