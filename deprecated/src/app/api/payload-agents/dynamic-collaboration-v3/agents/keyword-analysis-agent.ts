/**
 * Keyword Analysis Agent
 *
 * This file implements the specialized keyword analysis agent for the goal-based orchestration system.
 * It handles keyword analysis goals and produces keyword analysis artifacts.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import {
  MessageType,
  Message,
  GoalStatus,
  GoalType,
  CollaborationState,
  Artifact
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { ArtifactDecisionFramework, ArtifactDecisionType } from '../services/artifact-decision-framework';
import { FeedbackLoopSystem } from '../utils/feedback-loop-system-new';
import { AgentBase } from './agent-base';

/**
 * Keyword Analysis Agent
 */
export class KeywordAnalysisAgent extends AgentBase {
  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    super('keyword-analysis', sessionId);
    logger.info(`Keyword Analysis Agent initialized for session ${sessionId}`);
  }

  /**
   * Register message handlers
   */
  protected registerHandlers(): void {
    this.registerHandler(MessageType.GOAL_ASSIGNMENT, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.ARTIFACT_REQUEST, this.handleArtifactRequest.bind(this));
    this.registerHandler(MessageType.FEEDBACK, this.handleFeedback.bind(this));
    this.registerHandler(MessageType.CONSULTATION_REQUEST, this.handleConsultationRequest.bind(this));
  }

  /**
   * Process a goal
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  public async processGoal(goalId: string): Promise<boolean> {
    try {
      logger.info(`Keyword Analysis Agent processing goal ${goalId}`, {
        sessionId: this.getSessionId(),
        goalId
      });

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Get the goal from the byId object
      const goal = state.goals.byId[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, this.agentId);

      // Make a decision about artifact creation
      const decision = await this.decisionFramework.decideArtifactCreation('keyword-analysis', goalId);

      // If we should use an existing artifact
      if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
        // Complete the goal with the existing artifact
        await this.stateManager.completeGoal(goalId, decision.existingArtifactId);
        return true;
      }

      // If we should improve an existing artifact
      if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
        // Get the existing artifact
        const existingArtifact = state.artifacts[decision.existingArtifactId];
        if (!existingArtifact) {
          throw new Error(`Artifact ${decision.existingArtifactId} not found`);
        }

        // Import the OpenAI integration
        const { generateKeywordAnalysis } = await import('../utils/content-generation');

        // Generate improved keyword analysis using OpenAI
        const { content, keywords } = await generateKeywordAnalysis(
          state.topic,
          state.contentType,
          state.targetAudience
        );

        // Update the state with the keywords
        await this.stateManager.updateKeywords(keywords);

        // Create the improved artifact
        const artifactId = await this.stateManager.createArtifact(
          'keyword-analysis',
          'SEO Keyword Analysis (Improved)',
          content,
          'keyword-analysis',
          goalId,
          decision.existingArtifactId
        );

        // Initialize feedback loop system
        const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

        // Generate feedback on the keyword analysis
        const feedback = await feedbackSystem.generateFeedback(
          artifactId,
          'content-strategy',
          ['keyword relevance', 'search volume', 'competition analysis']
        );

        // Create a feedback request
        const requestId = await feedbackSystem.requestFeedback(
          'keyword-analysis',
          'content-strategy',
          artifactId,
          ['keyword relevance', 'search volume', 'competition analysis']
        );

        // Provide feedback
        await feedbackSystem.provideFeedback(
          'content-strategy',
          'keyword-analysis',
          requestId,
          feedback
        );

        // Complete the goal
        await this.stateManager.completeGoal(goalId, artifactId);
        return true;
      }

      // Otherwise, create a new artifact
      // Import the OpenAI integration
      const { generateKeywordAnalysis } = await import('../utils/content-generation');

      // Generate keyword analysis content using OpenAI
      const { content, keywords } = await generateKeywordAnalysis(
        state.topic,
        state.contentType,
        state.targetAudience
      );

      // Update the state with the keywords
      await this.stateManager.updateKeywords(keywords);

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'keyword-analysis',
        'SEO Keyword Analysis',
        content,
        'keyword-analysis',
        goalId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // Generate feedback on the keyword analysis
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'content-strategy',
        ['keyword relevance', 'search volume', 'competition analysis']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'keyword-analysis',
        'content-strategy',
        artifactId,
        ['keyword relevance', 'search volume', 'competition analysis']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'content-strategy',
        'keyword-analysis',
        requestId,
        feedback
      );

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing keyword analysis goal`, {
        sessionId: this.getSessionId(),
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Handle goal assignment message
   * @param message Goal assignment message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleGoalAssignment(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { goalId } = message.content;
      if (!goalId) {
        throw new Error('Goal ID not provided');
      }

      // Process the goal
      await this.processGoal(goalId);

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.ACKNOWLEDGMENT,
        {
          goalId,
          status: 'processing'
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling goal assignment`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });
      return null;
    }
  }

  /**
   * Handle artifact request message
   * @param message Artifact request message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleArtifactRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      // Find the most recent keyword analysis artifact
      const keywordAnalysisArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'keyword-analysis'
      );

      if (keywordAnalysisArtifacts.length === 0) {
        // No artifact found, create one
        const newGoal = {
          description: `Conduct keyword analysis for ${state.topic}`,
          type: GoalType.KEYWORD_ANALYSIS,
          dependencies: [],
          criteria: [
            'Identify primary keywords',
            'Analyze search volume',
            'Determine keyword difficulty',
            'Suggest long-tail variations'
          ]
        };

        const goals = await stateManager.defineGoals([newGoal]);
        await this.processGoal(goals[0]);

        // Get the artifact we just created
        const updatedState = await stateManager.getState();
        const newArtifacts = Object.values(updatedState.artifacts).filter(
          artifact => artifact.type === 'keyword-analysis'
        );

        if (newArtifacts.length === 0) {
          throw new Error('Failed to create keyword analysis artifact');
        }

        // Return the artifact
        return this.createMessage(
          message.from,
          MessageType.ARTIFACT_DELIVERY,
          {
            artifactId: newArtifacts[0].id,
            artifactType: 'keyword-analysis',
            content: newArtifacts[0].content
          },
          message.conversationId,
          message.id
        );
      }

      // Get the most recent artifact
      const artifact = keywordAnalysisArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // Return the artifact
      return this.createMessage(
        message.from,
        MessageType.ARTIFACT_DELIVERY,
        {
          artifactId: artifact.id,
          artifactType: 'keyword-analysis',
          content: artifact.content
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling artifact request`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle feedback message
   * @param message Feedback message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleFeedback(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { feedback, artifactId } = message.content;

      if (!feedback || !artifactId) {
        throw new Error('Missing required fields: feedback, artifactId');
      }

      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // Process the feedback
      await feedbackSystem.processFeedback(artifactId, feedback);

      // Check if we need to improve the artifact
      if (feedback.improvementNeeded) {
        // Create a new goal to improve the artifact
        const newGoal = {
          description: `Improve keyword analysis for ${state.topic} based on feedback`,
          type: GoalType.KEYWORD_ANALYSIS,
          dependencies: [],
          criteria: [
            'Address feedback points',
            'Improve keyword relevance',
            'Provide more detailed analysis'
          ]
        };

        const goals = await stateManager.defineGoals([newGoal]);
        await this.processGoal(goals[0]);
      }

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.ACKNOWLEDGMENT,
        {
          message: `Feedback received and processed for artifact ${artifactId}`,
          status: 'success'
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling feedback`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle consultation request message
   * @param message Consultation request message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleConsultationRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { question, context } = message.content;

      if (!question) {
        throw new Error('Missing required field: question');
      }

      // Generate a response based on the question and context
      const response = `Keyword Analysis Agent consultation response to: "${question}"\n\n` +
        `Based on our keyword analysis for ${state.topic}, we can provide the following insights:\n\n` +
        `1. The primary keywords for this content are related to ${state.topic}\n` +
        `2. Search volume for these keywords is moderate to high\n` +
        `3. Competition for these keywords is moderate\n` +
        `4. We recommend focusing on long-tail variations for better targeting`;

      // Send consultation response
      return this.createMessage(
        message.from,
        MessageType.CONSULTATION_RESPONSE,
        {
          response,
          originalQuestion: question
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling consultation request`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }
}
