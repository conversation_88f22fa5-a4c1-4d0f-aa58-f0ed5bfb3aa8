/**
 * Dynamic Collaboration V3 Artifact API
 *
 * This file implements the API endpoint for creating and managing artifacts.
 */

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { ArtifactStatus, MessageType } from '../state/unified-schema';

/**
 * POST /api/agents/dynamic-collaboration-v3/artifact
 * Create a new artifact
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await req.json();
    const { sessionId, goalId, type, title, content } = body;

    // Validate required fields
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required field: sessionId' },
        { status: 400 }
      );
    }

    if (!goalId) {
      return NextResponse.json(
        { error: 'Missing required field: goalId' },
        { status: 400 }
      );
    }

    if (!type) {
      return NextResponse.json(
        { error: 'Missing required field: type' },
        { status: 400 }
      );
    }

    // Get state manager
    const stateManager = new StateManager(sessionId);

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Check if goal exists
    if (!state.goals?.byId?.[goalId]) {
      return NextResponse.json(
        { error: 'Goal not found' },
        { status: 404 }
      );
    }

    // Create artifact ID
    const artifactId = uuidv4();
    const now = new Date().toISOString();

    // Create artifact
    const artifact = {
      id: artifactId,
      goalId,
      type,
      title: title || `${type} for ${state.topic}`,
      content: content || { text: `This is a placeholder ${type} for ${state.topic}` },
      status: ArtifactStatus.REVIEW,
      createdAt: now,
      updatedAt: now,
      createdBy: 'user',
      version: 1,
      metadata: {}
    };

    // Add artifact to state
    await stateManager.updateState(currentState => {
      if (!currentState) return currentState;

      // Add artifact
      const artifacts = { ...(currentState.artifacts || {}) };
      artifacts[artifactId] = artifact;

      // Update goal with artifact ID
      const goals = { ...currentState.goals };
      const goal = { ...goals.byId[goalId] };
      goal.artifactIds = [...(goal.artifactIds || []), artifactId];
      goal.progress = Math.min(goal.progress + 25, 75); // Increase progress but cap at 75%
      goals.byId = { ...goals.byId, [goalId]: goal };

      // Create message for artifact creation
      const messageId = uuidv4();
      const message = {
        id: messageId,
        timestamp: now,
        from: 'user',
        to: 'system',
        type: MessageType.ARTIFACT_UPDATE,
        content: { text: `Created artifact: ${title || type}` },
        conversationId: goalId,
        metadata: {
          goalId,
          artifactId
        }
      };

      // Add message
      const messages = { ...(currentState.messages || {}) };
      if (!messages.byId) {
        messages.byId = {};
        messages.allIds = [];
      }
      messages.byId = { ...messages.byId, [messageId]: message };
      messages.allIds = [...messages.allIds, messageId];

      return {
        ...currentState,
        artifacts,
        goals,
        messages,
        lastUpdated: now
      };
    });

    // Return success
    return NextResponse.json({
      success: true,
      artifactId,
      artifact
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error creating artifact`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/agents/dynamic-collaboration-v3/artifact
 * Get artifacts for a session
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Get session ID from query params
    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');
    const goalId = searchParams.get('goalId');

    // Validate required fields
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Get state manager
    const stateManager = new StateManager(sessionId);

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Get artifacts
    const artifacts = state.artifacts || {};

    // Filter by goal ID if provided
    const filteredArtifacts = goalId
      ? Object.values(artifacts).filter((artifact: any) => artifact.goalId === goalId)
      : Object.values(artifacts);

    // Return artifacts
    return NextResponse.json({
      success: true,
      artifacts: filteredArtifacts
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error getting artifacts`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
