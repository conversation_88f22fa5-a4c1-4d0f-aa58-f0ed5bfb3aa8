/**
 * Goal-Based Orchestrator
 *
 * This file implements a goal-based orchestrator for the dynamic collaboration system.
 * It coordinates goal creation, assignment, and completion without fixed phases.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import {
  GoalType,
  GoalStatus,
  MessageType,
  ContentGenerationParams,
  SessionStatus,
  Goal,
  WorkflowPhase,
  MilestoneStatus
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { StateNotFoundError, ConcurrencyError } from '../state/store';
import { ArtifactDecisionFramework, ArtifactDecisionType } from '../services/artifact-decision-framework';
import { ArtifactEvaluationService } from '../services/artifact-evaluation-service';
import { AgentRegistry } from '../agents/agent-registry';
import { enhancedMessageBus, EnhancedMessageBus } from '../../../agents/collaborative-iteration/utils/enhanced-message-bus';
import { ErrorRecoverySystem, ErrorSeverity, RetryConfig } from '../utils/error-recovery';
import { WorkflowProgressTracker } from '../utils/workflow-progress-tracker';
import { PerformanceOptimizer } from '../utils/performance-optimizer';

/**
 * Goal-Based Orchestrator
 *
 * This class orchestrates the dynamic collaboration workflow using a goal-based approach,
 * rather than fixed phases.
 */
export class GoalOrchestrator {
  private sessionId: string;
  private stateManager: StateManager;
  private decisionFramework: ArtifactDecisionFramework;
  private errorRecovery: ErrorRecoverySystem;
  private progressTracker: WorkflowProgressTracker;
  private performanceOptimizer: PerformanceOptimizer;
  private messageBus: any;

  /**
   * Default retry configuration
   */
  private static readonly DEFAULT_RETRY_CONFIG: RetryConfig = {
    maxRetries: 3,
    initialDelayMs: 1000,
    backoffFactor: 2,
    maxDelayMs: 30000
  };

  /**
   * Constructor
   * @param sessionId The session ID
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
    this.decisionFramework = new ArtifactDecisionFramework(sessionId);
    this.errorRecovery = new ErrorRecoverySystem(sessionId);
    this.progressTracker = new WorkflowProgressTracker(sessionId);
    this.performanceOptimizer = new PerformanceOptimizer(sessionId);
  }

  /**
   * Initialize a new collaboration session
   * @param sessionId Session ID
   * @param params Content generation parameters
   * @returns Promise<boolean> indicating success
   */
  public static async initiate(sessionId: string, params: ContentGenerationParams): Promise<boolean> {
    // Create a new orchestrator instance
    const orchestrator = new GoalOrchestrator(sessionId);

    try {
      logger.info(`Initializing goal-based collaboration session`, {
        sessionId,
        topic: params.topic
      });

      // Initialize the session with error recovery
      const success = await orchestrator.errorRecovery.executeWithRetry(
        async () => {
          // Initialize the session
          const initSuccess = await orchestrator.stateManager.initializeSession(params);
          if (!initSuccess) {
            throw new Error('Failed to initialize session');
          }
          return true;
        },
        'GoalOrchestrator',
        'initializeSession',
        { details: { topic: params.topic, contentType: params.contentType } }
      );

      // Define initial high-level goals with error recovery
      await orchestrator.errorRecovery.executeWithRetry(
        async () => orchestrator.defineInitialGoals(),
        'GoalOrchestrator',
        'defineInitialGoals',
        { details: { topic: params.topic } }
      );

      // Update workflow progress
      await orchestrator.progressTracker.updatePhaseProgress(WorkflowPhase.PLANNING, 25);

      // Trigger initial goal processing with error recovery
      await orchestrator.errorRecovery.executeWithRetry(
        async () => orchestrator.processGoals(),
        'GoalOrchestrator',
        'processGoals',
        { details: { initialProcessing: true } }
      );

      // Update workflow progress
      await orchestrator.progressTracker.updatePhaseProgress(WorkflowPhase.PLANNING, 50);

      return true;
    } catch (error) {
      const err = error as Error;

      // Record the error
      await orchestrator.errorRecovery.recordError(
        'GoalOrchestrator',
        'initiate',
        err,
        { details: { topic: params.topic, contentType: params.contentType } },
        ErrorSeverity.HIGH
      );

      logger.error(`Error initializing goal-based collaboration session`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });

      return false;
    }
  }

  /**
   * Define initial high-level goals
   * @returns Promise<boolean> indicating success
   */
  private async defineInitialGoals(): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Define research goal
      const researchGoal = {
        description: `Research ${state.topic} for ${state.contentType}`,
        type: GoalType.RESEARCH,
        dependencies: [],
        criteria: [
          'Gather comprehensive information about the topic',
          'Identify target audience needs and interests',
          'Analyze market trends and competition',
          'Identify key keywords and search terms'
        ]
      };

      // Define content creation goal
      const contentGoal = {
        description: `Create ${state.contentType} about ${state.topic}`,
        type: GoalType.CONTENT,
        dependencies: [],
        criteria: [
          'Develop a clear content strategy',
          'Create engaging and informative content',
          'Structure content appropriately for the format',
          'Address target audience needs and interests'
        ]
      };

      // Define quality goal
      const qualityGoal = {
        description: `Ensure quality of ${state.contentType} about ${state.topic}`,
        type: GoalType.QUALITY,
        dependencies: [],
        criteria: [
          'Optimize content for search engines',
          'Ensure content is error-free and well-written',
          'Verify content meets all requirements',
          'Ensure content is engaging and valuable to the audience'
        ]
      };

      // Define goals
      const goalIds = await this.stateManager.defineGoals([researchGoal, contentGoal, qualityGoal]);

      // Automatically activate the first goal (research)
      if (goalIds.length > 0) {
        logger.info(`Automatically activating initial goal: ${goalIds[0]}`);
        await this.stateManager.activateGoal(goalIds[0]);
      }

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error defining initial goals`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Process goals
   * @returns Promise<boolean> indicating success
   */
  public async processGoals(): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Get active goals
      const activeGoals = state.goals.activeIds.map(id => state.goals.byId[id]).filter(Boolean);

      // If no active goals, check if we need to create more specific goals
      if (activeGoals.length === 0) {
        logger.info(`No active goals found, checking if we need to create more specific goals`, {
          sessionId: this.sessionId
        });
        return await this.createMoreSpecificGoals();
      }

      logger.info(`Processing ${activeGoals.length} active goals`, {
        sessionId: this.sessionId,
        activeGoalIds: activeGoals.map(g => g.id),
        activeGoalTypes: activeGoals.map(g => g.type)
      });

      // Process each active goal
      for (const goal of activeGoals) {
        await this.processGoal(goal);
      }

      // Check if we need to activate the next goal
      const updatedState = await this.stateManager.getState();
      if (updatedState && updatedState.goals.activeIds.length === 0) {
        logger.info(`All active goals processed, checking for pending goals`, {
          sessionId: this.sessionId
        });

        // Try to create more specific goals or activate pending goals
        return await this.createMoreSpecificGoals();
      }

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing goals`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Process a specific goal
   * @param goal The goal to process
   * @returns Promise<boolean> indicating success
   */
  private async processGoal(goal: Goal): Promise<boolean> {
    try {
      logger.info(`Processing goal ${goal.id}`, {
        sessionId: this.sessionId,
        goalId: goal.id,
        goalType: goal.type
      });

      // Update goal progress
      await this.stateManager.updateGoalProgress(goal.id, 10);

      // Handle goal based on type with error recovery
      switch (goal.type) {
        case GoalType.RESEARCH:
          // Research is a high-level goal, break it down into more specific goals
          await this.errorRecovery.executeWithRetry(
            async () => this.breakdownResearchGoal(goal),
            'GoalOrchestrator',
            'breakdownResearchGoal',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.PLANNING, 75);
          break;

        case GoalType.CONTENT:
          // Content is a high-level goal, break it down into more specific goals
          await this.errorRecovery.executeWithRetry(
            async () => this.breakdownContentGoal(goal),
            'GoalOrchestrator',
            'breakdownContentGoal',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.PLANNING, 100);
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 25);
          break;

        case GoalType.QUALITY:
          // Quality is a high-level goal, break it down into more specific goals
          await this.errorRecovery.executeWithRetry(
            async () => this.breakdownQualityGoal(goal),
            'GoalOrchestrator',
            'breakdownQualityGoal',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 100);
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.REVIEW, 25);
          break;

        case GoalType.MARKET_RESEARCH:
          await this.errorRecovery.executeWithRetry(
            async () => this.triggerMarketResearchAgent(goal.id),
            'GoalOrchestrator',
            'triggerMarketResearchAgent',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 50);
          await this.progressTracker.updateMilestoneStatus('research-started', MilestoneStatus.COMPLETED);
          break;

        case GoalType.KEYWORD_ANALYSIS:
          await this.errorRecovery.executeWithRetry(
            async () => this.triggerKeywordAnalysisAgent(goal.id),
            'GoalOrchestrator',
            'triggerKeywordAnalysisAgent',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 100);
          await this.progressTracker.updateMilestoneStatus('research-completed', MilestoneStatus.COMPLETED);
          break;

        case GoalType.CONTENT_STRATEGY:
          await this.errorRecovery.executeWithRetry(
            async () => this.triggerContentStrategyAgent(goal.id),
            'GoalOrchestrator',
            'triggerContentStrategyAgent',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 25);
          await this.progressTracker.updateMilestoneStatus('outline-created', MilestoneStatus.COMPLETED);
          break;

        case GoalType.CONTENT_CREATION:
          await this.errorRecovery.executeWithRetry(
            async () => this.triggerContentCreationAgent(goal.id),
            'GoalOrchestrator',
            'triggerContentCreationAgent',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 75);
          await this.progressTracker.updateMilestoneStatus('draft-created', MilestoneStatus.COMPLETED);
          break;

        case GoalType.SEO_OPTIMIZATION:
          await this.errorRecovery.executeWithRetry(
            async () => this.triggerSeoOptimizationAgent(goal.id),
            'GoalOrchestrator',
            'triggerSeoOptimizationAgent',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.REVIEW, 50);
          await this.progressTracker.updateMilestoneStatus('seo-optimized', MilestoneStatus.COMPLETED);
          break;

        case GoalType.QUALITY_ASSESSMENT:
          await this.errorRecovery.executeWithRetry(
            async () => this.triggerQualityAssessmentAgent(goal.id),
            'GoalOrchestrator',
            'triggerQualityAssessmentAgent',
            { goalId: goal.id, goalType: goal.type }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.REVIEW, 100);
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.FINALIZATION, 50);
          await this.progressTracker.updateMilestoneStatus('final-review', MilestoneStatus.COMPLETED);
          break;
      }

      // Update goal progress to 100% when completed
      await this.stateManager.updateGoalProgress(goal.id, 100);

      // Update milestone from goal
      await this.progressTracker.updateMilestoneFromGoal(goal.id, goal.type);

      // Process goals again to ensure the workflow continues
      logger.info(`Processing goals again after completing goal: ${goal.id}`);
      await this.createMoreSpecificGoals();

      // Get updated state to check for pending goals
      const updatedState = await this.stateManager.getState();
      if (updatedState && updatedState.goals.activeIds.length === 0) {
        // If there are no active goals, try to process goals again
        logger.info(`No active goals after completing goal: ${goal.id}, processing goals again`);
        await this.processGoals();
      }

      return true;
    } catch (error) {
      const err = error as Error;

      // Record the error
      await this.errorRecovery.recordError(
        'GoalOrchestrator',
        'processGoal',
        err,
        { goalId: goal.id, goalType: goal.type },
        ErrorSeverity.MEDIUM
      );

      logger.error(`Error processing goal`, {
        sessionId: this.sessionId,
        goalId: goal.id,
        goalType: goal.type,
        error: err.message || String(error),
        stack: err.stack
      });

      return false;
    }
  }

  /**
   * Create more specific goals based on completed high-level goals
   * @returns Promise<boolean> indicating success
   */
  private async createMoreSpecificGoals(): Promise<boolean> {
    try {
      // Get current state with error recovery
      const state = await this.errorRecovery.executeWithRetry(
        async () => {
          const currentState = await this.stateManager.getState();
          if (!currentState) {
            throw new Error('Session state not found');
          }
          return currentState;
        },
        'GoalOrchestrator',
        'getState',
        { details: { operation: 'createMoreSpecificGoals' } }
      );

      // Get completed goals
      const completedGoals = state.goals.completedIds.map(id => state.goals.byId[id]).filter(Boolean);

      // Check if all high-level goals are completed
      const allHighLevelGoalsCompleted =
        this.isGoalTypeCompleted(completedGoals, GoalType.RESEARCH) &&
        this.isGoalTypeCompleted(completedGoals, GoalType.CONTENT) &&
        this.isGoalTypeCompleted(completedGoals, GoalType.QUALITY);

      if (allHighLevelGoalsCompleted) {
        // All goals are completed, complete the session
        await this.errorRecovery.executeWithRetry(
          async () => {
            await this.stateManager.completeSession();

            // Update workflow progress
            await this.progressTracker.updatePhaseProgress(WorkflowPhase.FINALIZATION, 100);
            await this.progressTracker.updateMilestoneStatus('article-completed', MilestoneStatus.COMPLETED);
            await this.progressTracker.updateArticleProgress('articleComplete', true);

            return true;
          },
          'GoalOrchestrator',
          'completeSession',
          { details: { allGoalsCompleted: true } }
        );

        logger.info(`All goals completed, session completed`, {
          sessionId: this.sessionId
        });

        return true;
      }

      // Check which high-level goals are completed and create more specific goals
      if (this.isGoalTypeCompleted(completedGoals, GoalType.RESEARCH) &&
          !this.isGoalTypeCompleted(completedGoals, GoalType.CONTENT)) {
        // Research is completed, but content is not, create content-specific goals
        const contentGoal = completedGoals.find(goal => goal.type === GoalType.CONTENT);
        if (contentGoal) {
          await this.errorRecovery.executeWithRetry(
            async () => this.breakdownContentGoal(contentGoal),
            'GoalOrchestrator',
            'breakdownContentGoal',
            { goalId: contentGoal.id, goalType: contentGoal.type, fromCreateMoreSpecificGoals: true }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 100);
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 25);
          await this.progressTracker.updateArticleProgress('researchComplete', true);

          return true;
        }
      }

      if (this.isGoalTypeCompleted(completedGoals, GoalType.CONTENT) &&
          !this.isGoalTypeCompleted(completedGoals, GoalType.QUALITY)) {
        // Content is completed, but quality is not, create quality-specific goals
        const qualityGoal = completedGoals.find(goal => goal.type === GoalType.QUALITY);
        if (qualityGoal) {
          await this.errorRecovery.executeWithRetry(
            async () => this.breakdownQualityGoal(qualityGoal),
            'GoalOrchestrator',
            'breakdownQualityGoal',
            { goalId: qualityGoal.id, goalType: qualityGoal.type, fromCreateMoreSpecificGoals: true }
          );

          // Update workflow progress
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 100);
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.REVIEW, 25);
          await this.progressTracker.updateArticleProgress('draftComplete', true);

          return true;
        }
      }

      // Check for pending goals that need to be activated
      const state2 = await this.stateManager.getState();
      if (state2) {
        // Update estimated time remaining based on progress
        const overallProgress = state2.workflowProgress.overallProgress;
        const elapsedTime = (new Date().getTime() - new Date(state2.startTime).getTime()) / 1000;
        const estimatedTotalTime = elapsedTime / (overallProgress / 100);
        const estimatedTimeRemaining = Math.max(0, estimatedTotalTime - elapsedTime);
        await this.progressTracker.updateEstimatedTimeRemaining(Math.round(estimatedTimeRemaining));

        // If there are no active goals, check for pending goals to activate
        if (state2.goals.activeIds.length === 0) {
          // Get all pending goals
          const pendingGoals = Object.entries(state2.goals.byId)
            .filter(([_, goal]) => goal.status === GoalStatus.PENDING)
            .map(([id, goal]) => ({ id, ...goal }));

          if (pendingGoals.length > 0) {
            // Sort pending goals by priority (market research > keyword analysis > content strategy > etc.)
            const priorityOrder = [
              GoalType.MARKET_RESEARCH,
              GoalType.KEYWORD_ANALYSIS,
              GoalType.CONTENT_STRATEGY,
              GoalType.CONTENT_CREATION,
              GoalType.SEO_OPTIMIZATION,
              GoalType.QUALITY_ASSESSMENT
            ];

            pendingGoals.sort((a, b) => {
              const aIndex = priorityOrder.indexOf(a.type);
              const bIndex = priorityOrder.indexOf(b.type);
              return aIndex - bIndex;
            });

            // Activate the highest priority pending goal with error recovery
            const goalToActivate = pendingGoals[0];
            logger.info(`Activating pending goal: ${goalToActivate.id}, type: ${goalToActivate.type}`);

            await this.errorRecovery.executeWithRetry(
              async () => this.stateManager.activateGoal(goalToActivate.id),
              'GoalOrchestrator',
              'activateGoal',
              { goalId: goalToActivate.id, details: { operation: 'createMoreSpecificGoals' } }
            );

            // Update workflow progress based on the activated goal
            await this.errorRecovery.executeWithRetry(
              async () => {
                switch (goalToActivate.type) {
                  case GoalType.MARKET_RESEARCH:
                    await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 25);
                    await this.progressTracker.updateMilestoneStatus('research-started', MilestoneStatus.IN_PROGRESS);
                    break;
                  case GoalType.KEYWORD_ANALYSIS:
                    await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 75);
                    break;
                  case GoalType.CONTENT_STRATEGY:
                    await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 25);
                    await this.progressTracker.updateMilestoneStatus('outline-created', MilestoneStatus.IN_PROGRESS);
                    break;
                  case GoalType.CONTENT_CREATION:
                    await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 75);
                    await this.progressTracker.updateMilestoneStatus('draft-created', MilestoneStatus.IN_PROGRESS);
                    break;
                  case GoalType.SEO_OPTIMIZATION:
                    await this.progressTracker.updatePhaseProgress(WorkflowPhase.REVIEW, 25);
                    await this.progressTracker.updateMilestoneStatus('seo-optimized', MilestoneStatus.IN_PROGRESS);
                    break;
                  case GoalType.QUALITY_ASSESSMENT:
                    await this.progressTracker.updatePhaseProgress(WorkflowPhase.REVIEW, 75);
                    await this.progressTracker.updateMilestoneStatus('final-review', MilestoneStatus.IN_PROGRESS);
                    break;
                }
              },
              'GoalOrchestrator',
              'updateProgressForActivatedGoal',
              { goalId: goalToActivate.id, goalType: goalToActivate.type, details: { operation: 'createMoreSpecificGoals' } }
            );

            // Process goals again to ensure the workflow continues
            logger.info(`Processing goals again after activating goal: ${goalToActivate.id}`);

            await this.errorRecovery.executeWithRetry(
              async () => this.processGoals(),
              'GoalOrchestrator',
              'processGoals',
              { goalId: goalToActivate.id, details: { operation: 'createMoreSpecificGoals', afterActivatingGoal: true } }
            );

            return true;
          }
        }
      }

      return false;
    } catch (error) {
      const err = error as Error;

      // Record the error
      await this.errorRecovery.recordError(
        'GoalOrchestrator',
        'createMoreSpecificGoals',
        err,
        { details: { operation: 'createMoreSpecificGoals' } },
        ErrorSeverity.MEDIUM
      );

      logger.error(`Error creating more specific goals`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });

      return false;
    }
  }

  /**
   * Check if a goal type is completed
   * @param completedGoals Array of completed goals
   * @param goalType The goal type to check
   * @returns True if the goal type is completed
   */
  private isGoalTypeCompleted(completedGoals: Goal[], goalType: GoalType): boolean {
    return completedGoals.some(goal => goal.type === goalType);
  }

  /**
   * Break down a research goal into more specific goals
   * @param goal The research goal
   * @returns Promise<boolean> indicating success
   */
  private async breakdownResearchGoal(goal: Goal): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Define market research goal
      const marketResearchGoal = {
        description: `Conduct market research for ${state.topic}`,
        type: GoalType.MARKET_RESEARCH,
        dependencies: [],
        criteria: [
          'Identify target audience demographics',
          'Analyze market trends',
          'Identify key competitors',
          'Determine market gaps and opportunities'
        ]
      };

      // Define keyword analysis goal
      const keywordAnalysisGoal = {
        description: `Analyze keywords for ${state.topic}`,
        type: GoalType.KEYWORD_ANALYSIS,
        dependencies: [],
        criteria: [
          'Identify primary keywords',
          'Identify secondary keywords',
          'Analyze keyword competition',
          'Determine keyword search volume'
        ]
      };

      // Define goals
      const goalIds = await this.stateManager.defineGoals([marketResearchGoal, keywordAnalysisGoal]);

      // Activate the first sub-goal (market research)
      if (goalIds.length > 0) {
        logger.info(`Activating market research goal: ${goalIds[0]}`);
        await this.stateManager.activateGoal(goalIds[0]);
      }

      // Complete the high-level research goal
      await this.stateManager.completeGoal(goal.id);

      // Update workflow progress
      await this.progressTracker.updatePhaseProgress(WorkflowPhase.PLANNING, 100);
      await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 25);
      await this.progressTracker.updateMilestoneStatus('research-started', MilestoneStatus.IN_PROGRESS);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error breaking down research goal`, {
        sessionId: this.sessionId,
        goalId: goal.id,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Break down a content goal into more specific goals
   * @param goal The content goal
   * @returns Promise<boolean> indicating success
   */
  private async breakdownContentGoal(goal: Goal): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Define content strategy goal
      const contentStrategyGoal = {
        description: `Develop content strategy for ${state.topic}`,
        type: GoalType.CONTENT_STRATEGY,
        dependencies: state.goals.completedIds,
        criteria: [
          'Define content structure',
          'Outline key sections',
          'Determine tone and style',
          'Identify key points to cover'
        ]
      };

      // Define content creation goal
      const contentCreationGoal = {
        description: `Create content for ${state.topic}`,
        type: GoalType.CONTENT_CREATION,
        dependencies: [],
        criteria: [
          'Write engaging introduction',
          'Develop comprehensive body content',
          'Create compelling conclusion',
          'Include relevant examples and data'
        ]
      };

      // Define goals
      const goalIds = await this.stateManager.defineGoals([contentStrategyGoal, contentCreationGoal]);

      // Activate the first sub-goal (content strategy)
      if (goalIds.length > 0) {
        logger.info(`Activating content strategy goal: ${goalIds[0]}`);
        await this.stateManager.activateGoal(goalIds[0]);
      }

      // Complete the high-level content goal
      await this.stateManager.completeGoal(goal.id);

      // Update workflow progress
      await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 100);
      await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 25);
      await this.progressTracker.updateMilestoneStatus('outline-created', MilestoneStatus.IN_PROGRESS);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error breaking down content goal`, {
        sessionId: this.sessionId,
        goalId: goal.id,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Break down a quality goal into more specific goals
   * @param goal The quality goal
   * @returns Promise<boolean> indicating success
   */
  private async breakdownQualityGoal(goal: Goal): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Define SEO optimization goal
      const seoOptimizationGoal = {
        description: `Optimize content for SEO`,
        type: GoalType.SEO_OPTIMIZATION,
        dependencies: state.goals.completedIds,
        criteria: [
          'Optimize title and headings',
          'Ensure proper keyword density',
          'Add meta description',
          'Improve readability'
        ]
      };

      // Define quality assessment goal
      const qualityAssessmentGoal = {
        description: `Assess content quality`,
        type: GoalType.QUALITY_ASSESSMENT,
        dependencies: [],
        criteria: [
          'Check grammar and spelling',
          'Ensure factual accuracy',
          'Verify content meets requirements',
          'Assess overall quality'
        ]
      };

      // Define goals
      const goalIds = await this.stateManager.defineGoals([seoOptimizationGoal, qualityAssessmentGoal]);

      // Activate the first sub-goal (SEO optimization)
      if (goalIds.length > 0) {
        logger.info(`Activating SEO optimization goal: ${goalIds[0]}`);
        await this.stateManager.activateGoal(goalIds[0]);
      }

      // Complete the high-level quality goal
      await this.stateManager.completeGoal(goal.id);

      // Update workflow progress
      await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 100);
      await this.progressTracker.updatePhaseProgress(WorkflowPhase.REVIEW, 25);
      await this.progressTracker.updateMilestoneStatus('seo-optimized', MilestoneStatus.IN_PROGRESS);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error breaking down quality goal`, {
        sessionId: this.sessionId,
        goalId: goal.id,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Trigger the market research agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerMarketResearchAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering market research agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Get current state with error recovery
      const state = await this.errorRecovery.executeWithRetry(
        async () => {
          const currentState = await this.stateManager.getState();
          if (!currentState) {
            throw new Error('Session state not found');
          }
          return currentState;
        },
        'GoalOrchestrator',
        'getState',
        { goalId, details: { operation: 'triggerMarketResearchAgent' } }
      );

      // Assign goal to market research agent
      await this.stateManager.assignGoal(goalId, 'market-research');

      // Import the OpenAI integration
      const { generateMarketResearch } = await import('../utils/content-generation');

      // Generate market research content using OpenAI with retry
      logger.info(`Generating market research content for goal: ${goalId}`, {
        sessionId: this.sessionId,
        topic: state.topic
      });

      let marketResearchContent;
      try {
        // Try to generate content with OpenAI with retry
        marketResearchContent = await this.errorRecovery.executeWithRetry(
          async () => generateMarketResearch(
            state.topic,
            state.contentType,
            state.targetAudience
          ),
          'GoalOrchestrator',
          'generateMarketResearch',
          { goalId, details: { operation: 'triggerMarketResearchAgent' } }
        );
      } catch (genError) {
        // If OpenAI fails, use fallback content
        logger.warn(`Using fallback market research content due to generation error: ${(genError as Error).message}`, {
          sessionId: this.sessionId,
          goalId
        });

        marketResearchContent = `# Market Research Report: ${state.topic}

## Target Audience Analysis
- ${state.targetAudience} shows interest in ${state.topic}
- Demographics include professionals and enthusiasts

## Market Trends
- Growing interest in ${state.topic} over the past year
- Shift towards mobile consumption of content

## Competitor Analysis
- Several established competitors in the space
- Gap exists for more ${state.targetAudience}-focused content

## Content Gap Analysis
- Limited in-depth technical content available
- Need for more beginner-friendly explanations

## Audience Pain Points
- Difficulty understanding complex aspects of ${state.topic}
- Lack of practical implementation guides

## Recommendations
- Create content that addresses specific pain points
- Focus on practical, actionable advice
- Use clear examples and case studies`;
      }

      // Create the artifact with the generated content with retry
      const artifactId = await this.errorRecovery.executeWithRetry(
        async () => this.stateManager.createArtifact(
          'market-research',
          'Market Research Report',
          marketResearchContent,
          'market-research',
          goalId
        ),
        'GoalOrchestrator',
        'createArtifact',
        { goalId, details: { operation: 'triggerMarketResearchAgent' } }
      );

      logger.info(`Created market research artifact: ${artifactId} for goal: ${goalId}`, {
        sessionId: this.sessionId
      });

      // Complete the goal with the artifact with retry
      await this.errorRecovery.executeWithRetry(
        async () => this.stateManager.completeGoal(goalId, artifactId),
        'GoalOrchestrator',
        'completeGoal',
        { goalId, details: { operation: 'triggerMarketResearchAgent' } }
      );

      // Update workflow progress with retry
      await this.errorRecovery.executeWithRetry(
        async () => {
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 50);
          await this.progressTracker.updateMilestoneStatus('research-started', MilestoneStatus.COMPLETED);
        },
        'GoalOrchestrator',
        'updateProgress',
        { goalId, details: { operation: 'triggerMarketResearchAgent' } }
      );

      // Activate the keyword analysis goal with retry
      const keywordGoals = Object.values(state.goals.byId).filter(
        g => g.type === GoalType.KEYWORD_ANALYSIS && g.status === GoalStatus.PENDING
      );

      if (keywordGoals.length > 0) {
        const keywordGoalId = keywordGoals[0].id;
        logger.info(`Activating keyword analysis goal: ${keywordGoalId}`);

        // Activate the keyword analysis goal with retry
        await this.errorRecovery.executeWithRetry(
          async () => this.stateManager.activateGoal(keywordGoalId),
          'GoalOrchestrator',
          'activateGoal',
          { goalId: keywordGoalId, details: { operation: 'triggerMarketResearchAgent' } }
        );

        // Process goals again to ensure the workflow continues
        logger.info(`Processing goals again after activating keyword analysis goal`);

        await this.errorRecovery.executeWithRetry(
          async () => this.processGoals(),
          'GoalOrchestrator',
          'processGoals',
          { goalId, details: { operation: 'triggerMarketResearchAgent', afterAgentTriggered: true } }
        );
      } else {
        // If no keyword goals found, create them
        logger.info(`No keyword analysis goals found, creating them`);

        // Define keyword analysis goal
        const keywordAnalysisGoal = {
          description: `Analyze keywords for ${state.topic}`,
          type: GoalType.KEYWORD_ANALYSIS,
          dependencies: [],
          criteria: [
            'Identify primary keywords',
            'Identify secondary keywords',
            'Analyze keyword competition',
            'Determine keyword search volume'
          ]
        };

        // Define the goal with retry
        const goalIds = await this.errorRecovery.executeWithRetry(
          async () => this.stateManager.defineGoals([keywordAnalysisGoal]),
          'GoalOrchestrator',
          'defineGoals',
          { details: { operation: 'triggerMarketResearchAgent' } }
        );

        if (goalIds.length > 0) {
          const keywordGoalId = goalIds[0];
          logger.info(`Created and activating keyword analysis goal: ${keywordGoalId}`);

          // Activate the keyword analysis goal with retry
          await this.errorRecovery.executeWithRetry(
            async () => this.stateManager.activateGoal(keywordGoalId),
            'GoalOrchestrator',
            'activateGoal',
            { goalId: keywordGoalId, details: { operation: 'triggerMarketResearchAgent' } }
          );

          // Process goals again to ensure the workflow continues
          logger.info(`Processing goals again after creating and activating keyword analysis goal`);

          await this.errorRecovery.executeWithRetry(
            async () => this.processGoals(),
            'GoalOrchestrator',
            'processGoals',
            { goalId: keywordGoalId, details: { operation: 'triggerMarketResearchAgent', afterCreatingGoals: true } }
          );
        }
      }

      return true;
    } catch (error) {
      const err = error as Error;

      // Record the error
      await this.errorRecovery.recordError(
        'GoalOrchestrator',
        'triggerMarketResearchAgent',
        err,
        { goalId, details: { operation: 'triggerMarketResearchAgent' } },
        ErrorSeverity.MEDIUM
      );

      logger.error(`Error triggering market research agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });

      // Even if there's an error, try to move the workflow forward
      try {
        const state = await this.stateManager.getState();
        if (state) {
          const keywordGoals = Object.values(state.goals.byId).filter(
            g => g.type === GoalType.KEYWORD_ANALYSIS && g.status === GoalStatus.PENDING
          );

          if (keywordGoals.length > 0) {
            const keywordGoalId = keywordGoals[0].id;
            logger.info(`Attempting to activate keyword analysis goal after error: ${keywordGoalId}`);

            // Activate the keyword analysis goal with retry
            await this.errorRecovery.executeWithRetry(
              async () => this.stateManager.activateGoal(keywordGoalId),
              'GoalOrchestrator',
              'activateGoal',
              { goalId: keywordGoalId, details: { operation: 'triggerMarketResearchAgent', errorRecovery: true } }
            );

            // Process goals again to ensure the workflow continues
            logger.info(`Processing goals again after activating keyword analysis goal in error recovery`);
            await this.errorRecovery.executeWithRetry(
              async () => this.processGoals(),
              'GoalOrchestrator',
              'processGoals',
              { goalId: keywordGoalId, details: { operation: 'triggerMarketResearchAgent', errorRecovery: true } }
            );
          } else {
            // If no keyword goals found, try to create them
            logger.info(`No keyword analysis goals found, creating them in error recovery`);

            // Define keyword analysis goal
            const keywordAnalysisGoal = {
              description: `Analyze keywords for ${state.topic}`,
              type: GoalType.KEYWORD_ANALYSIS,
              dependencies: [],
              criteria: [
                'Identify primary keywords',
                'Identify secondary keywords',
                'Analyze keyword competition',
                'Determine keyword search volume'
              ]
            };

            // Define the goal
            const goalIds = await this.stateManager.defineGoals([keywordAnalysisGoal]);

            if (goalIds.length > 0) {
              const keywordGoalId = goalIds[0];
              logger.info(`Created and activating keyword analysis goal in error recovery: ${keywordGoalId}`);

              // Activate the keyword analysis goal
              await this.stateManager.activateGoal(keywordGoalId);

              // Process goals again to ensure the workflow continues
              await this.processGoals();
            }
          }
        }
      } catch (recoveryError) {
        logger.error(`Failed to recover from market research agent error`, {
          sessionId: this.sessionId,
          goalId,
          error: (recoveryError as Error).message
        });
      }

      return false;
    }
  }

  /**
   * Trigger the keyword analysis agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerKeywordAnalysisAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering keyword analysis agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Get current state with error recovery
      const state = await this.errorRecovery.executeWithRetry(
        async () => {
          const currentState = await this.stateManager.getState();
          if (!currentState) {
            throw new Error('Session state not found');
          }
          return currentState;
        },
        'GoalOrchestrator',
        'getState',
        { goalId, details: { operation: 'triggerKeywordAnalysisAgent' } }
      );

      // Assign goal to keyword analysis agent
      await this.stateManager.assignGoal(goalId, 'keyword-analysis');

      // Import the OpenAI integration
      const { generateKeywordAnalysis } = await import('../utils/content-generation');

      // Generate keyword analysis content using OpenAI with retry
      logger.info(`Generating keyword analysis content for goal: ${goalId}`, {
        sessionId: this.sessionId,
        topic: state.topic
      });

      let keywordAnalysisResult;
      try {
        keywordAnalysisResult = await this.errorRecovery.executeWithRetry(
          async () => generateKeywordAnalysis(
            state.topic,
            state.contentType,
            state.targetAudience
          ),
          'GoalOrchestrator',
          'generateKeywordAnalysis',
          { goalId, details: { operation: 'triggerKeywordAnalysisAgent' } }
        );
      } catch (genError) {
        // If OpenAI fails, use fallback content
        logger.warn(`Using fallback keyword analysis content due to generation error: ${(genError as Error).message}`, {
          sessionId: this.sessionId,
          goalId
        });

        keywordAnalysisResult = {
          content: `# Keyword Analysis Report: ${state.topic}

## Primary Keywords
- ${state.topic}
- ${state.topic} guide
- ${state.topic} tutorial

## Secondary Keywords
- best ${state.topic} practices
- ${state.topic} for ${state.targetAudience}
- how to use ${state.topic}

## Long-tail Keywords
- how to implement ${state.topic} for beginners
- ${state.topic} advanced techniques
- ${state.topic} case studies

## Keyword Competition Analysis
- High competition for primary keywords
- Medium competition for secondary keywords
- Low competition for long-tail keywords

## Search Volume Analysis
- Primary keywords: High search volume
- Secondary keywords: Medium search volume
- Long-tail keywords: Low search volume but high conversion potential

## Recommendations
- Focus on long-tail keywords for better conversion
- Use primary keywords in titles and headings
- Incorporate secondary keywords throughout the content`,
          keywords: [
            state.topic,
            `${state.topic} guide`,
            `${state.topic} tutorial`,
            `best ${state.topic} practices`,
            `${state.topic} for ${state.targetAudience}`,
            `how to use ${state.topic}`
          ]
        };
      }

      // Create the artifact with the generated content with retry
      const artifactContent = {
        text: keywordAnalysisResult.content,
        keywords: keywordAnalysisResult.keywords
      };

      const artifactId = await this.errorRecovery.executeWithRetry(
        async () => this.stateManager.createArtifact(
          'keyword-analysis',
          'Keyword Analysis Report',
          artifactContent,
          'keyword-analysis',
          goalId
        ),
        'GoalOrchestrator',
        'createArtifact',
        { goalId, details: { operation: 'triggerKeywordAnalysisAgent' } }
      );

      logger.info(`Created keyword analysis artifact: ${artifactId} for goal: ${goalId}`, {
        sessionId: this.sessionId
      });

      // Complete the goal with the artifact with retry
      await this.errorRecovery.executeWithRetry(
        async () => this.stateManager.completeGoal(goalId, artifactId),
        'GoalOrchestrator',
        'completeGoal',
        { goalId, details: { operation: 'triggerKeywordAnalysisAgent' } }
      );

      // Update workflow progress with retry
      await this.errorRecovery.executeWithRetry(
        async () => {
          await this.progressTracker.updatePhaseProgress(WorkflowPhase.RESEARCH, 100);
          await this.progressTracker.updateMilestoneStatus('research-completed', MilestoneStatus.COMPLETED);
        },
        'GoalOrchestrator',
        'updateProgress',
        { goalId, details: { operation: 'triggerKeywordAnalysisAgent' } }
      );

      // Check if there's a content goal to activate with retry
      const contentGoals = Object.values(state.goals.byId).filter(
        g => g.type === GoalType.CONTENT && g.status === GoalStatus.PENDING
      );

      if (contentGoals.length > 0) {
        const contentGoalId = contentGoals[0].id;
        logger.info(`Activating content goal: ${contentGoalId}`);

        await this.errorRecovery.executeWithRetry(
          async () => this.stateManager.activateGoal(contentGoalId),
          'GoalOrchestrator',
          'activateGoal',
          { goalId: contentGoalId, details: { operation: 'triggerKeywordAnalysisAgent' } }
        );

        // Process goals again to ensure the workflow continues
        logger.info(`Processing goals again after activating content goal`);

        await this.errorRecovery.executeWithRetry(
          async () => this.processGoals(),
          'GoalOrchestrator',
          'processGoals',
          { goalId, details: { operation: 'triggerKeywordAnalysisAgent', afterAgentTriggered: true } }
        );
      } else {
        // If no content goal found, try to create more specific goals
        logger.info(`No content goal found, creating more specific goals`);

        await this.errorRecovery.executeWithRetry(
          async () => this.createMoreSpecificGoals(),
          'GoalOrchestrator',
          'createMoreSpecificGoals',
          { goalId, details: { operation: 'triggerKeywordAnalysisAgent' } }
        );

        // Process goals again to ensure the workflow continues
        logger.info(`Processing goals again after creating more specific goals`);

        await this.errorRecovery.executeWithRetry(
          async () => this.processGoals(),
          'GoalOrchestrator',
          'processGoals',
          { goalId, details: { operation: 'triggerKeywordAnalysisAgent', afterCreatingGoals: true } }
        );
      }

      return true;
    } catch (error) {
      const err = error as Error;

      // Record the error
      await this.errorRecovery.recordError(
        'GoalOrchestrator',
        'triggerKeywordAnalysisAgent',
        err,
        { goalId, details: { operation: 'triggerKeywordAnalysisAgent' } },
        ErrorSeverity.MEDIUM
      );

      logger.error(`Error triggering keyword analysis agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });

      // Even if there's an error, try to move the workflow forward
      try {
        const state = await this.stateManager.getState();
        if (state) {
          // Try to activate content goal
          const contentGoals = Object.values(state.goals.byId).filter(
            g => g.type === GoalType.CONTENT && g.status === GoalStatus.PENDING
          );

          if (contentGoals.length > 0) {
            const contentGoalId = contentGoals[0].id;
            logger.info(`Attempting to activate content goal after error: ${contentGoalId}`);
            await this.stateManager.activateGoal(contentGoalId);

            // Process goals again
            await this.processGoals();
          } else {
            // Try to create more specific goals
            logger.info(`No content goal found after error, creating more specific goals`);
            await this.createMoreSpecificGoals();
            await this.processGoals();
          }
        }
      } catch (recoveryError) {
        logger.error(`Failed to recover from keyword analysis agent error`, {
          sessionId: this.sessionId,
          goalId,
          error: (recoveryError as Error).message
        });
      }

      return false;
    }
  }

  /**
   * Trigger the content strategy agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerContentStrategyAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering content strategy agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'content-strategy');

      // Import the content strategy agent
      const { ContentStrategyAgent } = await import('../agents/content-strategy-agent');

      // Create an instance of the content strategy agent
      const contentStrategyAgent = new ContentStrategyAgent(this.sessionId);

      // Process the goal
      const success = await contentStrategyAgent.processGoal(goalId);

      if (!success) {
        throw new Error('Content strategy agent failed to process goal');
      }

      // Process goals again to check for new goals
      await this.processGoals();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error triggering content strategy agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Trigger the content creation agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerContentCreationAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering content creation agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Get current state with error recovery
      const state = await this.errorRecovery.executeWithRetry(
        async () => {
          const currentState = await this.stateManager.getState();
          if (!currentState) {
            throw new Error('Session state not found');
          }
          return currentState;
        },
        'GoalOrchestrator',
        'getState',
        { goalId, details: { operation: 'triggerContentCreationAgent' } }
      );

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'content-creation');

      // Import the content creation agent
      const { ContentCreationAgent } = await import('../agents/content-creation-agent');

      // Create an instance of the content creation agent
      const contentCreationAgent = new ContentCreationAgent(this.sessionId);

      // Process the goal with error recovery
      const success = await this.errorRecovery.executeWithRetry(
        async () => contentCreationAgent.processGoal(goalId),
        'GoalOrchestrator',
        'processGoal',
        { goalId, details: { operation: 'triggerContentCreationAgent' } }
      );

      if (!success) {
        throw new Error('Content creation agent failed to process goal');
      }

      // Create a draft article artifact using the performance optimizer
      const contentStrategyArtifacts = await this.performanceOptimizer.getArtifactsByType('content-strategy');
      if (contentStrategyArtifacts.length > 0) {
        const contentStrategy = contentStrategyArtifacts[0];

        // Create a draft article based on the content strategy
        await this.performanceOptimizer.addArtifact({
          type: 'draft-article',
          title: `Draft Article: ${state.topic}`,
          content: {
            title: `${state.topic}`,
            outline: contentStrategy.content.outline || [],
            introduction: "This is a draft introduction based on the content strategy.",
            body: "This is the main content of the article, which will be expanded based on the content strategy.",
            conclusion: "This is a draft conclusion that summarizes the key points.",
            status: "draft"
          },
          createdBy: 'content-creation',
          status: 'draft',
          version: 1,
          goalId
        });

        // Update workflow progress
        await this.progressTracker.updatePhaseProgress(WorkflowPhase.CREATION, 50);
      }

      // Process goals again to check for new goals with error recovery
      await this.errorRecovery.executeWithRetry(
        async () => this.processGoals(),
        'GoalOrchestrator',
        'processGoals',
        { goalId, details: { operation: 'triggerContentCreationAgent', afterAgentTriggered: true } }
      );

      return true;
    } catch (error) {
      const err = error as Error;

      // Record the error
      await this.errorRecovery.recordError(
        'GoalOrchestrator',
        'triggerContentCreationAgent',
        err,
        { goalId, details: { operation: 'triggerContentCreationAgent' } },
        ErrorSeverity.MEDIUM
      );

      logger.error(`Error triggering content creation agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });

      return false;
    }
  }

  /**
   * Trigger the SEO optimization agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerSeoOptimizationAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering SEO optimization agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Get current state with error recovery
      const state = await this.errorRecovery.executeWithRetry(
        async () => {
          const currentState = await this.stateManager.getState();
          if (!currentState) {
            throw new Error('Session state not found');
          }
          return currentState;
        },
        'GoalOrchestrator',
        'getState',
        { goalId, details: { operation: 'triggerSeoOptimizationAgent' } }
      );

      // Assign goal to agent with error recovery
      await this.errorRecovery.executeWithRetry(
        async () => this.stateManager.assignGoal(goalId, 'seo-optimization'),
        'GoalOrchestrator',
        'assignGoal',
        { goalId, details: { operation: 'triggerSeoOptimizationAgent' } }
      );

      // Import the SEO optimization agent
      const { SEOOptimizationAgent } = await import('../agents/seo-optimization-agent');

      // Create an instance of the SEO optimization agent
      const seoOptimizationAgent = new SEOOptimizationAgent(this.sessionId);

      // Process the goal with error recovery
      const success = await this.errorRecovery.executeWithRetry(
        async () => seoOptimizationAgent.processGoal(goalId),
        'GoalOrchestrator',
        'processGoal',
        { goalId, details: { operation: 'triggerSeoOptimizationAgent' } }
      );

      if (!success) {
        throw new Error('SEO optimization agent failed to process goal');
      }

      // Process goals again to check for new goals with error recovery
      await this.errorRecovery.executeWithRetry(
        async () => this.processGoals(),
        'GoalOrchestrator',
        'processGoals',
        { goalId, details: { operation: 'triggerSeoOptimizationAgent', afterAgentTriggered: true } }
      );

      return true;
    } catch (error) {
      const err = error as Error;

      // Record the error
      await this.errorRecovery.recordError(
        'GoalOrchestrator',
        'triggerSeoOptimizationAgent',
        err,
        { goalId, details: { operation: 'triggerSeoOptimizationAgent' } },
        ErrorSeverity.MEDIUM
      );

      logger.error(`Error triggering SEO optimization agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });

      // Even if there's an error, try to move the workflow forward
      try {
        const state = await this.stateManager.getState();
        if (state) {
          // Try to activate quality assessment goal
          const qualityGoals = Object.values(state.goals.byId).filter(
            g => g.type === GoalType.QUALITY_ASSESSMENT && g.status === GoalStatus.PENDING
          );

          if (qualityGoals.length > 0) {
            const qualityGoalId = qualityGoals[0].id;
            logger.info(`Attempting to activate quality assessment goal after error: ${qualityGoalId}`);
            await this.stateManager.activateGoal(qualityGoalId);

            // Process goals again
            await this.processGoals();
          } else {
            // Try to create more specific goals
            logger.info(`No quality assessment goal found after error, creating more specific goals`);
            await this.createMoreSpecificGoals();
            await this.processGoals();
          }
        }
      } catch (recoveryError) {
        logger.error(`Failed to recover from SEO optimization agent error`, {
          sessionId: this.sessionId,
          goalId,
          error: (recoveryError as Error).message
        });
      }

      return false;
    }
  }

  /**
   * Trigger the quality assessment agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerQualityAssessmentAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering quality assessment agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'quality-assessment');

      // Import the quality assessment agent
      const { QualityAssessmentAgent } = await import('../agents/quality-assessment-agent');

      // Create an instance of the quality assessment agent
      const qualityAssessmentAgent = new QualityAssessmentAgent(this.sessionId);

      // Process the goal
      const success = await qualityAssessmentAgent.processGoal(goalId);

      if (!success) {
        throw new Error('Quality assessment agent failed to process goal');
      }

      // Process goals again to check for new goals
      await this.processGoals();

      // Check if this is the final goal
      const updatedState = await this.stateManager.getState();
      if (updatedState && updatedState.goals.activeIds.length === 0) {
        await this.stateManager.completeSession();
      }

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error triggering quality assessment agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Get the current state of the session
   * @returns Promise<any> The session state
   */
  public async getState(): Promise<any> {
    try {
      return await this.stateManager.getState();
    } catch (error) {
      const err = error as Error;
      logger.error(`Error getting session state`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return null;
    }
  }
}
