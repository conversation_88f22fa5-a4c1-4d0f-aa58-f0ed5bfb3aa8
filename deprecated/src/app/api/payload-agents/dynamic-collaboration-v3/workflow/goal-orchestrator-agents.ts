/**
 * Goal Orchestrator Agent Methods
 *
 * This file contains the agent methods for the goal orchestrator.
 * These methods are separated from the main orchestrator file for better organization.
 */

import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { ArtifactDecisionFramework, ArtifactDecisionType } from '../services/artifact-decision-framework';
import { FeedbackLoopSystem } from '../utils/feedback-loop-system-new';
import {
  CollaborationState,
  GoalType,
  GoalStatus,
  MilestoneStatus,
  WorkflowPhase
} from '../state/unified-schema';

/**
 * Trigger the content strategy agent
 * @param sessionId Session ID
 * @param goalId Goal ID
 * @param stateManager State manager
 * @param decisionFramework Artifact decision framework
 * @returns Promise<boolean> indicating success
 */
export async function triggerContentStrategyAgent(
  sessionId: string,
  goalId: string,
  stateManager: StateManager,
  decisionFramework: ArtifactDecisionFramework
): Promise<boolean> {
  try {
    logger.info(`Triggering content strategy agent`, {
      sessionId,
      goalId
    });

    // Assign goal to agent
    await stateManager.assignGoal(goalId, 'content-strategy');

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Get the goal
    const goal = state.goals.byId[goalId];
    if (!goal) {
      throw new Error(`Goal ${goalId} not found`);
    }

    // Make a decision about artifact creation
    const decision = await decisionFramework.decideArtifactCreation('content-strategy', goalId);

    // If we should use an existing artifact
    if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
      // Complete the goal with the existing artifact
      await stateManager.completeGoal(goalId, decision.existingArtifactId);
      return true;
    }

    // Find market research and keyword analysis artifacts
    const marketResearchArtifacts = Object.values(state.artifacts).filter(
      artifact => artifact.type === 'market-research'
    );

    const keywordAnalysisArtifacts = Object.values(state.artifacts).filter(
      artifact => artifact.type === 'keyword-analysis'
    );

    if (marketResearchArtifacts.length === 0) {
      throw new Error('Market research artifact not found');
    }

    if (keywordAnalysisArtifacts.length === 0) {
      throw new Error('Keyword analysis artifact not found');
    }

    // Get the most recent artifacts
    const marketResearchArtifact = marketResearchArtifacts.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

    const keywordAnalysisArtifact = keywordAnalysisArtifacts.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

    // If we should improve an existing artifact
    if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
      // Get the existing artifact
      const existingArtifact = state.artifacts[decision.existingArtifactId];
      if (!existingArtifact) {
        throw new Error(`Artifact ${decision.existingArtifactId} not found`);
      }

      // Import the OpenAI integration
      const { generateContentStrategy } = await import('../utils/content-generation');

      // Generate improved content strategy using OpenAI
      const contentStrategyContent = await generateContentStrategy(
        state.topic,
        state.contentType,
        state.targetAudience,
        state.tone,
        marketResearchArtifact,
        keywordAnalysisArtifact
      );

      // Create the improved artifact
      const artifactId = await stateManager.createArtifact(
        'content-strategy',
        'Content Strategy Plan (Improved)',
        contentStrategyContent,
        'content-strategy',
        goalId,
        decision.existingArtifactId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(sessionId);

      // Generate feedback on the content strategy
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'content-creation',
        ['content structure', 'topic coverage', 'audience targeting']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'content-strategy',
        'content-creation',
        artifactId,
        ['content structure', 'topic coverage', 'audience targeting']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'content-creation',
        'content-strategy',
        requestId,
        feedback
      );

      // Complete the goal
      await stateManager.completeGoal(goalId, artifactId);
      return true;
    }

    // Otherwise, create a new artifact
    // Import the OpenAI integration
    const { generateContentStrategy } = await import('../utils/content-generation');

    // Generate content strategy using OpenAI
    const contentStrategyContent = await generateContentStrategy(
      state.topic,
      state.contentType,
      state.targetAudience,
      state.tone,
      marketResearchArtifact,
      keywordAnalysisArtifact
    );

    // Create the artifact with the generated content
    const artifactId = await stateManager.createArtifact(
      'content-strategy',
      'Content Strategy Plan',
      contentStrategyContent,
      'content-strategy',
      goalId
    );

    // Initialize feedback loop system
    const feedbackSystem = new FeedbackLoopSystem(sessionId);

    // Generate feedback on the content strategy
    const feedback = await feedbackSystem.generateFeedback(
      artifactId,
      'content-creation',
      ['content structure', 'topic coverage', 'audience targeting']
    );

    // Create a feedback request
    const requestId = await feedbackSystem.requestFeedback(
      'content-strategy',
      'content-creation',
      artifactId,
      ['content structure', 'topic coverage', 'audience targeting']
    );

    // Provide feedback
    await feedbackSystem.provideFeedback(
      'content-creation',
      'content-strategy',
      requestId,
      feedback
    );

    // Complete the goal
    await stateManager.completeGoal(goalId, artifactId);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error triggering content strategy agent`, {
      sessionId,
      goalId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Trigger the content creation agent
 * @param sessionId Session ID
 * @param goalId Goal ID
 * @param stateManager State manager
 * @param decisionFramework Artifact decision framework
 * @returns Promise<boolean> indicating success
 */
export async function triggerContentCreationAgent(
  sessionId: string,
  goalId: string,
  stateManager: StateManager,
  decisionFramework: ArtifactDecisionFramework
): Promise<boolean> {
  try {
    logger.info(`Triggering content creation agent`, {
      sessionId,
      goalId
    });

    // Assign goal to agent
    await stateManager.assignGoal(goalId, 'content-creation');

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Get the goal
    const goal = state.goals.byId[goalId];
    if (!goal) {
      throw new Error(`Goal ${goalId} not found`);
    }

    // Make a decision about artifact creation
    const decision = await decisionFramework.decideArtifactCreation('content-creation', goalId);

    // If we should use an existing artifact
    if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
      // Complete the goal with the existing artifact
      await stateManager.completeGoal(goalId, decision.existingArtifactId);
      return true;
    }

    // Find content strategy and keyword analysis artifacts
    const contentStrategyArtifacts = Object.values(state.artifacts).filter(
      artifact => artifact.type === 'content-strategy'
    );

    const keywordAnalysisArtifacts = Object.values(state.artifacts).filter(
      artifact => artifact.type === 'keyword-analysis'
    );

    if (contentStrategyArtifacts.length === 0) {
      throw new Error('Content strategy artifact not found');
    }

    if (keywordAnalysisArtifacts.length === 0) {
      throw new Error('Keyword analysis artifact not found');
    }

    // Get the most recent artifacts
    const contentStrategyArtifact = contentStrategyArtifacts.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

    const keywordAnalysisArtifact = keywordAnalysisArtifacts.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

    // If we should improve an existing artifact
    if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
      // Get the existing artifact
      const existingArtifact = state.artifacts[decision.existingArtifactId];
      if (!existingArtifact) {
        throw new Error(`Artifact ${decision.existingArtifactId} not found`);
      }

      // Import the OpenAI integration
      const { generateArticleContent } = await import('../utils/content-generation');

      // Generate improved article content using OpenAI
      const { title, content } = await generateArticleContent(
        state.topic,
        state.contentType,
        state.targetAudience,
        state.tone,
        contentStrategyArtifact,
        keywordAnalysisArtifact
      );

      // Create the improved artifact
      const artifactId = await stateManager.createArtifact(
        'content-creation',
        `${title} (Improved)`,
        content,
        'content-creation',
        goalId,
        decision.existingArtifactId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(sessionId);

      // Generate feedback on the content
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'seo-optimization',
        ['content quality', 'readability', 'engagement']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'content-creation',
        'seo-optimization',
        artifactId,
        ['content quality', 'readability', 'engagement']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'seo-optimization',
        'content-creation',
        requestId,
        feedback
      );

      // Complete the goal
      await stateManager.completeGoal(goalId, artifactId);
      return true;
    }

    // Otherwise, create a new artifact
    // Import the OpenAI integration
    const { generateArticleContent } = await import('../utils/content-generation');

    // Generate article content using OpenAI
    const { title, content } = await generateArticleContent(
      state.topic,
      state.contentType,
      state.targetAudience,
      state.tone,
      contentStrategyArtifact,
      keywordAnalysisArtifact
    );

    // Create the artifact with the generated content
    const artifactId = await stateManager.createArtifact(
      'content-creation',
      title,
      content,
      'content-creation',
      goalId
    );

    // Initialize feedback loop system
    const feedbackSystem = new FeedbackLoopSystem(sessionId);

    // Generate feedback on the content
    const feedback = await feedbackSystem.generateFeedback(
      artifactId,
      'seo-optimization',
      ['content quality', 'readability', 'engagement']
    );

    // Create a feedback request
    const requestId = await feedbackSystem.requestFeedback(
      'content-creation',
      'seo-optimization',
      artifactId,
      ['content quality', 'readability', 'engagement']
    );

    // Provide feedback
    await feedbackSystem.provideFeedback(
      'seo-optimization',
      'content-creation',
      requestId,
      feedback
    );

    // Complete the goal
    await stateManager.completeGoal(goalId, artifactId);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error triggering content creation agent`, {
      sessionId,
      goalId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Trigger the SEO optimization agent
 * @param sessionId Session ID
 * @param goalId Goal ID
 * @param stateManager State manager
 * @param decisionFramework Artifact decision framework
 * @returns Promise<boolean> indicating success
 */
export async function triggerSeoOptimizationAgent(
  sessionId: string,
  goalId: string,
  stateManager: StateManager,
  decisionFramework: ArtifactDecisionFramework
): Promise<boolean> {
  try {
    logger.info(`Triggering SEO optimization agent`, {
      sessionId,
      goalId
    });

    // Assign goal to agent
    await stateManager.assignGoal(goalId, 'seo-optimization');

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Get the goal
    const goal = state.goals.byId[goalId];
    if (!goal) {
      throw new Error(`Goal ${goalId} not found`);
    }

    // Make a decision about artifact creation
    const decision = await decisionFramework.decideArtifactCreation('seo-optimization', goalId);

    // If we should use an existing artifact
    if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
      // Complete the goal with the existing artifact
      await stateManager.completeGoal(goalId, decision.existingArtifactId);
      return true;
    }

    // Find content creation artifact
    const contentCreationArtifacts = Object.values(state.artifacts).filter(
      artifact => artifact.type === 'content-creation'
    );

    if (contentCreationArtifacts.length === 0) {
      throw new Error('Content creation artifact not found');
    }

    // Get the most recent artifact
    const contentCreationArtifact = contentCreationArtifacts.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

    // If we should improve an existing artifact
    if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
      // Get the existing artifact
      const existingArtifact = state.artifacts[decision.existingArtifactId];
      if (!existingArtifact) {
        throw new Error(`Artifact ${decision.existingArtifactId} not found`);
      }

      // Import the OpenAI integration
      const { optimizeContentForSEO } = await import('../utils/content-generation');

      // Get the title and content from the content creation artifact
      let title = '';
      let content = '';

      if (typeof contentCreationArtifact.content === 'string') {
        // If content is a string, extract title from the first line (assuming markdown)
        const lines = contentCreationArtifact.content.split('\n');
        if (lines[0].startsWith('# ')) {
          title = lines[0].substring(2);
          content = contentCreationArtifact.content;
        } else {
          title = contentCreationArtifact.title;
          content = contentCreationArtifact.content;
        }
      } else if (typeof contentCreationArtifact.content === 'object') {
        // If content is an object, extract title and content fields
        title = contentCreationArtifact.content.title || contentCreationArtifact.title;
        content = contentCreationArtifact.content.content || JSON.stringify(contentCreationArtifact.content);
      } else {
        title = contentCreationArtifact.title;
        content = JSON.stringify(contentCreationArtifact.content);
      }

      // Optimize content for SEO using OpenAI
      const { title: optimizedTitle, content: optimizedContent } = await optimizeContentForSEO(
        title,
        content,
        state.keywords
      );

      // Create the improved artifact
      const artifactId = await stateManager.createArtifact(
        'seo-optimization',
        `${optimizedTitle} (Improved)`,
        optimizedContent,
        'seo-optimization',
        goalId,
        decision.existingArtifactId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(sessionId);

      // Generate feedback on the SEO optimization
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'quality-assessment',
        ['seo effectiveness', 'keyword usage', 'readability']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'seo-optimization',
        'quality-assessment',
        artifactId,
        ['seo effectiveness', 'keyword usage', 'readability']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'quality-assessment',
        'seo-optimization',
        requestId,
        feedback
      );

      // Complete the goal
      await stateManager.completeGoal(goalId, artifactId);
      return true;
    }

    // Otherwise, create a new artifact
    // Import the OpenAI integration
    const { optimizeContentForSEO } = await import('../utils/content-generation');

    // Get the title and content from the content creation artifact
    let title = '';
    let content = '';

    if (typeof contentCreationArtifact.content === 'string') {
      // If content is a string, extract title from the first line (assuming markdown)
      const lines = contentCreationArtifact.content.split('\n');
      if (lines[0].startsWith('# ')) {
        title = lines[0].substring(2);
        content = contentCreationArtifact.content;
      } else {
        title = contentCreationArtifact.title;
        content = contentCreationArtifact.content;
      }
    } else if (typeof contentCreationArtifact.content === 'object') {
      // If content is an object, extract title and content fields
      title = contentCreationArtifact.content.title || contentCreationArtifact.title;
      content = contentCreationArtifact.content.content || JSON.stringify(contentCreationArtifact.content);
    } else {
      title = contentCreationArtifact.title;
      content = JSON.stringify(contentCreationArtifact.content);
    }

    // Optimize content for SEO using OpenAI
    const { title: optimizedTitle, content: optimizedContent } = await optimizeContentForSEO(
      title,
      content,
      state.keywords
    );

    // Create the artifact with the optimized content
    const artifactId = await stateManager.createArtifact(
      'seo-optimization',
      optimizedTitle,
      optimizedContent,
      'seo-optimization',
      goalId
    );

    // Initialize feedback loop system
    const feedbackSystem = new FeedbackLoopSystem(sessionId);

    // Generate feedback on the SEO optimization
    const feedback = await feedbackSystem.generateFeedback(
      artifactId,
      'quality-assessment',
      ['seo effectiveness', 'keyword usage', 'readability']
    );

    // Create a feedback request
    const requestId = await feedbackSystem.requestFeedback(
      'seo-optimization',
      'quality-assessment',
      artifactId,
      ['seo effectiveness', 'keyword usage', 'readability']
    );

    // Provide feedback
    await feedbackSystem.provideFeedback(
      'quality-assessment',
      'seo-optimization',
      requestId,
      feedback
    );

    // Complete the goal
    await stateManager.completeGoal(goalId, artifactId);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error triggering SEO optimization agent`, {
      sessionId,
      goalId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Trigger the quality assessment agent
 * @param sessionId Session ID
 * @param goalId Goal ID
 * @param stateManager State manager
 * @param decisionFramework Artifact decision framework
 * @returns Promise<boolean> indicating success
 */
export async function triggerQualityAssessmentAgent(
  sessionId: string,
  goalId: string,
  stateManager: StateManager,
  decisionFramework: ArtifactDecisionFramework
): Promise<boolean> {
  try {
    logger.info(`Triggering quality assessment agent`, {
      sessionId,
      goalId
    });

    // Assign goal to agent
    await stateManager.assignGoal(goalId, 'quality-assessment');

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Get the goal
    const goal = state.goals.byId[goalId];
    if (!goal) {
      throw new Error(`Goal ${goalId} not found`);
    }

    // Make a decision about artifact creation
    const decision = await decisionFramework.decideArtifactCreation('quality-assessment', goalId);

    // If we should use an existing artifact
    if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
      // Complete the goal with the existing artifact
      await stateManager.completeGoal(goalId, decision.existingArtifactId);
      return true;
    }

    // Find SEO optimization artifact
    const seoOptimizationArtifacts = Object.values(state.artifacts).filter(
      artifact => artifact.type === 'seo-optimization'
    );

    if (seoOptimizationArtifacts.length === 0) {
      throw new Error('SEO optimization artifact not found');
    }

    // Get the most recent artifact
    const seoOptimizationArtifact = seoOptimizationArtifacts.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

    // Create the feedback loop system
    const { FeedbackLoopSystem } = await import('../utils/feedback-loop-system');
    const feedbackSystem = new FeedbackLoopSystem(sessionId);

    // Generate feedback on the SEO-optimized content
    const feedback = await feedbackSystem.generateFeedback(
      seoOptimizationArtifact.id,
      'quality-assessment'
    );

    // Create a quality assessment artifact
    const artifactId = await stateManager.createArtifact(
      'quality-assessment',
      'Quality Assessment Report',
      {
        contentQuality: {
          accuracy: feedback.overallRating,
          relevance: feedback.overallRating - 5,
          comprehensiveness: feedback.overallRating - 10,
          clarity: feedback.overallRating - 3,
          engagement: feedback.overallRating - 7,
          overall: feedback.overallRating
        },
        strengths: feedback.strengths,
        areasForImprovement: feedback.areasForImprovement,
        specificFeedback: feedback.specificFeedback,
        summary: feedback.summary,
        finalVerdict: feedback.overallRating >= 80
          ? 'Approved for publication with minor improvements'
          : 'Needs significant improvements before publication'
      },
      'quality-assessment',
      goalId
    );

    // If the quality is good enough, incorporate the feedback
    if (feedback.overallRating >= 80) {
      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'quality-assessment',
        'seo-optimization',
        seoOptimizationArtifact.id,
        ['content quality', 'SEO optimization', 'readability']
      );

      // Provide feedback
      const responseId = await feedbackSystem.provideFeedback(
        'quality-assessment',
        'seo-optimization',
        requestId,
        feedback
      );

      // Incorporate feedback
      await feedbackSystem.incorporateFeedback(
        seoOptimizationArtifact.id,
        [responseId],
        'seo-optimization'
      );
    }

    // Complete the goal
    await stateManager.completeGoal(goalId, artifactId);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error triggering quality assessment agent`, {
      sessionId,
      goalId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Trigger the market research agent
 * @param sessionId Session ID
 * @param goalId Goal ID
 * @param stateManager State manager
 * @param decisionFramework Artifact decision framework
 * @returns Promise<boolean> indicating success
 */
export async function triggerMarketResearchAgent(
  sessionId: string,
  goalId: string,
  stateManager: StateManager,
  decisionFramework: ArtifactDecisionFramework
): Promise<boolean> {
  try {
    logger.info(`Triggering market research agent`, {
      sessionId,
      goalId
    });

    // Assign goal to agent
    await stateManager.assignGoal(goalId, 'market-research');

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Get the goal
    const goal = state.goals.byId[goalId];
    if (!goal) {
      throw new Error(`Goal ${goalId} not found`);
    }

    // Make a decision about artifact creation
    const decision = await decisionFramework.decideArtifactCreation('market-research', goalId);

    // If we should use an existing artifact
    if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
      // Complete the goal with the existing artifact
      await stateManager.completeGoal(goalId, decision.existingArtifactId);
      return true;
    }

    // If we should improve an existing artifact
    if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
      // Get the existing artifact
      const existingArtifact = state.artifacts[decision.existingArtifactId];
      if (!existingArtifact) {
        throw new Error(`Artifact ${decision.existingArtifactId} not found`);
      }

      // Import the OpenAI integration
      const { generateMarketResearch } = await import('../utils/content-generation');

      // Generate improved market research content using OpenAI
      const marketResearchContent = await generateMarketResearch(
        state.topic,
        state.contentType,
        state.targetAudience
      );

      // Create the improved artifact
      const artifactId = await stateManager.createArtifact(
        'market-research',
        'Market Research Report (Improved)',
        marketResearchContent,
        'market-research',
        goalId,
        decision.existingArtifactId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(sessionId);

      // Generate feedback on the market research
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'content-strategy',
        ['audience analysis', 'market trends', 'competitor analysis']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'market-research',
        'content-strategy',
        artifactId,
        ['audience analysis', 'market trends', 'competitor analysis']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'content-strategy',
        'market-research',
        requestId,
        feedback
      );

      // Complete the goal
      await stateManager.completeGoal(goalId, artifactId);
      return true;
    }

    // Otherwise, create a new artifact
    // Import the OpenAI integration
    const { generateMarketResearch } = await import('../utils/content-generation');

    // Generate market research content using OpenAI
    const marketResearchContent = await generateMarketResearch(
      state.topic,
      state.contentType,
      state.targetAudience
    );

    // Create the artifact with the generated content
    const artifactId = await stateManager.createArtifact(
      'market-research',
      'Market Research Report',
      marketResearchContent,
      'market-research',
      goalId
    );

    // Initialize feedback loop system
    const feedbackSystem = new FeedbackLoopSystem(sessionId);

    // Generate feedback on the market research
    const feedback = await feedbackSystem.generateFeedback(
      artifactId,
      'content-strategy',
      ['audience analysis', 'market trends', 'competitor analysis']
    );

    // Create a feedback request
    const requestId = await feedbackSystem.requestFeedback(
      'market-research',
      'content-strategy',
      artifactId,
      ['audience analysis', 'market trends', 'competitor analysis']
    );

    // Provide feedback
    await feedbackSystem.provideFeedback(
      'content-strategy',
      'market-research',
      requestId,
      feedback
    );

    // Complete the goal
    await stateManager.completeGoal(goalId, artifactId);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error triggering market research agent`, {
      sessionId,
      goalId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Trigger the keyword analysis agent
 * @param sessionId Session ID
 * @param goalId Goal ID
 * @param stateManager State manager
 * @param decisionFramework Artifact decision framework
 * @returns Promise<boolean> indicating success
 */
export async function triggerKeywordAnalysisAgent(
  sessionId: string,
  goalId: string,
  stateManager: StateManager,
  decisionFramework: ArtifactDecisionFramework
): Promise<boolean> {
  try {
    logger.info(`Triggering keyword analysis agent`, {
      sessionId,
      goalId
    });

    // Assign goal to agent
    await stateManager.assignGoal(goalId, 'keyword-analysis');

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Get the goal
    const goal = state.goals.byId[goalId];
    if (!goal) {
      throw new Error(`Goal ${goalId} not found`);
    }

    // Make a decision about artifact creation
    const decision = await decisionFramework.decideArtifactCreation('keyword-analysis', goalId);

    // If we should use an existing artifact
    if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
      // Complete the goal with the existing artifact
      await stateManager.completeGoal(goalId, decision.existingArtifactId);
      return true;
    }

    // If we should improve an existing artifact
    if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
      // Get the existing artifact
      const existingArtifact = state.artifacts[decision.existingArtifactId];
      if (!existingArtifact) {
        throw new Error(`Artifact ${decision.existingArtifactId} not found`);
      }

      // Import the OpenAI integration
      const { generateKeywordAnalysis } = await import('../utils/content-generation');

      // Generate improved keyword analysis using OpenAI
      const { content, keywords } = await generateKeywordAnalysis(
        state.topic,
        state.contentType,
        state.targetAudience
      );

      // Update the state with the keywords
      await stateManager.updateKeywords(keywords);

      // Create the improved artifact
      const artifactId = await stateManager.createArtifact(
        'keyword-analysis',
        'SEO Keyword Analysis (Improved)',
        content,
        'keyword-analysis',
        goalId,
        decision.existingArtifactId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(sessionId);

      // Generate feedback on the keyword analysis
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'content-strategy',
        ['keyword relevance', 'search volume', 'competition analysis']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'keyword-analysis',
        'content-strategy',
        artifactId,
        ['keyword relevance', 'search volume', 'competition analysis']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'content-strategy',
        'keyword-analysis',
        requestId,
        feedback
      );

      // Complete the goal
      await stateManager.completeGoal(goalId, artifactId);
      return true;
    }

    // Otherwise, create a new artifact
    // Import the OpenAI integration
    const { generateKeywordAnalysis } = await import('../utils/content-generation');

    // Generate keyword analysis content using OpenAI
    const { content, keywords } = await generateKeywordAnalysis(
      state.topic,
      state.contentType,
      state.targetAudience
    );

    // Update the state with the keywords
    await stateManager.updateKeywords(keywords);

    // Create the artifact with the generated content
    const artifactId = await stateManager.createArtifact(
      'keyword-analysis',
      'SEO Keyword Analysis',
      content,
      'keyword-analysis',
      goalId
    );

    // Initialize feedback loop system
    const feedbackSystem = new FeedbackLoopSystem(sessionId);

    // Generate feedback on the keyword analysis
    const feedback = await feedbackSystem.generateFeedback(
      artifactId,
      'content-strategy',
      ['keyword relevance', 'search volume', 'competition analysis']
    );

    // Create a feedback request
    const requestId = await feedbackSystem.requestFeedback(
      'keyword-analysis',
      'content-strategy',
      artifactId,
      ['keyword relevance', 'search volume', 'competition analysis']
    );

    // Provide feedback
    await feedbackSystem.provideFeedback(
      'content-strategy',
      'keyword-analysis',
      requestId,
      feedback
    );

    // Complete the goal
    await stateManager.completeGoal(goalId, artifactId);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error triggering keyword analysis agent`, {
      sessionId,
      goalId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}