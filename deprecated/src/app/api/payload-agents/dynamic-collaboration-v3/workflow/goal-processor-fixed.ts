/**
 * Goal Processor (Fixed Version)
 *
 * This file implements a fixed version of the goal processor that handles
 * goal processing, agent assignment, and artifact creation.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import {
  GoalStatus,
  GoalType,
  ArtifactStatus,
  WorkflowPhase,
  MessageType
} from '../state/unified-schema';
import { OpenAIClient } from '../clients/openai-client';
import { AgentFactory } from '../agents/agent-factory';

export class GoalProcessorFixed {
  private stateManager: StateManager;
  private openAIClient: OpenAIClient;
  private agentFactory: AgentFactory;

  constructor(private sessionId: string) {
    this.stateManager = new StateManager(sessionId);
    this.openAIClient = new OpenAIClient();
    this.agentFactory = new AgentFactory(sessionId, this.openAIClient);
  }

  /**
   * Process goals for the session
   */
  async processGoals(): Promise<boolean> {
    try {
      logger.info(`Processing goals for session: ${this.sessionId}`);

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        logger.error(`Session state not found: ${this.sessionId}`);
        return false;
      }

      // Check if goals are defined
      if (!state.goals?.byId || Object.keys(state.goals.byId).length === 0) {
        logger.error(`No goals defined for session: ${this.sessionId}`);
        return false;
      }

      // Get active goals
      const activeGoalIds = state.goals.activeIds || [];
      if (activeGoalIds.length === 0) {
        logger.info(`No active goals for session: ${this.sessionId}`);

        // Check if there are any pending goals that can be activated
        const pendingGoals = Object.values(state.goals.byId)
          .filter((goal: any) => goal.status === GoalStatus.PENDING);

        if (pendingGoals.length > 0) {
          // Find pending goals with no dependencies or all dependencies completed
          const readyGoals = pendingGoals.filter((goal: any) => {
            if (!goal.dependencies || goal.dependencies.length === 0) {
              return true;
            }

            // Check if all dependencies are completed
            return goal.dependencies.every((depId: string) => {
              const dep = state.goals.byId[depId];
              return dep && dep.status === GoalStatus.COMPLETED;
            });
          });

          if (readyGoals.length > 0) {
            // Activate the first ready goal
            const goalToActivate = readyGoals[0];
            logger.info(`Activating goal: ${goalToActivate.id}`);

            await this.stateManager.updateState(currentState => {
              if (!currentState) return currentState;

              // Update goal status
              const updatedGoals = {
                ...currentState.goals,
                byId: {
                  ...currentState.goals.byId,
                  [goalToActivate.id]: {
                    ...currentState.goals.byId[goalToActivate.id],
                    status: GoalStatus.ACTIVE
                  }
                },
                activeIds: [...(currentState.goals.activeIds || []), goalToActivate.id]
              };

              return {
                ...currentState,
                goals: updatedGoals,
                lastUpdated: new Date().toISOString()
              };
            });

            // Process again with the newly activated goal
            return await this.processGoals();
          }

          logger.info(`No ready goals to activate for session: ${this.sessionId}`);
        }

        // Check if all goals are completed
        const allGoalsCompleted = Object.values(state.goals.byId)
          .every((goal: any) => goal.status === GoalStatus.COMPLETED);

        if (allGoalsCompleted) {
          logger.info(`All goals completed for session: ${this.sessionId}`);

          // Update workflow progress
          await this.stateManager.updateState(currentState => {
            if (!currentState) return currentState;

            return {
              ...currentState,
              workflowProgress: {
                ...currentState.workflowProgress,
                status: 'completed',
                overallProgress: 100,
                lastUpdated: new Date().toISOString()
              },
              lastUpdated: new Date().toISOString()
            };
          });
        }

        return true;
      }

      // Process each active goal
      for (const goalId of activeGoalIds) {
        const goal = state.goals.byId[goalId];
        if (!goal) continue;

        logger.info(`Processing goal: ${goalId}, type: ${goal.type}`);

        // Check if goal already has artifacts
        const goalArtifacts = Object.values(state.artifacts || {})
          .filter((artifact: any) => artifact.goalId === goalId);

        if (goalArtifacts.length > 0) {
          // Check if all artifacts are approved
          const allApproved = goalArtifacts.every(
            (artifact: any) => artifact.status === ArtifactStatus.APPROVED
          );

          if (allApproved) {
            // Mark goal as completed
            logger.info(`All artifacts approved for goal: ${goalId}, marking as completed`);

            await this.stateManager.updateState(currentState => {
              if (!currentState) return currentState;

              // Update goal status
              const updatedGoals = {
                ...currentState.goals,
                byId: {
                  ...currentState.goals.byId,
                  [goalId]: {
                    ...currentState.goals.byId[goalId],
                    status: GoalStatus.COMPLETED,
                    progress: 100,
                    completedAt: new Date().toISOString()
                  }
                },
                activeIds: currentState.goals.activeIds.filter(id => id !== goalId),
                completedIds: [...(currentState.goals.completedIds || []), goalId]
              };

              return {
                ...currentState,
                goals: updatedGoals,
                lastUpdated: new Date().toISOString()
              };
            });

            // Activate next goal
            await this.activateNextGoal(goalId);

            continue;
          }

          // Process existing artifacts
          for (const artifact of goalArtifacts) {
            if (artifact.status === ArtifactStatus.REVIEW) {
              // Evaluate artifact
              await this.evaluateArtifact(goalId, artifact.id);
            }
          }

          continue;
        }

        // Create artifacts for the goal
        await this.createArtifactsForGoal(goalId, goal);
      }

      // Update workflow progress
      await this.updateWorkflowProgress();

      return true;
    } catch (error) {
      logger.error(`Error processing goals`, {
        sessionId: this.sessionId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });

      return false;
    }
  }

  /**
   * Create artifacts for a goal
   */
  private async createArtifactsForGoal(goalId: string, goal: any): Promise<void> {
    try {
      logger.info(`Creating artifacts for goal: ${goalId}, type: ${goal.type}`);

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) return;

      // Get agent for goal type
      const agent = this.agentFactory.getAgentForGoalType(goal.type);
      if (!agent) {
        logger.error(`No agent found for goal type: ${goal.type}`);
        return;
      }

      // Generate artifact
      const artifactId = uuidv4();
      const now = new Date().toISOString();

      // Create message for agent
      const messageId = uuidv4();
      const message = {
        id: messageId,
        type: MessageType.AGENT,
        content: `Create artifact for goal: ${goal.description}`,
        from: 'system',
        to: agent.id,
        timestamp: now,
        conversationId: goalId,
        metadata: {
          goalId,
          artifactId
        }
      };

      // Update state with message
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        return {
          ...currentState,
          messages: {
            ...currentState.messages,
            byId: {
              ...(currentState.messages?.byId || {}),
              [messageId]: message
            },
            allIds: [...(currentState.messages?.allIds || []), messageId]
          },
          lastUpdated: now
        };
      });

      // Generate artifact content
      const artifactContent = await agent.generateArtifact(goal, state);

      // Create artifact
      const artifact = {
        id: artifactId,
        goalId,
        type: this.getArtifactTypeForGoal(goal.type),
        title: `${goal.type} Artifact for ${state.topic}`,
        content: artifactContent,
        status: ArtifactStatus.REVIEW,
        createdAt: now,
        updatedAt: now,
        createdBy: agent.id,
        version: 1,
        metadata: {}
      };

      // Update state with artifact
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        return {
          ...currentState,
          artifacts: {
            ...(currentState.artifacts || {}),
            [artifactId]: artifact
          },
          lastUpdated: now
        };
      });

      // Update goal progress
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        return {
          ...currentState,
          goals: {
            ...currentState.goals,
            byId: {
              ...currentState.goals.byId,
              [goalId]: {
                ...currentState.goals.byId[goalId],
                progress: 50,
                artifactIds: [
                  ...(currentState.goals.byId[goalId].artifactIds || []),
                  artifactId
                ]
              }
            }
          },
          lastUpdated: now
        };
      });

      logger.info(`Created artifact for goal: ${goalId}, artifactId: ${artifactId}`);
    } catch (error) {
      logger.error(`Error creating artifacts for goal`, {
        goalId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }

  /**
   * Evaluate an artifact
   */
  private async evaluateArtifact(goalId: string, artifactId: string): Promise<void> {
    try {
      logger.info(`Evaluating artifact: ${artifactId} for goal: ${goalId}`);

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) return;

      const goal = state.goals.byId[goalId];
      const artifact = state.artifacts[artifactId];

      if (!goal || !artifact) {
        logger.error(`Goal or artifact not found: ${goalId}, ${artifactId}`);
        return;
      }

      // Get evaluation agent
      const agent = this.agentFactory.getEvaluationAgent();
      if (!agent) {
        logger.error(`No evaluation agent found`);
        return;
      }

      // Generate evaluation
      const evaluation = await agent.evaluateArtifact(artifact, goal, state);
      const now = new Date().toISOString();

      // Update artifact with evaluation
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const updatedArtifact = {
          ...currentState.artifacts[artifactId],
          status: evaluation.meetsRequirements ? ArtifactStatus.APPROVED : ArtifactStatus.REJECTED,
          updatedAt: now,
          metadata: {
            ...currentState.artifacts[artifactId].metadata,
            evaluation
          }
        };

        return {
          ...currentState,
          artifacts: {
            ...currentState.artifacts,
            [artifactId]: updatedArtifact
          },
          lastUpdated: now
        };
      });

      logger.info(`Evaluated artifact: ${artifactId}, approved: ${evaluation.meetsRequirements}`);
    } catch (error) {
      logger.error(`Error evaluating artifact`, {
        goalId,
        artifactId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }

  /**
   * Update workflow progress
   */
  private async updateWorkflowProgress(): Promise<void> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) return;

      // Calculate overall progress
      const goals = Object.values(state.goals.byId);
      const totalGoals = goals.length;

      if (totalGoals === 0) return;

      const completedGoals = goals.filter(g => g.status === GoalStatus.COMPLETED).length;
      const overallProgress = Math.round((completedGoals / totalGoals) * 100);

      // Determine current phase
      let currentPhase = WorkflowPhase.PLANNING;

      if (completedGoals === totalGoals) {
        currentPhase = WorkflowPhase.FINALIZATION;
      } else {
        const researchGoals = goals.filter(g => g.type === GoalType.RESEARCH);
        const contentGoals = goals.filter(g => g.type === GoalType.CONTENT);

        const allResearchCompleted = researchGoals.length > 0 &&
          researchGoals.every(g => g.status === GoalStatus.COMPLETED);

        const allContentCompleted = contentGoals.length > 0 &&
          contentGoals.every(g => g.status === GoalStatus.COMPLETED);

        if (allResearchCompleted && allContentCompleted) {
          currentPhase = WorkflowPhase.REVIEW;
        } else if (allResearchCompleted) {
          currentPhase = WorkflowPhase.CREATION;
        } else {
          currentPhase = WorkflowPhase.RESEARCH;
        }
      }

      // Calculate phase progress
      const phaseProgress: Record<WorkflowPhase, number> = {
        [WorkflowPhase.PLANNING]: 100, // Planning is always complete if we have goals
        [WorkflowPhase.RESEARCH]: 0,
        [WorkflowPhase.CREATION]: 0,
        [WorkflowPhase.REVIEW]: 0,
        [WorkflowPhase.FINALIZATION]: 0
      };

      // Research phase progress
      const researchGoals = goals.filter(g => g.type === GoalType.RESEARCH);
      if (researchGoals.length > 0) {
        const researchProgress = researchGoals.reduce((sum, g) => sum + g.progress, 0) / researchGoals.length;
        phaseProgress[WorkflowPhase.RESEARCH] = Math.round(researchProgress);
      }

      // Creation phase progress
      const contentGoals = goals.filter(g => g.type === GoalType.CONTENT);
      if (contentGoals.length > 0) {
        const contentProgress = contentGoals.reduce((sum, g) => sum + g.progress, 0) / contentGoals.length;
        phaseProgress[WorkflowPhase.CREATION] = Math.round(contentProgress);
      }

      // Review phase progress
      const qualityGoals = goals.filter(g => g.type === GoalType.QUALITY);
      if (qualityGoals.length > 0) {
        const qualityProgress = qualityGoals.reduce((sum, g) => sum + g.progress, 0) / qualityGoals.length;
        phaseProgress[WorkflowPhase.REVIEW] = Math.round(qualityProgress);
      }

      // Finalization phase progress
      phaseProgress[WorkflowPhase.FINALIZATION] = completedGoals === totalGoals ? 100 : 0;

      // Update state
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        return {
          ...currentState,
          currentPhase,
          workflowProgress: {
            ...currentState.workflowProgress,
            currentPhase,
            phaseProgress,
            overallProgress,
            lastUpdated: new Date().toISOString()
          },
          lastUpdated: new Date().toISOString()
        };
      });
    } catch (error) {
      logger.error(`Error updating workflow progress`, {
        sessionId: this.sessionId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }

  /**
   * Get artifact type for goal type
   */
  private getArtifactTypeForGoal(goalType: GoalType): string {
    switch (goalType) {
      case GoalType.RESEARCH:
        return 'research-summary';
      case GoalType.MARKET_RESEARCH:
        return 'market-research';
      case GoalType.KEYWORD_ANALYSIS:
        return 'keyword-analysis';
      case GoalType.CONTENT:
        return 'content-draft';
      case GoalType.CONTENT_STRATEGY:
        return 'content-strategy';
      case GoalType.CONTENT_CREATION:
        return 'content-creation';
      case GoalType.QUALITY:
        return 'quality-assessment';
      case GoalType.SEO_OPTIMIZATION:
        return 'seo-optimization';
      case GoalType.QUALITY_ASSESSMENT:
        return 'quality-assessment';
      default:
        return 'generic-artifact';
    }
  }

  /**
   * Activate the next goal based on the current workflow phase
   */
  private async activateNextGoal(completedGoalId: string): Promise<void> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) return;

      const completedGoal = state.goals.byId[completedGoalId];
      if (!completedGoal) return;

      // Determine which goal to activate next based on the completed goal type
      let nextGoalType: GoalType | null = null;

      switch (completedGoal.type) {
        case GoalType.RESEARCH:
        case GoalType.MARKET_RESEARCH:
        case GoalType.KEYWORD_ANALYSIS:
          // After research, activate content creation
          nextGoalType = GoalType.CONTENT;
          break;

        case GoalType.CONTENT:
        case GoalType.CONTENT_STRATEGY:
        case GoalType.CONTENT_CREATION:
          // After content creation, activate quality assessment
          nextGoalType = GoalType.QUALITY;
          break;

        case GoalType.QUALITY:
        case GoalType.SEO_OPTIMIZATION:
        case GoalType.QUALITY_ASSESSMENT:
          // After quality assessment, we're done
          nextGoalType = null;
          break;

        default:
          nextGoalType = null;
      }

      if (!nextGoalType) {
        logger.info(`No next goal type for completed goal: ${completedGoalId}`);
        return;
      }

      // Find a pending goal of the next type
      const pendingGoals = Object.values(state.goals.byId).filter(
        g => g.type === nextGoalType && g.status === GoalStatus.PENDING
      );

      if (pendingGoals.length === 0) {
        logger.info(`No pending goals of type ${nextGoalType} found`);
        return;
      }

      // Activate the first pending goal of the next type
      const nextGoalId = pendingGoals[0].id;
      logger.info(`Activating next goal: ${nextGoalId}, type: ${nextGoalType}`);
      await this.stateManager.activateGoal(nextGoalId);
    } catch (error) {
      logger.error(`Error activating next goal`, {
        completedGoalId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }
}
