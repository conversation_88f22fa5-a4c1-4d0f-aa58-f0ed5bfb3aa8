/**
 * Dynamic Collaboration V3 Debug API
 *
 * This file implements a debug endpoint to help diagnose issues with the
 * goal-based orchestration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { GoalOrchestrator } from '../workflow/goal-orchestrator';
import { GoalType, GoalStatus, WorkflowPhase } from '../state/unified-schema';
import { v4 as uuidv4 } from 'uuid';
import { GoalProcessorFixed } from '../workflow/goal-processor-fixed';

/**
 * GET /api/agents/dynamic-collaboration-v3/debug
 * Get debug information for a session
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Get session ID from query params
    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');

    // Validate required fields
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Get state manager
    const stateManager = new StateManager(sessionId);

    // Get state
    const state = await stateManager.getState();
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Extract relevant debug information
    const debugInfo = {
      sessionId: state.id,
      topic: state.topic,
      status: state.status,
      currentPhase: state.currentPhase,
      workflowProgress: state.workflowProgress,
      goalsCount: {
        total: Object.keys(state.goals?.byId || {}).length,
        active: state.goals?.activeIds?.length || 0,
        completed: state.goals?.completedIds?.length || 0
      },
      goals: state.goals,
      artifactsCount: Object.keys(state.artifacts || {}).length,
      messagesCount: Object.keys(state.messages?.byId || {}).length,
      errors: Object.keys(state.errors || {}).length,
      lastUpdated: state.lastUpdated
    };

    // Return debug info
    return NextResponse.json({
      success: true,
      debugInfo
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error(`Error getting debug information`, {
      error: err.message,
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/agents/dynamic-collaboration-v3/debug
 * Test goal orchestrator functionality
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await req.json();
    const { sessionId, action } = body;

    // Validate required fields
    if (!sessionId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: sessionId, action' },
        { status: 400 }
      );
    }

    // Get state manager
    const stateManager = new StateManager(sessionId);

    // Get state
    const state = await stateManager.getState();
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Create orchestrator
    const orchestrator = new GoalOrchestrator(sessionId);

    // Perform requested action
    let result;
    switch (action) {
      case 'getState':
        result = await orchestrator.getState();
        break;
      case 'processGoals':
        try {
          result = await orchestrator.processGoals();
        } catch (error) {
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
          });
        }
        break;

      case 'processGoalsFixed':
        try {
          const goalProcessor = new GoalProcessorFixed(sessionId);
          result = await goalProcessor.processGoals();
        } catch (error) {
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
          });
        }
        break;
      case 'fixGoals':
        // Fix goals by manually creating initial goals
        result = await fixGoals(sessionId, stateManager);
        break;
      case 'forceProgress':
        // Force progress by processing goals multiple times
        result = await forceProgress(sessionId, orchestrator);
        break;
      case 'activateNextGoal':
        // Activate the next pending goal
        result = await activateNextGoal(sessionId, stateManager);
        break;
      case 'defineInitialGoals':
        // This is a private method, so we can't call it directly
        result = 'Cannot call private method';
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Get updated state after action
    const updatedState = await stateManager.getState();

    // Return result
    return NextResponse.json({
      success: true,
      result,
      state: updatedState
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error(`Error in debug endpoint`, {
      error: err.message,
      stack: err.stack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: err.message,
        stack: err.stack
      },
      { status: 500 }
    );
  }
}

/**
 * Fix goals by manually creating initial goals
 */
async function fixGoals(sessionId: string, stateManager: StateManager): Promise<any> {
  try {
    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Check if goals are already defined
    const hasGoals = state.goals &&
                    state.goals.byId &&
                    Object.keys(state.goals.byId).length > 0;

    if (hasGoals) {
      return { message: 'Goals already defined', goalsCount: Object.keys(state.goals.byId).length };
    }

    // Create initial goals
    const now = new Date().toISOString();

    // Research goal
    const researchGoalId = uuidv4();
    const researchGoal = {
      id: researchGoalId,
      type: GoalType.RESEARCH,
      description: `Research ${state.topic} for ${state.contentType}`,
      criteria: [
        'Gather comprehensive information about the topic',
        'Identify target audience needs and interests',
        'Analyze market trends and competition',
        'Identify key keywords and search terms'
      ],
      status: GoalStatus.ACTIVE,
      progress: 0,
      dependencies: [],
      createdAt: now,
      version: 1
    };

    // Content goal
    const contentGoalId = uuidv4();
    const contentGoal = {
      id: contentGoalId,
      type: GoalType.CONTENT,
      description: `Create content for ${state.topic}`,
      criteria: [
        'Develop a content strategy',
        'Create engaging and informative content',
        'Ensure content meets target audience needs',
        'Optimize content for search engines'
      ],
      status: GoalStatus.PENDING,
      progress: 0,
      dependencies: [researchGoalId],
      createdAt: now,
      version: 1
    };

    // Quality goal
    const qualityGoalId = uuidv4();
    const qualityGoal = {
      id: qualityGoalId,
      type: GoalType.QUALITY,
      description: `Ensure quality of content for ${state.topic}`,
      criteria: [
        'Verify content accuracy and completeness',
        'Check grammar and spelling',
        'Ensure content is engaging and well-structured',
        'Validate SEO optimization'
      ],
      status: GoalStatus.PENDING,
      progress: 0,
      dependencies: [contentGoalId],
      createdAt: now,
      version: 1
    };

    // Update state with new goals
    await stateManager.updateState(currentState => {
      if (!currentState) return currentState;

      return {
        ...currentState,
        goals: {
          byId: {
            [researchGoalId]: researchGoal,
            [contentGoalId]: contentGoal,
            [qualityGoalId]: qualityGoal
          },
          allIds: [researchGoalId, contentGoalId, qualityGoalId],
          activeIds: [researchGoalId],
          completedIds: []
        },
        currentPhase: WorkflowPhase.PLANNING,
        workflowProgress: {
          ...currentState.workflowProgress,
          status: 'in_progress',
          currentPhase: WorkflowPhase.PLANNING,
          phaseProgress: {
            ...currentState.workflowProgress.phaseProgress,
            [WorkflowPhase.PLANNING]: 25
          },
          overallProgress: 5,
          lastUpdated: now
        },
        lastUpdated: now
      };
    });

    return {
      message: 'Goals fixed successfully',
      goals: {
        research: researchGoalId,
        content: contentGoalId,
        quality: qualityGoalId
      }
    };
  } catch (error) {
    logger.error(`Error fixing goals`, {
      sessionId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Force progress by processing goals multiple times
 */
async function forceProgress(sessionId: string, orchestrator: GoalOrchestrator): Promise<any> {
  try {
    logger.info(`Forcing progress for session: ${sessionId}`);

    // Get current state
    const state = await orchestrator.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Process goals multiple times to ensure progress (increased from 3 to 10 iterations)
    const results = [];
    for (let i = 0; i < 10; i++) {
      logger.info(`Processing goals iteration ${i + 1}`);
      const success = await orchestrator.processGoals();
      results.push({ iteration: i + 1, success });

      // If we succeeded, get the updated state to check progress
      if (success) {
        const updatedState = await orchestrator.getState();
        if (updatedState) {
          // Check if we have active goals
          const hasActiveGoals = updatedState.goals?.activeIds?.length > 0;

          // If no active goals, try to activate the next pending goal
          if (!hasActiveGoals) {
            logger.info(`No active goals after processing, activating next pending goal`);
            await activateNextGoal(sessionId, new StateManager(sessionId));
          }

          // Always try to process goals again after each iteration
          // This helps ensure the workflow continues to make progress
          await orchestrator.processGoals();
        }
      }

      // Add a small delay between iterations to allow async operations to complete
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Get the final state after all processing
    const finalState = await orchestrator.getState();

    return {
      message: 'Force progress completed',
      results,
      activeGoals: finalState?.goals?.activeIds || [],
      pendingGoals: Object.entries(finalState?.goals?.byId || {})
        .filter(([_, goal]: [string, any]) => goal.status === GoalStatus.PENDING)
        .map(([id]: [string, any]) => id),
      completedGoals: finalState?.goals?.completedIds || [],
      currentPhase: finalState?.currentPhase,
      workflowProgress: finalState?.workflowProgress
    };
  } catch (error) {
    logger.error(`Error forcing progress`, {
      sessionId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Activate the next pending goal
 */
async function activateNextGoal(sessionId: string, stateManager: StateManager): Promise<any> {
  try {
    logger.info(`Activating next pending goal for session: ${sessionId}`);

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Check if there are already active goals
    const hasActiveGoals = state.goals?.activeIds?.length > 0;

    if (hasActiveGoals) {
      return {
        message: 'There are already active goals',
        activeGoals: state.goals.activeIds
      };
    }

    // Get all pending goals
    const pendingGoals = Object.entries(state.goals?.byId || {})
      .filter(([_, goal]: [string, any]) => goal.status === GoalStatus.PENDING)
      .map(([id, goal]: [string, any]) => ({ id, ...goal }));

    if (pendingGoals.length === 0) {
      return { message: 'No pending goals to activate' };
    }

    // Sort by priority
    const priorityOrder = [
      GoalType.RESEARCH,
      GoalType.MARKET_RESEARCH,
      GoalType.KEYWORD_ANALYSIS,
      GoalType.CONTENT,
      GoalType.CONTENT_STRATEGY,
      GoalType.CONTENT_CREATION,
      GoalType.QUALITY,
      GoalType.SEO_OPTIMIZATION,
      GoalType.QUALITY_ASSESSMENT
    ];

    pendingGoals.sort((a: any, b: any) => {
      const aIndex = priorityOrder.indexOf(a.type);
      const bIndex = priorityOrder.indexOf(b.type);
      return aIndex - bIndex;
    });

    // Activate the highest priority goal
    const goalToActivate = pendingGoals[0];
    logger.info(`Activating goal: ${goalToActivate.id}, type: ${goalToActivate.type}`);

    // Update state to activate the goal
    await stateManager.updateState(currentState => {
      if (!currentState) return currentState;

      // Update the goal status
      const updatedGoal = {
        ...currentState.goals.byId[goalToActivate.id],
        status: GoalStatus.ACTIVE
      };

      // Update active goals list
      const activeIds = [...(currentState.goals.activeIds || [])];
      if (!activeIds.includes(goalToActivate.id)) {
        activeIds.push(goalToActivate.id);
      }

      return {
        ...currentState,
        goals: {
          ...currentState.goals,
          byId: {
            ...currentState.goals.byId,
            [goalToActivate.id]: updatedGoal
          },
          activeIds
        },
        lastUpdated: new Date().toISOString()
      };
    });

    return {
      message: 'Goal activated successfully',
      activatedGoal: {
        id: goalToActivate.id,
        type: goalToActivate.type
      }
    };
  } catch (error) {
    logger.error(`Error activating next goal`, {
      sessionId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}
