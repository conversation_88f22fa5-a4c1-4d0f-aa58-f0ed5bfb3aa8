/**
 * Artifact Evolution Tracker
 * 
 * This service tracks the evolution of artifacts through feedback cycles
 * and consultations, providing a comprehensive view of how artifacts
 * improve over time.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { IArtifactEvolutionTracker } from '../interfaces/system-interfaces';
import { Artifact } from '../state/unified-schema';

export class ArtifactEvolutionTracker implements IArtifactEvolutionTracker {
  private stateManager: StateManager;
  
  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(private sessionId: string) {
    this.stateManager = new StateManager(sessionId);
  }
  
  /**
   * Get the evolution history of an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @returns Promise<Artifact[]> Evolution history
   */
  public async getEvolutionHistory(
    sessionId: string,
    artifactId: string
  ): Promise<Artifact[]> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }
      
      // Build evolution chain
      const evolutionChain: Artifact[] = [];
      
      // Add the current artifact
      evolutionChain.push(artifact);
      
      // Get evolution chain from metadata
      const metadataChain = artifact.metadata?.evolutionChain || [];
      
      // Add previous version if not in chain
      if (artifact.previousVersionId && !metadataChain.includes(artifact.previousVersionId)) {
        metadataChain.push(artifact.previousVersionId);
      }
      
      // Add all artifacts in the evolution chain
      for (const versionId of metadataChain) {
        const versionArtifact = state.artifacts[versionId];
        if (versionArtifact) {
          evolutionChain.push(versionArtifact);
        }
      }
      
      // Sort by creation date
      evolutionChain.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
      
      return evolutionChain;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error getting artifact evolution history`, {
        sessionId,
        artifactId,
        error: err.message || String(error),
        stack: err.stack
      });
      
      return [];
    }
  }
  
  /**
   * Get consultations for an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @returns Promise<any[]> Consultations
   */
  public async getConsultations(
    sessionId: string,
    artifactId: string
  ): Promise<any[]> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }
      
      // Get consultations from metadata
      const consultations = artifact.metadata?.reasoningProcess?.consultations || [];
      
      // Get consultations from state
      const stateConsultations = Object.values(state.consultations || {})
        .filter((consultation: any) => consultation.artifactId === artifactId);
      
      // Combine both sources
      const allConsultations = [...consultations, ...stateConsultations];
      
      // Sort by timestamp
      allConsultations.sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
      
      return allConsultations;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error getting artifact consultations`, {
        sessionId,
        artifactId,
        error: err.message || String(error),
        stack: err.stack
      });
      
      return [];
    }
  }
  
  /**
   * Get feedback cycles for an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @returns Promise<any[]> Feedback cycles
   */
  public async getFeedbackCycles(
    sessionId: string,
    artifactId: string
  ): Promise<any[]> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      // Get feedback requests for this artifact
      const feedbackRequests = Object.values(state.feedbackRequests || {})
        .filter((request: any) => request.artifactId === artifactId);
      
      // Get feedback responses for these requests
      const feedbackCycles = [];
      
      for (const request of feedbackRequests) {
        // Find the corresponding response
        const response = Object.values(state.feedbackResponses || {})
          .find((response: any) => response.requestId === request.id);
        
        if (response) {
          // Find the improved artifact if any
          const improvedArtifact = Object.values(state.artifacts || {})
            .find((artifact: any) => 
              artifact.previousVersionId === artifactId && 
              artifact.metadata?.creationContext?.feedbackResponseId === response.id
            );
          
          feedbackCycles.push({
            request,
            response,
            improvedArtifactId: improvedArtifact?.id,
            cycle: {
              startTime: request.timestamp,
              feedbackTime: response.timestamp,
              incorporatedTime: improvedArtifact?.createdAt,
              fromAgent: request.fromAgent,
              toAgent: request.toAgent,
              specificAreas: request.specificAreas,
              rating: response.feedback?.overallRating,
              summary: response.feedback?.summary
            }
          });
        }
      }
      
      // Sort by start time
      feedbackCycles.sort((a, b) => 
        new Date(a.cycle.startTime).getTime() - new Date(b.cycle.startTime).getTime()
      );
      
      return feedbackCycles;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error getting artifact feedback cycles`, {
        sessionId,
        artifactId,
        error: err.message || String(error),
        stack: err.stack
      });
      
      return [];
    }
  }
  
  /**
   * Record an artifact evolution event
   * @param artifactId Artifact ID
   * @param previousVersionId Previous version ID
   * @param eventType Event type (feedback, consultation, etc.)
   * @param eventData Event data
   * @returns Promise<boolean> Success indicator
   */
  public async recordEvolutionEvent(
    artifactId: string,
    previousVersionId: string,
    eventType: 'feedback' | 'consultation' | 'manual',
    eventData: any
  ): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }
      
      // Get the artifacts
      const artifact = state.artifacts[artifactId];
      const previousArtifact = state.artifacts[previousVersionId];
      
      if (!artifact || !previousArtifact) {
        throw new Error(`Artifact not found`);
      }
      
      // Update the artifact metadata
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;
        
        // Get the current evolution chain
        const evolutionChain = artifact.metadata?.evolutionChain || [];
        
        // Add previous version if not already in chain
        if (!evolutionChain.includes(previousVersionId)) {
          evolutionChain.push(previousVersionId);
        }
        
        // Update the artifact
        const updatedArtifact = {
          ...artifact,
          metadata: {
            ...artifact.metadata,
            evolutionChain,
            reasoningProcess: {
              ...artifact.metadata?.reasoningProcess,
              inputs: [
                ...(artifact.metadata?.reasoningProcess?.inputs || []),
                previousVersionId
              ],
              considerations: [
                ...(artifact.metadata?.reasoningProcess?.considerations || []),
                `Improved from previous version based on ${eventType}`
              ],
              evolutionEvents: [
                ...(artifact.metadata?.reasoningProcess?.evolutionEvents || []),
                {
                  timestamp: new Date().toISOString(),
                  type: eventType,
                  data: eventData
                }
              ]
            }
          }
        };
        
        return {
          ...currentState,
          artifacts: {
            ...currentState.artifacts,
            [artifactId]: updatedArtifact
          }
        };
      });
      
      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error recording artifact evolution event`, {
        sessionId: this.sessionId,
        artifactId,
        previousVersionId,
        eventType,
        error: err.message || String(error),
        stack: err.stack
      });
      
      return false;
    }
  }
}
