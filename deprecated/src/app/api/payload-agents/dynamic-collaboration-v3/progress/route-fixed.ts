/**
 * Dynamic Collaboration V3 Progress API (Fixed Version)
 *
 * This file implements the API endpoint for progressing a dynamic collaboration session.
 * It includes enhanced error handling and detailed logging.
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../utils/logger';
import { GoalOrchestrator } from '../workflow/goal-orchestrator';
import { StateManager } from '../state/manager';

/**
 * POST /api/agents/dynamic-collaboration-v3/progress
 * Progress a dynamic collaboration session
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await req.json();
    const { sessionId, steps = 1 } = body;

    // Log the request
    logger.info(`Progress request received`, {
      sessionId,
      steps
    });

    // Validate required fields
    if (!sessionId) {
      logger.warn(`Missing required field: sessionId`);
      return NextResponse.json(
        { error: 'Missing required field: sessionId' },
        { status: 400 }
      );
    }

    // Verify session exists
    const stateManager = new StateManager(sessionId);
    const state = await stateManager.getState();
    
    if (!state) {
      logger.warn(`Session not found: ${sessionId}`);
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Create orchestrator
    logger.info(`Creating goal orchestrator for session: ${sessionId}`);
    const orchestrator = new GoalOrchestrator(sessionId);

    // Process goals with detailed error handling
    let success = false;
    let error = null;
    
    try {
      logger.info(`Processing goals for session: ${sessionId}, steps: ${steps}`);
      
      for (let i = 0; i < steps; i++) {
        logger.info(`Processing step ${i + 1} of ${steps}`);
        success = await orchestrator.processGoals();
        
        if (!success) {
          logger.warn(`Failed to process goals at step ${i + 1}`);
          break;
        }
      }
    } catch (err) {
      error = err;
      logger.error(`Error processing goals`, {
        sessionId,
        error: err instanceof Error ? err.message : String(err),
        stack: err instanceof Error ? err.stack : undefined
      });
    }

    if (error) {
      return NextResponse.json(
        { 
          error: 'Error processing goals',
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      );
    }

    if (!success) {
      return NextResponse.json(
        { 
          error: 'Failed to progress session',
          details: 'The orchestrator reported failure but did not throw an exception'
        },
        { status: 500 }
      );
    }

    // Get updated state
    logger.info(`Getting updated state for session: ${sessionId}`);
    const updatedState = await orchestrator.getState();

    // Return updated state
    logger.info(`Progress successful for session: ${sessionId}`);
    return NextResponse.json({ 
      success: true,
      state: updatedState
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error(`Unhandled error progressing dynamic collaboration session`, {
      error: err.message,
      stack: err.stack
    });

    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: err.message
      },
      { status: 500 }
    );
  }
}
