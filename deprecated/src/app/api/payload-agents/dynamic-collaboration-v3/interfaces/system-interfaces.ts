/**
 * System Interfaces
 * 
 * This file defines standardized interfaces for interaction between
 * the collaboration system and feedback loop system.
 */

import { 
  Goal, 
  Artifact, 
  FeedbackData, 
  FeedbackRequest, 
  FeedbackResponse,
  ArtifactStatus
} from '../state/unified-schema';

/**
 * Interface for the Collaboration System
 * Defines the contract that the collaboration system must fulfill
 */
export interface ICollaborationSystem {
  /**
   * Initialize a new collaboration session
   * @param params Session initialization parameters
   * @returns Promise<string> Session ID
   */
  initializeSession(params: any): Promise<string>;
  
  /**
   * Define goals for the session
   * @param sessionId Session ID
   * @param goals Goals to define
   * @returns Promise<string[]> Goal IDs
   */
  defineGoals(sessionId: string, goals: Partial<Goal>[]): Promise<string[]>;
  
  /**
   * Activate a goal
   * @param sessionId Session ID
   * @param goalId Goal ID
   * @returns Promise<boolean> Success indicator
   */
  activateGoal(sessionId: string, goalId: string): Promise<boolean>;
  
  /**
   * Complete a goal
   * @param sessionId Session ID
   * @param goalId Goal ID
   * @param artifactId Artifact ID that satisfies the goal
   * @returns Promise<boolean> Success indicator
   */
  completeGoal(sessionId: string, goalId: string, artifactId: string): Promise<boolean>;
  
  /**
   * Create an artifact
   * @param sessionId Session ID
   * @param type Artifact type
   * @param title Artifact title
   * @param content Artifact content
   * @param goalId Associated goal ID
   * @param previousVersionId Previous version ID (for evolution tracking)
   * @returns Promise<string> Artifact ID
   */
  createArtifact(
    sessionId: string,
    type: string,
    title: string,
    content: any,
    goalId?: string,
    previousVersionId?: string
  ): Promise<string>;
  
  /**
   * Record a consultation between agents
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @param consultingAgent Agent requesting consultation
   * @param consultedAgent Agent providing consultation
   * @param question Consultation question
   * @param response Consultation response
   * @returns Promise<boolean> Success indicator
   */
  recordConsultation(
    sessionId: string,
    artifactId: string,
    consultingAgent: string,
    consultedAgent: string,
    question: string,
    response: string
  ): Promise<boolean>;
  
  /**
   * Get the current state of the session
   * @param sessionId Session ID
   * @returns Promise<any> Session state
   */
  getState(sessionId: string): Promise<any>;
}

/**
 * Interface for the Feedback Loop System
 * Defines the contract that the feedback loop system must fulfill
 */
export interface IFeedbackLoopSystem {
  /**
   * Request feedback on an artifact
   * @param sessionId Session ID
   * @param fromAgent Agent requesting feedback
   * @param toAgent Agent to provide feedback
   * @param artifactId Artifact ID
   * @param specificAreas Specific areas to focus feedback on
   * @returns Promise<string> Request ID
   */
  requestFeedback(
    sessionId: string,
    fromAgent: string,
    toAgent: string,
    artifactId: string,
    specificAreas?: string[]
  ): Promise<string>;
  
  /**
   * Provide feedback on an artifact
   * @param sessionId Session ID
   * @param fromAgent Agent providing feedback
   * @param toAgent Agent who requested feedback
   * @param requestId Request ID
   * @param feedback Feedback data
   * @returns Promise<string> Response ID
   */
  provideFeedback(
    sessionId: string,
    fromAgent: string,
    toAgent: string,
    requestId: string,
    feedback: FeedbackData
  ): Promise<string>;
  
  /**
   * Generate feedback on an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @param fromAgent Agent providing feedback
   * @param specificAreas Specific areas to focus feedback on
   * @returns Promise<FeedbackData> Generated feedback
   */
  generateFeedback(
    sessionId: string,
    artifactId: string,
    fromAgent: string,
    specificAreas?: string[]
  ): Promise<FeedbackData>;
  
  /**
   * Incorporate feedback to improve an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @param responseIds Feedback response IDs
   * @param creatingAgent Agent creating the improved artifact
   * @returns Promise<string> Improved artifact ID
   */
  incorporateFeedback(
    sessionId: string,
    artifactId: string,
    responseIds: string[],
    creatingAgent: string
  ): Promise<string>;
  
  /**
   * Track feedback cycles for an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @returns Promise<any> Feedback cycle statistics
   */
  trackFeedbackCycle(
    sessionId: string,
    artifactId: string
  ): Promise<{
    cyclesCompleted: number;
    averageRating: number;
    improvementTrend: number;
    timeToIncorporate: number;
  }>;
}

/**
 * Interface for the Artifact Evolution Tracker
 * Defines the contract for tracking artifact evolution
 */
export interface IArtifactEvolutionTracker {
  /**
   * Get the evolution history of an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @returns Promise<Artifact[]> Evolution history
   */
  getEvolutionHistory(
    sessionId: string,
    artifactId: string
  ): Promise<Artifact[]>;
  
  /**
   * Get consultations for an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @returns Promise<any[]> Consultations
   */
  getConsultations(
    sessionId: string,
    artifactId: string
  ): Promise<any[]>;
  
  /**
   * Get feedback cycles for an artifact
   * @param sessionId Session ID
   * @param artifactId Artifact ID
   * @returns Promise<any[]> Feedback cycles
   */
  getFeedbackCycles(
    sessionId: string,
    artifactId: string
  ): Promise<any[]>;
}
