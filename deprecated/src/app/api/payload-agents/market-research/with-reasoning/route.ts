// src/app/(payload)/api/agents/market-research/with-reasoning/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  EnhancedA2AMessage, 
  Reasoning, 
  Part, 
  TextPart, 
  DataPart,
  CollaborationState,
  ArtifactType
} from '../../a2atypes';

/**
 * Generate audience analysis with explicit reasoning
 */
async function generateAudienceAnalysis(
  topic: string,
  targetAudience: string
): Promise<{ audienceAnalysis: any, reasoning: Reasoning }> {
  // Explicitly show reasoning process
  const thoughts = [
    `The topic "${topic}" appeals to specific demographic segments`,
    `Need to analyze "${targetAudience}" in depth to understand their needs`,
    `Market trends and audience behavior should inform content strategy`
  ];
  
  const considerations = [
    'Demographics impact content tone and complexity',
    'Audience pain points drive engagement',
    'Content consumption preferences vary by audience segment',
    'Competitive analysis reveals content gaps'
  ];
  
  const alternatives = [
    'Could focus purely on demographics (too superficial)',
    'Could analyze only consumer behavior (too narrow)',
    'Could rely only on industry reports (might miss audience specifics)'
  ];
  
  // Create audience analysis
  const audienceAnalysis = {
    demographics: {
      ageRange: targetAudience.includes('young') ? '18-34' : 
                targetAudience.includes('senior') ? '55+' : '25-54',
      gender: targetAudience.includes('women') ? 'Primarily female' :
              targetAudience.includes('men') ? 'Primarily male' : 'Balanced',
      income: targetAudience.includes('professional') ? 'Above average' :
              targetAudience.includes('budget') ? 'Cost-conscious' : 'Average',
      education: targetAudience.includes('professional') || targetAudience.includes('expert') ? 
                'Higher education' : 'Diverse educational backgrounds',
      location: targetAudience.includes('urban') ? 'Urban centers' :
               targetAudience.includes('rural') ? 'Rural areas' : 'Mixed'
    },
    psychographics: {
      interests: [
        topic,
        targetAudience.includes('professional') ? 'Career advancement' : 'Personal improvement',
        'Industry trends',
        'Best practices'
      ],
      painPoints: [
        `Lack of clear information about ${topic}`,
        `Uncertainty about implementing ${topic} effectively`,
        `Finding trustworthy resources on ${topic}`
      ],
      contentPreferences: [
        targetAudience.includes('busy') ? 'Concise, scannable content' : 'Comprehensive resources',
        targetAudience.includes('visual') ? 'Visual-heavy content' : 'Balanced media mix',
        targetAudience.includes('beginner') ? 'Step-by-step guides' : 'Advanced insights'
      ],
      motivations: [
        'Solving specific problems',
        'Improving knowledge or skills',
        'Staying current with trends'
      ]
    },
    contentConsumptionHabits: {
      preferredFormats: [
        targetAudience.includes('busy') ? 'Quick reference guides' : 'In-depth articles',
        'How-to content',
        targetAudience.includes('visual') ? 'Infographics and videos' : 'Text-based resources'
      ],
      researchBehavior: targetAudience.includes('thorough') ? 'Deep research before decisions' : 
                      'Quick information gathering',
      deviceUsage: targetAudience.includes('mobile') ? 'Primarily mobile' : 
                 targetAudience.includes('desktop') ? 'Primarily desktop' : 'Multi-device'
    },
    marketInsights: {
      competitiveAnalysis: `${targetAudience} typically compares multiple sources before making decisions about ${topic}`,
      contentGaps: `There's a shortage of ${targetAudience.includes('expert') ? 'advanced' : 'accessible'} content about ${topic} for ${targetAudience}`,
      trends: `Growing interest in ${topic} among ${targetAudience}, with particular focus on practical applications`
    }
  };
  
  // Complete reasoning
  const reasoning: Reasoning = {
    thoughts,
    considerations,
    alternatives,
    decision: `A comprehensive audience analysis for ${targetAudience} interested in ${topic} should focus on both demographics and psychographics to inform content strategy.`,
    confidence: 0.85
  };
  
  return { audienceAnalysis, reasoning };
}

/**
 * Handle enhanced A2A messages with explicit reasoning
 */
async function handleEnhancedMessage(message: EnhancedA2AMessage, state: CollaborationState): Promise<{
  message: EnhancedA2AMessage;
  stateUpdates?: any;
}> {
  console.log("Market Research Agent received enhanced message:", JSON.stringify(message));
  
  // Extract request type from message parts
  let requestType = '';
  for (const part of message.parts) {
    if (part.type === 'text') {
      const text = part.text;
      
      if (text.includes('audience analysis')) {
        requestType = 'audience-analysis';
      } else if (text.includes('market trends')) {
        requestType = 'market-trends';
      } else if (text.includes('competitor analysis')) {
        requestType = 'competitor-analysis';
      }
    }
  }
  
  // Default to audience analysis if no specific request type
  if (!requestType) {
    requestType = 'audience-analysis';
  }
  
  // Create parts for response
  const responseParts: Part[] = [];
  const stateUpdates: any = {};
  
  // Generate audience analysis
  const { audienceAnalysis, reasoning } = await generateAudienceAnalysis(
    state.topic,
    state.targetAudience
  );
  
  // Create text part with main response
  const textPart: TextPart = {
    type: 'text',
    text: `I've analyzed the target audience (${state.targetAudience}) for the topic "${state.topic}" and have identified key characteristics and preferences.

The audience demographics indicate ${audienceAnalysis.demographics.ageRange} age range with ${audienceAnalysis.demographics.gender.toLowerCase()} gender balance and ${audienceAnalysis.demographics.income.toLowerCase()} income levels.

Key psychographics include interests in ${audienceAnalysis.psychographics.interests.join(', ')} with pain points around ${audienceAnalysis.psychographics.painPoints[0].toLowerCase()}.

Content should be delivered in ${audienceAnalysis.contentConsumptionHabits.preferredFormats.join(', ')} formats to match their consumption habits.

Market insights reveal that ${audienceAnalysis.marketInsights.competitiveAnalysis}, which should inform our content strategy.`
  };
  
  // Create data part with structured analysis
  const dataPart: DataPart = {
    type: 'data',
    data: audienceAnalysis
  };
  
  responseParts.push(textPart, dataPart);
  
  // Create artifact
  const artifactId = uuidv4();
  const artifact = {
    id: artifactId,
    name: `Audience Analysis for ${state.topic}`,
    description: `Detailed audience analysis for ${state.targetAudience} interested in ${state.topic}`,
    type: 'audience-analysis' as ArtifactType,
    parts: [textPart, dataPart],
    metadata: {
      createdAt: new Date().toISOString(),
      createdBy: 'market-research'
    },
    index: 0,
    version: 1,
    contributors: ['market-research'],
    feedback: [],
    history: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'market-research',
        changes: 'Initial creation',
        parts: [textPart, dataPart],
        reasoning
      }
    ],
    status: 'draft',
    qualityScore: 80
  };
  
  // Add to state updates
  stateUpdates.marketResearch = audienceAnalysis;
  stateUpdates.artifactIndex = {
    [artifactId]: artifact
  };
  
  // Create response message
  const responseMessage: EnhancedA2AMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: 'market-research',
    to: message.from,
    role: 'agent',
    parts: responseParts,
    conversationId: message.conversationId,
    reasoning,
    intentions: ['inform'],
    replyTo: message.id,
    artifactReferences: [artifactId]
  };
  
  return { message: responseMessage, stateUpdates };
}

/**
 * API route handler for enhanced A2A communication
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("Market Research Agent received enhanced POST request:", JSON.stringify(body));
    
    // Extract the message and state from request body
    const { message, state } = body;
    
    // Handle the enhanced message
    const response = await handleEnhancedMessage(message, state);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in Market Research Agent enhanced POST handler:", error);
    
    // Create error response
    const errorResponse: EnhancedA2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'market-research',
      to: 'system',
      role: 'agent',
      parts: [
        {
          type: 'text',
          text: `Error processing message: ${(error as Error).message}`
        }
      ],
      conversationId: uuidv4(),
      reasoning: {
        thoughts: ['An error occurred while processing the message'],
        considerations: ['The error might be due to invalid input or internal processing'],
        decision: 'Return error message with details',
        confidence: 1.0
      },
      intentions: ['inform']
    };
    
    return NextResponse.json(
      { message: errorResponse, error: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * API route handler for agent capabilities
 */
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "Market Research Agent",
    status: "active",
    capabilities: [
      "audience-analysis", 
      "market-trends", 
      "competitor-analysis"
    ],
    enhancedProtocol: true,
    reasoningEnabled: true,
    artifactCreation: true
  });
}
