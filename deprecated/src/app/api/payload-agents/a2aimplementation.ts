
/**
 * Agent2Agent (A2A) Protocol Implementation
 * 
 * This file contains the implementation of the A2A protocol for the content generation system.
 * It includes agent cards for each specialized agent and methods for A2A task management.
 */

import { v4 as uuidv4 } from 'uuid';
import { 
  AgentCard, 
  AgentSkill, 
  A2ATask, 
  A2AMessage, 
  Part, 
  Artifact, 
  TaskState,
  TaskStatus,
  TaskSendParams,
  TaskGetParams,
  TaskCancelParams,
  TaskSubscribeParams,
  PushNotificationConfig
} from './a2atypes';
import { ContentGenerationState } from './contentGeneration';

// Define agent cards for each specialized agent
export const agentCards: Record<string, AgentCard> = {
  marketResearch: {
    name: "Market Research Agent",
    description: "Analyzes market trends, audience needs, and competitive landscape to inform content strategy",
    url: "/api/agents/market-research",
    provider: {
      organization: "AuthenCIO",
      url: "https://authencio.com"
    },
    version: "1.0.0",
    capabilities: {
      streaming: true,
      pushNotifications: false,
      stateTransitionHistory: true
    },
    authentication: {
      schemes: ["none"]
    },
    defaultInputModes: ["text/plain", "application/json"],
    defaultOutputModes: ["text/plain", "application/json"],
    skills: [
      {
        id: "audience-analysis",
        name: "Audience Analysis",
        description: "Analyzes target audience needs, preferences, and behaviors",
        tags: ["audience", "demographics", "psychographics", "market-research"],
        examples: [
          "Analyze the target audience for a product page about eco-friendly water bottles",
          "What are the key pain points for our target audience?"
        ]
      },
      {
        id: "competitor-analysis",
        name: "Competitor Analysis",
        description: "Analyzes competitor content, strategies, and market positioning",
        tags: ["competitors", "market-analysis", "content-gap"],
        examples: [
          "Analyze top competitors in the sustainable products market",
          "What content strategies are our competitors using?"
        ]
      },
      {
        id: "trend-analysis",
        name: "Trend Analysis",
        description: "Identifies emerging trends and topics in the market",
        tags: ["trends", "market-trends", "topic-research"],
        examples: [
          "What are the emerging trends in sustainable products?",
          "Identify trending topics related to eco-friendly living"
        ]
      }
    ]
  },
  
  seoKeyword: {
    name: "SEO Keyword Agent",
    description: "Researches, analyzes, and prioritizes keywords for content optimization",
    url: "/api/agents/seo-keyword",
    provider: {
      organization: "AuthenCIO",
      url: "https://authencio.com"
    },
    version: "1.0.0",
    capabilities: {
      streaming: true,
      pushNotifications: false,
      stateTransitionHistory: true
    },
    authentication: {
      schemes: ["none"]
    },
    defaultInputModes: ["text/plain", "application/json"],
    defaultOutputModes: ["text/plain", "application/json"],
    skills: [
      {
        id: "keyword-research",
        name: "Keyword Research",
        description: "Researches relevant keywords for content optimization",
        tags: ["seo", "keywords", "search-volume", "competition"],
        examples: [
          "Find the best keywords for a blog post about sustainable living",
          "What are the long-tail keywords for eco-friendly products?"
        ]
      },
      {
        id: "keyword-prioritization",
        name: "Keyword Prioritization",
        description: "Prioritizes keywords based on relevance, search volume, and competition",
        tags: ["seo", "keyword-strategy", "prioritization"],
        examples: [
          "Prioritize these keywords for a product page about eco-friendly water bottles",
          "Which keywords should we focus on for maximum impact?"
        ]
      },
      {
        id: "semantic-analysis",
        name: "Semantic Analysis",
        description: "Analyzes semantic relationships between keywords and topics",
        tags: ["seo", "semantic-seo", "topic-clusters"],
        examples: [
          "Create a semantic keyword map for sustainable living content",
          "What related terms should we include with our primary keywords?"
        ]
      }
    ]
  },
  
  contentStrategy: {
    name: "Content Strategy Agent",
    description: "Develops comprehensive content strategies based on audience, SEO, and business goals",
    url: "/api/agents/content-strategy",
    provider: {
      organization: "AuthenCIO",
      url: "https://authencio.com"
    },
    version: "1.0.0",
    capabilities: {
      streaming: true,
      pushNotifications: false,
      stateTransitionHistory: true
    },
    authentication: {
      schemes: ["none"]
    },
    defaultInputModes: ["text/plain", "application/json"],
    defaultOutputModes: ["text/plain", "application/json"],
    skills: [
      {
        id: "content-structure",
        name: "Content Structure Planning",
        description: "Designs optimal content structure based on topic, audience, and SEO requirements",
        tags: ["content-strategy", "structure", "outline"],
        examples: [
          "Create a content structure for a blog post about sustainable living",
          "What sections should we include in our product page?"
        ]
      },
      {
        id: "content-differentiation",
        name: "Content Differentiation Strategy",
        description: "Develops strategies to differentiate content from competitors",
        tags: ["content-strategy", "differentiation", "unique-value"],
        examples: [
          "How can we differentiate our content from competitors?",
          "What unique angles can we take for our sustainable living content?"
        ]
      },
      {
        id: "content-format",
        name: "Content Format Selection",
        description: "Recommends optimal content formats based on goals and audience",
        tags: ["content-strategy", "formats", "content-types"],
        examples: [
          "What content formats would work best for our eco-friendly product line?",
          "Should we use a comparison table, listicle, or guide format?"
        ]
      }
    ]
  },
  
  contentGeneration: {
    name: "Content Generation Agent",
    description: "Creates high-quality, engaging content based on strategy, research, and SEO requirements",
    url: "/api/agents/content-generation",
    provider: {
      organization: "AuthenCIO",
      url: "https://authencio.com"
    },
    version: "1.0.0",
    capabilities: {
      streaming: true,
      pushNotifications: false,
      stateTransitionHistory: true
    },
    authentication: {
      schemes: ["none"]
    },
    defaultInputModes: ["text/plain", "application/json"],
    defaultOutputModes: ["text/plain", "application/json"],
    skills: [
      {
        id: "product-page",
        name: "Product Page Creation",
        description: "Creates compelling product pages that convert",
        tags: ["content-creation", "product-page", "conversion"],
        examples: [
          "Create a product page for an eco-friendly water bottle",
          "Write product descriptions that highlight sustainability features"
        ]
      },
      {
        id: "blog-article",
        name: "Blog Article Creation",
        description: "Creates informative, engaging blog articles",
        tags: ["content-creation", "blog", "article"],
        examples: [
          "Write a blog post about sustainable living practices",
          "Create an in-depth guide to reducing plastic waste"
        ]
      },
      {
        id: "buying-guide",
        name: "Buying Guide Creation",
        description: "Creates comprehensive buying guides to aid purchase decisions",
        tags: ["content-creation", "buying-guide", "comparison"],
        examples: [
          "Create a buying guide for eco-friendly water bottles",
          "Write a comparison guide for sustainable household products"
        ]
      }
    ]
  },
  
  editorial: {
    name: "Editorial Agent",
    description: "Reviews and refines content for quality, clarity, and brand voice",
    url: "/api/agents/editorial",
    provider: {
      organization: "AuthenCIO",
      url: "https://authencio.com"
    },
    version: "1.0.0",
    capabilities: {
      streaming: true,
      pushNotifications: false,
      stateTransitionHistory: true
    },
    authentication: {
      schemes: ["none"]
    },
    defaultInputModes: ["text/plain", "application/json"],
    defaultOutputModes: ["text/plain", "application/json"],
    skills: [
      {
        id: "content-editing",
        name: "Content Editing",
        description: "Edits content for clarity, flow, and engagement",
        tags: ["editing", "clarity", "flow"],
        examples: [
          "Edit this product description for clarity and engagement",
          "Improve the flow of this blog post"
        ]
      },
      {
        id: "tone-alignment",
        name: "Tone and Voice Alignment",
        description: "Ensures content aligns with brand tone and voice",
        tags: ["editing", "tone", "voice", "brand"],
        examples: [
          "Adjust the tone of this content to match our brand voice",
          "Make this content sound more conversational"
        ]
      },
      {
        id: "quality-assurance",
        name: "Content Quality Assurance",
        description: "Checks content for accuracy, consistency, and completeness",
        tags: ["editing", "quality", "accuracy"],
        examples: [
          "Review this content for factual accuracy",
          "Ensure consistency across all sections of this guide"
        ]
      }
    ]
  },
  
  seoOptimization: {
    name: "SEO Optimization Agent",
    description: "Optimizes content for search engines while maintaining readability and user experience",
    url: "/api/agents/seo-optimization",
    provider: {
      organization: "AuthenCIO",
      url: "https://authencio.com"
    },
    version: "1.0.0",
    capabilities: {
      streaming: true,
      pushNotifications: false,
      stateTransitionHistory: true
    },
    authentication: {
      schemes: ["none"]
    },
    defaultInputModes: ["text/plain", "application/json"],
    defaultOutputModes: ["text/plain", "application/json"],
    skills: [
      {
        id: "on-page-optimization",
        name: "On-Page SEO Optimization",
        description: "Optimizes content elements for search engines",
        tags: ["seo", "on-page", "optimization"],
        examples: [
          "Optimize this product page for SEO",
          "Improve the keyword usage in this blog post"
        ]
      },
      {
        id: "meta-optimization",
        name: "Meta Element Optimization",
        description: "Creates optimized meta titles, descriptions, and headers",
        tags: ["seo", "meta", "titles", "descriptions"],
        examples: [
          "Create SEO-optimized meta elements for this content",
          "Write a compelling meta description that includes our target keywords"
        ]
      },
      {
        id: "schema-markup",
        name: "Schema Markup Generation",
        description: "Generates appropriate schema markup for content",
        tags: ["seo", "schema", "structured-data"],
        examples: [
          "Create schema markup for this product page",
          "What schema should we use for this buying guide?"
        ]
      }
    ]
  }
};

/**
 * A2A Protocol Methods Implementation
 */

// Create a new task or update an existing one
export function createOrUpdateTask(
  state: ContentGenerationState,
  params: TaskSendParams
): { state: ContentGenerationState; task: A2ATask } {
  const { id, sessionId = uuidv4(), message, metadata = {} } = params;
  const existingTask = state.a2aTasks?.[id];
  
  // Create a new task if it doesn't exist
  if (!existingTask) {
    const newTask: A2ATask = {
      id,
      sessionId,
      status: {
        state: "submitted",
        timestamp: new Date().toISOString()
      },
      history: [message],
      metadata
    };
    
    return {
      state: {
        ...state,
        a2aTasks: {
          ...(state.a2aTasks || {}),
          [id]: newTask
        },
        currentTaskId: id
      },
      task: newTask
    };
  }
  
  // Update existing task
  const updatedHistory = [...(existingTask.history || []), message];
  const updatedTask: A2ATask = {
    ...existingTask,
    history: updatedHistory,
    status: {
      ...existingTask.status,
      state: "working",
      timestamp: new Date().toISOString()
    },
    metadata: {
      ...existingTask.metadata,
      ...metadata
    }
  };
  
  return {
    state: {
      ...state,
      a2aTasks: {
        ...(state.a2aTasks || {}),
        [id]: updatedTask
      },
      currentTaskId: id
    },
    task: updatedTask
  };
}

// Get a task by ID
export function getTask(
  state: ContentGenerationState,
  params: TaskGetParams
): A2ATask | null {
  const { id, historyLength = 0 } = params;
  const task = state.a2aTasks?.[id];
  
  if (!task) {
    return null;
  }
  
  // If historyLength is specified, return only the last N items
  if (historyLength > 0 && task.history && task.history.length > historyLength) {
    return {
      ...task,
      history: task.history.slice(-historyLength)
    };
  }
  
  return task;
}

// Cancel a task
export function cancelTask(
  state: ContentGenerationState,
  params: TaskCancelParams
): { state: ContentGenerationState; task: A2ATask | null } {
  const { id } = params;
  const task = state.a2aTasks?.[id];
  
  if (!task) {
    return { state, task: null };
  }
  
  const canceledTask: A2ATask = {
    ...task,
    status: {
      state: "canceled",
      timestamp: new Date().toISOString()
    }
  };
  
  return {
    state: {
      ...state,
      a2aTasks: {
        ...(state.a2aTasks || {}),
        [id]: canceledTask
      }
    },
    task: canceledTask
  };
}

// Update task status
export function updateTaskStatus(
  state: ContentGenerationState,
  taskId: string,
  taskState: TaskState,
  message?: A2AMessage
): { state: ContentGenerationState; task: A2ATask | null } {
  const task = state.a2aTasks?.[taskId];
  
  if (!task) {
    return { state, task: null };
  }
  
  const updatedTask: A2ATask = {
    ...task,
    status: {
      state: taskState,
      message,
      timestamp: new Date().toISOString()
    }
  };
  
  return {
    state: {
      ...state,
      a2aTasks: {
        ...(state.a2aTasks || {}),
        [taskId]: updatedTask
      }
    },
    task: updatedTask
  };
}

// Add artifact to task
export function addArtifactToTask(
  state: ContentGenerationState,
  taskId: string,
  artifact: Artifact
): { state: ContentGenerationState; task: A2ATask | null } {
  const task = state.a2aTasks?.[taskId];
  
  if (!task) {
    return { state, task: null };
  }
  
  // If the artifact has append=true, append to existing artifact
  if (artifact.append && task.artifacts && task.artifacts[artifact.index]) {
    const existingArtifact = task.artifacts[artifact.index];
    const updatedArtifact = {
      ...existingArtifact,
      parts: [...existingArtifact.parts, ...artifact.parts]
    };
    
    const updatedArtifacts = [...task.artifacts];
    updatedArtifacts[artifact.index] = updatedArtifact;
    
    const updatedTask: A2ATask = {
      ...task,
      artifacts: updatedArtifacts
    };
    
    return {
      state: {
        ...state,
        a2aTasks: {
          ...(state.a2aTasks || {}),
          [taskId]: updatedTask
        }
      },
      task: updatedTask
    };
  }
  
  // Otherwise, add as a new artifact
  const updatedTask: A2ATask = {
    ...task,
    artifacts: [...(task.artifacts || []), artifact]
  };
  
  return {
    state: {
      ...state,
      a2aTasks: {
        ...(state.a2aTasks || {}),
        [taskId]: updatedTask
      }
    },
    task: updatedTask
  };
}

// Create a text message
export function createTextMessage(role: "user" | "agent", text: string): A2AMessage {
  return {
    role,
    parts: [
      {
        type: "text",
        text
      }
    ]
  };
}

// Create a data message
export function createDataMessage(role: "user" | "agent", data: Record<string, any>): A2AMessage {
  return {
    role,
    parts: [
      {
        type: "data",
        data
      }
    ]
  };
}

// Create a text artifact
export function createTextArtifact(text: string, name?: string): Artifact {
  return {
    name,
    parts: [
      {
        type: "text",
        text
      }
    ],
    index: 0
  };
}

// Initialize agent cards in state
export function initializeAgentCards(state: ContentGenerationState): ContentGenerationState {
  return {
    ...state,
    agentCards
  };
}