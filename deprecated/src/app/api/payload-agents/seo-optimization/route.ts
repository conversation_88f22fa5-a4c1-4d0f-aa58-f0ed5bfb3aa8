// src/app/(payload)/api/agents/seo-optimization/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { A2AMessage } from '../a2atypes';

// Define the SEO optimization state interface
export interface SeoOptimizationState {
  topic: string;
  content?: any;
  keywords?: string[];
  seoScore?: number;
  optimizationSuggestions?: string[];
}

// Helper function to generate SEO optimization information based on content and keywords
async function generateSeoOptimization(content: any, keywords: string[] = []): Promise<any> {
  console.log(`Generating SEO optimization for content`);
  
  // Extract title from content if possible
  const title = content?.title || "Untitled Content";
  const topic = content?.topic || keywords[0] || "the content";
  
  return {
    optimizedContent: content, // In a real implementation, this would contain actual optimized content
    seoScore: 85,
    optimizationSuggestions: [
      "Add more internal links to related content",
      "Increase keyword density in the first paragraph",
      "Add alt text to all images"
    ],
    keywordUsage: {
      primary: keywords[0] || "main keyword",
      secondary: keywords.slice(1).join(", ") || "secondary keywords",
      distribution: "Keywords are well-distributed throughout the content"
    },
    metaDescription: `Learn everything about ${title} in this comprehensive guide.`,
    seoNotes: [
      {
        type: "improvement",
        section: "Title",
        note: "Title is well-optimized for SEO"
      },
      {
        type: "warning",
        section: "Content",
        note: "Consider adding more headings with keywords"
      }
    ]
  };
}

// Function to handle agent messages
async function handleAgentMessage(message: A2AMessage): Promise<A2AMessage> {
  console.log("SEO Optimization Agent received message:", JSON.stringify(message));
  
  // Process the message based on its task type
  const task = message.task;
  
  if (task.type === "REQUEST_INFORMATION") {
    // Handle information request
    console.log("Handling information request");
    const query = task.query || "SEO optimization";
    
    // Create response message with general SEO information
    return {
      task: {
        type: "PROVIDE_INFORMATION",
        information: {
          seoBasics: "SEO optimization involves keyword research, on-page optimization, and content quality.",
          keyFactors: ["Keyword placement", "Meta descriptions", "Header structure", "Internal linking", "Page speed"],
          bestPractices: "Use keywords naturally, create high-quality content, and ensure good user experience."
        }
      },
      source: "seo-optimization-agent",
      target: message.source
    };
  } else if (task.type === "REQUEST_FEEDBACK") {
    // Handle feedback request for content optimization
    console.log("Handling feedback request");
    
    // Extract content and keywords from the message
    const content = task.content || {};
    const keywords = task.keywords || [];
    
    // Generate SEO optimization feedback
    const optimization = await generateSeoOptimization(content, keywords);
    
    // Create response message with feedback
    return {
      task: {
        type: "PROVIDE_FEEDBACK",
        feedback: optimization
      },
      source: "seo-optimization-agent",
      target: message.source
    };
  } else if (task.type === "COLLABORATE") {
    // Handle collaboration request
    console.log("Handling collaboration request");
    
    // Extract content and keywords from the message
    const content = task.content || {};
    const keywords = task.keywords || [];
    
    // Generate SEO optimization
    const optimization = await generateSeoOptimization(content, keywords);
    
    // Create response message with collaboration results
    return {
      task: {
        type: "COLLABORATION_RESULT",
        result: optimization
      },
      source: "seo-optimization-agent",
      target: message.source
    };
  } else {
    // Handle unknown task type
    console.log("Unknown task type:", task.type);
    
    // Create error response message
    return {
      task: {
        type: "ERROR",
        error: `Unsupported task type: ${task.type}`
      },
      source: "seo-optimization-agent",
      target: message.source
    };
  }
}

// API route handlers
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "SEO Optimization Agent", 
    status: "active",
    capabilities: ["seo-optimization", "content-scoring", "keyword-optimization"]
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("SEO Optimization Agent received POST request:", JSON.stringify(body));
    
    // Extract the message from the request body
    const { message, state } = body;
    console.log("SEO Optimization Agent received message:", JSON.stringify(message));
    
    // Adapt the message for the legacy handler
    // The legacy handler expects the message to be directly passed, not nested
    const adaptedMessage = {
      ...message,
      task: message.task || {
        type: message.type === 'REQUEST_FEEDBACK' ? 'REQUEST_FEEDBACK' : 'REQUEST_INFORMATION',
        query: typeof message.content === 'object' ? 
          (message.content.query || JSON.stringify(message.content)) : 
          message.content,
        content: message.content
      }
    };
    
    // Handle the message
    const response = await handleAgentMessage(adaptedMessage);
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in SEO Optimization Agent POST handler:", error);
    return NextResponse.json(
      { error: "Internal server error", details: (error as Error).message },
      { status: 500 }
    );
  }
}
