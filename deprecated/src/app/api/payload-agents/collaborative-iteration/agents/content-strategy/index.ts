// src/app/(payload)/api/agents/collaborative-iteration/agents/content-strategy/index.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeCollaborationState,
  StandardizedHandlerResult,
  AgentId,
  IterativeMessageType,
  ArtifactStatus,
  Goal
} from '../../types';
import { AgentBase } from '../../core/AgentBase';
import { AgentHandler } from '../../core/AgentHandler';
import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';
import { stateStore } from '../../utils/stateStore';
import logger from '../../utils/logger';

// Import the correct handlers
import {
  handleInitialRequest,
  handleArtifactRequest,
  handleFeedback,
  handleConsultationRequest,
  handleArtifactDelivery,
  handleDiscussionStart,
  handleDiscussionContribution,
  handleDiscussionSynthesisRequest
} from './handlers';

/**
 * Content Strategy Agent
 *
 * This agent is responsible for developing content strategy outlines
 * and collaborating with other agents to optimize content creation.
 */
export class ContentStrategyAgent extends AgentBase {
  /**
   * Constructor initializes the agent with required handlers
   * and sets up message handling
   */
  constructor() {
    super('content-strategy' as AgentId);
    logger.info('Content Strategy Agent initialized');
  }

  /**
   * Process a goal assigned to this agent
   * Override the base implementation to handle specific goal types
   */
  protected async processGoal(sessionId: string, goal: Goal): Promise<void> {
    logger.info(`Content Strategy Agent: Processing goal - ${goal.name}`, {
      sessionId,
      goalId: goal.id,
      agent: this.agentId
    });

    try {
      // Different handling based on goal type
      if (goal.type === 'create-content-strategy') {
        // Create a content strategy artifact
        const { topic, contentType, targetAudience, tone, keywords } = goal.metadata || {};

        // Create a message to handleInitialRequest
        const message: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'orchestrator',
          to: this.agentId,
          type: IterativeMessageType.INITIAL_REQUEST,
          content: {
            topic,
            contentType,
            targetAudience,
            tone,
            keywords
          },
          conversationId: sessionId
        };

        // Process the message
        await this.processMessage(sessionId, message);

        // Mark the goal as completed
        await stateStore.updateState(sessionId, (currentState) => {
          const goals = currentState.goals || [];
          const goalIndex = goals.findIndex(g => g.id === goal.id);
          if (goalIndex >= 0) {
            goals[goalIndex].status = 'completed';
            goals[goalIndex].completedAt = new Date().toISOString();
          }
          return {
            ...currentState,
            goals
          };
        });
      } else {
        // For other goal types, use the default implementation
        await super.processGoal(sessionId, goal);
      }
    } catch (error) {
      logger.error(`Error processing goal ${goal.id}:`, {
        sessionId,
        goalId: goal.id,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });

      // Mark the goal as blocked
      await stateStore.updateState(sessionId, (currentState) => {
        const goals = currentState.goals || [];
        const goalIndex = goals.findIndex(g => g.id === goal.id);
        if (goalIndex >= 0) {
          goals[goalIndex].status = 'blocked';
          goals[goalIndex].blockReason = error instanceof Error ? error.message : String(error);
        }
        return {
          ...currentState,
          goals
        };
      });
    }
  }

  /**
   * Register all message handlers
   * Implementation of the abstract method from AgentBase
   */
  protected registerHandlers(): void {
    // Register handlers for different message types
    this.handler.registerHandler(IterativeMessageType.INITIAL_REQUEST, handleInitialRequest);
    // Also handle REQUEST type with the same handler for consistent workflow messaging
    this.handler.registerHandler(IterativeMessageType.REQUEST, handleInitialRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_REQUEST, handleArtifactRequest);
    this.handler.registerHandler(IterativeMessageType.FEEDBACK, handleFeedback);
    this.handler.registerHandler(IterativeMessageType.CONSULTATION_REQUEST, handleConsultationRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_DELIVERY, handleArtifactDelivery);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_START, handleDiscussionStart);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_CONTRIBUTION, handleDiscussionContribution);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_SYNTHESIS_REQUEST, handleDiscussionSynthesisRequest);
    this.handler.registerHandler(IterativeMessageType.UPDATE, this.handleUpdateMessage.bind(this));
  }

  /**
   * Handle UPDATE message type
   */
  private async handleUpdateMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    logger.info(`Content Strategy Agent: Handling UPDATE message from ${message.from}`, {
      messageId: message.id,
      from: message.from
    });

    // Simply acknowledge the update
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `Content Strategy Agent acknowledged the update`,
        status: 'success'
      },
      message.conversationId,
      message.id
    );

    return {
      success: true,
      message: 'Update acknowledged',
      error: null,
      response
    };
  }
}

// Create and export singleton instance
export const contentStrategyAgent = new ContentStrategyAgent();
