// src/app/(payload)/api/agents/collaborative-iteration/agents/content-strategy/handlers.ts

import { v4 as uuidv4 } from 'uuid';
import { IterativeMessageType } from '../../types';
import { AgentIdEnum } from '../../utils/agentTypes';
import type {
  IterativeMessage,
  IterativeCollaborationState,
  ConsultationRequest,
  ConsultationResponse,
  IterativeArtifact,
  Consultation,
  ArtifactStatus,
  StandardizedHandlerResult
} from '../../types';

// Import Reasoning from the a2atypes module
import { Reasoning } from '../../../a2atypes';
import { storeAgentReasoning, createEnhancedReasoning } from '../../utils/reasoningUtils';

// Import additional modules
import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';

// Define the agent ID constant
const AGENT_ID = 'content-strategy' as const;
type AgentId = 'market-research' | 'seo-keyword' | 'content-strategy' | 'content-generation' | 'seo-optimization' | 'orchestrator' | 'human';

/**
 * Ensure correct type usage by creating a type guard
 */
function isIterativeArtifact(obj: any): obj is IterativeArtifact {
  return obj && typeof obj === 'object' && 'id' in obj && 'type' in obj;
}

// Update StandardizedHandlerResult to include artifact property
interface EnhancedHandlerResult extends StandardizedHandlerResult {
  artifact?: IterativeArtifact;
}
import {
  createContentStrategyArtifact,
  provideContentStrategyConsultation,
  analyzeContentStrategyQuestion,
  generateContentStrategyConsiderations,
  calculateConfidenceScore,
  convertEnhancedReasoningToReasoning,
  generateRandomInsight,
  EnhancedReasoning
} from './methods';

// Use the EnhancedReasoning type from methods.ts

// State management extension for reasoning storage
interface ExtendedAgentStateManager extends AgentStateManager {
  storeAgentReasoning: (sessionId: string, reasoning: EnhancedReasoning, fromAgentId: AgentId, artifactId?: string) => Promise<boolean>;
}

// Extended collaboration state
interface ExtendedCollaborationState extends IterativeCollaborationState {
  sessionId: string;
}

// Export all handlers for the Content Strategy agent
// This file consolidates handlers from the previous handlers.ts, handlers2.ts, handlers3.ts, and handlers4.ts files

// Adding implementations for all the required handlers:
// - handleInitialRequest (already in this file)
// - handleArtifactRequest (already in this file)
// - handleFeedback (from handlers2.ts)
// - handleConsultationRequest (from handlers2.ts)
// - handleArtifactDelivery (from handlers3.ts)
// - handleDiscussionStart (from handlers3.ts)
// - handleDiscussionContribution (from handlers4.ts)
// - handleDiscussionSynthesisRequest (from handlers4.ts)

/**
 * Handles initial requests to generate a content strategy
 */
export async function handleInitialRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<EnhancedHandlerResult> {
  console.log('Content Strategy Agent: Handling initial request');

  try {
    // Extract request details
    const { topic, contentType, targetAudience, tone, keywords = [] } = message.content || {};

    if (!topic) {
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Missing required parameter: topic',
          message: 'A topic is required to generate a content strategy.'
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: false,
        message: 'Missing required parameter: topic',
        error: 'Missing topic',
        response: errorResponse
      };
    }

    // If there's no sectionContent, replace it with an empty string
    const sectionContent = message.content?.sectionContent || '';

    // Initialize the agent's state if needed
    const agentState = state.agentStates?.[AGENT_ID] || {
      id: AGENT_ID,
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };

    // Update state with this processed message
    if (!agentState.processedRequests) {
      agentState.processedRequests = [];
    }
    agentState.processedRequests.push(message.id);
    agentState.lastUpdated = new Date().toISOString();

    // Create artifact for the content strategy using helper method
    const artifact = createContentStrategyArtifact(
      topic,
      contentType,
      targetAudience,
      tone,
      keywords,
      AGENT_ID
    ) as IterativeArtifact;

    // Add the artifact to state and track it in generatedArtifacts array
    await stateManager.trackNewArtifact(message.conversationId, artifact);

    // Update the agent state with the new artifact ID
    if (!agentState.generatedArtifacts) {
      agentState.generatedArtifacts = [];
    }
    agentState.generatedArtifacts.push(artifact.id);

    // Update the state with the updated agent state
    await stateManager.updateState(message.conversationId, (currentState) => {
      return {
        ...currentState,
        agentStates: {
          ...currentState.agentStates,
          [AGENT_ID]: agentState
        },
        // Update workflowProgress to indicate content strategy is complete
        workflowProgress: {
          ...currentState.workflowProgress,
          contentStrategyComplete: true
        }
      };
    });

    // Create response message
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: `Generated content strategy for "${topic}"`,
        artifactId: artifact.id,
        artifactType: artifact.type,
        data: artifact.content,
        artifact: artifact,
        reasoning: convertEnhancedReasoningToReasoning(artifact.metadata?.reasoning)
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: true,
      message: `Content strategy for "${topic}" successfully generated`,
      error: undefined,
      response: responseMessage,
      artifact,
      artifactUpdates: {
        new: {
          [artifact.id]: artifact
        }
      }
    };
  } catch (error: any) {
    console.error('Error handling initial request:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Failed to process initial request: ${error.message}`,
        message: 'An error occurred while processing the initial request.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: false,
      message: 'Failed to process initial request',
      error: error.message,
      response: errorResponse
    };
  }
}

/**
 * Handles feedback from other agents
 */
export async function handleFeedback(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<EnhancedHandlerResult> {
  console.log('Content Strategy Agent: Handling feedback');

  try {
    // Check if this is an evaluation feedback message
    if (message.content?.evaluation) {
      try {
        // Import the feedback handler
        const { processFeedback } = await import('../../utils/feedback-handler');

        // Process feedback using the common feedback handler
        const result = await processFeedback(
          message,
          state,
          stateManager,
          messaging,
          AGENT_ID
        );

        // If an artifact was updated, return it
        if (result.updatedArtifact) {
          return {
            success: true,
            message: 'Successfully processed evaluation feedback',
            error: undefined,
            response: result.response,
            artifact: result.updatedArtifact
          };
        }

        return {
          success: true,
          message: 'Successfully processed evaluation feedback',
          error: undefined,
          response: result.response
        };
      } catch (error) {
        console.error('Error processing evaluation feedback:', error);

        // Fall back to standard feedback handling if there's an error
        const errorResponse: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          type: IterativeMessageType.ERROR,
          from: AGENT_ID,
          to: message.from,
          content: {
            error: 'Error processing evaluation feedback',
            message: `An error occurred while processing the evaluation feedback: ${error instanceof Error ? error.message : String(error)}`
          },
          conversationId: message.conversationId
        };

        return {
          success: false,
          message: 'Failed to process evaluation feedback',
          error: error instanceof Error ? error.message : String(error),
          response: errorResponse
        };
      }
    }

    // Handle traditional feedback format
    const { feedback, artifactId, consultationId } = message.content || {};

    if (!artifactId && !consultationId) {
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Missing required parameters',
          message: 'Either artifactId or consultationId is required for feedback.'
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: false,
        message: 'Missing required parameters',
        error: 'Missing artifactId or consultationId',
        response: errorResponse
      };
    }

    // If feedback is for an artifact, update the artifact with the feedback
    if (artifactId && state.artifacts && state.artifacts[artifactId]) {
      const artifact = state.artifacts[artifactId];

      // Only process if this is our artifact
      if (artifact.createdBy === AGENT_ID) {
        // Update the artifact with the feedback
        const updatedArtifact = {
          ...artifact,
          metadata: {
            ...artifact.metadata,
            feedback: feedback || '',
            feedbackFrom: message.from,
            feedbackAt: new Date().toISOString()
          }
        };

        // Store the updated artifact
        await stateManager.updateArtifact(message.conversationId, updatedArtifact);

        // Create acknowledgment response
        const responseMessage: IterativeMessage = {
          id: uuidv4(),
          type: IterativeMessageType.ACKNOWLEDGMENT,
          from: AGENT_ID,
          to: message.from,
          content: {
            message: 'Feedback acknowledged and incorporated',
            artifactId,
            action: 'Feedback incorporated into content strategy'
          },
          timestamp: new Date().toISOString(),
          conversationId: message.conversationId,
          inReplyTo: message.id
        };

        return {
          success: true,
          message: 'Successfully processed feedback',
          error: undefined,
          response: responseMessage,
          artifact: updatedArtifact
        };
      }
    }

    // If feedback is for a consultation, just acknowledge it
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ACKNOWLEDGMENT,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: 'Feedback acknowledged',
        consultationId,
        action: 'Feedback will be used to improve future consultations'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: true,
      message: 'Successfully acknowledged feedback',
      error: undefined,
      response: responseMessage
    };
  } catch (error: any) {
    console.error('Error handling feedback:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Failed to process feedback: ${error.message}`,
        message: 'An error occurred while processing the feedback.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: false,
      message: 'Failed to process feedback',
      error: error.message,
      response: errorResponse
    };
  }
}

/**
 * Handles consultation requests from other agents
 */
export async function handleConsultationRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<EnhancedHandlerResult> {
  console.log('Content Strategy Agent: Handling consultation request');

  try {
    // Extract request details
    const { question, context, topic, artifactIds = [] } = message.content || {};

    if (!question) {
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Missing required parameter: question',
          message: 'A question is required for content strategy consultation.'
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: false,
        message: 'Missing required parameter: question',
        error: 'Missing question',
        response: errorResponse
      };
    }

    // Build a context object for the consultation
    const consultationContext = {
      topic: topic || '',
      artifactIds,
      fromAgent: message.from,
      additionalContext: context || {}
    };

    // Analyze the question to determine how to respond
    const analysis = await analyzeContentStrategyQuestion(question, consultationContext);

    // Generate consultation response
    const consultationResult = await provideContentStrategyConsultation(question, consultationContext);

    // Calculate confidence score based on analysis
    const confidenceScore = calculateConfidenceScore(
      consultationResult.confidence,
      consultationContext ? 'adequate' : 'limited',
      analysis.complexity
    );

    // Build considerations array
    const considerations = await generateContentStrategyConsiderations(analysis.type, consultationContext);

    // Create reasoning structure
    const reasoning: EnhancedReasoning = {
      context: consultationContext,
      considerations: considerations,
      decision: consultationResult.response,
      confidence: confidenceScore,
      timestamp: new Date().toISOString(),
      agentId: AGENT_ID,
      insights: [generateRandomInsight('general')],
      supportingEvidence: consultationResult.principles,
      process: 'Content Strategy Consultation',
      steps: [
        'Analyze consultation request',
        'Evaluate context and requirements',
        'Apply content strategy principles',
        'Generate recommendations'
      ],
      conclusion: consultationResult.response,
      thoughts: consultationResult.recommendations.map(r => r.suggestion)
    };

    // Store reasoning in conversation history using the global utility
    try {
      // Convert to required format before storing
      const formattedReasoning = {
        process: reasoning.process || 'Content Strategy Analysis',
        decision: reasoning.decision,
        confidence: reasoning.confidence,
        considerations: reasoning.considerations,
        supportingEvidence: reasoning.supportingEvidence || [],
        timestamp: reasoning.timestamp
      };

      await storeAgentReasoning(
        message.conversationId,  // sessionId
        AGENT_ID,                // agentId
        null,                    // artifact (optional)
        message.id,              // messageId
        message.conversationId,  // conversationId
        formattedReasoning as any // reasoning
      );
    } catch (reasoningError) {
      console.warn('Failed to store agent reasoning:', reasoningError);
      // Continue execution even if reasoning storage fails
    }

    // Create response message
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.CONSULTATION_RESPONSE,
      from: AGENT_ID,
      to: message.from,
      content: {
        question,
        response: consultationResult.response,
        confidence: confidenceScore,
        reasoning: convertEnhancedReasoningToReasoning(reasoning)
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: true,
      message: 'Successfully provided content strategy consultation',
      error: undefined,
      response: responseMessage
    };
  } catch (error: any) {
    console.error('Error handling consultation request:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Failed to process consultation request: ${error.message}`,
        message: 'An error occurred while processing the consultation request.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: false,
      message: 'Failed to process consultation request',
      error: error.message,
      response: errorResponse
    };
  }
}

/**
 * Handles artifact delivery from other agents
 */
export async function handleArtifactDelivery(
  message: IterativeMessage,
  state: ExtendedCollaborationState,
  stateManager: ExtendedAgentStateManager,
  messaging: AgentMessaging
): Promise<EnhancedHandlerResult> {
  console.log('===========================================================');
  console.log(`[CONTENT_STRATEGY_AGENT] handleArtifactDelivery: START - From ${message.from}`);
  console.log(`[CONTENT_STRATEGY_AGENT] Message ID: ${message.id}, Type: ${message.type}`);
  console.log(`[CONTENT_STRATEGY_AGENT] State topic: ${state.topic || 'undefined'}`);
  console.log(`[CONTENT_STRATEGY_AGENT] Message content keys: ${message.content ? Object.keys(message.content).join(', ') : 'none'}`);

  try {
    // Extract the artifact from the message
    const artifact = message.content?.artifact as IterativeArtifact;
    const artifactId = message.artifactId || message.content?.artifactId;

    console.log(`[CONTENT_STRATEGY_AGENT] Artifact present: ${!!artifact}, Artifact ID: ${artifactId || 'none'}`);

    if (!artifact) {
      console.error('[CONTENT_STRATEGY_AGENT] No artifact found in the delivery message');
      console.log('[CONTENT_STRATEGY_AGENT] Full message content:', JSON.stringify(message.content));
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'No artifact found in the delivery message',
          originalMessage: message
        },
        conversationId: message.conversationId
      };

      console.log('[CONTENT_STRATEGY_AGENT] Returning error response: No artifact found');
      return {
        success: false,
        message: 'Failed to process artifact delivery: no artifact found',
        error: 'No artifact found in the delivery message',
        response: errorResponse
      };
    }

    console.log(`[CONTENT_STRATEGY_AGENT] Processing artifact of type ${artifact.type} from ${message.from}`);
    console.log(`[CONTENT_STRATEGY_AGENT] Artifact details: ID=${artifact.id}, Name=${artifact.name}, CreatedBy=${artifact.createdBy}`);

    if (artifact.data) {
      console.log(`[CONTENT_STRATEGY_AGENT] Artifact data keys: ${Object.keys(artifact.data).join(', ')}`);
    }

    // Process the SEO keyword data to develop a content strategy
    // In reality, this would analyze the keywords, audience data, and market research
    // to develop a comprehensive content strategy

    // Create reasoning process for the artifact
    console.log(`[CONTENT_STRATEGY_AGENT] Creating enhanced reasoning trail`);
    const reasoning = createEnhancedReasoning(
      { artifactId: artifact.id, artifactType: artifact.type },
      [
        'Analyzed SEO keywords to identify content opportunities',
        'Determined optimal content structure based on audience needs',
        'Developed strategic recommendations for content creation'
      ],
      'Integrated SEO keyword data into comprehensive content strategy',
      AGENT_ID,
      { confidence: 0.85 }
    );

    // Add thoughts to meet the Reasoning type requirements
    const reasoningWithThoughts = {
      ...reasoning,
      thoughts: [
        'Analyzing SEO keyword data for content optimization',
        'Evaluating content structure based on keyword insights',
        'Formulating content strategy recommendations'
      ],
      considerations: reasoning.considerations || [],
      confidence: reasoning.confidence || 0.85
    };

    // Create a content strategy artifact
    const contentStrategyId = uuidv4();
    console.log(`[CONTENT_STRATEGY_AGENT] Creating content strategy artifact with ID: ${contentStrategyId}`);
    const contentStrategyArtifact: IterativeArtifact = {
      id: contentStrategyId,
      name: `Content Strategy for ${state.topic || 'Content'}`,
      type: 'content-strategy',
      status: 'completed' as ArtifactStatus,
      createdBy: AGENT_ID,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),

      metadata: {
        source: artifactId,
        topic: state.topic,
        contentType: state.contentType,
        targetAudience: state.targetAudience,
        recommendedLength: '1500-2000 words'
      },
      data: {
        contentOutline: [
          {
            section: 'Introduction',
            purpose: 'Hook the reader and establish the problem or topic',
            keyPoints: [
              'Establish relevance to target audience',
              'Introduce key problems or challenges',
              'Hint at solutions or benefits'
            ],
            recommendedLength: '200-300 words',
            targetKeywords: ['introductory keywords']
          },
          {
            section: 'Background/Context',
            purpose: 'Provide necessary context and background information',
            keyPoints: [
              'Explain relevant history or development',
              'Define key concepts and terminology',
              'Present current landscape or state of affairs'
            ],
            recommendedLength: '300-400 words',
            targetKeywords: ['contextual keywords']
          },
          {
            section: 'Main Content',
            purpose: 'Deliver the core value and information',
            keyPoints: [
              'Present key arguments or information points',
              'Support with evidence, examples, or data',
              'Address potential questions or objections'
            ],
            recommendedLength: '800-1000 words',
            targetKeywords: ['primary keywords', 'secondary keywords']
          },
          {
            section: 'Practical Application',
            purpose: 'Show how the information can be applied',
            keyPoints: [
              'Provide actionable steps or advice',
              'Include examples or case studies',
              'Offer tools or resources'
            ],
            recommendedLength: '300-400 words',
            targetKeywords: ['application keywords', 'how-to keywords']
          },
          {
            section: 'Conclusion',
            purpose: 'Summarize and provide next steps',
            keyPoints: [
              'Recap key points and benefits',
              'Provide final thoughts or recommendations',
              'Include call to action'
            ],
            recommendedLength: '150-200 words',
            targetKeywords: ['concluding keywords']
          }
        ],
        contentGoals: [
          'Educate the audience about the topic',
          'Establish authority in the subject matter',
          'Drive engagement and sharing',
          'Generate leads or conversions'
        ],
        toneAndStyle: {
          tone: state.tone || 'Professional',
          style: 'Informative yet conversational',
          brandVoice: 'Expert, helpful, approachable',
          readingLevel: 'College-educated but accessible'
        },
        keywords: {
          primary: ['main keyword 1', 'main keyword 2'],
          secondary: ['related keyword 1', 'related keyword 2', 'related keyword 3'],
          semanticKeywords: ['semantic keyword 1', 'semantic keyword 2']
        }
      },

      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: AGENT_ID,
          content: 'Initial content strategy based on keyword research',
          feedback: [],
          incorporatedConsultations: [],
          changes: 'Initial creation',
          reasoning: reasoningWithThoughts
        }
      ],
      qualityScore: 87
    };

    // Send the content strategy to the Content Generation agent (forward the workflow)
    const messageId = uuidv4();
    console.log(`[CONTENT_STRATEGY_AGENT] Creating artifact delivery message with ID: ${messageId}`);
    console.log(`[CONTENT_STRATEGY_AGENT] Sending content strategy to content-generation agent`);

    const response: IterativeMessage = {
      id: messageId,
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      from: AGENT_ID,
      to: 'content-generation', // Forward to the next agent in the workflow
      content: {
        artifact: contentStrategyArtifact,
        message: `Completed content strategy for ${state.topic || 'content'} based on keyword research.`,
        outline: contentStrategyArtifact.data.contentOutline.map((section: { section: string }) => section.section).join(', '),
        sections: contentStrategyArtifact.data.contentOutline.map((section: { title: string; content: string }) => {
          return `${section.title}: ${section.content}`;
        }).join('\n\n') || ''
      },
      artifactId: contentStrategyArtifact.id,
      conversationId: message.conversationId
    };

    console.log(`[CONTENT_STRATEGY_AGENT] handleArtifactDelivery: SUCCESS - Generated response to content-generation`);
    console.log(`[CONTENT_STRATEGY_AGENT] Response message ID: ${response.id}`);
    console.log(`[CONTENT_STRATEGY_AGENT] Response artifact ID: ${response.artifactId}`);
    console.log(`[CONTENT_STRATEGY_AGENT] Response receiver: ${response.to}`);
    console.log('===========================================================');

    // Add the artifact to state and track it in generatedArtifacts array
    await stateManager.trackNewArtifact(state.id, contentStrategyArtifact);

    // Update workflowProgress to indicate content strategy is complete
    await stateManager.updateState(state.id, (currentState) => {
      return {
        ...currentState,
        workflowProgress: {
          ...currentState.workflowProgress,
          contentStrategyComplete: true
        }
      };
    });

    return {
      success: true,
      message: 'Successfully processed keyword research and generated content strategy',
      error: null,
      response,
      artifact: contentStrategyArtifact,
      artifactUpdates: {
        new: {
          [contentStrategyArtifact.id]: contentStrategyArtifact
        }
      }
    };
  } catch (error: any) {
    console.error('[CONTENT_STRATEGY_AGENT] Error handling artifact delivery:', error);
    console.error('[CONTENT_STRATEGY_AGENT] Error stack:', error.stack);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Failed to process artifact delivery: ${error.message}`,
        message: 'An error occurred while processing the artifact delivery.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    console.log(`[CONTENT_STRATEGY_AGENT] handleArtifactDelivery: ERROR - ${error.message}`);
    console.log('===========================================================');

    return {
      success: false,
      message: 'Failed to process artifact delivery',
      error: error.message,
      response: errorResponse
    };
  }
}

/**
 * Handles discussion start requests
 */
export async function handleDiscussionStart(
  message: IterativeMessage,
  state: ExtendedCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<EnhancedHandlerResult> {
  console.log('Content Strategy Agent: Handling discussion start');

  try {
    // Extract discussion details
    const { topic, participants, context } = message.content || {};

    if (!topic) {
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Missing discussion topic',
          message: 'A topic is required to start a discussion.'
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: false,
        message: 'Missing discussion topic',
        error: 'Missing topic',
        response: errorResponse
      };
    }

    console.log(`Starting discussion on: ${topic}`);

    // Apply structured reasoning to analyze the topic and generate strategic insights
    const discussionContext = {
      topic,
      participants: participants || [],
      additionalContext: context || {},
      sessionId: state.sessionId
    };

    // Generate content strategy considerations using structured reasoning
    const considerations = [
      'Content pillar alignment and strategic fit',
      'Target audience needs and preferences',
      'Content format and distribution channel effectiveness',
      'Success metrics and performance indicators',
      'Content lifecycle and maintenance requirements',
      'Brand voice consistency and messaging alignment'
    ];

    // Create an enhanced reasoning object for the content strategy
    const reasoning = {
      considerations: considerations,
      decision: `Focus on creating a comprehensive ${state.contentType} about ${topic} for ${state.targetAudience} using a ${state.tone} tone.`,
      confidence: 0.85,
      thoughts: [
        'Analyzing audience demographics',
        'Evaluating content format options',
        'Considering distribution channels',
        'Planning content calendar',
        'Optimizing for SEO requirements'
      ]
    };

    // Store the reasoning in the state for future reference
    if (state.sessionId) {
      // Create a proper EnhancedReasoning object for storeAgentReasoning
      const enhancedReasoning: EnhancedReasoning = {
        context: {},
        considerations: reasoning.considerations,
        decision: reasoning.decision,
        confidence: reasoning.confidence,
        timestamp: new Date().toISOString(),
        agentId: AGENT_ID,
        insights: [],
        supportingEvidence: [],
        process: '',
        steps: [],
        thoughts: reasoning.thoughts || []
      };

      await storeAgentReasoning(
        state.sessionId,
        AGENT_ID,
        "",  // Empty string instead of null
        message.id,
        message.conversationId,
        enhancedReasoning
      );
    }

    // Create a response with content strategy perspective informed by structured reasoning
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
      from: AGENT_ID,
      to: message.from,
      content: {
        discussionId: uuidv4(), // In a real implementation, this would be stored in state
        contribution: 'From a content strategy perspective, here are my initial thoughts:',
        insights: [],
        considerations: reasoning.considerations,
        reasoning: reasoning.decision,
        topic
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: true,
      message: 'Discussion started with content strategy perspective using structured reasoning',
      error: undefined,
      response: responseMessage
    };
  } catch (error: any) {
    console.error('Error handling discussion start:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Failed to start discussion: ${error.message}`,
        message: 'An error occurred while starting the discussion.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: false,
      message: 'Failed to start discussion',
      error: error.message,
      response: errorResponse
    };
  }
}

/**
 * Handles discussion contributions from other agents
 */
export async function handleDiscussionContribution(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<EnhancedHandlerResult> {
  console.log('Content Strategy Agent: Handling discussion contribution');

  try {
    // Extract contribution details
    const { discussionId, contribution } = message.content || {};

    if (!contribution) {
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Missing contribution content',
          message: 'Contribution content is required for discussion.'
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: false,
        message: 'Missing contribution content',
        error: 'Missing contribution',
        response: errorResponse
      };
    }

    // In a real implementation, we would update the discussion in state
    // and possibly respond with additional insights based on the contribution
    console.log(`Processing discussion contribution for discussion: ${discussionId}`);

    // Determine if we should respond with another contribution
    // This logic would be more sophisticated in a real implementation
    const shouldRespond = Math.random() > 0.5;

    if (shouldRespond) {
      // Generate response contribution
      // This is a placeholder for more contextual content strategy insights
      const insights = [
        generateRandomInsight('alignment'),
        generateRandomInsight('audience')
      ];

      // Create a response with additional content strategy perspective
      const responseMessage: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
        from: AGENT_ID,
        to: message.from,
        content: {
          discussionId,
          contribution: 'Building on your points, from a content strategy perspective:',
          insights,
          inResponseTo: message.id
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: true,
        message: 'Responded to discussion contribution',
        error: undefined,
        response: responseMessage
      };
    } else {
      // Just acknowledge the contribution without a substantive response
      const responseMessage: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ACKNOWLEDGMENT,
        from: AGENT_ID,
        to: message.from,
        content: {
          message: 'Thank you for your contribution to the discussion.',
          acknowledgment: 'Contribution received',
          discussionId
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: true,
        message: 'Acknowledged discussion contribution',
        error: undefined,
        response: responseMessage
      };
    }
  } catch (error: any) {
    console.error('Error handling discussion contribution:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Failed to process discussion contribution: ${error.message}`,
        message: 'An error occurred while processing the discussion contribution.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: false,
      message: 'Failed to process discussion contribution',
      error: error.message,
      response: errorResponse
    };
  }
}

/**
 * Handles discussion synthesis request
 */
export async function handleDiscussionSynthesisRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<EnhancedHandlerResult> {
  console.log('Content Strategy Agent: Handling discussion synthesis request');

  try {
    // Extract synthesis details
    const { discussionId, topic } = message.content || {};

    if (!discussionId && !topic) {
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Missing discussion identifier',
          message: 'Either a discussionId or topic is required for synthesis.'
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: false,
        message: 'Missing discussion identifier',
        error: 'Missing discussionId or topic',
        response: errorResponse
      };
    }

    // In a real implementation, we would find the discussion in state
    // and synthesize the contributions from a content strategy perspective
    console.log(`Synthesizing discussion: ${discussionId || topic}`);

    // Generate synthesis content
    // This is a placeholder for a more sophisticated synthesis
    const insights = [
      generateRandomInsight('alignment'),
      generateRandomInsight('audience'),
      generateRandomInsight('format'),
      generateRandomInsight('distribution')
    ];

    // Create a response with content strategy synthesis
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.DISCUSSION_SYNTHESIS,
      from: AGENT_ID,
      to: message.from,
      content: {
        discussionId: discussionId || 'topic-based-discussion',
        synthesis: 'From a content strategy perspective, here is a synthesis of the discussion:',
        keyPoints: [
          'Content should be aligned with strategic business goals',
          'Target audience needs and preferences should drive content format choices',
          'Distribution channels should be selected based on audience behavior',
          'Measurement framework should track both engagement and conversion metrics'
        ],
        recommendations: [
          'Develop a content pillar structure to organize topic areas',
          'Create a content calendar to ensure consistent publishing',
          'Establish clear success metrics for each content piece',
          'Implement a feedback loop to continuously improve content strategy'
        ],
        insights,
        topic: topic || 'Discussion topic'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: true,
      message: 'Discussion synthesized from content strategy perspective',
      error: undefined,
      response: responseMessage
    };
  } catch (error: any) {
    console.error('Error handling discussion synthesis request:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Failed to synthesize discussion: ${error.message}`,
        message: 'An error occurred while synthesizing the discussion.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: false,
      message: 'Failed to synthesize discussion',
      error: error.message,
      response: errorResponse
    };
  }
}

/**
 * Handles requests for content strategy artifacts
 */
export async function handleArtifactRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<EnhancedHandlerResult> {
  console.log('Content Strategy Agent: Handling artifact request');

  try {
    // Extract request details
    const { artifactType, topic, contentType, targetAudience, tone, keywords = [] } = message.content || {};

    if (!topic) {
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Missing required parameter: topic',
          message: 'A topic is required to generate a content strategy.'
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: false,
        message: 'Missing required parameter: topic',
        error: 'Missing topic',
        response: errorResponse
      };
    }

    // Check if we already have a matching artifact
    let existingArtifact: IterativeArtifact | undefined;

    if (state.artifacts) {
      const artifactEntries = Object.entries(state.artifacts);
      for (const [id, artifact] of artifactEntries) {
        if (isIterativeArtifact(artifact) &&
            artifact.type === (artifactType || 'content-strategy') &&
            artifact.metadata?.topic === topic) {
          existingArtifact = artifact;
          break;
        }
      }
    }

    if (existingArtifact) {
      // Return the existing artifact
      const responseMessage: IterativeMessage = {
        id: uuidv4(),
        type: IterativeMessageType.ARTIFACT_DELIVERY,
        from: AGENT_ID,
        to: message.from,
        content: {
          message: `Retrieved existing content strategy for "${topic}"`,
          artifactId: existingArtifact.id,
          artifactType: existingArtifact.type,
          data: existingArtifact.content,
          reasoning: convertEnhancedReasoningToReasoning(existingArtifact.metadata?.reasoning)
        },
        timestamp: new Date().toISOString(),
        conversationId: message.conversationId,
        inReplyTo: message.id
      };

      return {
        success: true,
        message: `Retrieved existing content strategy for "${topic}"`,
        error: undefined,
        response: responseMessage,
        artifact: existingArtifact
      };
    }

    // Generate strategy content based on the topic and other parameters
    const strategyContent = `Content strategy for ${topic} targeting ${targetAudience || 'general audience'} with ${tone || 'neutral'} tone.\n\nKey focus areas:\n- Audience engagement\n- Content distribution\n- Performance measurement\n\nRecommended content types:\n- Blog posts\n- Social media updates\n- Email newsletters`;

    // Create a new content strategy artifact based on the synthesized discussion
    const artifact: IterativeArtifact = {
      id: uuidv4(),
      name: `Content Strategy for ${topic}`,
      type: 'content-strategy',
      status: 'completed' as ArtifactStatus,
      content: strategyContent,
      metadata: {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords,
        generatedAt: new Date().toISOString()
      },
      createdBy: AGENT_ID,
      createdAt: new Date().toISOString(),
      currentVersion: 1,
      iterations: [],
      qualityScore: 0.8
    };

    // Add the artifact to state and track it in generatedArtifacts array
    await stateManager.trackNewArtifact(message.conversationId, artifact);

    // Update workflowProgress to indicate content strategy is complete
    await stateManager.updateState(message.conversationId, (currentState) => {
      return {
        ...currentState,
        workflowProgress: {
          ...currentState.workflowProgress,
          contentStrategyComplete: true
        }
      };
    });

    // Create response message
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: `Generated content strategy for "${topic}"`,
        artifactId: artifact.id,
        artifactType: artifact.type,
        data: artifact.content,
        reasoning: artifact.metadata?.reasoning ? convertEnhancedReasoningToReasoning(artifact.metadata.reasoning) : undefined
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: true,
      message: `Content strategy for "${topic}" successfully generated`,
      error: undefined,
      response: responseMessage,
      artifact
    };
  } catch (error: any) {
    console.error('Error handling artifact request:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Failed to process artifact request: ${error.message}`,
        message: 'An error occurred while processing the artifact request.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    return {
      success: false,
      message: 'Failed to process artifact request',
      error: error.message,
      response: errorResponse
    };
  }
}

