// src/app/(payload)/api/agents/collaborative-iteration/agents/seo-optimization/methods.ts

import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';
import {
  IterativeArtifact,
  AgentId,
} from '../../types';
import {
  EnhancedReasoning,
  createEnhancedReasoning,
  validateReasoning,
  crossValidateReasoning
} from '../../utils/reasoningUtils';
import { optimizeForSeo, SeoOptimizationResult } from '../../utils/seo-optimization';
import { calculateContentQualityMetrics } from '../../utils/content-quality-metrics';
import { assessArtifactQuality } from '../../utils/quality-assessment';

// Initialize OpenAI if API key is available
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null;

// SEO optimization thresholds and settings
const SEO_SETTINGS = {
  keywordDensityMin: 1.0,
  keywordDensityMax: 2.5,
  minContentLength: 300,
  optimalContentLength: 1200,
  minHeadings: 3,
  maxHeadingLength: 60,
  maxMetaDescriptionLength: 155,
  maxTitleTagLength: 60
};

/**
 * Analyze content for SEO optimization using enhanced SEO optimization utilities
 *
 * @param content - The content to analyze
 * @param keywords - Target keywords for SEO optimization
 * @param topic - The main topic of the content (optional)
 * @param targetAudience - The intended audience for the content (optional)
 */
export async function analyzeSeoContent(
  content: string,
  keywords: string[] = [],
  topic: string = '',
  targetAudience: string = 'general'
): Promise<{
  analysis: string;
  recommendations: string;
  keywordDensity: Record<string, number>;
  score: number;
  seoOptimizationResult?: SeoOptimizationResult;
}> {
  console.log('Analyzing content for SEO optimization');

  if (!content) {
    throw new Error('No content provided for SEO analysis');
  }

  // If OpenAI API key isn't available, return a mock analysis
  if (!openai) {
    return mockSeoAnalysis(content, keywords);
  }

  try {
    // Create a mock artifact for SEO optimization
    const mockArtifact = {
      id: 'temp-seo-analysis',
      type: 'content',
      content: content,
      createdAt: new Date().toISOString(),
      createdBy: 'seo-optimization',
      status: 'completed'
    };

    // Use enhanced SEO optimization utilities
    const seoOptimizationResult = optimizeForSeo(mockArtifact, keywords);

    // Calculate content quality metrics for additional insights
    const contentMetrics = calculateContentQualityMetrics(mockArtifact, keywords);

    // Generate analysis from SEO optimization result
    const analysis = generateAnalysisFromSeoResult(seoOptimizationResult, contentMetrics);

    // Generate recommendations from SEO optimization result
    const recommendations = generateRecommendationsFromSeoResult(seoOptimizationResult);

    // Extract keyword density from SEO optimization result
    const keywordDensity = seoOptimizationResult.onPageSeo.content.keywordDensity;

    // Calculate score based on SEO optimization result
    const score = Math.round(seoOptimizationResult.overallScore * 100);

    // If we still need to use OpenAI for additional analysis, we can do that here
    if (openai && topic) {
      // Enhanced prompt for SEO analysis
      const prompt = `You are an SEO expert analyzing content for optimization.

Content to analyze:
${content.substring(0, 4000)}... ${content.length > 4000 ? '(content truncated)' : ''}

Topic: ${topic}
Target Audience: ${targetAudience}
Target Keywords: ${keywords.join(', ')}
Word Count: ${content.split(/\s+/).filter(word => word.length > 0).length}

I've already performed a technical SEO analysis with the following results:
- Overall SEO score: ${score}/100
- Keyword density: ${Object.entries(keywordDensity).map(([k, v]) => `${k}: ${v}%`).join(', ')}
- Content quality score: ${Math.round(contentMetrics.overallScore * 100)}/100
- Readability score: ${contentMetrics.readability.fleschKincaidScore.toFixed(1)}

Please provide additional insights that would complement this technical analysis, focusing on:
- Strategic SEO considerations for this specific topic and audience
- Competitive positioning in search results
- User intent alignment
`;

      // Use OpenAI to provide additional strategic insights
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {"role": "system", "content": "You are an expert SEO strategist. Provide strategic insights that complement technical SEO analysis."},
          {"role": "user", "content": prompt}
        ],
        temperature: 0.7,
        max_tokens: 1000,
      });

      // Extract the response and append to our analysis
      const additionalInsights = completion.choices[0]?.message?.content || '';
      if (additionalInsights) {
        return {
          analysis: analysis + "\n\n## Strategic Insights\n\n" + additionalInsights,
          recommendations,
          keywordDensity,
          score,
          seoOptimizationResult
        };
      }
    }

    return {
      analysis,
      recommendations,
      keywordDensity,
      score,
      seoOptimizationResult
    };
  } catch (error) {
    console.error('Error analyzing content for SEO:', error);

    // Fallback to basic analysis if enhanced utilities fail
    try {
      // Calculate word count
      const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

      // Analyze keyword density
      const keywordDensity: Record<string, number> = {};
      keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = content.match(regex) || [];
        const density = (matches.length / wordCount) * 100;
        keywordDensity[keyword] = parseFloat(density.toFixed(2));
      });

      // Enhanced prompt for SEO analysis
      const prompt = `You are an SEO expert analyzing content for optimization.

Content to analyze:
${content.substring(0, 4000)}... ${content.length > 4000 ? '(content truncated)' : ''}

Topic: ${topic}
Target Audience: ${targetAudience}
Target Keywords: ${keywords.join(', ')}
Word Count: ${wordCount}

Provide a comprehensive SEO analysis including:
- Overall SEO score (0-100)
- Detailed assessment of keyword usage and placement
- Content structure and readability analysis
- Specific recommendations for improvement
`;

      // Use OpenAI to analyze the content for SEO
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {"role": "system", "content": "You are an expert SEO analyst. Analyze the content provided and offer detailed SEO recommendations."},
          {"role": "user", "content": prompt}
        ],
        temperature: 0.7,
        max_tokens: 1200,
      });

      // Extract the response
      const responseText = completion.choices[0]?.message?.content || '';

      // Parse the response
      const analysisRegex = /Analysis:([\s\S]*?)(?=Recommendations:|$)/i;
      const recommendationsRegex = /Recommendations:([\s\S]*?)(?=$)/i;

      const analysisMatch = responseText.match(analysisRegex);
      const recommendationsMatch = responseText.match(recommendationsRegex);

      const analysis = analysisMatch ? analysisMatch[1].trim() : 'No analysis provided.';
      const recommendations = recommendationsMatch ? recommendationsMatch[1].trim() : 'No recommendations provided.';

      // Calculate score based on keyword density and other factors
      let score = 70; // Base score

      // Adjust score based on keyword density
      let keywordScoreContribution = 0;
      for (const [keyword, density] of Object.entries(keywordDensity)) {
        if (density >= SEO_SETTINGS.keywordDensityMin && density <= SEO_SETTINGS.keywordDensityMax) {
          keywordScoreContribution += 5;
        } else if (density > 0) {
          keywordScoreContribution += 2;
        }
      }
      score += Math.min(keywordScoreContribution, 15); // Max 15 points for keywords

      // Adjust score based on content length
      if (wordCount >= SEO_SETTINGS.optimalContentLength) {
        score += 10;
      } else if (wordCount >= SEO_SETTINGS.minContentLength) {
        score += 5;
      }

      // Adjust score based on headings
      const headingCount = (content.match(/<h[1-6]|##/g) || []).length;
      if (headingCount >= SEO_SETTINGS.minHeadings) {
        score += 5;
      }

      return {
        analysis,
        recommendations,
        keywordDensity,
        score
      };
    } catch (fallbackError) {
      console.error('Error in fallback SEO analysis:', fallbackError);
      return mockSeoAnalysis(content, keywords);
    }
  }
}

/**
 * Generate analysis text from SEO optimization result
 */
function generateAnalysisFromSeoResult(
  seoResult: SeoOptimizationResult,
  contentMetrics: any
): string {
  // Create a comprehensive analysis from the SEO optimization result
  const analysis = [
    `## SEO Analysis Summary\n`,
    `Overall SEO Score: ${Math.round(seoResult.overallScore * 100)}/100\n`,

    `### On-Page SEO Analysis\n`,
    `- **Title Tag**: ${seoResult.onPageSeo.titleTag.score >= 0.8 ? '✅ Excellent' : seoResult.onPageSeo.titleTag.score >= 0.6 ? '✓ Good' : '❌ Needs improvement'}\n`,
    `  Original: "${seoResult.onPageSeo.titleTag.original}"\n`,
    `  ${seoResult.onPageSeo.titleTag.original !== seoResult.onPageSeo.titleTag.optimized ? `Optimized: "${seoResult.onPageSeo.titleTag.optimized}"\n` : ''}`,

    `- **Meta Description**: ${seoResult.onPageSeo.metaDescription.score >= 0.8 ? '✅ Excellent' : seoResult.onPageSeo.metaDescription.score >= 0.6 ? '✓ Good' : '❌ Needs improvement'}\n`,
    `  ${seoResult.onPageSeo.metaDescription.original ? `Original: "${seoResult.onPageSeo.metaDescription.original.substring(0, 100)}${seoResult.onPageSeo.metaDescription.original.length > 100 ? '...' : ''}"\n` : ''}`,

    `- **Headings Structure**: ${seoResult.onPageSeo.headings.score >= 0.8 ? '✅ Excellent' : seoResult.onPageSeo.headings.score >= 0.6 ? '✓ Good' : '❌ Needs improvement'}\n`,
    `  Found ${seoResult.onPageSeo.headings.original.length} headings\n`,

    `- **Keyword Usage**: ${seoResult.onPageSeo.content.score >= 0.8 ? '✅ Excellent' : seoResult.onPageSeo.content.score >= 0.6 ? '✓ Good' : '❌ Needs improvement'}\n`,
    Object.entries(seoResult.onPageSeo.content.keywordDensity).map(([keyword, density]) =>
      `  "${keyword}": ${density}% density ${density >= 0.5 && density <= 2.5 ? '(optimal)' : density < 0.5 ? '(too low)' : '(too high)'}\n`
    ).join(''),

    `### Semantic SEO Analysis\n`,
    `- **Topic Clusters**: ${seoResult.semanticSeo.topicClusters.slice(0, 3).join(', ')}${seoResult.semanticSeo.topicClusters.length > 3 ? '...' : ''}\n`,
    `- **Related Entities**: ${seoResult.semanticSeo.relatedEntities.slice(0, 5).join(', ')}${seoResult.semanticSeo.relatedEntities.length > 5 ? '...' : ''}\n`,

    `### SERP Feature Potential\n`,
    `- **Featured Snippet Potential**: ${Math.round(seoResult.serpFeatures.featuredSnippetPotential * 100)}%\n`,
    `- **FAQ Schema Potential**: ${Math.round(seoResult.serpFeatures.faqSchemaPotential * 100)}%\n`,
    `- **How-To Schema Potential**: ${Math.round(seoResult.serpFeatures.howToSchemaPotential * 100)}%\n`,

    `### Content Quality Metrics\n`,
    `- **Readability**: Flesch-Kincaid Score ${contentMetrics.readability.fleschKincaidScore.toFixed(1)} (${
      contentMetrics.readability.fleschKincaidScore >= 60 && contentMetrics.readability.fleschKincaidScore <= 80 ? 'Optimal - Grade 7-8 level' :
      contentMetrics.readability.fleschKincaidScore > 80 ? 'Very Easy - Grade 6 or below' :
      contentMetrics.readability.fleschKincaidScore >= 50 ? 'Fairly Difficult - Grade 10-12' :
      'Difficult - College level or above'
    })\n`,
    `- **Estimated Reading Time**: ${contentMetrics.readability.readingTimeMinutes} minutes\n`,
    `- **Content Structure**: ${contentMetrics.structure.headingCount} headings, ${contentMetrics.structure.paragraphCount} paragraphs, ${contentMetrics.structure.listCount} lists\n`,
    `- **Engagement Potential**: ${Math.round(contentMetrics.engagement.engagementScore * 100)}%\n`
  ].join('');

  return analysis;
}

/**
 * Generate recommendations text from SEO optimization result
 */
function generateRecommendationsFromSeoResult(
  seoResult: SeoOptimizationResult
): string {
  // Create comprehensive recommendations from the SEO optimization result
  const recommendations = [
    `## SEO Improvement Recommendations\n\n`,

    `### High Priority Improvements\n`,

    // Title tag recommendations
    ...(seoResult.onPageSeo.titleTag.suggestions.length > 0 ?
      [`#### Title Tag Optimization\n`,
       ...seoResult.onPageSeo.titleTag.suggestions.map(s => `- ${s}\n`),
       `\n`] : []),

    // Meta description recommendations
    ...(seoResult.onPageSeo.metaDescription.suggestions.length > 0 ?
      [`#### Meta Description Optimization\n`,
       ...seoResult.onPageSeo.metaDescription.suggestions.map(s => `- ${s}\n`),
       `\n`] : []),

    // Content recommendations
    ...(seoResult.onPageSeo.content.suggestions.length > 0 ?
      [`#### Content Optimization\n`,
       ...seoResult.onPageSeo.content.suggestions.map(s => `- ${s}\n`),
       `\n`] : []),

    `### Semantic SEO Improvements\n`,

    // Semantic SEO recommendations
    ...(seoResult.semanticSeo.suggestions.length > 0 ?
      [...seoResult.semanticSeo.suggestions.map(s => `- ${s}\n`),
       `\n`] : [`- No specific semantic SEO recommendations.\n\n`]),

    `### SERP Feature Opportunities\n`,

    // SERP feature recommendations
    ...(seoResult.serpFeatures.suggestions.length > 0 ?
      [...seoResult.serpFeatures.suggestions.map(s => `- ${s}\n`),
       `\n`] : [`- No specific SERP feature recommendations.\n\n`]),

    `### Structured Data Implementation\n`,

    // Structured data recommendations
    ...(seoResult.structuredData.suggestions.length > 0 ?
      [...seoResult.structuredData.suggestions.map(s => `- ${s}\n`),
       `\n`] : [`- No specific structured data recommendations.\n\n`]),

    `### Additional Recommendations\n`,

    // Overall recommendations
    ...(seoResult.suggestions.length > 0 ?
      [...seoResult.suggestions.map(s => `- ${s}\n`),
       `\n`] : [`- No additional recommendations.\n\n`]),

    // Schema examples if available
    ...(Object.keys(seoResult.structuredData.schemaExamples).length > 0 ?
      [`### Schema Markup Examples\n\n`] : []),

    // Add schema examples
    ...Object.entries(seoResult.structuredData.schemaExamples).map(([schemaType, example]) =>
      `#### ${schemaType} Schema Example\n\`\`\`json\n${JSON.stringify(example, null, 2)}\n\`\`\`\n\n`
    )
  ].join('');

  return recommendations;
}

/**
 * Generate SEO recommendations based on content and keywords using enhanced SEO optimization utilities
 */
export async function generateSeoRecommendations(
  content: string,
  keywords: string[] = []
): Promise<{
  recommendations: string;
  prioritizedTasks: string[];
  keywordSuggestions: string[];
  score: number;
  seoOptimizationResult?: SeoOptimizationResult;
}> {
  console.log('Generating SEO recommendations');

  if (!content) {
    throw new Error('No content provided for SEO recommendations');
  }

  // If OpenAI API key isn't available, return mock recommendations
  if (!openai) {
    return mockSeoRecommendations(content, keywords);
  }

  try {
    // Create a mock artifact for SEO optimization
    const mockArtifact = {
      id: 'temp-seo-recommendations',
      type: 'content',
      content: content,
      createdAt: new Date().toISOString(),
      createdBy: 'seo-optimization',
      status: 'completed'
    };

    // Use enhanced SEO optimization utilities
    const seoOptimizationResult = optimizeForSeo(mockArtifact, keywords);

    // Generate recommendations from SEO optimization result
    const recommendations = generateRecommendationsFromSeoResult(seoOptimizationResult);

    // Extract prioritized tasks from SEO optimization result
    const prioritizedTasks = extractPrioritizedTasksFromSeoResult(seoOptimizationResult);

    // Generate keyword suggestions using OpenAI
    let keywordSuggestions: string[] = [];

    if (openai) {
      // Enhanced prompt for keyword suggestions
      const prompt = `You are an SEO expert providing keyword suggestions for content.

Content to analyze:
${content.substring(0, 2000)}... ${content.length > 2000 ? '(content truncated)' : ''}

Current Target Keywords: ${keywords.join(', ')}

Based on the content and current keywords, suggest additional related keywords that would help improve search visibility.
Focus on:
1. Long-tail variations of current keywords
2. Related terms that users might search for
3. Semantic variations that search engines would associate with this topic

Format your response as a comma-separated list of keywords only.
`;

      // Use OpenAI to generate keyword suggestions
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {"role": "system", "content": "You are an expert SEO keyword researcher. Provide only a comma-separated list of keywords."},
          {"role": "user", "content": prompt}
        ],
        temperature: 0.7,
        max_tokens: 500,
      });

      // Extract the response
      const responseText = completion.choices[0]?.message?.content || '';

      // Parse the keywords
      keywordSuggestions = responseText
        .split(',')
        .map(keyword => keyword.trim())
        .filter(keyword => keyword.length > 0);
    }

    // If no keyword suggestions were generated, extract some from the SEO result
    if (keywordSuggestions.length === 0) {
      keywordSuggestions = [
        ...seoOptimizationResult.semanticSeo.relatedEntities.slice(0, 3),
        ...seoOptimizationResult.semanticSeo.topicClusters.slice(0, 3)
      ];
    }

    // Calculate score based on SEO optimization result
    const score = Math.round(seoOptimizationResult.overallScore * 100);

    return {
      recommendations,
      prioritizedTasks,
      keywordSuggestions,
      score,
      seoOptimizationResult
    };
  } catch (error) {
    console.error('Error generating SEO recommendations:', error);

    // Fallback to basic recommendations if enhanced utilities fail
    try {
      // Enhanced prompt for SEO recommendations
      const prompt = `You are an SEO expert providing recommendations to improve content for search engines.

Content to analyze:
${content.substring(0, 4000)}... ${content.length > 4000 ? '(content truncated)' : ''}

Target Keywords: ${keywords.join(', ')}

Provide comprehensive SEO recommendations including:
1. Overall recommendations for improving the content's search ranking
2. A prioritized list of 3-5 specific tasks to implement
3. Additional keyword suggestions related to the topic
4. An SEO optimization score (0-100)

Format your response with these sections:
- Recommendations: (general recommendations)
- Prioritized Tasks: (numbered list of specific actions)
- Keyword Suggestions: (comma-separated list)
- Score: (numerical score)
`;

      // Use OpenAI to generate recommendations
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {"role": "system", "content": "You are an expert SEO consultant providing actionable recommendations."},
          {"role": "user", "content": prompt}
        ],
        temperature: 0.7,
        max_tokens: 1000,
      });

      // Extract the response
      const responseText = completion.choices[0]?.message?.content || '';

      // Parse the response
      const recommendationsRegex = /Recommendations:([\s\S]*?)(?=Prioritized Tasks:|$)/i;
      const tasksRegex = /Prioritized Tasks:([\s\S]*?)(?=Keyword Suggestions:|$)/i;
      const keywordsRegex = /Keyword Suggestions:([\s\S]*?)(?=Score:|$)/i;
      const scoreRegex = /Score:\s*(\d+)/i;

      const recommendationsMatch = responseText.match(recommendationsRegex);
      const tasksMatch = responseText.match(tasksRegex);
      const keywordsMatch = responseText.match(keywordsRegex);
      const scoreMatch = responseText.match(scoreRegex);

      const recommendations = recommendationsMatch ? recommendationsMatch[1].trim() : 'No recommendations provided.';

      // Extract prioritized tasks
      let prioritizedTasks: string[] = [];
      if (tasksMatch && tasksMatch[1]) {
        prioritizedTasks = tasksMatch[1]
          .split(/[\n\r]/)
          .map(task => task.replace(/^\d+\.\s*|^-\s*/, '').trim())
          .filter(task => task.length > 0);
      }

      // Extract keyword suggestions
      let keywordSuggestions: string[] = [];
      if (keywordsMatch && keywordsMatch[1]) {
        keywordSuggestions = keywordsMatch[1]
          .split(',')
          .map(keyword => keyword.trim())
          .filter(keyword => keyword.length > 0);
      }

      // Extract score
      const score = scoreMatch ? parseInt(scoreMatch[1]) : 75;

      return {
        recommendations,
        prioritizedTasks,
        keywordSuggestions,
        score
      };
    } catch (fallbackError) {
      console.error('Error in fallback SEO recommendations:', fallbackError);
      return mockSeoRecommendations(content, keywords);
    }
  }
}

/**
 * Extract prioritized tasks from SEO optimization result
 */
function extractPrioritizedTasksFromSeoResult(
  seoResult: SeoOptimizationResult
): string[] {
  const prioritizedTasks: string[] = [];

  // Add title tag task if score is low
  if (seoResult.onPageSeo.titleTag.score < 0.7 && seoResult.onPageSeo.titleTag.suggestions.length > 0) {
    prioritizedTasks.push(`Optimize title tag: ${seoResult.onPageSeo.titleTag.suggestions[0]}`);
  }

  // Add meta description task if score is low
  if (seoResult.onPageSeo.metaDescription.score < 0.7 && seoResult.onPageSeo.metaDescription.suggestions.length > 0) {
    prioritizedTasks.push(`Improve meta description: ${seoResult.onPageSeo.metaDescription.suggestions[0]}`);
  }

  // Add content optimization task if score is low
  if (seoResult.onPageSeo.content.score < 0.7 && seoResult.onPageSeo.content.suggestions.length > 0) {
    prioritizedTasks.push(`Enhance content: ${seoResult.onPageSeo.content.suggestions[0]}`);
  }

  // Add semantic SEO task
  if (seoResult.semanticSeo.suggestions.length > 0) {
    prioritizedTasks.push(`Improve semantic relevance: ${seoResult.semanticSeo.suggestions[0]}`);
  }

  // Add SERP feature task
  if (seoResult.serpFeatures.suggestions.length > 0) {
    prioritizedTasks.push(`Optimize for SERP features: ${seoResult.serpFeatures.suggestions[0]}`);
  }

  // Add structured data task
  if (seoResult.structuredData.suggestions.length > 0) {
    prioritizedTasks.push(`Implement structured data: ${seoResult.structuredData.suggestions[0]}`);
  }

  // Add general task
  if (seoResult.suggestions.length > 0) {
    prioritizedTasks.push(seoResult.suggestions[0]);
  }

  // Limit to 5 tasks
  return prioritizedTasks.slice(0, 5);
}

/**
 * Provide SEO consultation based on a question and context
 */
export async function provideSeoConsultation(
  topic: string,
  contentToAnalyze: string = '',
  context: Record<string, any> = {}
): Promise<{
  response: string;
  suggestions: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
}> {
  console.log('Providing SEO consultation');

  if (!topic && !contentToAnalyze) {
    throw new Error('Topic or content is required for SEO consultation');
  }

  // If OpenAI API key isn't available, return mock consultation response
  if (!openai) {
    return mockSeoConsultation(topic || 'SEO consultation', context);
  }

  try {
    // Build context string from the context object
    let contextString = '';
    if (Object.keys(context).length > 0) {
      contextString = Object.entries(context)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');
    }

    // Enhanced prompt for SEO consultation
    const prompt = `You are an SEO expert providing consultation on ${topic}.

${contentToAnalyze ? `Content to analyze:\n${contentToAnalyze.substring(0, 4000)}... ${contentToAnalyze.length > 4000 ? '(content truncated)' : ''}\n\n` : ''}

${contextString ? `Additional context:\n${contextString}\n\n` : ''}

Question/Topic: ${topic}

Provide a comprehensive SEO consultation response including:
1. Detailed answer to the question or topic analysis
2. Specific suggestions in different areas of SEO that would help

Format your response with these sections:
- Response: (your detailed answer)
- Suggestions: (list of specific suggestions with priority levels)
`;

    // Use OpenAI to generate consultation
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {"role": "system", "content": "You are an expert SEO consultant providing actionable advice."},
        {"role": "user", "content": prompt}
      ],
      temperature: 0.7,
      max_tokens: 1000,
    });

    // Extract the response
    const responseText = completion.choices[0]?.message?.content || '';

    // Parse the response
    const responseRegex = /Response:([\s\S]*?)(?=Suggestions:|$)/i;
    const suggestionsRegex = /Suggestions:([\s\S]*?)(?=$)/i;

    const responseMatch = responseText.match(responseRegex);
    const suggestionsMatch = responseText.match(suggestionsRegex);

    const response = responseMatch ? responseMatch[1].trim() : 'No consultation response provided.';

    // Extract suggestions and priorities
    let suggestions: Array<{
      area: string;
      suggestion: string;
      priority: 'high' | 'medium' | 'low';
    }> = [];

    if (suggestionsMatch && suggestionsMatch[1]) {
      const suggestionsText = suggestionsMatch[1].trim();
      // Look for patterns like "Area (High): Suggestion" or "- Area (Medium): Suggestion"
      const suggestionItems = suggestionsText.split(/[\n\r]/).filter(item => item.trim().length > 0);

      for (const item of suggestionItems) {
        // Try to match with different patterns
        const pattern1 = /([^(:]*)(?:\(([^)]*)\))?:(.+)/i;
        const pattern2 = /-\s*([^(:]*)(?:\(([^)]*)\))?:(.+)/i;

        const match = item.match(pattern1) || item.match(pattern2);

        if (match) {
          const area = match[1]?.trim() || 'General';
          let priority = match[2]?.trim().toLowerCase() || 'medium';
          const suggestion = match[3]?.trim() || item.trim();

          // Normalize priority
          if (!['high', 'medium', 'low'].includes(priority)) {
            priority = 'medium';
          }

          suggestions.push({
            area,
            suggestion,
            priority: priority as 'high' | 'medium' | 'low'
          });
        } else {
          // If no pattern matches, add as a medium priority general suggestion
          suggestions.push({
            area: 'General',
            suggestion: item.trim(),
            priority: 'medium'
          });
        }
      }
    }

    // If no suggestions were parsed but response exists, create a default suggestion
    if (suggestions.length === 0 && response) {
      suggestions.push({
        area: 'General',
        suggestion: 'Consider implementing the recommendations in the consultation response.',
        priority: 'medium'
      });
    }

    return {
      response,
      suggestions
    };
  } catch (error) {
    console.error('Error providing SEO consultation:', error);
    return mockSeoConsultation(topic || 'SEO consultation', context);
  }
}

/**
 * Generate SEO feedback for content
 */
export async function generateSeoFeedback(
  content: string,
  keywords: string[] = []
): Promise<any> {
  console.log('Generating SEO feedback');

  if (!content) {
    throw new Error('Content is required for generating SEO feedback');
  }

  // If OpenAI API key isn't available, return a mock feedback response
  if (!openai) {
    return mockSeoFeedback(content, keywords);
  }

  try {
    // Enhanced prompt for SEO feedback
    const prompt = `You are an SEO expert providing specific feedback on content.

Content to analyze:
${content.substring(0, 4000)}... ${content.length > 4000 ? '(content truncated)' : ''}

${keywords.length > 0 ? `Target Keywords: ${keywords.join(', ')}` : ''}

Provide comprehensive SEO feedback including:
1. Strengths of the content from an SEO perspective
2. Weaknesses that could affect search performance
3. Specific suggestions for improvement
4. Overall assessment

Format your response as a JSON object with these sections:
{
  "strengths": ["strength 1", "strength 2", ...],
  "weaknesses": ["weakness 1", "weakness 2", ...],
  "suggestions": ["suggestion 1", "suggestion 2", ...],
  "overallAssessment": "Your overall assessment here"
}
`;

    // Use OpenAI to generate feedback
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {"role": "system", "content": "You are an expert SEO consultant providing detailed feedback."},
        {"role": "user", "content": prompt}
      ],
      temperature: 0.7,
      max_tokens: 1000,
      response_format: { type: "json_object" }
    });

    // Extract the response
    const responseText = completion.choices[0]?.message?.content || '{}';

    try {
      // Parse the JSON response
      const feedbackResult = JSON.parse(responseText);
      return feedbackResult;
    } catch (jsonError) {
      console.error('Error parsing JSON feedback:', jsonError);
      // Fallback to returning the raw text
      return {
        strengths: [],
        weaknesses: [],
        suggestions: [],
        overallAssessment: responseText
      };
    }
  } catch (error) {
    console.error('Error generating SEO feedback:', error);
    return mockSeoFeedback(content, keywords);
  }
}

/**
 * Generate a contribution to a discussion
 */
export async function generateDiscussionContribution(
  topic: string,
  lastMessage: string,
  participants: AgentId[] = [],
  perspective: string = ''
): Promise<{
  content: string;
  reasoning: EnhancedReasoning;
}> {
  console.log('Generating SEO discussion contribution');

  if (!topic) {
    throw new Error('Topic is required for discussion contribution');
  }

  // If OpenAI API key isn't available, return a mock contribution
  if (!openai) {
    return mockDiscussionContribution(topic);
  }

  try {
    // Enhanced prompt for discussion contribution
    const prompt = `You are an SEO expert contributing to a collaborative discussion on the topic: "${topic}".

${lastMessage ? `Last message in the discussion: "${lastMessage}"` : ''}

${participants.length > 0 ? `Participants: ${participants.join(', ')}` : ''}

${perspective ? `Your perspective: ${perspective}` : 'Your perspective: SEO optimization expert'}

Contribute to this discussion by:
1. Addressing points from the last message if applicable
2. Providing valuable SEO insights related to the topic
3. Being collaborative and constructive
4. Suggesting specific actionable ideas

Keep your response concise (3-5 paragraphs) and focused on providing SEO value to the discussion.
`;

    // Use OpenAI to generate discussion contribution
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {"role": "system", "content": "You are an expert SEO consultant contributing to a collaborative discussion."},
        {"role": "user", "content": prompt}
      ],
      temperature: 0.7,
      max_tokens: 800,
    });

    // Extract the response
    const content = completion.choices[0]?.message?.content || 'As an SEO expert, I recommend focusing on keyword optimization, quality content, and technical SEO factors to improve search visibility.';

    // Create reasoning for the contribution
    const reasoning: EnhancedReasoning = createEnhancedReasoning(
      [
        'Contributing SEO expertise to collaborative discussion',
        'Addressing previous points raised in the conversation',
        'Providing actionable SEO recommendations'
      ],
      [
        'The specific topic being discussed',
        'Current SEO best practices',
        'How SEO connects to the broader content strategy',
        'Technical vs. content SEO factors'
      ],
      'Provide balanced SEO perspective with actionable insights',
      'seo-optimization',
      { confidence: 0.9 }
    );

    return {
      content,
      reasoning
    };
  } catch (error) {
    console.error('Error generating discussion contribution:', error);
    return mockDiscussionContribution(topic);
  }
}

/**
 * Mock SEO analysis for when OpenAI is not available
 */
function mockSeoAnalysis(
  content: string,
  keywords: string[] = []
): {
  analysis: string;
  recommendations: string;
  keywordDensity: Record<string, number>;
  score: number;
} {
  // Calculate word count
  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

  // Create mock keyword density
  const keywordDensity: Record<string, number> = {};
  keywords.forEach(keyword => {
    // Generate a random density between 0.5 and 2.5
    keywordDensity[keyword] = parseFloat((Math.random() * 2 + 0.5).toFixed(2));
  });

  return {
    analysis: `This content is approximately ${wordCount} words long. Generally, search engines prefer content that is comprehensive and provides value to readers. The keyword density appears to be within acceptable ranges for most of the target keywords, but there's room for optimization. The content structure could be improved with more headings and better keyword placement in important areas like the title, headings, and first paragraph.`,

    recommendations: `1. Increase content length to at least 1200 words for comprehensive coverage.\n2. Add more headings with target keywords.\n3. Optimize the meta description to include primary keywords.\n4. Add internal links to related content.\n5. Include more semantic variants of the target keywords.\n6. Improve readability with shorter paragraphs and bullet points.`,

    keywordDensity,

    score: 75
  };
}

/**
 * Mock SEO recommendations for when OpenAI is not available
 */
function mockSeoRecommendations(
  content: string,
  keywords: string[] = []
): {
  recommendations: string;
  prioritizedTasks: string[];
  keywordSuggestions: string[];
  score: number;
} {
  return {
    recommendations: `Based on the analysis of your content, there are several opportunities to improve SEO performance. The content needs better keyword optimization, especially in key positions like headings and the opening paragraph. The content structure could be improved with more subheadings to break up the text and improve readability. Additionally, consider adding more supporting content to provide comprehensive coverage of the topic.`,

    prioritizedTasks: [
      "Optimize the title tag with primary keyword at the beginning",
      "Add 2-3 more H2 subheadings containing target keywords",
      "Improve the meta description to include primary and secondary keywords",
      "Add internal links to at least 3 related pages",
      "Increase content length by adding more valuable information"
    ],

    keywordSuggestions: keywords.length > 0
      ? [...keywords, `best ${keywords[0]}`, `${keywords[0]} guide`, `how to choose ${keywords[0]}`].slice(0, 5)
      : ["search engine optimization", "SEO strategy", "keyword research", "content marketing", "on-page SEO"],

    score: 78
  };
}

/**
 * Mock SEO consultation for when OpenAI is not available
 */
function mockSeoConsultation(
  topic: string,
  context: Record<string, any> = {}
): {
  response: string;
  suggestions: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
} {
  return {
    response: `Regarding your question about ${topic}, the most important SEO factor to consider is creating high-quality, valuable content that addresses the search intent of your target audience. Search engines prioritize content that provides comprehensive answers to searchers' questions. Focus on thoroughly covering the topic with well-structured content using appropriate headings, bullet points, and formatting to improve readability. Additionally, technical SEO factors like page speed, mobile optimization, and proper schema markup are increasingly important for rankings.`,

    suggestions: [
      {
        area: "Keyword Strategy",
        suggestion: "Conduct thorough keyword research focusing on search intent and long-tail variations",
        priority: "high"
      },
      {
        area: "Content Structure",
        suggestion: "Use a clear hierarchy of headings (H1, H2, H3) with keywords naturally incorporated",
        priority: "medium"
      },
      {
        area: "Technical SEO",
        suggestion: "Improve page loading speed by optimizing images and implementing lazy loading",
        priority: "high"
      },
      {
        area: "Internal Linking",
        suggestion: "Create a strategic internal linking structure to distribute page authority",
        priority: "medium"
      },
      {
        area: "User Experience",
        suggestion: "Ensure mobile responsiveness and easy navigation to reduce bounce rates",
        priority: "low"
      }
    ]
  };
}

/**
 * Mock SEO feedback for when OpenAI is not available
 */
function mockSeoFeedback(
  content: string,
  keywords: string[] = []
): {
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  overallAssessment: string;
} {
  return {
    strengths: [
      "Good use of headings to structure content",
      "Natural incorporation of some target keywords",
      "Content appears to address the main topic comprehensively"
    ],

    weaknesses: [
      "Keyword density could be improved for primary terms",
      "Meta description is missing or not optimized",
      "Internal linking structure is minimal",
      "Content may be too short for competitive keywords"
    ],

    suggestions: [
      "Add the primary keyword closer to the beginning of the title tag",
      "Create a compelling meta description including primary and secondary keywords",
      "Add 2-3 relevant internal links to other pages on your site",
      "Expand the content with additional helpful information to reach at least 1000 words",
      "Include more semantic variations of your target keywords"
    ],

    overallAssessment: "The content provides a solid foundation but needs optimization in several key areas to improve search performance. Focus on enhancing keyword usage, technical SEO elements, and content expansion to create more comprehensive coverage of the topic."
  };
}

/**
 * Mock discussion contribution for when OpenAI is not available
 */
function mockDiscussionContribution(topic: string): {
  content: string;
  reasoning: EnhancedReasoning;
} {
  return {
    content: `From an SEO perspective, I'd like to contribute a few important points regarding ${topic}.

First, we should ensure our content strategy aligns with search intent by focusing on what users are actually looking for rather than just keyword volume. This means conducting thorough keyword research that identifies not just popular terms, but the questions and problems our audience is trying to solve.

Second, we need to consider content structure and technical optimization. Well-structured content with proper heading hierarchy (H1, H2, H3) not only helps users navigate information but also helps search engines understand content hierarchy and relevance. Additionally, page speed, mobile-friendliness, and structured data implementation are increasingly important ranking factors.

Finally, we should develop a comprehensive internal linking strategy to distribute page authority throughout the site and guide users to related content, enhancing both SEO performance and user experience. This creates a stronger site architecture that search engines can easily crawl and understand.`,

    reasoning: createEnhancedReasoning(
      [
        'Contributing SEO expertise to the discussion',
        'Addressing critical SEO factors relevant to the topic',
        'Balancing technical and content-focused recommendations'
      ],
      [
        'Current SEO best practices',
        'The specific topic being discussed',
        'How SEO connects to broader content strategy',
        'Balancing user experience and search optimization'
      ],
      'Provide actionable SEO insights focused on search intent, content structure, and site architecture',
      'seo-optimization',
      { confidence: 0.85 }
    )
  };
}
