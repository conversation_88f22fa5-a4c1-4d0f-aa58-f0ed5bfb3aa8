// src/app/(payload)/api/agents/collaborative-iteration/agents/seo-optimization/index.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  StandardizedHandlerResult,
  AgentId,
  Goal
} from '../../types';
import { AgentBase } from '../../core/AgentBase';
import { AgentHandler } from '../../core/AgentHandler';
import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';
import { stateStore } from '../../utils/stateStore';
import logger from '../../utils/logger';

// Import all handlers from a single file
import {
  handleSeoOptimizationInitialRequest,
  handleArtifactRequest,
  handleConsultationRequest,
  handleFeedback,
  handleDiscussionRequest
} from './handlers';

// Re-export the handlers for use in the consolidated system
export {
  handleSeoOptimizationInitialRequest
};

/**
 * SEO Optimization Agent
 *
 * This agent is responsible for analyzing content for SEO optimization opportunities
 * and providing recommendations to improve search engine rankings.
 */
export class SeoOptimizationAgent extends AgentBase {
  /**
   * Constructor initializes the agent with required handlers
   * and sets up message handling
   */
  constructor() {
    super('seo-optimization' as AgentId);
    logger.info('SEO Optimization Agent initialized');
  }

  /**
   * Process a goal assigned to this agent
   * Override the base implementation to handle specific goal types
   */
  protected async processGoal(sessionId: string, goal: Goal): Promise<void> {
    logger.info(`SEO Optimization Agent: Processing goal - ${goal.name}`, {
      sessionId,
      goalId: goal.id,
      agent: this.agentId
    });

    try {
      // Different handling based on goal type
      if (goal.type === 'optimize-content' || goal.type === 'seo-analysis') {
        // Create a message to handle the goal
        const message: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'orchestrator',
          to: this.agentId,
          type: IterativeMessageType.INITIAL_REQUEST,
          content: {
            goalId: goal.id,
            name: goal.name,
            description: goal.description,
            parameters: goal.metadata || {}
          },
          conversationId: sessionId
        };

        // Process the message
        await this.processMessage(sessionId, message);

        // Mark the goal as completed and update workflow progress
        await stateStore.updateState(sessionId, (currentState) => {
          const goals = currentState.goals || [];
          const goalIndex = goals.findIndex(g => g.id === goal.id);
          if (goalIndex >= 0) {
            goals[goalIndex].status = 'completed';
            goals[goalIndex].completedAt = new Date().toISOString();
          }
          return {
            ...currentState,
            goals,
            workflowProgress: {
              ...currentState.workflowProgress,
              seoOptimizationComplete: true
            }
          };
        });

        // Get the current state to access topic and other metadata
        const state = await stateStore.getState(sessionId);

        // Explicitly trigger SEO optimization validation to move to the next phase
        try {
          logger.info(`Triggering SEO optimization validation for session ${sessionId}`, {
            sessionId,
            goalId: goal.id,
            timestamp: new Date().toISOString()
          });

          const { validateSeoOptimization } = require('../../workflows/content-generation-workflow');

          // Execute validation immediately
          await validateSeoOptimization(
            sessionId,
            state.topic,
            state.contentType,
            state.targetAudience,
            state.tone,
            {}
          );

          logger.info(`SEO optimization validation completed for session ${sessionId}`, {
            sessionId,
            goalId: goal.id,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          logger.error(`Error triggering SEO optimization validation`, {
            sessionId,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
          });
        }
      } else {
        // For other goal types, use the default implementation
        await super.processGoal(sessionId, goal);
      }
    } catch (error) {
      logger.error(`Error processing goal ${goal.id}:`, {
        sessionId,
        goalId: goal.id,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });

      // Mark the goal as blocked
      await stateStore.updateState(sessionId, (currentState) => {
        const goals = currentState.goals || [];
        const goalIndex = goals.findIndex(g => g.id === goal.id);
        if (goalIndex >= 0) {
          goals[goalIndex].status = 'blocked';
          goals[goalIndex].blockReason = error instanceof Error ? error.message : String(error);
        }
        return {
          ...currentState,
          goals
        };
      });
    }
  }

  /**
   * Register message handlers for different message types
   * Implementation of the abstract method from AgentBase
   */
  protected registerHandlers(): void {
    // Register handlers for different message types
    this.handler.registerHandler(IterativeMessageType.INITIAL_REQUEST, handleSeoOptimizationInitialRequest);
    // Also handle REQUEST type with the same handler for consistent workflow messaging
    this.handler.registerHandler(IterativeMessageType.REQUEST, handleSeoOptimizationInitialRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_REQUEST, handleArtifactRequest);
    this.handler.registerHandler(IterativeMessageType.CONSULTATION_REQUEST, handleConsultationRequest);
    this.handler.registerHandler(IterativeMessageType.FEEDBACK, handleFeedback);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_CONTRIBUTION, handleDiscussionRequest);
    this.handler.registerHandler(IterativeMessageType.UPDATE, this.handleUpdateMessage.bind(this));
  }

  /**
   * Handle UPDATE message type
   */
  private async handleUpdateMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    logger.info(`SEO Optimization Agent: Handling UPDATE message from ${message.from}`, {
      messageId: message.id,
      from: message.from
    });

    // Simply acknowledge the update
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `SEO Optimization Agent acknowledged the update`,
        status: 'success'
      },
      message.conversationId,
      message.id
    );

    return {
      success: true,
      message: 'Update acknowledged',
      error: null,
      response
    };
  }
}

// Create and export a singleton instance
export const seoOptimizationAgent = new SeoOptimizationAgent();
