// src/app/(payload)/api/agents/collaborative-iteration/agents/seo-optimization/handlers.ts

/**
 * SEO Optimization Agent Handlers
 *
 * This file contains message handlers for the SEO Optimization Agent in the
 * collaborative agent system. These handlers process different types of messages
 * and implement the agent's functionality for SEO analysis, recommendations,
 * and content optimization.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  AgentState,
  IterativeArtifact,
  ArtifactStatus,
  Consultation,
  AgentId
} from '../../types';
import { StandardizedHandlerResult } from '../../utils/agentTypes';
import { AgentStateManager } from '../../utils/agentStateManager';
import { AgentMessaging } from '../../utils/agentMessaging';
import {
  createEnhancedReasoning,
  storeAgentReasoning,
  EnhancedReasoning
} from '../../utils/reasoningUtils';
import {
  analyzeSeoContent,
  generateSeoRecommendations,
  provideSeoConsultation,
  generateSeoFeedback,
  generateDiscussionContribution
} from './methods';

// Agent identifier
const AGENT_ID = AgentId.SEO_OPTIMIZATION;

// For compatibility with the original implementation in index.ts
export { handleInitialRequest as handleSeoOptimizationInitialRequest };

/**
 * Handle initial request to the SEO Optimization Agent
 * This is called when the agent is first engaged in a collaboration
 */
export async function handleInitialRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Optimization Agent: Handling initial request from ${message.from}`);

  // Extract request details
  const { topic, contentType, targetAudience, tone, keywords = [] } = message.content || {};

  // Initialize the agent's state if needed
  const agentState: AgentState = state.agentStates?.[AGENT_ID] || {
    id: AGENT_ID,
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };

  // Update state with this processed message
  if (!agentState.processedRequests) {
    agentState.processedRequests = [];
  }
  agentState.processedRequests.push(message.id);
  agentState.lastUpdated = new Date().toISOString();

  try {
    // Create reasoning for the SEO optimization report
    const reasoning: EnhancedReasoning = createEnhancedReasoning(
      [
        'Analyzing content requirements for SEO optimization',
        'Identifying key SEO factors for the target audience',
        'Formulating optimization strategies for search visibility'
      ],
      [
        'Search engine ranking factors',
        'Target audience search behavior',
        'Content type SEO requirements',
        'Keyword competitiveness and relevance'
      ],
      'Generate comprehensive SEO optimization report with actionable recommendations',
      AGENT_ID,
      { confidence: 0.9 }
    );

    // Store the reasoning
    await storeAgentReasoning(message.conversationId, AGENT_ID, null, message.id, message.conversationId, reasoning);

    // Create an SEO Optimization Report artifact with detailed, multi-line content
    const seoOptimizationArtifact: IterativeArtifact = {
      id: uuidv4(),
      name: `SEO Optimization Report for ${topic || 'Content'}`,
      type: 'seo-optimization',
      status: 'completed' as ArtifactStatus,
      createdBy: AGENT_ID,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords,
        analysisDate: new Date().toISOString(),
        toolsUsed: ['SEMrush', 'Ahrefs', 'Google Search Console', 'PageSpeed Insights'],
        confidenceScore: 92
      },
      content: {
        // Use a string for the main content to ensure it displays properly in agent discussions
        text: `# SEO Optimization Report for ${topic || 'Your Content'}

## Executive Summary
This comprehensive SEO optimization report provides actionable recommendations for improving search visibility for ${topic || 'your content'}. Our analysis indicates significant opportunities to enhance organic traffic through strategic keyword implementation, content structure improvements, and technical SEO optimizations.

## Keyword Analysis

### Primary Keywords
${keywords.slice(0, 3).map(keyword => `- **${keyword}**: High priority, use in title, headings, and first paragraph (target density: 1.5-2.5%)`).join('\n')}

### Secondary Keywords
${keywords.slice(3, 6).map(keyword => `- **${keyword}**: Medium priority, include in subheadings and body content (target density: 0.8-1.5%)`).join('\n')}

### Related Keywords
- ${topic || 'content'} guide
- best ${topic || 'content'}
- ${topic || 'content'} for ${targetAudience || 'beginners'}

## On-Page Optimization Recommendations

### Title Tag
- Use primary keyword near the beginning of the title tag
- Keep under 60 characters for optimal display in search results
- Make it compelling and click-worthy
- Example: "${keywords[0] || 'Primary Keyword'}: Complete Guide to ${topic || 'Your Topic'} (${new Date().getFullYear()})"

### Meta Description
- Include primary and secondary keywords naturally
- Keep between 150-160 characters
- Include a clear value proposition and call-to-action
- Example: "Discover proven ${keywords[0] || 'primary keyword'} strategies for ${targetAudience || 'your audience'}. Our comprehensive guide covers ${keywords[1] || 'secondary keyword'} techniques that deliver results."

### Heading Structure
- Use H1 for main title with primary keyword
- Use H2/H3 for sections with secondary keywords
- Maintain logical hierarchy (H1 → H2 → H3)
- Include keywords naturally, not forced

### Content Optimization
- Create comprehensive, well-structured content of at least 1,200 words
- Front-load important information in the first 200 words
- Use bullet points and numbered lists for better readability
- Include primary keywords in the first paragraph
- Maintain keyword density of 1-2% for primary keywords

### Image Optimization
- Use descriptive filenames containing relevant keywords
- Add comprehensive alt text with keywords where appropriate
- Compress images for faster loading
- Consider adding schema markup for rich results

## Technical SEO Recommendations

### Mobile Optimization
- Ensure responsive design and fast loading on mobile devices
- Test with Google's Mobile-Friendly Test tool
- Optimize tap targets and font sizes for mobile users

### Page Speed
- Optimize images, minimize CSS/JS, use browser caching
- Implement lazy loading for images and videos
- Consider using a content delivery network (CDN)
- Target a page load time under 3 seconds

### Schema Markup
- Implement appropriate schema markup for rich snippets
- Consider Article, FAQ, or HowTo schema depending on content type
- Test implementation with Google's Rich Results Test tool

### URL Structure
- Create clean, descriptive URLs containing primary keywords
- Keep URLs short and readable
- Use hyphens to separate words
- Example: example.com/${keywords[0] || 'primary-keyword'}-guide

## Competitive Analysis

### Top Competitors
- competitor1.com - Strong in ${keywords[0] || 'primary keyword'} content
- competitor2.com - Ranks well for ${keywords[1] || 'secondary keyword'} terms
- competitor3.com - Excellent technical SEO implementation

### Competitive Gaps
- More comprehensive content than competitors
- Better internal linking structure
- More engaging multimedia content

## Action Plan

1. Optimize page title and meta description with primary keywords
2. Restructure content with proper heading hierarchy
3. Improve keyword density in first 100 words
4. Add internal links to related content
5. Implement schema markup for better rich snippets
6. Optimize images with descriptive filenames and alt text
7. Improve page speed through technical optimizations
8. Create a content update schedule to keep information fresh

## Expected Outcomes

With these optimizations implemented, we anticipate:
- Improved rankings for target keywords
- Increased organic traffic by 15-30%
- Better user engagement metrics
- Higher conversion rates from organic visitors

*Report generated on ${new Date().toLocaleDateString()} by SEO Optimization Agent*`,

        // Include structured data for programmatic access
        summary: `This SEO optimization report provides recommendations for improving search visibility for ${topic || 'your content'}.`,
        keywordAnalysis: {
          primaryKeywords: keywords.slice(0, 3).map(keyword => ({
            keyword,
            usage: 'Optimize title, headings, and first paragraph',
            density: '1.5-2.5%',
            importance: 'High',
            searchVolume: Math.floor(Math.random() * 5000) + 2000,
            difficulty: Math.floor(Math.random() * 50) + 30,
            cpc: `$${(Math.random() * 5 + 1).toFixed(2)}`
          })),
          secondaryKeywords: keywords.slice(3, 6).map(keyword => ({
            keyword,
            usage: 'Include in subheadings and body content',
            density: '0.8-1.5%',
            importance: 'Medium',
            searchVolume: Math.floor(Math.random() * 3000) + 1000,
            difficulty: Math.floor(Math.random() * 40) + 20,
            cpc: `$${(Math.random() * 3 + 0.5).toFixed(2)}`
          })),
          relatedKeywords: [
            `best ${topic || 'content'}`,
            `${topic || 'content'} guide`,
            `${topic || 'content'} for ${targetAudience || 'beginners'}`
          ]
        },
        onPageOptimization: {
          title: `Use primary keyword near the beginning of the title tag (60 chars max)`,
          metaDescription: `Include primary and secondary keywords naturally in meta description (150-160 chars)`,
          headings: `Use H1 for main title with primary keyword, H2/H3 for sections with secondary keywords`,
          content: `Create comprehensive, well-structured content of at least 1,200 words`,
          images: `Use descriptive filenames and alt text containing relevant keywords`
        },
        technicalSeo: {
          mobileOptimization: `Ensure responsive design and fast loading on mobile devices`,
          pagespeed: `Optimize images, minimize CSS/JS, use browser caching`,
          schema: `Implement appropriate schema markup for rich snippets`,
          urls: `Create clean, descriptive URLs containing primary keywords`
        },
        competitiveAnalysis: {
          topCompetitors: [
            {
              domain: `competitor1.com`,
              strengths: [`Strong in ${keywords[0] || 'primary keyword'} content`, 'High domain authority'],
              weaknesses: ['Slow page load speed', 'Poor mobile experience'],
              keywordRankings: {
                [keywords[0] || 'keyword1']: 3,
                [keywords[1] || 'keyword2']: 8
              }
            },
            {
              domain: `competitor2.com`,
              strengths: [`Ranks well for ${keywords[1] || 'secondary keyword'} terms`, 'Excellent content depth'],
              weaknesses: ['Limited keyword targeting', 'Poor internal linking'],
              keywordRankings: {
                [keywords[0] || 'keyword1']: 5,
                [keywords[1] || 'keyword2']: 2
              }
            },
            {
              domain: `competitor3.com`,
              strengths: ['Excellent technical SEO implementation', 'Strong backlink profile'],
              weaknesses: ['Content lacks depth', 'Poor user engagement metrics'],
              keywordRankings: {
                [keywords[0] || 'keyword1']: 7,
                [keywords[1] || 'keyword2']: 4
              }
            }
          ],
          gaps: [
            `More comprehensive content than competitors`,
            `Better internal linking structure`,
            `More engaging multimedia content`
          ]
        },
        actionPlan: [
          {
            action: `Optimize page title and meta description with primary keywords`,
            priority: 'High',
            difficulty: 'Easy',
            impact: 'High',
            timeframe: '1-2 days'
          },
          {
            action: `Restructure content with proper heading hierarchy`,
            priority: 'High',
            difficulty: 'Medium',
            impact: 'Medium',
            timeframe: '2-3 days'
          },
          {
            action: `Improve keyword density in first 100 words`,
            priority: 'Medium',
            difficulty: 'Easy',
            impact: 'Medium',
            timeframe: '1 day'
          },
          {
            action: `Add internal links to related content`,
            priority: 'Medium',
            difficulty: 'Medium',
            impact: 'Medium',
            timeframe: '1-2 days'
          },
          {
            action: `Implement schema markup for better rich snippets`,
            priority: 'Medium',
            difficulty: 'Hard',
            impact: 'High',
            timeframe: '3-5 days'
          }
        ],
        expectedOutcomes: {
          rankings: 'Improved positions for target keywords',
          traffic: 'Increased organic traffic by 15-30%',
          engagement: 'Better user engagement metrics',
          conversions: 'Higher conversion rates from organic visitors'
        }
      },
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: AGENT_ID,
          content: 'Initial SEO optimization report',
          feedback: [],
          incorporatedConsultations: [],
          changes: 'Initial creation',
          reasoning
        }
      ],
      qualityScore: 0.92
    };

    console.log(`SEO Optimization Agent: Created SEO optimization artifact with ID: ${seoOptimizationArtifact.id}`);

    // Store the artifact in the state and track it in generatedArtifacts array
    await stateManager.trackNewArtifact(state.id, seoOptimizationArtifact);

    // Update the agent state with the new artifact ID
    if (!agentState.generatedArtifacts) {
      agentState.generatedArtifacts = [];
    }
    agentState.generatedArtifacts.push(seoOptimizationArtifact.id);

    // Update the workflowProgress to indicate SEO optimization is complete
    await stateManager.updateState(state.id, (currentState) => {
      return {
        ...currentState,
        agentStates: {
          ...currentState.agentStates,
          [AGENT_ID]: agentState
        },
        workflowProgress: {
          ...currentState.workflowProgress,
          seoOptimizationComplete: true
        }
      };
    });

    console.log(`SEO Optimization Agent: Updated workflowProgress.seoOptimizationComplete to true`);

    // Create a response message with the artifact delivery
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: `Generated SEO optimization report for ${topic || 'your content'}`,
        artifactId: seoOptimizationArtifact.id,
        artifactType: seoOptimizationArtifact.type,
        artifact: seoOptimizationArtifact
      },
      conversationId: message.conversationId
    };

    // Update the state with the updated agent state
    const updatedState: IterativeCollaborationState = {
      ...state,
      agentStates: {
        ...state.agentStates,
        [AGENT_ID]: agentState
      }
    };

    // Return the response and state updates in StandardizedHandlerResult format
    return {
      response: responseMessage,
      stateUpdates: updatedState,
      artifactUpdates: {
        new: {
          [seoOptimizationArtifact.id]: seoOptimizationArtifact
        }
      }
    };
  } catch (error) {
    console.error('Error generating SEO optimization artifact:', error);

    // Create a response message with the agent's introduction and capabilities
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.RESPONSE,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: 'SEO Optimization Agent is ready to collaborate on content optimization, SEO analysis, and creating recommendations for better search visibility. I can analyze content for SEO improvements, provide recommendations for ranking better, and help optimize existing content.',
        capabilities: [
          'SEO content analysis',
          'Search optimization recommendations',
          'Content structure improvement',
          'Metadata optimization',
          'SEO best practices consultation'
        ],
        error: `Failed to generate SEO optimization artifact: ${error instanceof Error ? error.message : String(error)}`
      },
      conversationId: message.conversationId
    };

    // Update the state with the updated agent state
    const updatedState: IterativeCollaborationState = {
      ...state,
      agentStates: {
        ...state.agentStates,
        [AGENT_ID]: agentState
      }
    };

    return {
      response: responseMessage,
      stateUpdates: updatedState
    };
  }
}

/**
 * Handle artifact request from another agent
 */
export async function handleArtifactRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Optimization Agent: Handling artifact request from ${message.from}`);

  // Extract request details
  const { artifactType, topic, industry, targetAudience, contentType, region, content, url, keywords, contentArtifactId } = message.content;

  if (!['seo-analysis', 'seo-recommendations', 'seo-feedback'].includes(artifactType as string)) {
    // Send a response that we can't generate this artifact type
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.SYSTEM_MESSAGE, // Using SYSTEM_MESSAGE for error
      from: AGENT_ID,
      to: message.from,
      content: {
        error: `Unsupported artifact type: ${artifactType}`,
        message: `SEO Optimization Agent cannot generate artifacts of type: ${artifactType}. Supported types are: seo-analysis, seo-recommendations, seo-feedback.`
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse
    };
  }

  // Initialize agent state if needed
  const agentState: AgentState = state.agentStates?.[AGENT_ID] || {
    id: AGENT_ID,
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };

  // Update state with this processed message
  if (!agentState.processedRequests) {
    agentState.processedRequests = [];
  }
  agentState.processedRequests.push(message.id);
  agentState.lastUpdated = new Date().toISOString();

  try {
    let contentToAnalyze = '';
    let keywordList: string[] = Array.isArray(keywords) ? keywords : [];

    // If a content artifact ID is provided, fetch that content
    if (contentArtifactId) {
      // Find the artifact in state.artifacts (object)
      const contentArtifact = state.artifacts && contentArtifactId in state.artifacts ?
        state.artifacts[contentArtifactId] :
        undefined;

      if (!contentArtifact) {
        throw new Error(`Content artifact with ID ${contentArtifactId} not found`);
      }

      // Extract content from the artifact - safely handle possibly undefined content
      if (contentArtifact.content) {
        const contentObj = contentArtifact.content as Record<string, any>;

        // Try to find content in various fields
        if (typeof contentObj === 'string') {
          contentToAnalyze = contentObj;
        } else if (contentObj.content && typeof contentObj.content === 'string') {
          contentToAnalyze = contentObj.content;
        } else if (contentObj.text && typeof contentObj.text === 'string') {
          contentToAnalyze = contentObj.text;
        } else if (contentObj.analysis && typeof contentObj.analysis === 'string') {
          contentToAnalyze = contentObj.analysis;
        } else if (contentObj.recommendations && typeof contentObj.recommendations === 'string') {
          contentToAnalyze = contentObj.recommendations;
        } else {
          contentToAnalyze = JSON.stringify(contentObj);
        }

        // Extract keywords if available
        if (contentObj.keywords && Array.isArray(contentObj.keywords)) {
          keywordList = keywordList.length ? keywordList : contentObj.keywords;
        }
      }
    } else if (content) {
      // Use the content directly if provided
      contentToAnalyze = typeof content === 'string' ? content : JSON.stringify(content);
    } else if (url) {
      // If only URL is provided, use that
      contentToAnalyze = `URL: ${url}`;
    } else {
      throw new Error('No content or artifact provided for SEO analysis');
    }

    let artifact: IterativeArtifact;
    let enhancedReasoning: EnhancedReasoning;

    // Handle different artifact types
    switch (artifactType) {
      case 'seo-analysis':
        // Generate SEO analysis
        const analysisResult = await analyzeSeoContent(contentToAnalyze, keywordList);

        // Create enhanced reasoning
        enhancedReasoning = createEnhancedReasoning(
          [
            'Analyzing content for SEO effectiveness',
            'Evaluating keyword usage and placement',
            'Assessing content structure and readability'
          ],
          [
            'Search engine ranking factors',
            'Target audience preferences',
            'Industry best practices',
            'Competitor strategies'
          ],
          'Provide detailed SEO analysis with actionable recommendations',
          AGENT_ID,
          { confidence: 0.9 }
        );

        // Store the reasoning
        await storeAgentReasoning(message.conversationId, AGENT_ID, null, message.id, message.conversationId, enhancedReasoning);

        // Create the artifact
        artifact = {
          id: uuidv4(),
          type: 'seo-analysis',
          name: 'SEO Analysis Report',
          currentVersion: 1,
          iterations: [],
          qualityScore: analysisResult.score / 100,
          content: {
            analysis: analysisResult.analysis,
            recommendations: analysisResult.recommendations,
            keywordDensity: analysisResult.keywordDensity,
            optimizationScore: analysisResult.score
          },
          status: 'completed' as ArtifactStatus,
          createdBy: AGENT_ID,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            keywords: keywordList
          }
        };
        break;

      case 'seo-recommendations':
        // Generate SEO recommendations
        const recommendations = await generateSeoRecommendations(contentToAnalyze, keywordList);

        // Create enhanced reasoning
        enhancedReasoning = createEnhancedReasoning(
          [
            'Identifying opportunities for SEO improvement',
            'Prioritizing recommendations based on impact',
            'Considering technical and content-based improvements'
          ],
          [
            'Current content performance',
            'Keyword competitiveness',
            'User search intent',
            'Implementation difficulty'
          ],
          'Provide prioritized SEO recommendations with expected impact',
          AGENT_ID,
          { confidence: 0.85 }
        );

        // Store the reasoning
        await storeAgentReasoning(message.conversationId, AGENT_ID, null, message.id, message.conversationId, enhancedReasoning);

        // Create the artifact
        artifact = {
          id: uuidv4(),
          type: 'seo-recommendations',
          name: 'SEO Optimization Recommendations',
          currentVersion: 1,
          iterations: [],
          qualityScore: recommendations.score / 100,
          content: {
            recommendations: recommendations.recommendations,
            prioritizedTasks: recommendations.prioritizedTasks,
            keywordSuggestions: recommendations.keywordSuggestions,
            score: recommendations.score
          },
          status: 'completed' as ArtifactStatus,
          createdBy: AGENT_ID,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            keywords: keywordList
          }
        };
        break;

      case 'seo-feedback':
        // Generate SEO feedback
        const feedback = await generateSeoFeedback(contentToAnalyze, keywordList);

        // Create enhanced reasoning
        enhancedReasoning = createEnhancedReasoning(
          [
            'Evaluating content against SEO best practices',
            'Identifying strengths and weaknesses',
            'Formulating constructive feedback'
          ],
          [
            'Content quality and relevance',
            'Keyword optimization',
            'Technical SEO elements',
            'User experience factors'
          ],
          'Provide balanced SEO feedback with specific improvement suggestions',
          AGENT_ID,
          { confidence: 0.9 }
        );

        // Store the reasoning
        await storeAgentReasoning(message.conversationId, AGENT_ID, null, message.id, message.conversationId, enhancedReasoning);

        // Safely handle feedback structure
        let feedbackContent: Record<string, any>;
        if (typeof feedback === 'string') {
          feedbackContent = { overallFeedback: feedback };
        } else {
          const feedbackObj = feedback as Record<string, any>;
          feedbackContent = {
            strengths: Array.isArray(feedbackObj?.strengths) ? feedbackObj.strengths : [],
            weaknesses: Array.isArray(feedbackObj?.weaknesses) ? feedbackObj.weaknesses : [],
            suggestions: Array.isArray(feedbackObj?.suggestions) ? feedbackObj.suggestions : [],
            overallAssessment: feedbackObj?.overallAssessment || 'No assessment provided'
          };
        }

        // Create the artifact
        artifact = {
          id: uuidv4(),
          type: 'seo-feedback',
          name: 'SEO Content Feedback',
          currentVersion: 1,
          iterations: [],
          qualityScore: 0.85,
          content: feedbackContent,
          status: 'completed' as ArtifactStatus,
          createdBy: AGENT_ID,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {
            keywords: keywordList
          }
        };
        break;

      default:
        throw new Error(`Unsupported artifact type: ${artifactType}`);
    }

    // Create the response message with the artifact
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: `Here is the requested ${artifactType} for your content.`,
        artifactId: artifact.id,
        artifactType: artifact.type
      },
      conversationId: message.conversationId
    };

    // Update the state with the generated artifact and processed request
    if (!agentState.generatedArtifacts) {
      agentState.generatedArtifacts = [];
    }
    agentState.generatedArtifacts.push(artifact.id);

    // Update the state with the agent state and the new artifact
    const updatedState: IterativeCollaborationState = {
      ...state,
      agentStates: {
        ...state.agentStates,
        [AGENT_ID]: agentState
      },
      artifacts: {
        ...state.artifacts,
        [artifact.id]: artifact
      }
    };

    return {
      response: responseMessage,
      stateUpdates: updatedState
    };
  } catch (error) {
    console.error('Error generating SEO artifact:', error);

    // Create error response
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.SYSTEM_MESSAGE, // Using SYSTEM_MESSAGE for error
      from: AGENT_ID,
      to: message.from,
      content: {
        error: 'Failed to generate SEO artifact',
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse
    };
  }
}

/**
 * Handle consultation request from another agent
 */
export async function handleConsultationRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Optimization Agent: Handling consultation request from ${message.from}`);

  // Extract request details
  const { topic, context, questionType, content } = message.content;

  // Initialize agent state if needed
  const agentState: AgentState = state.agentStates?.[AGENT_ID] || {
    id: AGENT_ID,
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };

  try {
    // Create enhanced reasoning
    const reasoning: EnhancedReasoning = createEnhancedReasoning(
      [
        'Analyzing consultation request regarding SEO',
        'Identifying key SEO considerations related to the question',
        'Formulating expert SEO advice'
      ],
      [
        'Current SEO best practices',
        'Relevance to the specific content type',
        'Potential impact on search visibility',
        'Implementation complexity'
      ],
      'Provide specific, actionable SEO consultation',
      AGENT_ID,
      { confidence: 0.9 }
    );

    // Store the reasoning
    await storeAgentReasoning(message.conversationId, AGENT_ID, null, message.id, message.conversationId, reasoning);

    // Generate the consultation response
    const response = await provideSeoConsultation(
      topic as string || '',
      content as string || '',
      context as Record<string, any> || {}
    );

    // Create a consultation record
    const consultation: Consultation = {
      id: uuidv4(),
      fromAgent: AGENT_ID,
      toAgent: message.from,
      artifactId: '',  // No specific artifact for general consultation
      timestamp: new Date().toISOString(),
      feedback: typeof response === 'string' ? response : response.response,
      suggestions: Array.isArray(response.suggestions) ? response.suggestions : [],
      incorporated: false,
      requestId: message.id,
      question: content as string || topic as string || 'SEO consultation'
    };

    // Create the response message with the consultation
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.CONSULTATION_RESPONSE,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: 'Here is my SEO consultation response.',
        consultationId: consultation.id,
        answer: typeof response === 'string' ? response : response.response
      },
      conversationId: message.conversationId
    };

    // Update state tracking
    if (!agentState.consultationsProvided) {
      agentState.consultationsProvided = [];
    }
    agentState.consultationsProvided.push(consultation.id);
    if (!agentState.processedRequests) {
      agentState.processedRequests = [];
    }
    agentState.processedRequests.push(message.id);
    agentState.lastUpdated = new Date().toISOString();

    // Update the state with the new consultation and updated agent state
    const updatedState: IterativeCollaborationState = {
      ...state,
      agentStates: {
        ...state.agentStates,
        [AGENT_ID]: agentState
      },
      consultations: {
        ...state.consultations,
        [consultation.id]: consultation
      }
    };

    return {
      response: responseMessage,
      stateUpdates: updatedState
    };
  } catch (error) {
    console.error('Error providing SEO consultation:', error);

    // Create error response
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.SYSTEM_MESSAGE,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: 'Failed to provide SEO consultation',
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse
    };
  }
}

/**
 * Handle feedback from another agent or user
 */
export async function handleFeedback(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Optimization Agent: Handling feedback from ${message.from}`);

  // Check if this is an evaluation feedback message
  if (message.content?.evaluation) {
    try {
      // Import the feedback handler
      const { processFeedback } = await import('../../utils/feedback-handler');

      // Process feedback using the common feedback handler
      const result = await processFeedback(
        message,
        state,
        stateManager,
        messaging,
        AGENT_ID
      );

      // If an artifact was updated, return it
      if (result.updatedArtifact) {
        return {
          response: result.response,
          artifact: result.updatedArtifact
        };
      }

      return { response: result.response };
    } catch (error) {
      console.error('Error processing evaluation feedback:', error);

      // Fall back to standard feedback handling if there's an error
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Error processing evaluation feedback',
          message: `An error occurred while processing the evaluation feedback: ${error instanceof Error ? error.message : String(error)}`
        },
        conversationId: message.conversationId
      };

      return { response: errorResponse };
    }
  }

  // Handle traditional feedback format
  const { feedback, artifactId, rating } = message.content;

  // Initialize agent state if needed
  const agentState: AgentState = state.agentStates?.[AGENT_ID] || {
    id: AGENT_ID,
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };

  // Create acknowledgement response
  const responseMessage: IterativeMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    type: IterativeMessageType.ACKNOWLEDGMENT,
    from: AGENT_ID,
    to: message.from,
    content: {
      message: `Thank you for your feedback on ${artifactId ? `artifact ${artifactId}` : 'my work'}. I'll incorporate your suggestions in future SEO optimizations.`,
      acknowledgedMessageId: message.id
    },
    conversationId: message.conversationId
  };

  // Update state tracking
  if (!agentState.processedRequests) {
    agentState.processedRequests = [];
  }
  agentState.processedRequests.push(message.id);
  agentState.lastUpdated = new Date().toISOString();

  // If feedback references a specific artifact, try to update it
  if (artifactId && state.artifacts && state.artifacts[artifactId] && state.artifacts[artifactId].createdBy === AGENT_ID) {
    // Create a deep copy of the state artifacts to avoid direct mutation
    const updatedArtifacts = {
      ...state.artifacts,
      [artifactId]: {
        ...state.artifacts[artifactId],
        metadata: {
          ...state.artifacts[artifactId].metadata,
          feedback: feedback as string,
          feedbackRating: rating,
          feedbackFrom: message.from,
          feedbackAt: new Date().toISOString()
        }
      }
    };
    state = {
      ...state,
      artifacts: updatedArtifacts
    };
  }

  // Update the state
  const updatedState: IterativeCollaborationState = {
    ...state,
    agentStates: {
      ...state.agentStates,
      [AGENT_ID]: agentState
    }
    // Artifacts already updated in the state object above
  };

  return {
    response: responseMessage,
    stateUpdates: updatedState
  };
}

/**
 * Handle discussion contributions in collaborative sessions
 */
export async function handleDiscussionRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Optimization Agent: Handling discussion request from ${message.from}`);

  // Extract discussion details
  const { topic, discussionId, lastMessage, perspective } = message.content;

  // Initialize agent state if needed
  const agentState: AgentState = state.agentStates?.[AGENT_ID] || {
    id: AGENT_ID,
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };

  try {
    // Generate a discussion contribution
    const contribution = await generateDiscussionContribution(
      topic as string,
      lastMessage as string,
      [], // Participants - could be extracted from the discussion if needed
      perspective as string || 'seo-optimization'
    );

    // Store the reasoning
    await storeAgentReasoning(message.conversationId, AGENT_ID, null, message.id, message.conversationId, contribution.reasoning);

    // Create the response message
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: contribution.content,
        discussionId,
        perspective: 'seo-optimization'
      },
      conversationId: message.conversationId
    };

    // Update state tracking
    if (!agentState.processedRequests) {
      agentState.processedRequests = [];
    }
    agentState.processedRequests.push(message.id);
    agentState.lastUpdated = new Date().toISOString();

    // Update the state
    const updatedState: IterativeCollaborationState = {
      ...state,
      agentStates: {
        ...state.agentStates,
        [AGENT_ID]: agentState
      }
    };

    return {
      response: responseMessage,
      stateUpdates: updatedState
    };
  } catch (error) {
    console.error('Error generating discussion contribution:', error);

    // Create error response
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.SYSTEM_MESSAGE,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: 'Failed to generate discussion contribution',
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse
    };
  }
}
