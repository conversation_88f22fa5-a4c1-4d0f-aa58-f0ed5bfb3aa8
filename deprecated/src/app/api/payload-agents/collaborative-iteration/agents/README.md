# Consolidated Agent System for Collaborative Iteration

This directory contains the consolidated implementation of all agents used in the AuthenCIO collaborative iteration system.

## Structure

The agent system follows a consistent implementation pattern:

```
agents/
├── content-generation/        # Content Generation Agent
│   ├── index.ts               # Main agent class and singleton export
│   ├── methods.ts             # Agent-specific methods
│   └── handlers.ts            # Message handlers
├── content-strategy/          # Content Strategy Agent
│   ├── index.ts
│   ├── methods.ts
│   └── handlers.ts
├── market-research/           # Market Research Agent
│   ├── index.ts
│   ├── methods.ts
│   └── handlers.ts
├── seo-keyword/               # SEO Keyword Agent
│   ├── index.ts
│   ├── methods.ts
│   └── handlers.ts
├── seo-optimization/          # SEO Optimization Agent
│   ├── index.ts
│   ├── methods.ts
│   └── handlers.ts
└── api/                       # API routes for agent system
    └── route.ts               # NextJS API route handler
```

## Agent Implementation Pattern

Each agent follows the same implementation pattern:

1. **Index file**: Exports the main agent class and a singleton instance.
2. **Methods file**: Contains the core logic and specialized methods for the agent.
3. **Handlers file**: Contains message handlers for different types of messages.

## Usage

Import the agents and utilities from the main `index.ts` file:

```typescript
import {
  contentGenerationAgent,
  seoKeywordAgent,
  marketResearchAgent,
  contentStrategyAgent,
  seoOptimizationAgent,
  messageBus
} from '../agents';
```

## Core Features

1. **State Management**: All agents track their state including processed requests, generated artifacts, and consultations.
2. **Structured Reasoning**: Agents use chain-of-thought reasoning to provide explainable outputs.
3. **Circuit Breaker Pattern**: Prevents infinite loops and message storms between agents.
4. **Artifact Checking**: Prevents redundant work by checking if artifacts already exist.

## Integration with API

The API route handler in `api/route.ts` provides endpoints for:

1. Session creation
2. Message handling
3. State retrieval
4. Goal management
5. Agent collaboration

## Utilities

All agents utilize common utilities from the parent directory:

- `messageBus`: For agent-to-agent communication
- `agentStateManager`: For tracking agent state
- `agentMessaging`: For structured message handling
- `agentHandler`: For routing messages to the appropriate handler
- `agentCommunicationManager`: For enhanced agent collaboration
