/**
 * Consolidated Agent System Exports
 *
 * This file exports all agent implementations and utilities for the collaborative agent system.
 * It serves as the single source of truth for all agent code.
 */

// Import agent classes
import { MarketResearchAgent } from './market-research';
import { SeoKeywordAgent } from './seo-keyword';
import { ContentStrategyAgent } from './content-strategy';
import { ContentGenerationAgent } from './content-generation';
import { SeoOptimizationAgent } from './seo-optimization';

// Create singleton instances of each agent
export const marketResearchAgent = new MarketResearchAgent();
export const seoKeywordAgent = new SeoKeywordAgent();
export const contentStrategyAgent = new ContentStrategyAgent();
export const contentGenerationAgent = new ContentGenerationAgent();
export const seoOptimizationAgent = new SeoOptimizationAgent();

// Export agent classes for direct instantiation if needed
export {
  MarketResearchAgent,
  SeoKeywordAgent,
  ContentStrategyAgent,
  ContentGenerationAgent,
  SeoOptimizationAgent
};

// Export agent IDs
export const CONTENT_GENERATION_AGENT_ID = 'content-generation';
export const MARKET_RESEARCH_AGENT_ID = 'market-research';
export const SEO_KEYWORD_AGENT_ID = 'seo-keyword';
export const CONTENT_STRATEGY_AGENT_ID = 'content-strategy';
export const SEO_OPTIMIZATION_AGENT_ID = 'seo-optimization';

// Export Market Research handlers
export {
  handleMarketResearchInitialRequest,
  handleMarketResearchArtifactRequest
} from './market-research';

// Export SEO Keyword handlers
export {
  handleSeoKeywordInitialRequest,
  handleSeoKeywordArtifactRequest
} from './seo-keyword';

// Create placeholder handlers for the remaining agents
// These will be implemented in future PRs

// Content Generation handlers
export const handleContentGenerationInitialRequest = async (message: any, state: any, stateManager: any, messaging: any) => {
  console.log('Content Generation Initial Request handler called');

  const { topic, contentType, targetAudience, tone } = message.content || {};

  if (!topic) {
    return {
      success: false,
      message: 'Topic is required for content generation',
      response: await messaging.send(
        message.conversationId,
        message.from,
        'ERROR',
        {
          error: 'Missing required fields',
          message: 'Topic is required for content generation'
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Find market research, keyword research, and content strategy artifacts if available
  let marketResearch = null;
  let keywordResearch = null;
  let contentStrategy = null;
  let seoOptimization = null;

  if (state.artifacts) {
    // Find market research artifact
    const marketResearchArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'market-research')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (marketResearchArtifacts.length > 0) {
      marketResearch = marketResearchArtifacts[0];
    }

    // Find keyword research artifact
    const keywordArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'seo-keywords')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (keywordArtifacts.length > 0) {
      keywordResearch = keywordArtifacts[0];
    }

    // Find content strategy artifact
    const contentStrategyArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'content-strategy')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (contentStrategyArtifacts.length > 0) {
      contentStrategy = contentStrategyArtifacts[0];
    }

    // Find SEO optimization artifact
    const seoOptimizationArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'seo-optimization')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (seoOptimizationArtifacts.length > 0) {
      seoOptimization = seoOptimizationArtifacts[0];
    }
  }

  // Generate blog content based on available information
  const blogTitle = contentStrategy ?
    contentStrategy.iterations[0].content.contentStructure.title :
    `The Ultimate Guide to ${topic}`;

  const primaryKeyword = keywordResearch ?
    keywordResearch.iterations[0].content.primaryKeyword :
    topic;

  const secondaryKeywords = keywordResearch ?
    keywordResearch.iterations[0].content.secondaryKeywords :
    [`${topic} guide`, `${topic} examples`, `how to use ${topic}`, `${topic} benefits`];

  const targetAudienceInfo = marketResearch ?
    marketResearch.iterations[0].content.targetAudience :
    targetAudience || 'general audience';

  const contentSections = contentStrategy ?
    contentStrategy.iterations[0].content.contentStructure.sections :
    [
      {
        heading: `Understanding ${topic}`,
        content: `A comprehensive explanation of ${topic}, including its definition, importance, and key concepts.`
      },
      {
        heading: `Benefits of ${topic}`,
        content: `A detailed exploration of the benefits and advantages of ${topic} for the target audience.`
      },
      {
        heading: `How to Implement ${topic}`,
        content: `Step-by-step instructions on how to implement or use ${topic} effectively.`
      },
      {
        heading: `${topic} Best Practices`,
        content: `Expert tips and best practices for maximizing the value of ${topic}.`
      },
      {
        heading: `Common Challenges with ${topic} and How to Overcome Them`,
        content: `A discussion of common challenges and obstacles related to ${topic}, along with practical solutions.`
      },
      {
        heading: `Case Studies: ${topic} in Action`,
        content: `Real-world examples and case studies demonstrating the successful application of ${topic}.`
      },
      {
        heading: `Future Trends in ${topic}`,
        content: `An exploration of emerging trends and future developments in ${topic}.`
      },
      {
        heading: `Conclusion`,
        content: `A summary of key points and a call to action.`
      }
    ];

  // Generate the blog content
  const blogContent = {
    title: blogTitle,
    metaDescription: seoOptimization ?
      seoOptimization.iterations[0].content.optimizedElements.metaDescription :
      `Learn everything you need to know about ${topic} in this comprehensive guide. Discover best practices, examples, and step-by-step instructions.`,
    introduction: `
# ${blogTitle}

*Published on ${new Date().toLocaleDateString()} | Last updated: ${new Date().toLocaleDateString()}*

Are you looking to understand ${primaryKeyword} and how it can benefit your business? You've come to the right place. In this comprehensive guide, we'll explore everything you need to know about ${primaryKeyword}, from basic concepts to advanced strategies.

${targetAudienceInfo === 'Startups' ?
  `As a startup, implementing ${primaryKeyword} effectively can give you a competitive edge and accelerate your growth.` :
  `Whether you're new to ${primaryKeyword} or looking to enhance your existing knowledge, this guide will provide valuable insights.`}

Let's dive in and discover how ${primaryKeyword} can transform your approach and deliver exceptional results.
`,
    tableOfContents: `
## Table of Contents

${contentSections.map((section, index) => `${index + 1}. [${section.heading}](#${section.heading.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '')})`).join('\n')}
`,
    body: contentSections.map(section => `
## ${section.heading}

${section.content}

${section.heading.includes('Benefits') ?
  `The key benefits of ${primaryKeyword} include:

* Improved efficiency and productivity
* Enhanced customer satisfaction
* Cost reduction and resource optimization
* Competitive advantage in the marketplace
* Scalability for future growth` : ''}

${section.heading.includes('Implement') ?
  `Here's a step-by-step approach to implementing ${primaryKeyword}:

1. **Assessment**: Evaluate your current situation and identify needs
2. **Planning**: Develop a comprehensive implementation strategy
3. **Resource Allocation**: Assign the necessary resources and team members
4. **Implementation**: Execute the plan in phases
5. **Monitoring**: Track progress and make adjustments as needed
6. **Evaluation**: Assess results and identify areas for improvement` : ''}

${section.heading.includes('Best Practices') ?
  `When working with ${primaryKeyword}, follow these best practices:

* Start with clear objectives and goals
* Ensure stakeholder buy-in across your organization
* Implement robust measurement and tracking systems
* Continuously train and educate your team
* Stay updated on industry trends and innovations
* Regularly review and optimize your approach` : ''}

${section.heading.includes('Challenges') ?
  `Common challenges when implementing ${primaryKeyword} include:

1. **Resource constraints**: Limited budget or personnel
2. **Resistance to change**: Organizational reluctance to adopt new approaches
3. **Technical complexity**: Navigating the learning curve
4. **Integration issues**: Aligning with existing systems and processes
5. **Measuring ROI**: Quantifying the impact and value

To overcome these challenges, focus on clear communication, phased implementation, comprehensive training, and establishing concrete metrics for success.` : ''}

${section.heading.includes('Case Studies') ?
  `### Case Study: XYZ Company

XYZ Company implemented ${primaryKeyword} to address declining customer engagement. By following a structured approach, they achieved:

* 45% increase in customer satisfaction
* 30% reduction in operational costs
* 25% improvement in team productivity
* 50% faster response times

Their success demonstrates the transformative potential of ${primaryKeyword} when implemented strategically.` : ''}

${section.heading.includes('Future Trends') ?
  `The future of ${primaryKeyword} looks promising, with several emerging trends:

1. **AI and automation** enhancing capabilities and efficiency
2. **Integration with other technologies** creating powerful synergies
3. **Personalization and customization** becoming standard features
4. **Mobile-first approaches** dominating the landscape
5. **Data-driven decision making** becoming increasingly sophisticated

Staying ahead of these trends will position you for continued success.` : ''}

${section.heading.includes('Conclusion') ?
  `In conclusion, ${primaryKeyword} offers tremendous potential for organizations willing to invest in its implementation. By understanding the core concepts, following best practices, and learning from successful case studies, you can harness the power of ${primaryKeyword} to drive significant improvements in your business.

Remember that successful implementation requires commitment, resources, and a willingness to adapt. Start with small steps, measure your progress, and continuously refine your approach.

Ready to transform your business with ${primaryKeyword}? [Contact us](#) today to learn how we can help you get started.` : ''}
`).join('\n'),
    conclusion: `
## Final Thoughts

${primaryKeyword} continues to evolve and offer new opportunities for businesses across industries. By staying informed about best practices and emerging trends, you can maximize the value it brings to your organization.

We hope this guide has provided you with valuable insights and practical strategies for implementing ${primaryKeyword} effectively. If you have any questions or need further assistance, don't hesitate to [reach out to our team](#).

### Frequently Asked Questions

**Q: What is the biggest benefit of ${primaryKeyword}?**
A: While benefits vary by organization, most businesses report significant improvements in efficiency, customer satisfaction, and competitive advantage.

**Q: How long does it take to implement ${primaryKeyword}?**
A: Implementation timelines vary based on organizational size, complexity, and resources. A basic implementation might take 2-3 months, while more comprehensive approaches could take 6-12 months.

**Q: Is ${primaryKeyword} suitable for small businesses?**
A: Absolutely! ${primaryKeyword} can be scaled to fit organizations of any size, with solutions available for various budgets and requirements.

**Q: How can I measure the ROI of ${primaryKeyword}?**
A: Key metrics include efficiency improvements, cost savings, revenue growth, customer satisfaction scores, and employee productivity measures.

**Q: What are the most common mistakes when implementing ${primaryKeyword}?**
A: Common pitfalls include inadequate planning, insufficient training, lack of clear objectives, poor change management, and failure to measure results effectively.
`,
    callToAction: contentStrategy ?
      contentStrategy.iterations[0].content.contentStructure.callToAction :
      `Ready to transform your business with ${primaryKeyword}? Contact us today to learn how we can help you get started.`,
    keywords: {
      primary: primaryKeyword,
      secondary: secondaryKeywords,
      usage: {
        title: true,
        headings: true,
        introduction: true,
        body: true,
        conclusion: true
      }
    },
    metadata: {
      wordCount: 2500,
      readingTime: '10-12 minutes',
      targetAudience: targetAudienceInfo,
      contentType: contentType || 'blog-article',
      tone: tone || 'informative'
    },
    seoOptimization: seoOptimization ? {
      metaTitle: seoOptimization.iterations[0].content.optimizedElements.title,
      metaDescription: seoOptimization.iterations[0].content.optimizedElements.metaDescription,
      keywordDensity: {
        primary: '1.8%',
        secondary: 'Balanced throughout'
      },
      headingStructure: 'H1 > H2 > H3 hierarchy',
      internalLinks: 3,
      externalLinks: 2,
      imageAltTags: seoOptimization.iterations[0].content.optimizedElements.imageAltText
    } : {
      metaTitle: blogTitle,
      metaDescription: `Learn everything you need to know about ${topic} in this comprehensive guide. Discover best practices, examples, and step-by-step instructions.`,
      keywordDensity: {
        primary: '1.5%',
        secondary: 'Balanced throughout'
      },
      headingStructure: 'H1 > H2 > H3 hierarchy',
      internalLinks: 3,
      externalLinks: 2,
      imageAltTags: [
        `${topic} implementation diagram`,
        `${topic} best practices infographic`,
        `${topic} case study results chart`
      ]
    }
  };

  // Create blog content artifact
  const artifact = {
    id: require('uuid').v4(),
    name: `Blog Article: ${blogTitle}`,
    type: 'blog-content',
    status: 'completed',
    createdBy: 'content-generation',
    createdAt: new Date().toISOString(),
    currentVersion: 1,
    iterations: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'content-generation',
        content: blogContent,
        feedback: [],
        incorporatedConsultations: []
      }
    ],
    qualityScore: 90,
    contentType: contentType || 'blog-article',
    wordCount: 2500,
    seoScore: 85
  };

  // Add the artifact to the state
  await stateManager.addArtifact(message.conversationId, artifact);

  // Update workflow progress
  await require('../utils/stateStore').stateStore.updateState(message.conversationId, (state: any) => {
    if (state.workflowProgress) {
      state.workflowProgress.contentGenerationComplete = true;
    }
    return state;
  });

  // Return the response
  return {
    success: true,
    message: `Successfully generated blog content for ${topic}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      'ARTIFACT_DELIVERY',
      {
        artifact,
        message: `Generated blog content for ${topic}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
};

export const handleContentGenerationArtifactRequest = async (message: any, state: any, stateManager: any, messaging: any) => {
  console.log('Content Generation Artifact Request handler called');

  // Extract request details
  const { artifactId, artifactType } = message.content || {};

  // Find the requested artifact
  let artifact = null;

  if (artifactId && state.artifacts && state.artifacts[artifactId]) {
    artifact = state.artifacts[artifactId];
  } else if (artifactType === 'blog-content' && state.artifacts) {
    // Find the latest blog content artifact
    const blogArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'blog-content' && a.createdBy === 'content-generation')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (blogArtifacts.length > 0) {
      artifact = blogArtifacts[0];
    }
  }

  if (!artifact) {
    return {
      success: false,
      message: 'Artifact not found',
      response: await messaging.send(
        message.conversationId,
        message.from,
        'ERROR',
        {
          error: 'Artifact not found',
          message: `Could not find the requested artifact`
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Return the artifact
  return {
    success: true,
    message: `Successfully retrieved artifact ${artifact.id}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      'ARTIFACT_DELIVERY',
      {
        artifact,
        message: `Retrieved artifact ${artifact.id}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
};

// Content Strategy handlers
export const handleContentStrategyInitialRequest = async (message: any, state: any, stateManager: any, messaging: any) => {
  console.log('Content Strategy Initial Request handler called');

  const { topic, contentType, targetAudience, tone } = message.content || {};

  if (!topic) {
    return {
      success: false,
      message: 'Topic is required for content strategy',
      response: await messaging.send(
        message.conversationId,
        message.from,
        'ERROR',
        {
          error: 'Missing required fields',
          message: 'Topic is required for content strategy'
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Find market research and keyword research artifacts if available
  let marketResearch = null;
  let keywordResearch = null;

  if (state.artifacts) {
    // Find market research artifact
    const marketResearchArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'market-research')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (marketResearchArtifacts.length > 0) {
      marketResearch = marketResearchArtifacts[0];
    }

    // Find keyword research artifact
    const keywordArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'seo-keywords')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (keywordArtifacts.length > 0) {
      keywordResearch = keywordArtifacts[0];
    }
  }

  // Generate content strategy based on available information
  const contentStrategy = {
    topic,
    contentType: contentType || 'blog-article',
    targetAudience: targetAudience || 'general audience',
    tone: tone || 'informative',
    contentGoals: [
      'Establish authority on the topic',
      'Improve search engine visibility',
      'Engage target audience',
      'Drive conversions',
      'Build brand awareness'
    ],
    contentStructure: {
      title: `The Ultimate Guide to ${topic}`,
      introduction: `An engaging introduction to ${topic} that hooks the reader and establishes the value of the content.`,
      sections: [
        {
          heading: `Understanding ${topic}`,
          content: `A comprehensive explanation of ${topic}, including its definition, importance, and key concepts.`,
          keywords: keywordResearch ? [keywordResearch.iterations[0].content.primaryKeyword] : [topic]
        },
        {
          heading: `Benefits of ${topic}`,
          content: `A detailed exploration of the benefits and advantages of ${topic} for the target audience.`,
          keywords: keywordResearch ? keywordResearch.iterations[0].content.secondaryKeywords.slice(0, 2) : [`${topic} benefits`]
        },
        {
          heading: `How to Implement ${topic}`,
          content: `Step-by-step instructions on how to implement or use ${topic} effectively.`,
          keywords: keywordResearch ? keywordResearch.iterations[0].content.longTailKeywords.slice(0, 2) : [`how to use ${topic}`]
        },
        {
          heading: `${topic} Best Practices`,
          content: `Expert tips and best practices for maximizing the value of ${topic}.`,
          keywords: keywordResearch ? keywordResearch.iterations[0].content.secondaryKeywords.slice(2, 4) : [`${topic} best practices`]
        },
        {
          heading: `Common Challenges with ${topic} and How to Overcome Them`,
          content: `A discussion of common challenges and obstacles related to ${topic}, along with practical solutions.`,
          keywords: keywordResearch ? keywordResearch.iterations[0].content.longTailKeywords.slice(2, 4) : [`${topic} challenges`]
        },
        {
          heading: `Case Studies: ${topic} in Action`,
          content: `Real-world examples and case studies demonstrating the successful application of ${topic}.`,
          keywords: keywordResearch ? keywordResearch.iterations[0].content.relatedKeywords.slice(0, 2) : [`${topic} examples`]
        },
        {
          heading: `Future Trends in ${topic}`,
          content: `An exploration of emerging trends and future developments in ${topic}.`,
          keywords: keywordResearch ? keywordResearch.iterations[0].content.relatedKeywords.slice(2, 4) : [`${topic} trends`]
        },
        {
          heading: `Conclusion`,
          content: `A summary of key points and a call to action.`,
          keywords: keywordResearch ? [keywordResearch.iterations[0].content.primaryKeyword] : [topic]
        }
      ],
      callToAction: `Learn more about how we can help you with ${topic} by contacting us today.`
    },
    contentFormat: contentType || 'blog-article',
    contentLength: '2000-2500 words',
    mediaRecommendations: [
      'Infographics illustrating key concepts',
      'Charts or graphs showing relevant statistics',
      'Images demonstrating practical applications',
      'Video tutorials for complex processes',
      'Interactive elements to increase engagement'
    ],
    distributionChannels: [
      'Company blog',
      'Email newsletter',
      'Social media platforms',
      'Industry forums',
      'Partner websites'
    ],
    keywordStrategy: keywordResearch ? {
      primaryKeyword: keywordResearch.iterations[0].content.primaryKeyword,
      secondaryKeywords: keywordResearch.iterations[0].content.secondaryKeywords,
      keywordPlacement: [
        'Title tag',
        'H1 heading',
        'First paragraph',
        'Subheadings',
        'Meta description',
        'Image alt text',
        'URL'
      ]
    } : {
      primaryKeyword: topic,
      secondaryKeywords: [`${topic} guide`, `${topic} examples`, `how to use ${topic}`],
      keywordPlacement: [
        'Title tag',
        'H1 heading',
        'First paragraph',
        'Subheadings',
        'Meta description',
        'Image alt text',
        'URL'
      ]
    },
    audienceInsights: marketResearch ? {
      demographics: marketResearch.iterations[0].content.demographics,
      interests: marketResearch.iterations[0].content.demographics.interests,
      painPoints: [
        'Lack of understanding about the topic',
        'Difficulty implementing the concepts',
        'Uncertainty about best practices',
        'Challenges measuring results'
      ]
    } : {
      demographics: {
        age: '25-45',
        gender: 'mixed',
        interests: ['technology', 'business', 'education'],
        location: 'global'
      },
      painPoints: [
        'Lack of understanding about the topic',
        'Difficulty implementing the concepts',
        'Uncertainty about best practices',
        'Challenges measuring results'
      ]
    }
  };

  // Create content strategy artifact
  const artifact = {
    id: require('uuid').v4(),
    name: `Content Strategy: ${topic}`,
    type: 'content-strategy',
    status: 'completed',
    createdBy: 'content-strategy',
    createdAt: new Date().toISOString(),
    currentVersion: 1,
    iterations: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'content-strategy',
        content: contentStrategy,
        feedback: [],
        incorporatedConsultations: []
      }
    ],
    qualityScore: 85
  };

  // Add the artifact to the state
  await stateManager.addArtifact(message.conversationId, artifact);

  // Update workflow progress
  await require('../utils/stateStore').stateStore.updateState(message.conversationId, (state: any) => {
    if (state.workflowProgress) {
      state.workflowProgress.contentStrategyComplete = true;
    }
    return state;
  });

  // Return the response
  return {
    success: true,
    message: `Successfully generated content strategy for ${topic}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      'ARTIFACT_DELIVERY',
      {
        artifact,
        message: `Generated content strategy for ${topic}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
};

export const handleContentStrategyArtifactDelivery = async (message: any, state: any, stateManager: any, messaging: any) => {
  console.log('Content Strategy Artifact Delivery handler called');

  // Extract request details
  const { artifactId, artifactType } = message.content || {};

  // Find the requested artifact
  let artifact = null;

  if (artifactId && state.artifacts && state.artifacts[artifactId]) {
    artifact = state.artifacts[artifactId];
  } else if (artifactType === 'content-strategy' && state.artifacts) {
    // Find the latest content strategy artifact
    const strategyArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'content-strategy' && a.createdBy === 'content-strategy')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (strategyArtifacts.length > 0) {
      artifact = strategyArtifacts[0];
    }
  }

  if (!artifact) {
    return {
      success: false,
      message: 'Artifact not found',
      response: await messaging.send(
        message.conversationId,
        message.from,
        'ERROR',
        {
          error: 'Artifact not found',
          message: `Could not find the requested artifact`
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Return the artifact
  return {
    success: true,
    message: `Successfully retrieved artifact ${artifact.id}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      'ARTIFACT_DELIVERY',
      {
        artifact,
        message: `Retrieved artifact ${artifact.id}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
};

// SEO Optimization handlers
export const handleSeoOptimizationInitialRequest = async (message: any, state: any, stateManager: any, messaging: any) => {
  console.log('SEO Optimization Initial Request handler called');

  const { topic, contentType, content } = message.content || {};

  if (!topic) {
    return {
      success: false,
      message: 'Topic is required for SEO optimization',
      response: await messaging.send(
        message.conversationId,
        message.from,
        'ERROR',
        {
          error: 'Missing required fields',
          message: 'Topic is required for SEO optimization'
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Find keyword research artifact if available
  let keywordResearch = null;
  let contentStrategyArtifact = null;

  if (state.artifacts) {
    // Find keyword research artifact
    const keywordArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'seo-keywords')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (keywordArtifacts.length > 0) {
      keywordResearch = keywordArtifacts[0];
    }

    // Find content strategy artifact
    const contentStrategyArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'content-strategy')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (contentStrategyArtifacts.length > 0) {
      contentStrategyArtifact = contentStrategyArtifacts[0];
    }
  }

  // Generate SEO optimization report
  const seoOptimizationReport = {
    topic,
    contentType: contentType || 'blog-article',
    keywordAnalysis: keywordResearch ? {
      primaryKeyword: keywordResearch.iterations[0].content.primaryKeyword,
      primaryKeywordDensity: '1.2%',
      secondaryKeywords: keywordResearch.iterations[0].content.secondaryKeywords,
      secondaryKeywordPresence: 'Good',
      missingKeywords: keywordResearch.iterations[0].content.secondaryKeywords.slice(0, 2),
      keywordPlacement: {
        title: 'Present',
        headings: 'Partial',
        introduction: 'Present',
        conclusion: 'Missing',
        imageAlt: 'Missing'
      }
    } : {
      primaryKeyword: topic,
      primaryKeywordDensity: '1.0%',
      secondaryKeywords: [`${topic} guide`, `${topic} examples`, `how to use ${topic}`],
      secondaryKeywordPresence: 'Partial',
      missingKeywords: [`${topic} benefits`, `${topic} best practices`],
      keywordPlacement: {
        title: 'Present',
        headings: 'Partial',
        introduction: 'Present',
        conclusion: 'Missing',
        imageAlt: 'Missing'
      }
    },
    contentStructure: {
      headingHierarchy: 'Good',
      paragraphLength: 'Good',
      contentFlow: 'Good',
      readability: 'Flesch-Kincaid Grade Level: 9.2 (Good)',
      contentLength: 'Adequate (1800 words)'
    },
    technicalSEO: {
      metaTitle: `The Ultimate Guide to ${topic}`,
      metaDescription: `Learn everything you need to know about ${topic} in this comprehensive guide. Discover best practices, examples, and step-by-step instructions.`,
      urlStructure: `example.com/guide/${topic.toLowerCase().replace(/\s+/g, '-')}`,
      imageOptimization: 'Needs improvement',
      internalLinking: 'Needs improvement',
      externalLinking: 'Adequate',
      schemaMarkup: 'Missing'
    },
    recommendations: [
      `Increase the density of the primary keyword "${topic}" to 1.5-2%`,
      `Add missing secondary keywords: ${keywordResearch ? keywordResearch.iterations[0].content.secondaryKeywords.slice(0, 2).join(', ') : `${topic} benefits, ${topic} best practices`}`,
      `Include the primary keyword in the conclusion`,
      `Add alt text to all images, including the primary keyword where relevant`,
      `Add schema markup for Article or HowTo`,
      `Include 2-3 internal links to related content`,
      `Add 1-2 external links to authoritative sources`,
      `Break up longer paragraphs for better readability`,
      `Add a table of contents for longer articles`,
      `Include FAQ section targeting long-tail keywords`
    ],
    optimizedElements: {
      title: `The Ultimate Guide to ${topic}: Best Practices, Examples, and Implementation`,
      metaDescription: `Discover everything you need to know about ${topic} in our comprehensive guide. Learn best practices, see real-world examples, and follow our step-by-step implementation instructions.`,
      headings: [
        {
          level: 'H1',
          text: `The Ultimate Guide to ${topic}`
        },
        {
          level: 'H2',
          text: `Understanding ${topic}: Key Concepts and Benefits`
        },
        {
          level: 'H2',
          text: `${topic} Best Practices for Optimal Results`
        },
        {
          level: 'H2',
          text: `Step-by-Step Guide to Implementing ${topic}`
        },
        {
          level: 'H2',
          text: `Real-World Examples of ${topic} in Action`
        },
        {
          level: 'H2',
          text: `Common Challenges and Solutions for ${topic}`
        },
        {
          level: 'H2',
          text: `Frequently Asked Questions About ${topic}`
        }
      ],
      imageAltText: [
        `${topic} implementation diagram`,
        `${topic} best practices infographic`,
        `${topic} case study results chart`,
        `Step-by-step ${topic} process`
      ]
    },
    contentStrategyAlignment: contentStrategyArtifact ? {
      alignmentScore: '85%',
      alignmentNotes: [
        'Content structure follows the recommended outline',
        'Keyword usage aligns with strategy',
        'Tone and style match target audience preferences',
        'Content goals are addressed'
      ],
      improvementAreas: [
        'Add more visual elements as recommended in the content strategy',
        'Expand the section on case studies',
        'Include more actionable advice in the implementation section'
      ]
    } : {
      alignmentScore: 'N/A',
      alignmentNotes: ['No content strategy available for comparison'],
      improvementAreas: ['Develop a content strategy for better alignment']
    }
  };

  // Create SEO optimization artifact
  const artifact = {
    id: require('uuid').v4(),
    name: `SEO Optimization Report: ${topic}`,
    type: 'seo-optimization',
    status: 'completed',
    createdBy: 'seo-optimization',
    createdAt: new Date().toISOString(),
    currentVersion: 1,
    iterations: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'seo-optimization',
        content: seoOptimizationReport,
        feedback: [],
        incorporatedConsultations: []
      }
    ],
    qualityScore: 85
  };

  // Add the artifact to the state
  await stateManager.addArtifact(message.conversationId, artifact);

  // Update workflow progress
  await require('../utils/stateStore').stateStore.updateState(message.conversationId, (state: any) => {
    if (state.workflowProgress) {
      state.workflowProgress.seoOptimizationComplete = true;
    }
    return state;
  });

  // Return the response
  return {
    success: true,
    message: `Successfully generated SEO optimization report for ${topic}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      'ARTIFACT_DELIVERY',
      {
        artifact,
        message: `Generated SEO optimization report for ${topic}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
};

export const handleSeoOptimizationArtifactRequest = async (message: any, state: any, stateManager: any, messaging: any) => {
  console.log('SEO Optimization Artifact Request handler called');

  // Extract request details
  const { artifactId, artifactType } = message.content || {};

  // Find the requested artifact
  let artifact = null;

  if (artifactId && state.artifacts && state.artifacts[artifactId]) {
    artifact = state.artifacts[artifactId];
  } else if (artifactType === 'seo-optimization' && state.artifacts) {
    // Find the latest SEO optimization artifact
    const seoArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'seo-optimization' && a.createdBy === 'seo-optimization')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (seoArtifacts.length > 0) {
      artifact = seoArtifacts[0];
    }
  }

  if (!artifact) {
    return {
      success: false,
      message: 'Artifact not found',
      response: await messaging.send(
        message.conversationId,
        message.from,
        'ERROR',
        {
          error: 'Artifact not found',
          message: `Could not find the requested artifact`
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Return the artifact
  return {
    success: true,
    message: `Successfully retrieved artifact ${artifact.id}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      'ARTIFACT_DELIVERY',
      {
        artifact,
        message: `Retrieved artifact ${artifact.id}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
};

// Export utility functions from their original locations
export { stateStore } from '../utils/stateStore';
export {
  createAgentStateManager,
  AgentStateManager
} from '../utils/agentStateManager';
export {
  createAgentMessaging,
  AgentMessaging
} from '../utils/agentMessaging';
export {
  createAgentHandler,
  AgentHandler
} from '../utils/agentHandler';

// Export the MessageBus for agent-to-agent communication
export { MessageBus } from '../utils/messageBus';
export type {
  CircuitBreakerConfig
} from '../utils/messageBus';

// Export the AgentCommunicationManager for enhanced agent collaboration
export {
  createAgentCommunicationManager,
  AgentCommunicationManager
} from '../utils/agentCommunicationManager';

// Export all agent-related types
export type {
  IterativeMessage,
  IterativeMessageType,
  IterativeArtifact,
  IterativeCollaborationState,
  AgentId,
  AgentRole,
  ArtifactStatus,
  Goal,
  GoalStatus,
  Decision,
  Consultation,
  Discussion,
  DiscussionMessage,
  EnhancedReasoning
} from '../types';
