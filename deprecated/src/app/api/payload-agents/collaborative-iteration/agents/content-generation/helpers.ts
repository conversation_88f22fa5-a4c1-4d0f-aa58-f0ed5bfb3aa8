import OpenAI from 'openai';
import { IterativeArtifact, ArtifactStatus } from '../../types';
import { createChainOfThoughtReasoning } from '../../utils/reasoningUtils';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY as string,
});

/**
 * Interface for content generation result
 */
export interface GeneratedContent {
  title: string;
  content: string;
  summary: string;
  sections: {
    title: string;
    content: string;
  }[];
  wordCount: number;
  readingTime: number;
  qualityScore: number;
}

/**
 * Generate content based on provided parameters and insights
 */
export async function generateContent(
  contentType: string,
  topic: string,
  keywords: string[],
  targetAudience?: string,
  tone?: string,
  length: 'short' | 'medium' | 'long' = 'medium',
  insights?: any
): Promise<GeneratedContent> {
  console.log(`Generating ${contentType} content for topic: ${topic}`);
  
  // Default values for missing parameters
  const audience = targetAudience || 'general audience';
  const contentTone = tone || 'informative';
  
  // Parse insights if available
  let marketResearchInsights = '';
  let keywordResearchInsights = '';
  let contentStrategyInsights = '';
  let additionalInsights = '';
  
  if (insights) {
    marketResearchInsights = typeof insights.marketResearch === 'string' 
      ? insights.marketResearch 
      : JSON.stringify(insights.marketResearch);
    
    keywordResearchInsights = typeof insights.keywordResearch === 'string' 
      ? insights.keywordResearch 
      : JSON.stringify(insights.keywordResearch);
    
    contentStrategyInsights = typeof insights.contentStrategy === 'string' 
      ? insights.contentStrategy 
      : JSON.stringify(insights.contentStrategy);
    
    additionalInsights = typeof insights.additionalInsights === 'string' 
      ? insights.additionalInsights 
      : JSON.stringify(insights.additionalInsights);
  }
  
  // Create a prompt for content generation based on content type
  let systemPrompt = '';
  let contentFormat = '';
  
  switch (contentType.toLowerCase()) {
    case 'blog-post':
      systemPrompt = `You are an expert content writer specializing in blog posts. Your task is to create a high-quality, engaging, and informative blog post.`;
      contentFormat = `Create a well-structured blog post with a compelling headline, introduction, multiple H2 sections with H3 subsections where appropriate, and a conclusion with a call to action.`;
      break;
    case 'product-page':
      systemPrompt = `You are an expert copywriter specializing in product pages. Your task is to create compelling product copy that drives conversions.`;
      contentFormat = `Create product page content with a headline, compelling product description, key features/benefits section, specifications, and a strong call to action.`;
      break;
    case 'buying-guide':
      systemPrompt = `You are an expert content writer specializing in buying guides. Your task is to create a comprehensive, informative buying guide.`;
      contentFormat = `Create a detailed buying guide with an introduction explaining the importance of the product category, key factors to consider when buying, comparisons of different options, recommendations for different user needs/budgets, and a conclusion.`;
      break;
    default:
      systemPrompt = `You are an expert content writer. Your task is to create high-quality, engaging, and informative content.`;
      contentFormat = `Create well-structured content with a compelling headline, introduction, multiple sections, and a conclusion.`;
  }
  
  // Determine word count target based on length parameter
  let wordCountTarget = 800; // Default medium length
  if (length === 'short') {
    wordCountTarget = 500;
  } else if (length === 'long') {
    wordCountTarget = 1500;
  }
  
  // Create a chain-of-thought reasoning before generating content
  const reasoning = await createChainOfThoughtReasoning(
    {
      contentType,
      topic,
      keywords: Array.isArray(keywords) ? keywords.join(', ') : keywords,
      targetAudience: audience,
      tone: contentTone,
      length
    },
    `Generate high-quality ${contentType} content for the topic "${topic}" targeted at ${audience} with a ${contentTone} tone.`,
    `Content generation plan for ${topic} ${contentType}`,
    'content-generation',
    {
      confidence: 0.9,
      steps: [
        'Analyze market research insights to understand audience needs',
        'Review keyword research to identify SEO opportunities',
        'Examine content strategy recommendations for structure',
        'Create an outline based on gathered insights',
        'Generate comprehensive content following best practices',
      ],
      insights: [
        'The content should address specific pain points identified in market research',
        'Strategic placement of keywords will improve SEO performance',
        'Following the recommended content structure will enhance readability',
        'Maintaining the appropriate tone is crucial for audience engagement',
        'Including data and examples will strengthen credibility'
      ]
    }
  );
  
  try {
    // Generate content using OpenAI
    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: `${systemPrompt} Use markdown formatting.
          
Your response must include:
1. A compelling title (wrapped in a single # header)
2. A well-structured outline with multiple sections (H2) and subsections (H3) where appropriate
3. Comprehensive content that is engaging, informative, and valuable to the reader
4. A strong conclusion with appropriate call-to-action
5. The content should be approximately ${wordCountTarget} words.

${contentFormat}

The content should be optimized for the following parameters:
- Target audience: ${audience}
- Tone: ${contentTone}
- Primary keywords: ${Array.isArray(keywords) ? keywords.join(', ') : keywords}

Ensure the content is factually accurate, well-organized, and delivers value to the reader.`
        },
        {
          role: "user",
          content: `Generate ${contentType} content for the topic: "${topic}".

Here are the insights to incorporate:

1. Market Research Insights:
${marketResearchInsights}

2. Keyword Research:
${keywordResearchInsights}

3. Content Strategy Recommendations:
${contentStrategyInsights}

4. Additional Insights:
${additionalInsights}

Please use these insights to create comprehensive, engaging, and valuable content that resonates with the target audience (${audience}) and maintains a ${contentTone} tone throughout.`
        }
      ],
      temperature: 0.7,
      max_tokens: 4000
    });

    // Extract the generated content
    const generatedText = response.choices[0]?.message?.content || '';
    
    // Parse the content to extract title and sections
    const lines = generatedText.split('\n');
    
    // Extract title (first # header)
    let title = topic; // Default to topic if no title found
    for (const line of lines) {
      if (line.startsWith('# ')) {
        title = line.replace('# ', '');
        break;
      }
    }
    
    // Extract sections
    const sections: { title: string; content: string }[] = [];
    let currentSection: { title: string; content: string } | null = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (line.startsWith('## ')) {
        // Save previous section if exists
        if (currentSection) {
          sections.push(currentSection);
        }
        
        // Start new section
        currentSection = {
          title: line.replace('## ', ''),
          content: ''
        };
      } else if (currentSection) {
        // Add line to current section
        currentSection.content += line + '\n';
      }
    }
    
    // Add the last section
    if (currentSection) {
      sections.push(currentSection);
    }
    
    // Calculate word count
    const wordCount = generatedText.split(/\s+/).length;
    
    // Calculate reading time (average reading speed: 200 words per minute)
    const readingTime = Math.ceil(wordCount / 200);
    
    // Generate a summary
    const summary = await generateContentSummary(title, generatedText);
    
    // Return structured content
    return {
      title,
      content: generatedText,
      summary,
      sections,
      wordCount,
      readingTime,
      qualityScore: 85 // Default quality score
    };
  } catch (error) {
    console.error('Error generating content with OpenAI:', error);
    
    // Return a fallback content with error message
    return {
      title: `${topic} - Draft`,
      content: `# ${topic}\n\n**Note: Content generation encountered an error.**\n\nPlease try again or contact support if the issue persists.\n\nError details: ${(error as Error).message}`,
      summary: `Content generation for "${topic}" encountered an error.`,
      sections: [
        {
          title: 'Error Information',
          content: `Content generation failed with error: ${(error as Error).message}`
        }
      ],
      wordCount: 50,
      readingTime: 1,
      qualityScore: 0
    };
  }
}

/**
 * Generate a summary of the content
 */
async function generateContentSummary(title: string, content: string): Promise<string> {
  try {
    // Extract a portion of the content for summarization (to avoid token limits)
    const contentExcerpt = content.slice(0, 2000) + (content.length > 2000 ? '...' : '');
    
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "Create a concise 2-3 sentence summary of the provided content that captures its main focus and value proposition."
        },
        {
          role: "user",
          content: `Title: ${title}\n\nContent: ${contentExcerpt}`
        }
      ],
      temperature: 0.7,
      max_tokens: 150
    });
    
    return response.choices[0]?.message?.content || `A ${title} article covering key aspects and insights.`;
  } catch (error) {
    console.error('Error generating content summary:', error);
    return `An article about ${title} with valuable information and insights.`;
  }
}

/**
 * Generate feedback based on provided content and feedback
 */
export async function generateContentFeedback(
  artifact: IterativeArtifact,
  feedback: string,
  suggester: string
): Promise<string> {
  try {
    const content = artifact.content || '';
    
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a content improvement specialist. Based on the provided feedback, create a concrete improvement plan for the content."
        },
        {
          role: "user",
          content: `Content: ${JSON.stringify(content)}\n\nFeedback from ${suggester}: ${feedback}\n\nPlease provide a specific improvement plan with actionable steps.`
        }
      ],
      temperature: 0.7,
      max_tokens: 500
    });
    
    return response.choices[0]?.message?.content || "I'll review this feedback and make appropriate improvements to the content.";
  } catch (error) {
    console.error('Error generating content feedback:', error);
    return "I'll incorporate this feedback in the next content iteration.";
  }
}

/**
 * Generate consultation response for a question about content
 */
export async function generateContentConsultation(
  question: string,
  context?: string,
  contentToAnalyze?: string
): Promise<{ response: string; suggestions: string[] }> {
  try {
    const contextPrompt = context ? `\n\nContext: ${context}` : '';
    const contentPrompt = contentToAnalyze ? `\n\nContent to analyze: ${contentToAnalyze}` : '';
    
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a content creation consultant with expertise in writing, SEO, and audience engagement. Provide clear, actionable advice to questions about content creation."
        },
        {
          role: "user",
          content: `Question: ${question}${contextPrompt}${contentPrompt}\n\nPlease provide a detailed response with actionable suggestions.`
        }
      ],
      temperature: 0.7,
      max_tokens: 800
    });
    
    const consultationResponse = response.choices[0]?.message?.content || "I'll need more information to provide a meaningful consultation.";
    
    // Generate specific suggestions
    let suggestions: string[] = [];
    
    // Second call to generate specific suggestions
    try {
      const suggestionsResponse = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "Based on the consultation response, extract 3-5 specific, actionable suggestions as short bullet points (1-2 sentences each)."
          },
          {
            role: "user",
            content: `Consultation response: ${consultationResponse}\n\nExtract 3-5 key actionable suggestions as bullet points.`
          }
        ],
        temperature: 0.7,
        max_tokens: 400
      });
      
      const suggestionsText = suggestionsResponse.choices[0]?.message?.content || '';
      
      // Parse bullet points
      suggestions = suggestionsText
        .split('\n')
        .filter(line => line.trim().startsWith('-') || line.trim().startsWith('•'))
        .map(line => line.replace(/^[-•]\s*/, '').trim());
      
      // If no bullet points were found, create default suggestions
      if (suggestions.length === 0) {
        suggestions = [
          "Consider reviewing the content structure for better flow and readability.",
          "Strengthen the introduction and conclusion to create a more complete narrative.",
          "Add more specific examples or data points to support the main arguments."
        ];
      }
    } catch (error) {
      console.error('Error generating suggestions:', error);
      suggestions = [
        "Review content for clarity and engagement.",
        "Consider audience needs more carefully.",
        "Strengthen supporting evidence."
      ];
    }
    
    return {
      response: consultationResponse,
      suggestions
    };
  } catch (error) {
    console.error('Error generating content consultation:', error);
    return {
      response: "I'm currently unable to provide a detailed consultation due to a technical issue. Please try again later.",
      suggestions: [
        "Try breaking down your question into more specific aspects.",
        "Provide more context about your target audience and goals.",
        "Consider developing a content outline before full development."
      ]
    };
  }
}
