// src/app/(payload)/api/agents/collaborative-iteration/agents/content-generation/handlers.ts
// Fully modular implementation of Content Generation Agent handlers

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  EnhancedReasoning,
  AgentId,
  AgentState,
  StandardizedHandlerResult,
  ArtifactStatus,
  Consultation,
  Iteration,
  IterativeArtifact
} from '../../types';

import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';
import { stateStore } from '../../utils/stateStore';
import {
  generateContent,
  generateContentFeedback,
  generateContentConsultation
} from './helpers';
import { storeAgentReasoning, createEnhancedReasoning } from '../../utils/reasoningUtils';

// Define the agent ID constant
const AGENT_ID: AgentId = AgentId.CONTENT_GENERATION;

// Extended artifact interface with additional properties
interface ExtendedArtifact extends IterativeArtifact {
  content?: any;
  metadata?: any;
  updatedAt?: string;
}

/**
 * Handle initial request for content generation
 */
export async function handleContentGenerationInitialRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`Content Generation Agent: Handling initial request from ${message.from}`);

  // Extract request details
  const { contentType, topic, keywords = [], targetAudience, tone, length } = message.content || {};

  if (!contentType || !topic) {
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from as AgentId,
      content: {
        error: 'Missing required fields',
        message: 'Content type and topic are required for content generation.'
      },
      conversationId: message.conversationId
    };

    return {
      success: false,
      message: 'Failed to process request - missing required fields',
      error: 'Missing content type or topic',
      response: errorResponse
    };
  }

  try {
    // Generate content based on the request
    const generatedContent = await generateContent(
      contentType,
      topic,
      keywords,
      targetAudience,
      tone,
      length
    );

    // Create content artifact with standardized type
    const artifact: IterativeArtifact = {
      id: uuidv4(),
      // Use a standardized type that the workflow orchestrator recognizes
      type: 'generated-content',
      name: `${contentType.charAt(0).toUpperCase() + contentType.slice(1)} about ${topic}`,
      status: 'completed' as ArtifactStatus,
      createdBy: AGENT_ID,
      createdAt: new Date().toISOString(),
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: AGENT_ID,
          content: {
            text: generatedContent.content,
            title: generatedContent.title,
            sections: generatedContent.sections,
            wordCount: generatedContent.wordCount,
            keywords,
            targetAudience,
            tone,
            requestedLength: length,
            originalContentType: contentType // Store the original content type for reference
          },
          feedback: [],
          incorporatedConsultations: []
        }
      ],
      qualityScore: generatedContent.qualityScore,
      // Add metadata to store the original content type
      metadata: {
        originalContentType: contentType,
        contentGenerationComplete: true
      }
    };

    // Send response with the generated content
    const response: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      from: AGENT_ID,
      to: message.from as AgentId,
      content: {
        artifact,
        message: `Generated ${contentType} about ${topic}.`
      },
      artifactId: artifact.id,
      conversationId: message.conversationId
    };

    // Add the artifact to the state directly to ensure it's properly tracked
    await stateManager.addArtifact(state.id, artifact);
    console.log(`Content Generation Agent: Created and stored artifact ${artifact.id} directly`);

    // Return the response and artifact updates (keeping for backward compatibility)
    return {
      success: true,
      message: `Successfully generated ${contentType} about ${topic}`,
      response,
      artifactUpdates: {
        new: {
          [artifact.id]: artifact
        }
      }
    };
  } catch (error) {
    console.error('Error generating content:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from as AgentId,
      content: {
        error: 'Content generation failed',
        message: error instanceof Error ? error.message : String(error)
      },
      conversationId: message.conversationId
    };

    return {
      success: false,
      message: 'Failed to generate content',
      error: error instanceof Error ? error.message : String(error),
      response: errorResponse
    };
  }
}

/**
 * Handle artifact request from another agent
 */
export async function handleContentGenerationArtifactRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`Content Generation Agent: Handling artifact request from ${message.from}`);

  // Extract request details
  const { artifactType, artifactId } = message.content || {};

  if (!artifactType) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Missing required fields',
        message: 'Artifact type is required for artifact request'
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Find artifacts created by this agent
  const ownArtifacts = (Object.values(state.artifacts || {}) as ExtendedArtifact[])
    .filter((a: ExtendedArtifact) => a.createdBy === AGENT_ID && a.type === artifactType);

  if (artifactId) {
    // If a specific artifact is requested
    const requestedArtifact = state.artifacts?.[artifactId] as ExtendedArtifact;

    if (!requestedArtifact) {
      const errorResponse = await messaging.send(
        state.id,
        message.from as AgentId,
        IterativeMessageType.ERROR,
        {
          error: 'Artifact not found',
          message: `Could not find artifact with ID ${artifactId}`
        },
        message.conversationId
      );

      return { response: errorResponse };
    }

    // Send the requested artifact
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ARTIFACT_DELIVERY,
      {
        artifactId: requestedArtifact.id,
        artifactType: requestedArtifact.type,
        content: requestedArtifact.content,
        metadata: requestedArtifact.metadata
      },
      message.conversationId,
      message.id
    );

    return { response };
  } else if (ownArtifacts.length > 0) {
    // Send the latest artifact of the requested type
    const latestArtifact = ownArtifacts.sort((a, b) => {
      const dateA = a.updatedAt ? new Date(a.updatedAt).getTime() : new Date(a.createdAt).getTime();
      const dateB = b.updatedAt ? new Date(b.updatedAt).getTime() : new Date(b.createdAt).getTime();
      return dateB - dateA;
    })[0];

    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ARTIFACT_DELIVERY,
      {
        artifactId: latestArtifact.id,
        artifactType: latestArtifact.type,
        content: latestArtifact.content,
        metadata: latestArtifact.metadata
      },
      message.conversationId,
      message.id
    );

    return { response };
  } else {
    // No artifacts of the requested type
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'No artifacts available',
        message: `No artifacts of type ${artifactType} have been created yet`
      },
      message.conversationId,
      message.id
    );

    return { response };
  }
}

/**
 * Handle feedback from another agent
 */
export async function handleFeedback(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`Content Generation Agent: Handling feedback from ${message.from}`);

  // Check if this is an evaluation feedback message
  if (message.content?.evaluation) {
    try {
      // Import the feedback handler
      const { processFeedback } = await import('../../utils/feedback-handler');

      // Process feedback using the common feedback handler
      const result = await processFeedback(
        message,
        state,
        stateManager,
        messaging,
        AGENT_ID
      );

      // If an artifact was updated, return it
      if (result.updatedArtifact) {
        return {
          response: result.response,
          artifact: result.updatedArtifact
        };
      }

      return { response: result.response };
    } catch (error) {
      console.error('Error processing evaluation feedback:', error);

      // Fall back to standard feedback handling if there's an error
      const errorResponse = await messaging.send(
        state.id,
        message.from as AgentId,
        IterativeMessageType.ERROR,
        {
          error: 'Error processing evaluation feedback',
          message: `An error occurred while processing the evaluation feedback: ${error instanceof Error ? error.message : String(error)}`
        },
        message.conversationId
      );

      return { response: errorResponse };
    }
  }

  // Handle traditional feedback format
  const { artifactId, feedback, suggestions } = message.content || {};

  if (!artifactId || !feedback) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Missing required fields',
        message: 'Artifact ID and feedback content are required for processing feedback'
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Get the artifact from state
  const artifact = state.artifacts?.[artifactId];

  if (!artifact) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Artifact not found',
        message: `Could not find artifact with ID ${artifactId}`
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Integrate feedback and generate improvement plan
  const improvementResponse = await generateContentFeedback(
    artifact as IterativeArtifact,
    feedback,
    suggestions as AgentId
  );

  // Send acknowledgment with improvement plan
  const response = await messaging.send(
    state.id,
    message.from as AgentId,
    IterativeMessageType.ACKNOWLEDGMENT,
    {
      originalMessageId: message.id,
      message: 'Feedback received and processed',
      improvementPlan: improvementResponse.improvementPlan
    },
    message.conversationId
  );

  return { response };
}

/**
 * Handle consultation request from another agent
 */
export async function handleConsultationRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`Content Generation Agent: Handling consultation request from ${message.from}`);

  // Extract consultation details
  const { artifactId, question, context } = message.content || {};

  if (!question) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Missing required fields',
        message: 'Question is required for consultation'
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Generate consultation response
  const contentToAnalyze = artifactId ? JSON.stringify(state.artifacts?.[artifactId]) : undefined;
  const consultation = await generateContentConsultation(question, context, contentToAnalyze);

  // Create consultation record
  const consultationRecord: Consultation = {
    id: uuidv4(),
    fromAgent: AGENT_ID,
    toAgent: message.from as AgentId,
    artifactId: artifactId || '',
    timestamp: new Date().toISOString(),
    question,
    feedback: consultation.response, // Use response property instead of feedback
    suggestions: consultation.suggestions,
    incorporated: false,
    requestId: message.id
  };

  // Send consultation response
  const response = await messaging.send(
    state.id,
    message.from as AgentId,
    IterativeMessageType.CONSULTATION_RESPONSE,
    {
      consultationId: consultationRecord.id,
      feedback: consultation.response, // Use response property instead of feedback
      suggestions: consultation.suggestions
    },
    message.conversationId,
    message.id
  );

  return {
    response,
    consultationUpdates: {
      new: {
        [consultationRecord.id]: consultationRecord
      }
    }
  };
}

/**
 * Handle artifact delivery from another agent
 */
export async function handleArtifactDelivery(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`Content Generation Agent: Handling artifact delivery from ${message.from}`);

  // Extract artifact details
  const { artifactId, artifactType, content } = message.content || {};

  if (!artifactId || !content) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Missing required fields',
        message: 'Artifact ID and content are required'
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Send acknowledgment
  const response = await messaging.send(
    state.id,
    message.from as AgentId,
    IterativeMessageType.ACKNOWLEDGMENT,
    {
      originalMessageId: message.id,
      message: `Received artifact ${artifactId} successfully`
    },
    message.conversationId
  );

  return { response };
}

/**
 * Handle discussion start request
 */
export async function handleDiscussionStart(
  state: AgentState,
  message: IterativeMessage,
  messaging: AgentMessaging
): Promise<{ response: IterativeMessage }> {
  console.log(`[${AGENT_ID}] handleDiscussionStart: Processing discussion start request`);

  // Extract discussion details
  const { discussionId, topic, prompt } = message.content || {};

  if (!discussionId || !topic) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Missing required fields',
        message: 'Discussion ID and topic are required for starting a discussion'
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Generate structured reasoning for content creation perspective using the utility function
  const considerations = [
    'Content structure: Introduction, body sections with clear hierarchy, conclusion with CTA',
    'Engagement techniques: Storytelling, questions, examples, and case studies',
    'Readability factors: Short paragraphs, bullet points, subheadings',
    'Tone and voice: Professional but conversational, authoritative but accessible'
  ];

  const decision = 'Provide a content creation perspective focusing on structure, engagement, and readability';

  const discussionReasoning = createEnhancedReasoning(
    { topic, prompt },
    considerations,
    decision,
    AGENT_ID,
    {
      confidence: 0.92,
      steps: [
        'Evaluate the topic and prompt for content creation requirements',
        'Identify key content structure elements needed',
        'Assess engagement techniques applicable to the topic',
        'Consider narrative approaches and tone options',
        'Formulate a comprehensive content creation perspective'
      ],
      insights: [
        'The topic requires a balance of informative and engaging content',
        'A clear structure with H2/H3 hierarchy will improve readability',
        'Storytelling elements can enhance user engagement',
        'Visual content suggestions may complement the written material',
        'Tone should match the target audience expectations'
      ]
    }
  );

  // Store the reasoning in the agent's state
  await storeAgentReasoning(
    state.id,
    AGENT_ID,
    "",
    message.id,
    message.conversationId,
    discussionReasoning
  );

  // Generate content creation perspective
  const perspective = `As a Content Generation specialist, I recommend approaching this topic with a structured content strategy:

1. **Content Structure**:
   - Start with a compelling introduction that addresses the reader's pain points
   - Use a clear H2/H3 hierarchy for scannable content
   - Include 5-7 main sections covering key aspects of the topic
   - End with a conclusion that reinforces the main points and includes a call-to-action

2. **Engagement Techniques**:
   - Incorporate storytelling elements to illustrate key points
   - Use rhetorical questions to maintain reader interest
   - Include relevant examples and case studies
   - Add data visualizations where appropriate

3. **Readability Optimization**:
   - Keep paragraphs short (3-4 sentences maximum)
   - Use bullet points and numbered lists for digestible information
   - Include pull quotes for important statements
   - Ensure a conversational tone while maintaining authority

4. **Content Enhancement**:
   - Suggest complementary visual content (infographics, diagrams)
   - Recommend internal linking strategy
   - Include FAQ section addressing common questions

This approach balances informational depth with engagement techniques to create content that both educates and resonates with the target audience.`;

  // Send the perspective as a response
  const response = await messaging.send(
    state.id,
    message.from as AgentId,
    IterativeMessageType.DISCUSSION_CONTRIBUTION,
    {
      discussionId,
      perspective,
      contributor: AGENT_ID,
      contributionType: 'content-creation-perspective'
    },
    message.conversationId,
    message.id
  );

  return { response };
}

/**
 * Helper function to find the latest artifact by type
 */
function findLatestArtifactByType(artifacts: Record<string, IterativeArtifact>, type: string): IterativeArtifact | null {
  if (!artifacts) return null;

  const artifactsOfType = Object.values(artifacts)
    .filter(artifact => artifact.type === type)
    .sort((a, b) => {
      // Sort by creation date, newest first
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA;
    });

  return artifactsOfType.length > 0 ? artifactsOfType[0] : null;
}

/**
 * Handle discussion contribution from another agent
 */
export async function handleDiscussionContribution(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`Content Generation Agent: Handling discussion contribution from ${message.from}`);

  const { discussionId, contribution, forceRealContent } = message.content || {};

  // If this is just an acknowledgment, just acknowledge it
  if (message.type === IterativeMessageType.ACKNOWLEDGMENT) {
    const ackResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        originalMessageId: message.id,
        message: `Noted contribution to discussion ${discussionId}`
      },
      message.conversationId
    );
    return { response: ackResponse };
  }

  // If this is a request with forceRealContent flag, we should generate content
  // even if some fields are missing
  const isForceRealContentRequest = forceRealContent === true;

  // For non-forceRealContent requests, require discussionId and contribution
  if (!isForceRealContentRequest && (!discussionId || !contribution)) {
    const ackResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        originalMessageId: message.id,
        message: `Insufficient information for content generation. Need discussionId and contribution.`
      },
      message.conversationId
    );
    return { response: ackResponse };
  }

  // Log that we're processing a forceRealContent request
  if (isForceRealContentRequest) {
    console.log(`Content Generation Agent: Processing forceRealContent request from ${message.from}`);
  }

  // This is a substantial contribution, let's generate real content based on it
  try {
    // Extract basic content parameters from state
    const { contentType, topic, keywords = [], targetAudience, tone } = state;

    // Find all relevant artifacts and discussion messages
    const allMessages = state.messages || [];

    // Compile insights from existing artifacts
    const marketResearchArtifact = findLatestArtifactByType(state.artifacts || {}, 'market-research');
    const keywordResearchArtifact = findLatestArtifactByType(state.artifacts || {}, 'keyword-research');
    const contentStrategyArtifact = findLatestArtifactByType(state.artifacts || {}, 'content-strategy');

    // Extract insights from previous discussions
    const discussionMessages = allMessages.filter(msg =>
      msg.conversationId === discussionId &&
      (msg.type === IterativeMessageType.DISCUSSION_CONTRIBUTION || msg.type === IterativeMessageType.RESPONSE)
    );

    // Compile insights for content generation
    const compiledInsights = {
      marketResearch: marketResearchArtifact ?
        JSON.stringify(marketResearchArtifact.iterations[marketResearchArtifact.iterations.length - 1].content) :
        discussionMessages
          .filter(msg => msg.from === AgentId.MARKET_RESEARCH)
          .map(msg => msg.content?.contribution || msg.content?.response || '')
          .join('\n\n'),

      keywordResearch: keywordResearchArtifact ?
        JSON.stringify(keywordResearchArtifact.iterations[keywordResearchArtifact.iterations.length - 1].content) :
        discussionMessages
          .filter(msg => msg.from === AgentId.SEO_KEYWORD)
          .map(msg => msg.content?.contribution || msg.content?.response || '')
          .join('\n\n'),

      contentStrategy: contentStrategyArtifact ?
        JSON.stringify(contentStrategyArtifact.iterations[contentStrategyArtifact.iterations.length - 1].content) :
        discussionMessages
          .filter(msg => msg.from === AgentId.CONTENT_STRATEGY)
          .map(msg => msg.content?.contribution || msg.content?.response || '')
          .join('\n\n'),

      additionalInsights: discussionMessages
        .map(msg => `${msg.from}: ${msg.content?.contribution || msg.content?.response || ''}`)
        .join('\n\n'),

      forceRealContent: true // Always force real content generation
    };

    console.log(`Content Generation Agent: Generating content for topic ${topic} with insights from discussion ${discussionId}`);

    // Generate content based on all available insights
    const generatedContent = await generateContent(
      contentType || 'blog-post',
      topic || 'General topic',
      keywords,
      targetAudience,
      tone,
      'long', // Default to long-form content
      compiledInsights
    );

    console.log(`Content Generation Agent: Content generated with title: ${generatedContent.title}`);

    // Create a proper artifact with the content
    const contentId = uuidv4();
    const timestamp = new Date().toISOString();

    // Debug the current state of artifacts
    console.log(`Content Generation Agent: Current state of artifacts before creating new artifact:`, {
      hasArtifacts: !!state.artifacts,
      artifactsType: typeof state.artifacts,
      artifactsCount: state.artifacts ?
        (Array.isArray(state.artifacts) ? state.artifacts.length : Object.keys(state.artifacts).length) : 0,
      artifactsKeys: state.artifacts && typeof state.artifacts === 'object' ? Object.keys(state.artifacts) : []
    });

    // Create content artifact
    // Create artifact with proper structure following the IterativeArtifact type
    const artifact: IterativeArtifact = {
      id: contentId,
      type: contentType || 'blog-post',
      name: `${generatedContent.title || topic} - Generated Content`,
      status: 'completed' as ArtifactStatus,
      createdBy: AGENT_ID,
      createdAt: timestamp,
      updatedAt: timestamp,
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: timestamp,
          agent: AGENT_ID,
          content: generatedContent.content,
          feedback: [],
          incorporatedConsultations: []
        }
      ],
      qualityScore: generatedContent.qualityScore,
      // Add proper typing for extended properties
      // These properties will be accessible in the frontend
      data: {
        title: generatedContent.title,
        content: generatedContent.content,
        summary: generatedContent.summary,
        sections: generatedContent.sections,
        metadata: {
          wordCount: generatedContent.wordCount,
          readingTime: generatedContent.wordCount / 200, // Approximate reading time
          keywords: keywords
        }
      },
      metadata: {
        wordCount: generatedContent.wordCount,
        readingTime: generatedContent.wordCount / 200, // Approximate reading time
        keywords: keywords,
        targetAudience: targetAudience || 'general audience',
        tone: tone || 'informative'
      }
    };

    // Initialize artifacts properly as an object if needed
    if (!state.artifacts) {
      console.log(`Content Generation Agent: Initializing artifacts object for state ${state.id}`);
      state.artifacts = {};
    }

    // Handle different artifact storage formats
    if (Array.isArray(state.artifacts)) {
      console.log(`Content Generation Agent: Converting artifacts array to object format`);
      // Convert the array to the proper Record structure
      const artifactsRecord: Record<string, any> = {};
      state.artifacts.forEach(artifact => {
        if (artifact && artifact.id) {
          artifactsRecord[artifact.id] = artifact;
        }
      });
      // Add the new artifact
      artifactsRecord[contentId] = artifact;
      state.artifacts = artifactsRecord;
    } else {
      // Just add the new artifact to the existing record
      console.log(`Content Generation Agent: Adding artifact ${contentId} to existing artifacts object`);
      state.artifacts[contentId] = artifact;
    }

    // CRITICAL: Explicitly save the updated state with the new artifact
    console.log(`Content Generation Agent: Saving state with new artifact ${contentId}`);
    await stateStore.setState(state.id, state);

    // Verify the artifact was added correctly
    const verificationState = await stateStore.getState(state.id);
    console.log(`Content Generation Agent: Verification after save:`, {
      hasArtifacts: !!verificationState?.artifacts,
      artifactsType: verificationState?.artifacts ? typeof verificationState.artifacts : 'undefined',
      artifactsCount: verificationState?.artifacts ?
        (Array.isArray(verificationState.artifacts) ? verificationState.artifacts.length : Object.keys(verificationState.artifacts).length) : 0,
      artifactsKeys: verificationState?.artifacts && typeof verificationState.artifacts === 'object' ? Object.keys(verificationState.artifacts) : [],
      hasNewArtifact: verificationState?.artifacts && typeof verificationState.artifacts === 'object' && contentId in verificationState.artifacts
    });

    console.log(`Content Generation Agent: Added artifact ${contentId} to state. Current artifacts:`,
      Object.keys(state.artifacts).length);
    console.log(`Content Generation Agent: Artifact structure:`,
      { id: contentId, type: artifact.type, title: artifact.data?.title || 'Untitled' });


    // Create a decision record for this content generation
    if (!state.decisions) state.decisions = [];
    const decisionId = uuidv4();

    // Add a decision with chain-of-thought reasoning - ensuring it matches the Decision type
    state.decisions.push({
      id: decisionId,
      title: `Content Generation for "${generatedContent.title || topic}"`,
      description: `Generated content based on discussion inputs and artifacts.`,
      createdBy: AGENT_ID,
      agent: AGENT_ID, // Required by Decision type
      createdAt: timestamp,
      updatedAt: timestamp,
      timestamp: timestamp, // Required by Decision type
      status: 'completed',
      type: 'content-generation',
      artifactId: contentId,
      reasoning: `# Content Generation Decision

## Input Analysis
- Content Type: ${contentType || 'blog-post'}
- Topic: ${topic || 'General topic'}
- Keywords: ${keywords.join(', ')}
- Target Audience: ${targetAudience || 'general audience'}
- Tone: ${tone || 'informative'}

## Generation Process
1. Analyzed market research insights to understand audience needs
2. Incorporated keyword research for SEO optimization
3. Applied content strategy recommendations for structure
4. Generated comprehensive content following best practices
5. Added proper metadata for tracking

## Output Details
- Title: ${generatedContent.title}
- Word Count: ${generatedContent.wordCount}
- Reading Time: ${generatedContent.readingTime} minutes
- Sections: ${generatedContent.sections.length}
- Quality Score: ${generatedContent.qualityScore}/100

This content was generated based on collaborative input from multiple agents and synthesizes all available insights.`
    });

    // Send response with the generated content
    // For forceRealContent requests, we need to ensure the content is properly delivered
    const isForceRealContentRequest = forceRealContent === true;

    // Create a more comprehensive response for forceRealContent requests
    const responseContent = isForceRealContentRequest ? {
      artifactId: contentId,
      artifactType: contentType || 'blog-post',
      content: generatedContent.content, // Include the full content directly in the message
      data: generatedContent.content,    // Include in data field as well for redundancy
      title: generatedContent.title,
      summary: generatedContent.summary,
      message: `Generated content based on discussion ${discussionId || 'unknown'}`,
      artifact: artifact,
      // Include additional fields to ensure the content is accessible
      text: generatedContent.content,
      response: generatedContent.content,
      forceRealContent: true,
      generatedAt: new Date().toISOString()
    } : {
      artifactId: contentId,
      artifactType: contentType || 'blog-post',
      content: generatedContent.content,
      title: generatedContent.title,
      summary: generatedContent.summary,
      message: `Generated content based on discussion ${discussionId || 'unknown'}`,
      artifact: artifact
    };

    // Log the response being sent
    console.log(`Content Generation Agent: Sending ${isForceRealContentRequest ? 'forceRealContent' : 'standard'} response with artifact ID ${contentId}`);

    const contentResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ARTIFACT_DELIVERY,
      responseContent,
      message.conversationId,
      message.id
    );

    // For forceRealContent requests, make an extra effort to ensure the artifact is stored
    if (isForceRealContentRequest) {
      try {
        // Double-check that the artifact is in the state
        const currentState = await stateStore.getState(state.id);
        if (!currentState?.artifacts ||
            (typeof currentState.artifacts === 'object' && !(contentId in currentState.artifacts))) {
          console.log(`Content Generation Agent: Artifact ${contentId} not found in state, attempting to add it again`);

          // Initialize artifacts properly as an object if needed
          if (!currentState.artifacts) {
            currentState.artifacts = {};
          }

          // Handle different artifact storage formats
          if (Array.isArray(currentState.artifacts)) {
            // Convert the array to the proper Record structure
            const artifactsRecord: Record<string, any> = {};
            currentState.artifacts.forEach(artifact => {
              if (artifact && artifact.id) {
                artifactsRecord[artifact.id] = artifact;
              }
            });
            // Add the new artifact
            artifactsRecord[contentId] = artifact;
            currentState.artifacts = artifactsRecord;
          } else {
            // Just add the new artifact to the existing record
            currentState.artifacts[contentId] = artifact;
          }

          // CRITICAL: Explicitly save the updated state with the new artifact again
          console.log(`Content Generation Agent: Re-saving state with artifact ${contentId}`);
          await stateStore.setState(state.id, currentState);

          // Also update our local state reference
          state.artifacts = currentState.artifacts;

          console.log(`Content Generation Agent: Re-saved artifact ${contentId} to state`);

          // Verify the artifact was added correctly after re-save
          const verificationState = await stateStore.getState(state.id);
          console.log(`Content Generation Agent: Verification after re-save:`, {
            hasArtifacts: !!verificationState?.artifacts,
            artifactsType: verificationState?.artifacts ? typeof verificationState.artifacts : 'undefined',
            artifactsCount: verificationState?.artifacts ?
              (Array.isArray(verificationState.artifacts) ? verificationState.artifacts.length : Object.keys(verificationState.artifacts).length) : 0,
            artifactsKeys: verificationState?.artifacts && typeof verificationState.artifacts === 'object' ? Object.keys(verificationState.artifacts) : [],
            hasNewArtifact: verificationState?.artifacts && typeof verificationState.artifacts === 'object' && contentId in verificationState.artifacts
          });
        }
      } catch (stateError) {
        console.error(`Content Generation Agent: Error ensuring artifact is stored:`, stateError);
      }
    }

    // Return response and artifact updates
    return {
      success: true,
      message: `Successfully generated content based on discussion ${discussionId || 'unknown'}`,
      response: contentResponse,
      artifactUpdates: {
        new: {
          [contentId]: artifact
        }
      },
      stateUpdates: {
        decisions: state.decisions
      }
    };
  } catch (error) {
    console.error('Error generating content from discussion contribution:', error);

    // Send error response
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Content generation failed',
        message: error instanceof Error ? error.message : String(error)
      },
      message.conversationId,
      message.id
    );

    return {
      success: false,
      message: 'Failed to generate content from discussion contribution',
      error: error instanceof Error ? error.message : String(error),
      response: errorResponse
    };
  }
}

/**
 * Handle discussion synthesis request
 */
export async function handleDiscussionSynthesisRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`Content Generation Agent: Handling discussion synthesis request from ${message.from}`);

  // Extract synthesis details
  const { discussionId, perspectives } = message.content || {};

  if (!discussionId || !perspectives) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Missing required fields',
        message: 'Discussion ID and perspectives are required for synthesis'
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Generate structured reasoning for synthesis using the utility function
  const considerations = [
    'Audience targeting: Combine demographic insights with search intent analysis',
    'Content structure: Use H2/H3 hierarchy that accommodates both SEO and readability',
    'Engagement factors: Narrative techniques + data visualization + clear CTAs',
    'SEO implementation: Strategic keyword placement without compromising flow'
  ];

  const decision = 'Synthesize a comprehensive content creation approach that balances audience needs, search discoverability, and engagement';

  const synthesisReasoning = createEnhancedReasoning(
    { topic: discussionId, perspectives: perspectives },
    considerations,
    decision,
    AGENT_ID,
    {
      confidence: 0.94,
      steps: [
        'Analyze all contributed perspectives for content creation insights',
        'Identify common themes and complementary approaches',
        'Evaluate content structure recommendations across perspectives',
        'Assess engagement strategies from different viewpoints',
        'Formulate an integrated content creation approach'
      ],
      insights: [
        'Market research and SEO insights provide complementary audience targeting strategies',
        'Content structure recommendations from different perspectives can be harmonized',
        'A balanced approach between SEO optimization and readability is essential',
        'Multiple engagement techniques can be combined for maximum impact'
      ]
    }
  );

  // Store the reasoning in the agent's state
  await storeAgentReasoning(
    state.id,
    AGENT_ID,
    "", // No specific artifact
    message.id,
    message.conversationId,
    synthesisReasoning
  );

  // Generate synthesis of perspectives with structured reasoning
  const synthesis = `## INTEGRATED CONTENT CREATION APPROACH

After analyzing all perspectives through a content creation lens, I recommend the following integrated approach:

### AUDIENCE-CENTRIC FOUNDATION
* Combine market research demographics with search intent analysis
* Create content that addresses both informational and commercial needs
* Develop user personas that merge market segments with keyword research

### OPTIMIZED CONTENT STRUCTURE
* Use a 4-part structure: compelling hook → problem exploration → solution framework → actionable conclusion
* Implement H2/H3 hierarchy that incorporates primary and secondary keywords
* Create a content flow that guides readers from awareness to consideration

### ENGAGEMENT MAXIMIZATION
* Balance data-driven insights with narrative storytelling
* Incorporate visual elements strategically at key decision points
* Use a mix of formats: explanatory text, examples, quotes, and interactive elements

### TECHNICAL OPTIMIZATION
* Implement SEO best practices without compromising readability
* Maintain keyword density of 1.5-2.5% with natural placement
* Optimize meta elements and structured data for enhanced visibility

This balanced approach creates content that serves both audience needs and business objectives while maintaining discoverability through search engines.`;

  // Send synthesis response
  const response = await messaging.send(
    state.id,
    message.from as AgentId,
    IterativeMessageType.DISCUSSION_SYNTHESIS,
    {
      discussionId,
      synthesis: {
        content: synthesis,
        agent: AGENT_ID,
        timestamp: new Date().toISOString(),
        reasoning: synthesisReasoning
      }
    },
    message.conversationId,
    message.id
  );

  return { response };
}
