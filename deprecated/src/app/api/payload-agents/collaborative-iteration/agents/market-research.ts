/**
 * Market Research Agent
 *
 * This agent is responsible for conducting market research and providing insights.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeArtifact,
  ArtifactStatus,
  Goal
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';

/**
 * Handle initial request for market research
 */
export async function handleMarketResearchInitialRequest(
  message: IterativeMessage,
  state: any,
  stateManager: any,
  messaging: any
): Promise<any> {
  logger.info(`Market Research Agent: Handling initial request`, { sessionId: message.conversationId });

  // Extract request details
  const { topic, contentType, targetAudience, tone } = message.content || {};

  if (!topic) {
    return {
      success: false,
      message: 'Topic is required for market research',
      response: await messaging.send(
        message.conversationId,
        message.from,
        IterativeMessageType.ERROR,
        {
          error: 'Missing required fields',
          message: 'Topic is required for market research'
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Generate mock market research data
  const marketResearch = {
    topic,
    targetAudience: targetAudience || 'general audience',
    demographics: {
      age: '25-45',
      gender: 'mixed',
      interests: ['technology', 'business', 'education'],
      location: 'global'
    },
    competitors: [
      {
        name: 'Competitor A',
        strengths: ['Strong brand recognition', 'High-quality content'],
        weaknesses: ['Infrequent updates', 'Limited topic coverage']
      },
      {
        name: 'Competitor B',
        strengths: ['Comprehensive coverage', 'Strong SEO'],
        weaknesses: ['Technical language', 'Poor mobile experience']
      }
    ],
    trends: [
      'Increasing demand for visual content',
      'Growing interest in interactive elements',
      'Shift towards mobile consumption',
      'Preference for concise, actionable information'
    ],
    opportunities: [
      'Gap in market for beginner-friendly content',
      'Underserved niche in practical applications',
      'Potential for video content expansion',
      'Social media integration opportunities'
    ],
    insights: [
      `The target audience for ${topic} is primarily ${targetAudience || 'general audience'} looking for practical information`,
      `Content should focus on actionable advice and real-world applications`,
      `Visual elements and case studies will increase engagement`,
      `Mobile optimization is critical for this audience`
    ]
  };

  // Create market research artifact
  const artifact: IterativeArtifact = {
    id: uuidv4(),
    name: `Market Research: ${topic}`,
    type: 'market-research',
    status: 'completed' as ArtifactStatus,
    createdBy: 'market-research',
    createdAt: new Date().toISOString(),
    currentVersion: 1,
    iterations: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'market-research',
        content: marketResearch,
        feedback: [],
        incorporatedConsultations: []
      }
    ],
    qualityScore: 85
  };

  // Add the artifact to the state
  await stateManager.addArtifact(message.conversationId, artifact);

  // Update workflow progress
  await stateStore.updateState(message.conversationId, (state) => {
    if (state.workflowProgress) {
      state.workflowProgress.marketResearchComplete = true;
    }
    return state;
  });

  // Return the response
  return {
    success: true,
    message: `Successfully generated market research for ${topic}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      IterativeMessageType.ARTIFACT_DELIVERY,
      {
        artifact,
        message: `Generated market research for ${topic}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
}

/**
 * Handle artifact request for market research
 */
export async function handleMarketResearchArtifactRequest(
  message: IterativeMessage,
  state: any,
  stateManager: any,
  messaging: any
): Promise<any> {
  logger.info(`Market Research Agent: Handling artifact request`, { sessionId: message.conversationId });

  // Extract request details
  const { artifactId, artifactType } = message.content || {};

  // Find the requested artifact
  let artifact: IterativeArtifact | null = null;

  if (artifactId && state.artifacts && state.artifacts[artifactId]) {
    artifact = state.artifacts[artifactId];
  } else if (artifactType === 'market-research' && state.artifacts) {
    // Find the latest market research artifact
    const marketResearchArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'market-research' && a.createdBy === 'market-research')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (marketResearchArtifacts.length > 0) {
      artifact = marketResearchArtifacts[0] as IterativeArtifact;
    }
  }

  if (!artifact) {
    return {
      success: false,
      message: 'Artifact not found',
      response: await messaging.send(
        message.conversationId,
        message.from,
        IterativeMessageType.ERROR,
        {
          error: 'Artifact not found',
          message: `Could not find the requested artifact`
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Return the artifact
  return {
    success: true,
    message: `Successfully retrieved artifact ${artifact.id}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      IterativeMessageType.ARTIFACT_DELIVERY,
      {
        artifact,
        message: `Retrieved artifact ${artifact.id}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
}

/**
 * Market Research Agent class
 */
export class MarketResearchAgent {
  private agentId = 'market-research';

  /**
   * Process a message sent to this agent
   */
  async processMessage(sessionId: string, message: IterativeMessage): Promise<any> {
    logger.info(`Market Research Agent processing message of type ${message.type}`, {
      sessionId,
      messageId: message.id,
      messageType: message.type
    });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return null;
      }

      // Create a simple state manager for the handlers
      const stateManager = {
        addArtifact: async (sessionId: string, artifact: IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (!state.artifacts) {
              state.artifacts = {};
            }
            state.artifacts[artifact.id] = artifact;

            // Also add to generatedArtifacts array if it exists
            if (state.generatedArtifacts) {
              state.generatedArtifacts.push(artifact.id);
            } else {
              state.generatedArtifacts = [artifact.id];
            }

            return state;
          });
        },
        updateArtifact: async (sessionId: string, artifactId: string, updateFn: (artifact: IterativeArtifact) => IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (state.artifacts && state.artifacts[artifactId]) {
              state.artifacts[artifactId] = updateFn(state.artifacts[artifactId]);
            }
            return state;
          });
        }
      };

      // Create a simple messaging utility for the handlers
      const messaging = {
        send: async (
          sessionId: string,
          to: string,
          type: IterativeMessageType,
          content: any,
          conversationId?: string,
          inReplyTo?: string,
          artifactId?: string
        ) => {
          const message: IterativeMessage = {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: this.agentId,
            to,
            type,
            content,
            conversationId: conversationId || sessionId,
            inReplyTo,
            artifactId
          };

          // Store the message in the state
          await stateStore.updateState(sessionId, (state) => {
            if (!state.messages) {
              state.messages = [];
            }
            state.messages.push(message);
            return state;
          });

          return message;
        }
      };

      // Handle different message types
      switch (message.type) {
        case IterativeMessageType.INITIAL_REQUEST:
        case IterativeMessageType.REQUEST:
          return await handleMarketResearchInitialRequest(message, state, stateManager, messaging);

        case IterativeMessageType.ARTIFACT_REQUEST:
          return await handleMarketResearchArtifactRequest(message, state, stateManager, messaging);

        case IterativeMessageType.FEEDBACK:
          // Import the handleFeedback function from the handlers file
          const { handleFeedback } = await import('./market-research/handlers');
          return await handleFeedback(message, state, stateManager, messaging);

        default:
          logger.warn(`Market Research Agent: Unhandled message type ${message.type}`, {
            sessionId,
            messageType: message.type
          });
          return null;
      }
    } catch (error) {
      logger.error(`Error processing message in Market Research Agent`, {
        sessionId,
        messageId: message.id,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Act on the current state of the session
   * This method is called by the workflow orchestrator
   */
  async act(sessionId: string): Promise<boolean> {
    logger.info(`Market Research Agent acting on session ${sessionId}`, { sessionId });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return false;
      }

      // Find active goals assigned to this agent
      const activeGoals = (state.goals || []).filter((goal: Goal) =>
        goal.status === 'active' && goal.assignedTo === this.agentId
      );

      if (activeGoals.length === 0) {
        logger.info(`No active goals for Market Research Agent in session ${sessionId}`, { sessionId });
        return false;
      }

      // Process each active goal
      for (const goal of activeGoals) {
        logger.info(`Market Research Agent processing goal ${goal.id}`, {
          sessionId,
          goalId: goal.id,
          goalName: goal.name
        });

        // Create a message for this goal
        const message: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: this.agentId,
          type: IterativeMessageType.INITIAL_REQUEST,
          content: {
            topic: state.topic,
            contentType: state.contentType,
            targetAudience: state.targetAudience,
            tone: state.tone,
            ...goal.metadata
          },
          conversationId: sessionId,
          goalId: goal.id
        };

        // Process the message
        const result = await this.processMessage(sessionId, message);

        if (result && result.success) {
          // Mark the goal as completed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'completed';
              state.goals[goalIndex].completedAt = new Date().toISOString();
            }
            return state;
          });

          logger.info(`Market Research Agent completed goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name
          });
        } else {
          logger.error(`Market Research Agent failed to complete goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name,
            error: result?.message || 'Unknown error'
          });

          // Mark the goal as failed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'failed';
              state.goals[goalIndex].failedAt = new Date().toISOString();
              state.goals[goalIndex].failureReason = result?.message || 'Unknown error';
            }
            return state;
          });
        }
      }

      return true;
    } catch (error) {
      logger.error(`Error in Market Research Agent act method`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      return false;
    }
  }


}
