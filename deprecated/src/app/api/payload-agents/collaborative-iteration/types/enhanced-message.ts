import { v4 as uuidv4 } from 'uuid';
import { 
  A2AMessage, 
  EnhancedA2AMessage, 
  Reasoning, 
  MessageIntention, 
  RequestedAction, 
  Part 
} from '../../a2atypes';
import { IterativeMessageType, AgentId } from '../types';

/**
 * Enhanced IterativeMessage that extends EnhancedA2AMessage
 * This provides compatibility with both the A2A protocol and the iterative collaboration system
 */
export interface EnhancedIterativeMessage extends EnhancedA2AMessage {
  // Core A2A fields inherited from EnhancedA2AMessage
  // Additional IterativeMessage specific fields
  type: IterativeMessageType;
  sessionId?: string;
  parentId?: string;
  artifactId?: string;
  version?: number;
}

/**
 * Create an enhanced iterative message
 */
export function createEnhancedIterativeMessage(
  from: AgentId | string,
  to: AgentId | string | (AgentId | string)[],
  type: IterativeMessageType,
  content: any,
  options: {
    conversationId?: string;
    sessionId?: string;
    inReplyTo?: string;
    parentId?: string;
    artifactId?: string;
    version?: number;
    reasoning?: Reasoning;
    intentions?: MessageIntention[];
    requestedActions?: RequestedAction[];
    artifactReferences?: string[];
    decisionReferences?: string[];
    metadata?: Record<string, any>;
  } = {}
): EnhancedIterativeMessage {
  // Generate default values
  const id = uuidv4();
  const timestamp = new Date().toISOString();
  const conversationId = options.conversationId || options.sessionId || id;
  
  // Create text part from content
  const parts: Part[] = [];
  
  if (typeof content === 'string') {
    parts.push({
      type: 'text',
      text: content
    });
  } else {
    parts.push({
      type: 'data',
      data: content
    });
  }
  
  // Create the enhanced message
  return {
    // A2AMessage fields
    role: from === 'system' ? 'system' : 'agent',
    parts,
    
    // EnhancedA2AMessage fields
    id,
    timestamp,
    from: from.toString(),
    to: Array.isArray(to) ? to.map(t => t.toString()) : to.toString(),
    conversationId,
    replyTo: options.inReplyTo,
    reasoning: options.reasoning,
    intentions: options.intentions || [],
    requestedActions: options.requestedActions || [],
    artifactReferences: options.artifactReferences || [],
    decisionReferences: options.decisionReferences || [],
    
    // EnhancedIterativeMessage fields
    type,
    sessionId: options.sessionId,
    parentId: options.parentId,
    artifactId: options.artifactId,
    version: options.version,
    metadata: options.metadata || {}
  };
}
