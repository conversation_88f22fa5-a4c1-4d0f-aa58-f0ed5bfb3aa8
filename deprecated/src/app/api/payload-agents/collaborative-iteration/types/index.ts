// src/app/(payload)/api/agents/collaborative-iteration/types/index.ts

// Import external types but don't reuse them directly to avoid conflicts
// We define our own local versions of these types
// import { Reasoning as ExternalReasoning } from '../../a2atypes';

/**
 * Agent identifiers for the collaborative system
 */
export type AgentId = 
  | 'market-research'
  | 'seo-keyword'
  | 'content-strategy'
  | 'content-generation'
  | 'seo-optimization'
  | 'orchestrator'
  | 'human';

/**
 * Thought - a single unit of reasoning
 */
export interface Thought {
  thought: string;
  reasoning?: string;
  confidence: number;
  timestamp: string;
}

/**
 * Consideration - a factor in decision making
 */
export interface Consideration {
  option?: string;
  factor?: string;
  pros?: string[];
  cons?: string[];
  impact?: string;
  explanation?: string;
  decision?: string;
}

/**
 * Reasoning format for agent collaborative thought processes
 */
export interface Reasoning {
  thoughts: Thought[];
  considerations: Consideration[];
  process: string;
  reasoning: string;
  conclusion: string;
  decision: string;
  confidence: number;
  timestamp: string;
  perspective?: string;
  rationale?: string;
  chain?: string[];
}

/**
 * Enhanced reasoning with more detailed thought processes
 */
export interface EnhancedReasoning {
  insights: string[];
  supportingEvidence: string[];
  considerations: string[] | Array<{
    option?: string;
    factor?: string;
    pros?: string[];
    cons?: string[];
    impact?: string;
    explanation?: string;
    decision?: string;
  }>;
  conclusion: string;
  nextSteps?: string[];
  process?: string;
  steps?: string[];
  decision?: string;
  timestamp?: string;
  confidence?: number;
  alternatives?: string[];
}

/**
 * Message types for iterative agent collaboration
 */
export type IterativeMessageType = 
  | 'INITIAL_REQUEST'                // Initial content generation request
  | 'CONSULTATION_REQUEST'           // Request for feedback on content
  | 'CONSULTATION_RESPONSE'          // Provide feedback on content
  | 'ITERATION_REQUEST'              // Request for a new iteration of content
  | 'ITERATION_RESPONSE'             // Provide a new iteration of content
  | 'FINAL_OUTPUT'                   // Final content after iterations
  | 'SYSTEM_MESSAGE'                 // System message
  | 'ERROR'                          // Error message
  | 'ARTIFACT_DELIVERY'              // Deliver an artifact to another agent
  | 'ARTIFACT_REQUEST'               // Request an artifact from another agent
  | 'FEEDBACK'                       // Direct feedback on content without explicit consultation
  | 'FEEDBACK_REQUEST'               // Request for feedback on content
  | 'ARTIFACT_BROADCAST'             // Broadcast an artifact to all agents
  | 'DISCUSSION_START'               // Initiate a new discussion among agents
  | 'DISCUSSION_PERSPECTIVE_REQUEST' // Request for perspective in a discussion
  | 'DISCUSSION_SYNTHESIS_REQUEST'   // Request for synthesis of perspectives
  | 'DISCUSSION_SYNTHESIS'           // Synthesis of discussion contributions
  | 'DISCUSSION_CONTRIBUTION'        // Contribution to an ongoing discussion
  | 'ACKNOWLEDGMENT';                // Acknowledge receipt of a message

/**
 * Artifact status in the iterative process
 */
export enum ArtifactStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  APPROVED = 'approved'
}

/**
 * Basic message structure for iterative collaboration
 */
export interface IterativeMessage {
  id: string;
  timestamp: string;
  from: AgentId;
  to: AgentId | AgentId[];
  type: IterativeMessageType;
  content: any;
  conversationId: string;
  parentId?: string;
  reasoning?: Reasoning | EnhancedReasoning;
  metadata?: {
    [key: string]: any;
  }
}

/**
 * Consultation record - tracks feedback between agents
 */
export interface Consultation {
  id: string;
  fromAgent: AgentId;
  toAgent: AgentId;
  question?: string;
  context?: any;
  response?: any;
  timestamp: string;
  artifactId?: string;
  feedback?: string;
  suggestions?: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  incorporated?: boolean;
  requestId?: string;
}

/**
 * Iteration - represents a version of content with its history
 */
export interface Iteration {
  version: number;
  timestamp: string;
  agent: AgentId;
  content: any;
  feedback?: string[];
  incorporatedConsultations?: string[];
  changes?: string;
  reasoning?: Reasoning;
}

/**
 * Artifact - a content piece produced by agents
 */
export interface IterativeArtifact {
  id: string;
  type: string;
  name: string;
  description?: string;
  content: any;
  status: ArtifactStatus;
  createdBy: AgentId;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
  versions?: Iteration[];
  tags?: string[];
  feedback?: Array<{
    from: AgentId;
    content: string;
    timestamp: string;
  }>;
}

/**
 * StandardizedHandlerResult provides a consistent structure for all agent handler results
 */
export interface StandardizedHandlerResult {
  success: boolean;
  message: string;
  error: string | null;
  response: IterativeMessage;
  artifact?: IterativeArtifact;
}

/**
 * Agent state for tracking interactions
 */
export interface AgentState {
  id: AgentId;
  processedRequests?: string[];
  generatedArtifacts?: string[];
  consultationsProvided?: string[];
  consultationsReceived?: string[];
  lastUpdated?: string;
  feedback?: Array<{
    artifactId: string;
    from: AgentId;
    content: string;
    timestamp: string;
  }>;
}

/**
 * Decision - represents an agent's reasoning and decision-making process
 */
export interface Decision {
  id: string;
  agent: AgentId;
  timestamp: string;
  context?: string;
  reasoning?: string;
  outcome?: string;
  confidence?: number;
  originalReasoning?: Reasoning | EnhancedReasoning;
}

/**
 * Discussion - represents a structured discussion between agents
 */
export interface Discussion {
  id: string;
  topic: string;
  participants: AgentId[];
  leadAgent: AgentId;
  status: 'active' | 'completed';
  messages: DiscussionMessage[];
  resolution?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DiscussionMessage {
  id: string;
  agentId: AgentId;
  content: any;
  type: 'perspective' | 'synthesis' | 'response';
  timestamp: string;
}

/**
 * Event - represents system events like circuit breakers
 */
export interface Event {
  type: 'CIRCUIT_BREAKER' | 'RATE_LIMIT' | 'CONSECUTIVE_MESSAGES' | 'SYSTEM';
  timestamp: string;
  data: any;
}

/**
 * Goal - a task that needs to be completed in the collaboration
 */
export interface Goal {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'blocked';
  assignedTo?: AgentId;
  dependsOn?: string[];
  createdAt: string;
  completedAt?: string;
  artifactType?: string;
}

/**
 * Iterative collaboration state
 */
export interface IterativeCollaborationState {
  id: string;
  status: string;
  artifacts?: IterativeArtifact[];
  messages?: IterativeMessage[];
  goals?: Goal[];
  decisions?: Decision[];
  consultations?: Consultation[];
  discussions?: Discussion[];
  events?: Event[];
  agentStates?: Record<AgentId, AgentState>;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
}

/**
 * Agent interface for collaborative agents
 */
export interface Agent {
  agentId: AgentId;
  shouldAct(sessionId: string): Promise<boolean>;
  act(sessionId: string): Promise<void>;
  processMessage(sessionId: string, message: IterativeMessage): Promise<StandardizedHandlerResult | null>;
}
