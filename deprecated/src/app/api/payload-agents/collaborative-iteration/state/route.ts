import { NextRequest, NextResponse } from 'next/server'
import { getPayloadClient } from '@/payload/payloadClient'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'

/**
 * GET handler for fetching the current state of a collaborative iteration session
 */
export async function GET(req: NextRequest) {
  try {
    // Get session to check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get session ID from query parameters
    const { searchParams } = new URL(req.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json({ error: 'Missing sessionId parameter' }, { status: 400 })
    }

    // Get payload client
    const payload = await getPayloadClient()

    // Fetch the collaboration session from the database
    const collaborationSession = await payload.find({
      collection: 'collaborationSessions',
      where: {
        id: {
          equals: sessionId,
        },
      },
    })

    if (!collaborationSession || collaborationSession.docs.length === 0) {
      return NextResponse.json({ error: 'Collaboration session not found' }, { status: 404 })
    }

    // Return the session data
    return NextResponse.json(collaborationSession.docs[0])
  } catch (error) {
    console.error('Error fetching collaboration state:', error)
    return NextResponse.json(
      { error: 'Failed to fetch collaboration state' },
      { status: 500 }
    )
  }
}

/**
 * POST handler for updating the state of a collaborative iteration session
 */
export async function POST(req: NextRequest) {
  try {
    // Get session to check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { sessionId, state } = await req.json()

    if (!sessionId || !state) {
      return NextResponse.json(
        { error: 'Missing required parameters: sessionId and state' },
        { status: 400 }
      )
    }

    // Get payload client
    const payload = await getPayloadClient()

    // Update the collaboration session in the database
    const updatedSession = await payload.update({
      collection: 'collaborationSessions',
      id: sessionId,
      data: {
        state: JSON.stringify(state),
        lastUpdated: new Date().toISOString(),
      },
    })

    // Return the updated session data
    return NextResponse.json(updatedSession)
  } catch (error) {
    console.error('Error updating collaboration state:', error)
    return NextResponse.json(
      { error: 'Failed to update collaboration state' },
      { status: 500 }
    )
  }
}
