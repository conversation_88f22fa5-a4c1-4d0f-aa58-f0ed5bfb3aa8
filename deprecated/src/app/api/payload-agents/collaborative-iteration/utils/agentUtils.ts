import { v4 as uuidv4 } from 'uuid';
import { 
  IterativeMessage, 
  IterativeArtifact, 
  Consultation,
  AgentState,
  IterativeCollaborationState
} from '../types';
import { AgentId, StandardizedHandlerResult } from './agentTypes';

/**
 * Creates a standard message structure with common properties
 */
export function createMessage(
  fromAgent: AgentId, 
  toAgent: AgentId | AgentId[], 
  type: string, 
  content: Record<string, any>,
  conversationId: string
): IterativeMessage {
  return {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: fromAgent,
    to: toAgent,
    type: type as any,
    content,
    conversationId
  };
}

/**
 * Create an acknowledgment message
 */
export function createAcknowledgment(
  fromAgent: AgentId, 
  toAgent: AgentId,
  originalMessageId: string,
  message: string,
  conversationId: string
): IterativeMessage {
  return createMessage(
    fromAgent,
    toAgent,
    'ACKNOWLEDGMENT',
    {
      message,
      originalMessageId
    },
    conversationId
  );
}

/**
 * Create an artifact request message
 */
export function createArtifactRequest(
  fromAgent: AgentId,
  toAgent: AgentId,
  artifactType: string,
  description: string,
  conversationId: string
): IterativeMessage {
  return createMessage(
    fromAgent,
    toAgent,
    'ARTIFACT_REQUEST',
    {
      artifactType,
      description
    },
    conversationId
  );
}

/**
 * Create an artifact delivery message
 */
export function createArtifactDelivery(
  fromAgent: AgentId,
  toAgent: AgentId,
  artifact: IterativeArtifact,
  originalMessageId: string | undefined,
  conversationId: string
): IterativeMessage {
  return createMessage(
    fromAgent,
    toAgent,
    'ARTIFACT_DELIVERY',
    {
      artifactId: artifact.id,
      artifactType: artifact.type,
      originalMessageId
    },
    conversationId
  );
}

/**
 * Create a feedback message
 */
export function createFeedback(
  fromAgent: AgentId,
  toAgent: AgentId,
  feedbackContent: string,
  artifactId: string | undefined,
  originalMessageId: string | undefined,
  conversationId: string
): IterativeMessage {
  return createMessage(
    fromAgent,
    toAgent,
    'FEEDBACK',
    {
      feedback: feedbackContent,
      artifactId,
      originalMessageId
    },
    conversationId
  );
}

/**
 * Create a consultation request message
 */
export function createConsultationRequest(
  fromAgent: AgentId,
  toAgent: AgentId,
  question: string,
  context: Record<string, any> | undefined,
  conversationId: string
): IterativeMessage {
  return createMessage(
    fromAgent,
    toAgent,
    'CONSULTATION_REQUEST',
    {
      question,
      context: context || {}
    },
    conversationId
  );
}

/**
 * Create a consultation response message
 */
export function createConsultationResponse(
  fromAgent: AgentId,
  toAgent: AgentId,
  consultation: Consultation,
  originalMessageId: string,
  conversationId: string
): IterativeMessage {
  return createMessage(
    fromAgent,
    toAgent,
    'CONSULTATION_RESPONSE',
    {
      consultationId: consultation.id,
      response: consultation.response,
      originalMessageId
    },
    conversationId
  );
}

/**
 * Create an error message
 */
export function createErrorMessage(
  fromAgent: AgentId,
  toAgent: AgentId,
  errorMessage: string,
  originalMessageId: string | undefined,
  conversationId: string
): IterativeMessage {
  return createMessage(
    fromAgent,
    toAgent,
    'ERROR',
    {
      message: errorMessage,
      originalMessageId
    },
    conversationId
  );
}

/**
 * Get agent state from collaboration state
 */
export function getAgentState(
  state: IterativeCollaborationState,
  agentId: AgentId
): AgentState {
  // Return existing agent state or create a new one
  return state.agentStates?.[agentId] || {
    id: agentId,
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };
}

/**
 * Check if a message has already been processed by an agent
 */
export function hasProcessedMessage(
  state: IterativeCollaborationState,
  agentId: AgentId,
  messageId: string
): boolean {
  const agentState = getAgentState(state, agentId);
  return (agentState.processedRequests || []).includes(messageId);
}

/**
 * Mark a message as processed by an agent
 */
export function trackProcessedMessage(
  state: IterativeCollaborationState,
  agentId: AgentId,
  messageId: string
): IterativeCollaborationState {
  const agentState = getAgentState(state, agentId);
  
  // Skip if already processed
  if ((agentState.processedRequests || []).includes(messageId)) {
    return state;
  }
  
  // Update processed requests
  const processedRequests = [...(agentState.processedRequests || []), messageId];
  
  // Update state
  return {
    ...state,
    agentStates: {
      ...state.agentStates,
      [agentId]: {
        ...agentState,
        processedRequests,
        lastUpdated: new Date().toISOString()
      }
    }
  };
}

/**
 * Add an artifact to the collaboration state
 */
export function addArtifact(
  state: IterativeCollaborationState,
  agentId: AgentId,
  artifact: IterativeArtifact
): IterativeCollaborationState {
  const agentState = getAgentState(state, agentId);
  
  // Skip if already tracked
  if ((agentState.generatedArtifacts || []).includes(artifact.id)) {
    return {
      ...state,
      artifacts: {
        ...state.artifacts,
        [artifact.id]: artifact
      }
    };
  }
  
  // Update generated artifacts
  const generatedArtifacts = [...(agentState.generatedArtifacts || []), artifact.id];
  
  // Update state
  return {
    ...state,
    artifacts: {
      ...state.artifacts,
      [artifact.id]: artifact
    },
    agentStates: {
      ...state.agentStates,
      [agentId]: {
        ...agentState,
        generatedArtifacts,
        lastUpdated: new Date().toISOString()
      }
    }
  };
}

/**
 * Add a consultation to the collaboration state
 */
export function addConsultation(
  state: IterativeCollaborationState,
  providerId: AgentId,
  consultation: Consultation
): IterativeCollaborationState {
  const providerState = getAgentState(state, providerId);
  const requesterState = getAgentState(state, consultation.requester as AgentId);
  
  // Skip if already tracked
  if ((providerState.consultationsProvided || []).includes(consultation.id)) {
    return {
      ...state,
      consultations: {
        ...state.consultations,
        [consultation.id]: consultation
      }
    };
  }
  
  // Update consultations provided by provider
  const consultationsProvided = [...(providerState.consultationsProvided || []), consultation.id];
  
  // Update consultations received by requester
  const consultationsReceived = [...(requesterState.consultationsReceived || []), consultation.id];
  
  // Update state
  return {
    ...state,
    consultations: {
      ...state.consultations,
      [consultation.id]: consultation
    },
    agentStates: {
      ...state.agentStates,
      [providerId]: {
        ...providerState,
        consultationsProvided,
        lastUpdated: new Date().toISOString()
      },
      [consultation.requester]: {
        ...requesterState,
        consultationsReceived,
        lastUpdated: new Date().toISOString()
      }
    }
  };
}

/**
 * Standardize a handler result from the old format to the new format
 */
export function standardizeResult(result: any): StandardizedHandlerResult {
  // If result is already in standardized format, return it as is
  if (result.response && (result.stateUpdates !== undefined || 
                        result.artifactUpdates !== undefined || 
                        result.consultationUpdates !== undefined)) {
    return result;
  }
  
  // Extract components from legacy format
  const { 
    response,
    updatedState, 
    newArtifacts, 
    updatedArtifacts,
    newConsultations
  } = result;
  
  // Build standardized result
  const standardized: StandardizedHandlerResult = {
    response
  };
  
  // Add state updates if provided
  if (updatedState) {
    standardized.stateUpdates = {};
    
    // Handle agent states appropriately
    if (updatedState.agentStates) {
      standardized.stateUpdates.agentStates = updatedState.agentStates;
    } 
  }
  
  // Add artifact updates if provided
  if (newArtifacts || updatedArtifacts) {
    standardized.artifactUpdates = {};
    
    if (newArtifacts) {
      standardized.artifactUpdates.new = newArtifacts;
    }
    
    if (updatedArtifacts) {
      standardized.artifactUpdates.updated = updatedArtifacts;
    }
  }
  
  // Add consultation updates if provided
  if (newConsultations) {
    standardized.consultationUpdates = {
      new: newConsultations
    };
  }
  
  return standardized;
}

/**
 * Apply the standardized result to update the collaboration state
 */
export function applyResult(
  state: IterativeCollaborationState,
  result: StandardizedHandlerResult
): IterativeCollaborationState {
  let updatedState = { ...state };
  
  // Apply state updates
  if (result.stateUpdates) {
    updatedState = {
      ...updatedState,
      ...result.stateUpdates
    };
  }
  
  // Apply artifact updates
  if (result.artifactUpdates) {
    // Apply new artifacts
    if (result.artifactUpdates.new) {
      updatedState = {
        ...updatedState,
        artifacts: {
          ...updatedState.artifacts,
          ...result.artifactUpdates.new
        }
      };
    }
    
    // Apply updated artifacts
    if (result.artifactUpdates.updated) {
      updatedState = {
        ...updatedState,
        artifacts: {
          ...updatedState.artifacts,
          ...result.artifactUpdates.updated
        }
      };
    }
  }
  
  // Apply consultation updates
  if (result.consultationUpdates?.new) {
    updatedState = {
      ...updatedState,
      consultations: {
        ...updatedState.consultations,
        ...result.consultationUpdates.new
      }
    };
  }
  
  // Add message to messages array
  updatedState = {
    ...updatedState,
    messages: [
      ...updatedState.messages,
      result.response
    ]
  };
  
  return updatedState;
}
