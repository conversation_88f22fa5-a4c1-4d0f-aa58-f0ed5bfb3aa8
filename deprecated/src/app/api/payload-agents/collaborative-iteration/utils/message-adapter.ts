import { 
  A2AMessage, 
  EnhancedA2AMessage, 
  Reasoning, 
  Part 
} from '../../a2atypes';
import { 
  IterativeMessage, 
  IterativeMessageType, 
  EnhancedReasoning 
} from '../types';
import { EnhancedIterativeMessage } from '../types/enhanced-message';

/**
 * Convert a standard IterativeMessage to EnhancedIterativeMessage
 */
export function convertToEnhancedIterativeMessage(message: IterativeMessage): EnhancedIterativeMessage {
  // Create parts from content
  const parts: Part[] = [];
  
  if (typeof message.content === 'string') {
    parts.push({
      type: 'text',
      text: message.content
    });
  } else {
    parts.push({
      type: 'data',
      data: message.content
    });
  }
  
  // Convert reasoning if present
  let reasoning: Reasoning | undefined;
  if (message.reasoning) {
    reasoning = {
      thoughts: Array.isArray(message.reasoning.thoughts) 
        ? message.reasoning.thoughts 
        : message.reasoning.process ? [message.reasoning.process] : [],
      considerations: Array.isArray(message.reasoning.considerations) 
        ? message.reasoning.considerations 
        : message.reasoning.steps ? message.reasoning.steps : [],
      alternatives: message.reasoning.alternatives || [],
      decision: message.reasoning.decision || '',
      confidence: message.reasoning.confidence || 0.8
    };
  }
  
  // Create enhanced message
  return {
    // A2AMessage fields
    role: message.from === 'system' ? 'system' : 'agent',
    parts,
    
    // EnhancedA2AMessage fields
    id: message.id,
    timestamp: message.timestamp,
    from: message.from,
    to: message.to,
    conversationId: message.conversationId,
    replyTo: message.inReplyTo,
    reasoning,
    intentions: [], // Default empty intentions
    
    // EnhancedIterativeMessage fields
    type: message.type,
    sessionId: message.sessionId,
    artifactId: message.artifactId,
    version: message.version,
    metadata: message.metadata || {}
  };
}

/**
 * Convert an EnhancedIterativeMessage to standard IterativeMessage
 */
export function convertToIterativeMessage(message: EnhancedIterativeMessage): IterativeMessage {
  // Extract content from parts
  let content: any;
  
  for (const part of message.parts) {
    if (part.type === 'text') {
      content = part.text;
      break;
    } else if (part.type === 'data') {
      content = part.data;
      break;
    }
  }
  
  // Convert reasoning if present
  let reasoning: Reasoning | EnhancedReasoning | undefined;
  if (message.reasoning) {
    reasoning = {
      thoughts: message.reasoning.thoughts,
      considerations: message.reasoning.considerations,
      alternatives: message.reasoning.alternatives,
      decision: message.reasoning.decision,
      confidence: message.reasoning.confidence
    };
  }
  
  // Create iterative message
  return {
    id: message.id,
    timestamp: message.timestamp,
    from: message.from,
    to: message.to,
    type: message.type,
    content,
    conversationId: message.conversationId,
    inReplyTo: message.replyTo,
    sessionId: message.sessionId,
    artifactId: message.artifactId,
    version: message.version,
    reasoning,
    metadata: message.metadata
  };
}

/**
 * Convert an A2AMessage to EnhancedIterativeMessage
 */
export function convertA2AToEnhancedIterativeMessage(
  message: A2AMessage | EnhancedA2AMessage,
  type: IterativeMessageType = IterativeMessageType.SYSTEM_MESSAGE,
  sessionId?: string
): EnhancedIterativeMessage {
  // Check if message is already an EnhancedA2AMessage
  const enhancedMessage = message as EnhancedA2AMessage;
  
  // Extract content from parts
  let content: any;
  for (const part of message.parts) {
    if (part.type === 'text') {
      content = part.text;
      break;
    } else if (part.type === 'data') {
      content = part.data;
      break;
    }
  }
  
  // Create enhanced iterative message
  return {
    ...enhancedMessage,
    // Ensure required fields are present
    id: enhancedMessage.id || `msg_${Date.now()}`,
    timestamp: enhancedMessage.timestamp || new Date().toISOString(),
    from: enhancedMessage.from || 'system',
    to: enhancedMessage.to || 'all',
    conversationId: enhancedMessage.conversationId || sessionId || `conv_${Date.now()}`,
    role: message.role,
    parts: message.parts,
    type,
    sessionId,
    metadata: {
      ...enhancedMessage.metadata,
      convertedFromA2A: true
    }
  };
}
