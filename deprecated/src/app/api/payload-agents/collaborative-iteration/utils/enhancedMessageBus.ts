/**
 * Enhanced Message Bus for Collaborative Agent System
 * 
 * This class handles message passing between agents with:
 * - Robust session ID tracking
 * - Circuit breaker pattern to prevent agent loops
 * - Rate limiting to prevent agent overloading
 * - Comprehensive error handling and logging
 */

import { v4 as uuidv4 } from 'uuid';
import { 
  IterativeMessage, 
  IterativeCollaborationState, 
  IterativeMessageType,
  IterativeArtifact 
} from '../types';
import { stateStore } from './stateStore';
import logger from './logger';

/**
 * Configuration for circuit breaker
 */
export interface CircuitBreakerConfig {
  maxMessagesPerAgent: number;  // Maximum messages an agent can send in a time window
  timeWindowMs: number;         // Time window in milliseconds
  maxConsecutiveMessages: number; // Maximum consecutive messages between same agents
  cooldownPeriodMs: number;     // Cooldown period after circuit is broken
}

/**
 * Default circuit breaker configuration
 */
const DEFAULT_CIRCUIT_BREAKER: CircuitBreakerConfig = {
  maxMessagesPerAgent: 10,
  timeWindowMs: 60000, // 1 minute
  maxConsecutiveMessages: 5,
  cooldownPeriodMs: 30000 // 30 seconds
};

/**
 * Message bus with circuit breaker for handling agent-to-agent communication
 */
export class EnhancedMessageBus {
  private circuitBreakerConfig: CircuitBreakerConfig;
  private messageCounters: Record<string, { count: number, lastReset: number }> = {};
  private consecutiveMessages: Record<string, number> = {};
  private brokenCircuits: Record<string, number> = {}; // Maps agent pair to time when circuit broke
  private activeSessions: Map<string, {
    messageQueue: IterativeMessage[];
    messageHistory: IterativeMessage[];
    messageHandlers: Record<string, (message: IterativeMessage) => Promise<any>>;
  }> = new Map();

  constructor(config: Partial<CircuitBreakerConfig> = {}) {
    this.circuitBreakerConfig = { ...DEFAULT_CIRCUIT_BREAKER, ...config };
    logger.info('Enhanced Message Bus initialized', { config: this.circuitBreakerConfig });
  }

  /**
   * Create a session-specific message bus
   */
  createSessionBus(sessionId: string): {
    sendMessage: (message: IterativeMessage) => Promise<any>;
    getHistory: () => IterativeMessage[];
    createBroadcastMessage: (
      from: string, 
      type: IterativeMessageType, 
      content: any
    ) => IterativeMessage;
    createDirectMessage: (
      from: string, 
      to: string | string[], 
      type: IterativeMessageType, 
      content: any, 
      inReplyTo?: string
    ) => IterativeMessage;
  } {
    if (!sessionId) {
      logger.error('Attempted to create session bus with null or undefined sessionId');
      throw new Error('Session ID is required to create a session bus');
    }
    
    // Add the session to active sessions if it doesn't exist
    if (!this.activeSessions.has(sessionId)) {
      logger.debug(`Creating new session bus for session ${sessionId}`);
      this.activeSessions.set(sessionId, {
        messageQueue: [],
        messageHistory: [],
        messageHandlers: {}
      });
    }
    
    // Create a session-specific message bus
    return {
      sendMessage: async (message: IterativeMessage) => {
        // Ensure message has sessionId
        if (!message.conversationId && !message.sessionId) {
          message.conversationId = sessionId;
          message.sessionId = sessionId;
        }
        
        // Call the main sendMessage method with message and sessionId as parameters
        return this.sendMessage(message, sessionId);
      },
      getHistory: () => {
        const session = this.activeSessions.get(sessionId);
        return session ? session.messageHistory : [];
      },
      createBroadcastMessage: (
        from: string,
        type: IterativeMessageType,
        content: any
      ): IterativeMessage => {
        return {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from,
          to: 'all', // Special recipient for broadcast
          type,
          content,
          conversationId: sessionId,
          sessionId: sessionId
        };
      },
      createDirectMessage: (
        from: string,
        to: string | string[],
        type: IterativeMessageType,
        content: any,
        inReplyTo?: string
      ): IterativeMessage => {
        return {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from,
          to,
          type,
          content,
          conversationId: sessionId,
          sessionId: sessionId,
          inReplyTo
        };
      }
    };
  }

  /**
   * Send a message with session ID and optional callback
   * @param sessionId - ID of the session for the message
   * @param message - The message to send
   * @param callback - Optional callback function to process the message
   */
  async sendMessage(sessionId: string, message: IterativeMessage, callback?: (message: IterativeMessage) => Promise<any>): Promise<any>;
  
  /**
   * Send a message with an optional session ID
   * @param message - The message to send
   * @param sessionId - Optional session ID
   */
  async sendMessage(message: IterativeMessage, sessionId?: string): Promise<any>;
  
  /**
   * Implementation of sendMessage with unified parameter handling
   */
  async sendMessage(
    sessionIdOrMessage: string | IterativeMessage,
    messageOrCallback?: IterativeMessage | Function | string,
    callbackFn?: Function
  ): Promise<any> {
    // Handle different parameter patterns
    let message: IterativeMessage;
    let sessionId: string | undefined;
    let callback: Function | undefined;
    
    // Pattern 1: (sessionId, message, callback)
    if (typeof sessionIdOrMessage === 'string' && typeof messageOrCallback === 'object' && messageOrCallback !== null) {
      sessionId = sessionIdOrMessage;
      message = messageOrCallback as IterativeMessage;
      callback = callbackFn;
    }
    // Pattern 2: (message, sessionId?)
    else if (typeof sessionIdOrMessage === 'object' && sessionIdOrMessage !== null) {
      message = sessionIdOrMessage as IterativeMessage;
      if (typeof messageOrCallback === 'string') {
        sessionId = messageOrCallback;
        callback = undefined;
      } else if (typeof messageOrCallback === 'function') {
        sessionId = undefined;
        callback = messageOrCallback as Function;
      } else {
        sessionId = undefined;
        callback = undefined;
      }
    }
    // Invalid pattern
    else {
      logger.error('Invalid parameter pattern for sendMessage', {
        sessionIdOrMessageType: typeof sessionIdOrMessage,
        messageOrCallbackType: typeof messageOrCallback
      });
      return { success: false, error: 'Invalid parameter pattern' };
    }
    // Ensure message is an object, not a string
    if (typeof message !== 'object' || message === null) {
      logger.error('Invalid message format: expected object, got ' + typeof message, {
        message: typeof message === 'string' ? (message as string).substring(0, 100) : String(message)
      });
      return { success: false, error: 'Invalid message format' };
    }
    
    // Get the session ID from parameters or message
    if (!sessionId) {
      sessionId = message.conversationId || message.sessionId;
    }
    
    if (!sessionId) {
      logger.error('No session ID in message or provided as parameter', {
        messageId: message.id,
        from: message.from,
        to: message.to,
        type: message.type
      });
      return { success: false, error: 'No session ID provided' };
    }
    
    // Ensure the message has the sessionId set
    message.conversationId = sessionId;
    message.sessionId = sessionId;
    
    logger.debug(`Sending message in session ${sessionId}`, {
      sessionId,
      messageId: message.id,
      type: message.type,
      from: message.from,
      to: message.to
    });
    
    // Map generic REQUEST messages to specific types if needed
    if (message.type === IterativeMessageType.REQUEST) {
      const state = await stateStore.getState(sessionId);
      if (state) {
        message = await this.mapRequestToSpecificType(message, state);
        // Log the mapping if it occurred
        logger.debug(`Mapped message type from REQUEST to ${message.type}`, {
          sessionId,
          messageId: message.id,
          from: message.from,
          to: message.to
        });
      }
    }

    // Store message in state
    try {
      await stateStore.storeMessage(sessionId, message);
    } catch (error) {
      logger.error('Failed to store message in state', {
        sessionId,
        messageId: message.id,
        error: (error as Error).message
      });
    }
    
    // If a callback function is provided, call it with the message
    let result = null;
    if (callback && typeof callback === 'function') {
      try {
        logger.debug(`Executing callback for message ${message.id}`, { sessionId });
        // Pass both sessionId and message to the callback function
        result = await callback(sessionId, message);
      } catch (error) {
        logger.error(`Error executing callback for message ${message.id}`, {
          sessionId,
          error: (error as Error).message,
          stack: (error as Error).stack
        });
        return { success: false, error: (error as Error).message };
      }
    }
    
    // Process message based on recipient type
    if (message.to === 'all') {
      // Handle broadcast messages
      logger.debug(`Broadcasting message from ${message.from} to all agents`, { sessionId });
      // Implementation depends on registered agents
      return { success: true, broadcast: true, result };
    } else {
      // Process direct message
      const toAgents = Array.isArray(message.to) ? message.to : [message.to];
      
      // For each recipient, check circuit breaker and rate limits
      for (const toAgent of toAgents) {
        if (this.isCircuitBroken(message.from, toAgent)) {
          logger.warn(`Circuit broken between ${message.from} and ${toAgent}`, { sessionId });
          return { 
            error: 'Circuit breaker triggered',
            message: 'Too many messages between these agents in a short time period'
          };
        }
        
        if (this.isRateLimited(message.from)) {
          logger.warn(`Rate limit exceeded for agent ${message.from}`, { sessionId });
          return {
            error: 'Rate limit exceeded',
            message: `Agent ${message.from} has sent too many messages in a short time period`
          };
        }
        
        if (this.tooManyConsecutiveMessages(message.from, toAgent)) {
          logger.warn(`Too many consecutive messages between ${message.from} and ${toAgent}`, { sessionId });
          this.breakCircuit(message.from, toAgent);
          return {
            error: 'Too many consecutive messages',
            message: `Agents ${message.from} and ${toAgent} have exchanged too many consecutive messages`
          };
        }
        
        // Update consecutive message counter
        this.updateConsecutiveCounter(message.from, toAgent);
      }
      
      return { success: true, direct: true };
    }
  }
  
  /**
   * Send a message and wait for a response (synchronous operation)
   */
  async sendMessageAndWaitForResponse(
    message: IterativeMessage,
    processorFn: (message: IterativeMessage) => Promise<IterativeMessage>,
    timeoutMs: number = 15000
  ): Promise<IterativeMessage | null> {
    // Get session ID
    const sessionId = message.conversationId || message.sessionId;
    
    if (!sessionId) {
      logger.error('No session ID in message for sendMessageAndWaitForResponse', {
        messageId: message.id
      });
      return null;
    }
    
    // Send the message
    const sendResult = await this.sendMessage(message);
    
    if (sendResult.error) {
      logger.error(`Error sending message: ${sendResult.error}`, {
        sessionId,
        messageId: message.id
      });
      return null;
    }
    
    // Process the message
    try {
      const response = await processorFn(message);
      
      // Store the response in state
      await stateStore.storeMessage(sessionId, response);
      
      return response;
    } catch (error) {
      logger.error('Error processing message', {
        sessionId,
        messageId: message.id,
        error: (error as Error).message
      });
      return null;
    }
  }
  
  /**
   * Check if an agent is rate limited
   */
  private isRateLimited(agent: string): boolean {
    const now = Date.now();
    
    // Initialize counter if it doesn't exist
    if (!this.messageCounters[agent]) {
      this.messageCounters[agent] = { count: 0, lastReset: now };
      return false;
    }
    
    // Reset counter if time window has passed
    if (now - this.messageCounters[agent].lastReset > this.circuitBreakerConfig.timeWindowMs) {
      this.messageCounters[agent] = { count: 0, lastReset: now };
      return false;
    }
    
    // Increment counter
    this.messageCounters[agent].count++;
    
    // Check if rate limit exceeded
    return this.messageCounters[agent].count > this.circuitBreakerConfig.maxMessagesPerAgent;
  }
  
  /**
   * Check if there are too many consecutive messages between agents
   */
  private tooManyConsecutiveMessages(fromAgent: string, toAgent: string): boolean {
    const key = this.getCircuitKey(fromAgent, toAgent);
    return (this.consecutiveMessages[key] || 0) >= this.circuitBreakerConfig.maxConsecutiveMessages;
  }
  
  /**
   * Update consecutive message counter
   */
  private updateConsecutiveCounter(fromAgent: string, toAgent: string): void {
    const key = this.getCircuitKey(fromAgent, toAgent);
    this.consecutiveMessages[key] = (this.consecutiveMessages[key] || 0) + 1;
  }
  
  /**
   * Break the circuit between two agents for the cooldown period
   */
  private breakCircuit(fromAgent: string, toAgent: string): void {
    const key = this.getCircuitKey(fromAgent, toAgent);
    this.brokenCircuits[key] = Date.now() + this.circuitBreakerConfig.cooldownPeriodMs;
    logger.warn(`Circuit broken between ${fromAgent} and ${toAgent} for ${this.circuitBreakerConfig.cooldownPeriodMs}ms`);
  }
  
  /**
   * Check if the circuit is currently broken between agents
   */
  private isCircuitBroken(fromAgent: string, toAgent: string): boolean {
    const key = this.getCircuitKey(fromAgent, toAgent);
    const brokenUntil = this.brokenCircuits[key] || 0;
    return Date.now() < brokenUntil;
  }
  
  /**
   * Generate a consistent key for a circuit between two agents
   */
  private getCircuitKey(agent1: string, agent2: string): string {
    // Sort agent IDs to ensure consistency regardless of which is from/to
    const agents = [agent1, agent2].sort();
    return `${agents[0]}-${agents[1]}`;
  }

  /**
   * Maps a generic REQUEST type message to a more specific message type based on context
   * This is needed because agents only register handlers for specific message types
   */
  private async mapRequestToSpecificType(message: IterativeMessage, state: any): Promise<IterativeMessage> {
    // If it's not a REQUEST type, return the original message
    if (message.type !== IterativeMessageType.REQUEST) {
      return message;
    }
    
    logger.debug(`Mapping REQUEST message from ${message.from} to ${message.to}`);
    
    const toAgent = typeof message.to === 'string' ? message.to : (Array.isArray(message.to) ? message.to[0] : '');
    const fromAgent = message.from;
    const phase = state.currentPhase || 'planning';
    const agentState = state.agentStates?.[toAgent as AgentId] || {};
    
    // Check if this is the first message to this agent (no processed requests)
    const isFirstMessageToAgent = !agentState.processedRequests || agentState.processedRequests.length === 0;
    
    // Check if we have artifacts already
    const artifacts = state.artifacts || {};
    const hasArtifacts = Object.keys(artifacts).length > 0;
    
    // Check for specific artifact types
    const hasContentArtifacts = Object.values(artifacts).some((a: any) => a.type === 'content');
    const hasKeywordArtifacts = Object.values(artifacts).some((a: any) => a.type === 'keywords');
    const hasMarketResearchArtifacts = Object.values(artifacts).some((a: any) => a.type === 'market_research');
    const hasStrategyArtifacts = Object.values(artifacts).some((a: any) => a.type === 'content_strategy');
    
    // Check message content for specific indicators
    const messageContent = message.content || {};
    const contentType = messageContent.type;
    const artifactType = messageContent.artifactType;
    const hasArtifactReference = Boolean(
      messageContent.artifactType || 
      messageContent.artifactId || 
      (messageContent.artifacts && messageContent.artifacts.length > 0) || 
      (messageContent.artifactIds && messageContent.artifactIds.length > 0)
    );
    
    // Check for discussions
    const isDiscussionContribution = contentType === 'discussion_contribution' || 
                                   messageContent.discussionId;
    const isDiscussionStart = contentType === 'discussion_start';
    const isInDiscussionPhase = phase === 'discussion';
    
    // Check for consultation
    const isConsultation = contentType === 'consultation' || 
                          phase === 'research' || 
                          (fromAgent !== 'system' && fromAgent !== toAgent && !isDiscussionContribution);
    
    // Check if we're in feedback phase
    const isFeedbackPhase = phase === 'review' || phase === 'refinement';
    const isFeedbackMessage = contentType === 'feedback' || messageContent.feedback;
    
    // Agent-specific context
    // For SEO Keyword Agent
    const isSeoKeywordAgent = toAgent === 'seo-keyword';
    const requestingKeywords = artifactType === 'keywords' || messageContent.keywordType;
    
    // For Market Research Agent
    const isMarketResearchAgent = toAgent === 'market-research';
    const requestingMarketResearch = artifactType === 'market_research' || messageContent.researchType;
    
    // For Content Strategy Agent
    const isContentStrategyAgent = toAgent === 'content-strategy';
    const requestingContentStrategy = artifactType === 'content_strategy' || messageContent.strategyType;
    
    // For Content Generation Agent
    const isContentGenAgent = toAgent === 'content-generation';
    const requestingContent = artifactType === 'content' || messageContent.contentType;
    const isIterationRequest = contentType === 'iteration_request' || messageContent.iteration;
    
    // For SEO Optimization Agent
    const isSeoOptAgent = toAgent === 'seo-optimization';
    const requestingSeoAnalysis = artifactType === 'seo_analysis' || messageContent.seoType;
    
    // Log the analysis factors
    logger.debug(`Message analysis: 
      Agent: ${toAgent} 
      Phase: ${phase} 
      isFirstMessage: ${isFirstMessageToAgent} 
      hasArtifacts: ${hasArtifacts} 
      hasArtifactReference: ${hasArtifactReference} 
      isConsultation: ${isConsultation} 
      isFeedback: ${isFeedbackMessage || isFeedbackPhase}
      isDiscussion: ${isDiscussionContribution || isDiscussionStart || isInDiscussionPhase}`
    );
    
    // Map to specific type based on context
    let specificType: IterativeMessageType;
    
    if (isFirstMessageToAgent) {
      // First message should be mapped to INITIAL_REQUEST
      specificType = IterativeMessageType.INITIAL_REQUEST;
      logger.debug(`Mapped to INITIAL_REQUEST because this is the first message to ${toAgent}`);
    } else if (isDiscussionStart || (isInDiscussionPhase && !isDiscussionContribution)) {
      // If starting a discussion
      specificType = IterativeMessageType.DISCUSSION_START;
      logger.debug(`Mapped to DISCUSSION_START because ${isDiscussionStart ? 'content type indicates discussion start' : 'we are in discussion phase'}`);
    } else if (isDiscussionContribution) {
      // If contributing to a discussion
      specificType = IterativeMessageType.DISCUSSION_CONTRIBUTION;
      logger.debug(`Mapped to DISCUSSION_CONTRIBUTION because content indicates discussion contribution`);
    } else if (isFeedbackMessage || isFeedbackPhase) {
      // If explicitly providing feedback or in feedback phase
      specificType = IterativeMessageType.FEEDBACK;
      logger.debug(`Mapped to FEEDBACK because ${isFeedbackMessage ? 'content type is feedback' : 'we are in feedback/review phase'}`);
    } else if (isConsultation) {
      // If it's a consultation between agents
      specificType = IterativeMessageType.CONSULTATION_REQUEST;
      logger.debug(`Mapped to CONSULTATION_REQUEST because message is from ${fromAgent} to ${toAgent} in ${phase} phase`);
    } else if (isIterationRequest && isContentGenAgent) {
      // If explicitly requesting content iteration
      specificType = IterativeMessageType.ITERATION_REQUEST;
      logger.debug(`Mapped to ITERATION_REQUEST for content generation agent`);
    } else if (hasArtifactReference || requestingKeywords || requestingMarketResearch || 
              requestingContentStrategy || requestingContent || requestingSeoAnalysis) {
      // If requesting any kind of artifact
      specificType = IterativeMessageType.ARTIFACT_REQUEST;
      logger.debug(`Mapped to ARTIFACT_REQUEST because message references or requests artifacts`);
    } else {
      // Determine most appropriate default based on agent and phase
      if (isContentGenAgent && hasStrategyArtifacts) {
        specificType = IterativeMessageType.ITERATION_REQUEST;
        logger.debug(`Default mapped to ITERATION_REQUEST for content generation agent with strategy artifacts available`);
      } else if (isSeoOptAgent && hasContentArtifacts) {
        specificType = IterativeMessageType.FEEDBACK;
        logger.debug(`Default mapped to FEEDBACK for SEO optimization agent with content artifacts available`);
      } else {
        // Default to ARTIFACT_REQUEST as fallback for subsequent requests
        specificType = IterativeMessageType.ARTIFACT_REQUEST;
        logger.debug(`Default mapped to ARTIFACT_REQUEST as fallback`);
      }
    }
    
    logger.debug(`Mapped REQUEST to ${specificType} based on context`);
    
    // Create a new message with the specific type
    return {
      ...message,
      type: specificType
    };
  }
}

// Export a singleton instance
export const enhancedMessageBus = new EnhancedMessageBus();
