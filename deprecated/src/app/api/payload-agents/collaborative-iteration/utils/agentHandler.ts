import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeArtifact,
  Consultation,
  AgentState,
  IterativeCollaborationState
} from '../types';
import { AgentId, StandardizedHandlerResult } from './agentTypes';
import { AgentStateManager } from './agentStateManager';
import { AgentMessaging } from './agentMessaging';
import { stateStore } from './stateStore';

/**
 * Message handler type
 */
export type MessageHandler = (
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
) => Promise<StandardizedHandlerResult>;

/**
 * Agent Handler
 * Processes incoming messages and manages agent-specific logic
 */
export class AgentHandler {
  private agentId: AgentId;
  private stateManager: AgentStateManager;
  private messaging: AgentMessaging;
  private handlers: Record<string, MessageHandler>;

  constructor(agentId: AgentId) {
    this.agentId = agentId;
    this.stateManager = new AgentStateManager(agentId);
    this.messaging = new AgentMessaging(agentId, this.stateManager);
    this.handlers = {};
  }

  /**
   * Register a message handler for a specific message type
   */
  registerHandler(messageType: string, handler: MessageHandler): void {
    this.handlers[messageType] = handler;
  }

  /**
   * Process an incoming message
   */
  async processMessage(
    sessionId: string,
    message: IterativeMessage
  ): Promise<StandardizedHandlerResult | null> {
    // Get the current state
    const state = await this.stateManager.getSessionState(sessionId);

    if (!state) {
      console.error(`No state found for session ${sessionId}`);
      return null;
    }

    // Validate message type - ensure it's not undefined
    if (!message.type) {
      console.error(`Message type is undefined for message ${message.id} from ${message.from} to ${message.to}`);

      // Set a default type for messages with undefined type
      message.type = 'REQUEST' as any;
      console.warn(`Setting default message type 'REQUEST' for message ${message.id}`);
    }

    // Check if this message is intended for this agent
    if (typeof message.to === 'string' && message.to !== this.agentId && message.to !== 'all') {
      return null;
    }

    if (Array.isArray(message.to) && !message.to.includes(this.agentId) && !message.to.includes('all')) {
      return null;
    }

    // Check if this message has already been processed
    const isProcessed = await this.stateManager.hasProcessedMessage(sessionId, message.id);

    if (isProcessed) {
      return null;
    }

    // Get the handler for this message type
    const handler = this.handlers[message.type];

    // Try to use a default handler if no specific handler is found
    if (!handler) {
      // No handler for this message type
      console.warn(`No handler registered for message type ${message.type} in agent ${this.agentId}`);

      // Try to use the REQUEST handler as a fallback if available
      const defaultHandler = this.handlers['REQUEST'];
      if (defaultHandler) {
        console.log(`Using default REQUEST handler for message type ${message.type} in agent ${this.agentId}`);

        try {
          // Process the message with the default handler
          const result = await defaultHandler(message, state, this.stateManager, this.messaging);

          // Track this message as processed
          await this.stateManager.trackProcessedMessage(sessionId, message.id);

          // Apply the result
          await this.stateManager.applyResult(sessionId, result);

          return result;
        } catch (error) {
          console.error(`Error processing message ${message.id} with default handler:`, error);
        }
      }

      // Create a default acknowledgment if no default handler or it failed
      const response = await this.messaging.sendAcknowledgment(
        sessionId,
        message.from as AgentId,
        message.id,
        `No handler available for message type ${message.type}`,
        message.conversationId
      );

      // Track this message as processed
      await this.stateManager.trackProcessedMessage(sessionId, message.id);

      return { response };
    }

    try {
      // Process the message with the handler
      const result = await handler(message, state, this.stateManager, this.messaging);

      // Track this message as processed
      await this.stateManager.trackProcessedMessage(sessionId, message.id);

      // Apply the result
      await this.stateManager.applyResult(sessionId, result);

      return result;
    } catch (error) {
      console.error(`Error processing message ${message.id} by agent ${this.agentId}:`, error);

      // Create an error response
      const response = await this.messaging.sendErrorMessage(
        sessionId,
        message.from as AgentId,
        `Error processing message: ${error.message}`,
        message.id,
        message.conversationId
      );

      // Track this message as processed
      await this.stateManager.trackProcessedMessage(sessionId, message.id);

      return { response };
    }
  }

  /**
   * Create a new artifact
   */
  createArtifact(
    name: string,
    type: string,
    content: any
  ): IterativeArtifact {
    return {
      id: uuidv4(),
      name,
      type,
      createdBy: this.agentId,
      createdAt: new Date().toISOString(),
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: this.agentId,
          content,
          feedback: [],
          incorporatedConsultations: []
        }
      ],
      status: 'draft',
      qualityScore: 0
    };
  }

  /**
   * Create a new consultation
   */
  createConsultation(
    requester: AgentId,
    question: string,
    response: any,
    context?: Record<string, any>
  ): Consultation {
    return {
      id: uuidv4(),
      requester,
      provider: this.agentId,
      timestamp: new Date().toISOString(),
      question,
      context: context || {},
      response
    };
  }

  /**
   * Get the state manager
   */
  getStateManager(): AgentStateManager {
    return this.stateManager;
  }

  /**
   * Get the messaging utility
   */
  getMessaging(): AgentMessaging {
    return this.messaging;
  }
}

/**
 * Create an agent handler for a specific agent
 */
export function createAgentHandler(agentId: AgentId): AgentHandler {
  return new AgentHandler(agentId);
}
