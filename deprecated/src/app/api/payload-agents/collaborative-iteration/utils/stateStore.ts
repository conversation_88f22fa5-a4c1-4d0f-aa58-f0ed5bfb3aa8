import { IterativeCollaborationState } from '../types';
import logger from './logger';
import { redisStateStore } from './redisStateStore';

// Define a global variable to hold the state store instance
declare global {
  // Using a specific name to avoid conflicts
  var __globalCollaborationStateStore: StateStore | undefined;
}

/**
 * This file has been updated to use the Redis state store implementation
 * All operations are forwarded to the Redis state store
 * This ensures consistency across the application regardless of import source
 */

/**
 * State store for collaborative agent sessions
 * Updated to use Redis for persistent storage across API routes and serverless functions
 * This class forwards all operations to the Redis state store
 */
export class StateStore {
  constructor() {
    console.log('StateStore instance created');
  }

  /**
   * Get a collaboration state by ID
   * Forwards to Redis state store
   */
  async getState(sessionId: string): Promise<IterativeCollaborationState | null> {
    // Forward to Redis state store
    return redisStateStore.getState(sessionId);
  }

  /**
   * Get a collaboration state by ID synchronously
   * This is a convenience method for cases where we need to check state without awaiting
   * Note: This will return null if the state is not in memory
   */
  getStateSync(sessionId: string): IterativeCollaborationState | null {
    // Forward to Redis state store if it has a sync method
    if ('getStateSync' in redisStateStore) {
      return (redisStateStore as any).getStateSync(sessionId);
    }

    // Fallback to null if no sync method is available
    logger.warn(`getStateSync called but Redis state store doesn't support it for session ${sessionId}`);
    return null;
  }

  /**
   * Save a collaboration state
   * Forwards to Redis state store
   */
  async setState(sessionId: string, state: IterativeCollaborationState): Promise<void> {
    // Forward to Redis state store
    return redisStateStore.setState(sessionId, state);
  }

  /**
   * List all session IDs
   * Forwards to Redis state store
   */
  async listSessions(): Promise<string[]> {
    return redisStateStore.listSessions();
  }

  /**
   * Check if a session exists
   * Forwards to Redis state store
   */
  async sessionExists(sessionId: string): Promise<boolean> {
    return redisStateStore.sessionExists(sessionId);
  }

  /**
   * Delete a session and its state
   * Forwards to Redis state store
   */
  async deleteSession(sessionId: string): Promise<boolean> {
    return redisStateStore.deleteSession(sessionId);
  }

  /**
   * Store a message in the collaboration state
   * Implements message storage using the Redis state store
   */
  async storeMessage(sessionId: string, message: any): Promise<boolean> {
    if (!sessionId) {
      logger.error('Attempted to store message with null or undefined sessionId');
      return false;
    }

    try {
      // Get the current state
      const state = await this.getState(sessionId);

      if (!state) {
        logger.error(`Cannot store message - session ${sessionId} not found`, { messageId: message.id });
        return false;
      }

      // Ensure messages array exists
      if (!state.messages) {
        state.messages = [];
      }

      // Add the message
      state.messages.push(message);

      // Save the updated state
      await this.setState(sessionId, state);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error storing message for session ${sessionId}`, {
        sessionId,
        messageId: message.id,
        error: err.message,
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Get state (alias for getState for API compatibility)
   * Throws an error if the session is not found
   */
  async get(sessionId: string): Promise<IterativeCollaborationState> {
    const state = await this.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    return state;
  }

  /**
   * Set state (alias for setState for API compatibility)
   */
  async set(sessionId: string, state: IterativeCollaborationState): Promise<void> {
    await this.setState(sessionId, state);
  }

  /**
   * Update a collaboration state using a callback function
   * This provides atomic updates
   */
  async updateState(
    sessionId: string,
    updateFn: (state: IterativeCollaborationState) => IterativeCollaborationState
  ): Promise<IterativeCollaborationState | null> {
    // Forward to Redis state store
    return redisStateStore.updateState(sessionId, updateFn);
  }
}

// Ensure we have a true global singleton across all imports
// even in Next.js which can have multiple instances of a module
let stateStore: StateStore;

if (global.__globalCollaborationStateStore) {
  console.log('Using existing global StateStore instance');
  stateStore = global.__globalCollaborationStateStore;
} else {
  console.log('Created new global StateStore instance');
  stateStore = new StateStore();
  global.__globalCollaborationStateStore = stateStore;
}

// Log initialization (using async IIFE to handle promise)
(async () => {
  const sessions = await stateStore.listSessions();
  console.log(`State store initialized with sessions: ${sessions.join(', ') || 'none'}`);
})();

// Export the singleton instance
export { stateStore };
export default stateStore;

// For debugging purposes, print the current state of the store
// Using an IIFE to handle the async operation
(async () => {
  const sessions = await stateStore.listSessions();
  console.log(`State store initialized with sessions: ${sessions.join(', ') || 'none'}`);
})();
