import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeArtifact,
  Consultation,
  Reasoning
} from '../types';
import { AgentId } from './agentTypes';
import { AgentStateManager } from './agentStateManager';

/**
 * Agent Messaging Utility
 * Provides standardized methods for creating and sending messages between agents
 */
export class AgentMessaging {
  private agentId: AgentId;
  private stateManager: AgentStateManager;

  constructor(agentId: AgentId, stateManager: AgentStateManager) {
    this.agentId = agentId;
    this.stateManager = stateManager;
  }

  /**
   * Create a standard message structure with common properties
   */
  createMessage(
    toAgent: AgentId | AgentId[],
    type: string,
    content: Record<string, any>,
    conversationId: string,
    reasoning?: Reasoning
  ): IterativeMessage {
    // Validate message type to ensure it's not undefined or invalid
    if (!type) {
      console.warn(`Creating message with undefined type, defaulting to REQUEST`);
      type = 'REQUEST';
    }

    // Ensure the type is a valid IterativeMessageType
    // If not, default to REQUEST as a fallback
    const validTypes = [
      'REQUEST', 'RESPONSE', 'UPDATE', 'QUESTION', 'ANSWER', 'SUGGESTION', 'CONFIRMATION',
      'INITIAL_REQUEST', 'FINAL_OUTPUT', 'SYSTEM_MESSAGE', 'ERROR',
      'FEEDBACK', 'FEEDBACK_REQUEST',
      'CONSULTATION_REQUEST', 'CONSULTATION_RESPONSE',
      'ITERATION_REQUEST', 'ITERATION_RESPONSE',
      'ARTIFACT_DELIVERY', 'ARTIFACT_REQUEST', 'ARTIFACT_BROADCAST',
      'DISCUSSION_START', 'DISCUSSION_PERSPECTIVE_REQUEST', 'DISCUSSION_SYNTHESIS_REQUEST',
      'DISCUSSION_SYNTHESIS', 'DISCUSSION_CONTRIBUTION',
      'ACKNOWLEDGMENT'
    ];

    if (!validTypes.includes(type)) {
      console.warn(`Message type "${type}" is not a valid IterativeMessageType, defaulting to REQUEST`);
      type = 'REQUEST';
    }

    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: this.agentId,
      to: toAgent,
      type: type as any,
      content,
      conversationId,
      reasoning
    };
  }

  /**
   * Send a message to the collaboration state
   */
  async sendMessage(
    sessionId: string,
    message: IterativeMessage
  ): Promise<IterativeMessage> {
    await this.stateManager.addMessage(sessionId, message);
    return message;
  }

  /**
   * Create and send an acknowledgment message
   */
  async sendAcknowledgment(
    sessionId: string,
    toAgent: AgentId,
    originalMessageId: string,
    message: string,
    conversationId: string,
    reasoning?: Reasoning
  ): Promise<IterativeMessage> {
    const ackMessage = this.createMessage(
      toAgent,
      'ACKNOWLEDGMENT',
      {
        message,
        originalMessageId
      },
      conversationId,
      reasoning
    );

    return this.sendMessage(sessionId, ackMessage);
  }

  /**
   * Create and send an artifact request message
   */
  async sendArtifactRequest(
    sessionId: string,
    toAgent: AgentId,
    artifactType: string,
    description: string,
    conversationId: string,
    reasoning?: Reasoning
  ): Promise<IterativeMessage> {
    const requestMessage = this.createMessage(
      toAgent,
      'ARTIFACT_REQUEST',
      {
        artifactType,
        description
      },
      conversationId,
      reasoning
    );

    return this.sendMessage(sessionId, requestMessage);
  }

  /**
   * Create and send an artifact delivery message
   */
  async sendArtifactDelivery(
    sessionId: string,
    toAgent: AgentId,
    artifact: IterativeArtifact,
    originalMessageId: string | undefined,
    conversationId: string,
    reasoning?: Reasoning
  ): Promise<IterativeMessage> {
    // First track the artifact in the state
    await this.stateManager.trackNewArtifact(sessionId, artifact);

    // Then send the delivery message
    const deliveryMessage = this.createMessage(
      toAgent,
      'ARTIFACT_DELIVERY',
      {
        artifactId: artifact.id,
        artifactType: artifact.type,
        originalMessageId,
        // Include the full artifact in the message for better display in agent discussions
        artifact: artifact
      },
      conversationId,
      reasoning
    );

    return this.sendMessage(sessionId, deliveryMessage);
  }

  /**
   * Create and send a feedback message
   */
  async sendFeedback(
    sessionId: string,
    toAgent: AgentId,
    feedbackContent: string,
    artifactId: string | undefined,
    originalMessageId: string | undefined,
    conversationId: string,
    reasoning?: Reasoning
  ): Promise<IterativeMessage> {
    const feedbackMessage = this.createMessage(
      toAgent,
      'FEEDBACK',
      {
        feedback: feedbackContent,
        artifactId,
        originalMessageId
      },
      conversationId,
      reasoning
    );

    // Track feedback in the recipient's state
    if (toAgent) {
      await this.stateManager.trackFeedback(
        sessionId,
        this.agentId,
        feedbackMessage.id,
        feedbackContent,
        artifactId
      );
    }

    return this.sendMessage(sessionId, feedbackMessage);
  }

  /**
   * Create and send a consultation request message
   */
  async sendConsultationRequest(
    sessionId: string,
    toAgent: AgentId,
    question: string,
    context: Record<string, any> | undefined,
    conversationId: string,
    reasoning?: Reasoning
  ): Promise<IterativeMessage> {
    const requestMessage = this.createMessage(
      toAgent,
      'CONSULTATION_REQUEST',
      {
        question,
        context: context || {}
      },
      conversationId,
      reasoning
    );

    return this.sendMessage(sessionId, requestMessage);
  }

  /**
   * Create and send a consultation response message
   */
  async sendConsultationResponse(
    sessionId: string,
    toAgent: AgentId,
    consultation: Consultation,
    originalMessageId: string,
    conversationId: string,
    reasoning?: Reasoning
  ): Promise<IterativeMessage> {
    // First track the consultation in the state
    await this.stateManager.trackNewConsultation(sessionId, consultation);

    // Then send the response message
    const responseMessage = this.createMessage(
      toAgent,
      'CONSULTATION_RESPONSE',
      {
        consultationId: consultation.id,
        response: consultation.response,
        originalMessageId
      },
      conversationId,
      reasoning
    );

    return this.sendMessage(sessionId, responseMessage);
  }

  /**
   * Create and send an error message
   */
  async sendErrorMessage(
    sessionId: string,
    toAgent: AgentId,
    errorMessage: string,
    originalMessageId: string | undefined,
    conversationId: string,
    reasoning?: Reasoning
  ): Promise<IterativeMessage> {
    const errorMsgObj = this.createMessage(
      toAgent,
      'ERROR',
      {
        message: errorMessage,
        originalMessageId
      },
      conversationId,
      reasoning
    );

    return this.sendMessage(sessionId, errorMsgObj);
  }

  /**
   * Create and send a chain-of-thought reasoning message
   * This helps agents explain their reasoning process
   */
  async sendReasoningMessage(
    sessionId: string,
    toAgent: AgentId,
    reasoningContent: string,
    steps: string[],
    originalMessageId: string | undefined,
    conversationId: string
  ): Promise<IterativeMessage> {
    const reasoning: Reasoning = {
      process: reasoningContent,
      steps: steps,
      timestamp: new Date().toISOString()
    };

    const reasoningMessage = this.createMessage(
      toAgent,
      'REASONING',
      {
        reasoning,
        originalMessageId
      },
      conversationId,
      reasoning
    );

    return this.sendMessage(sessionId, reasoningMessage);
  }
}

/**
 * Create an agent messaging utility for a specific agent
 */
export function createAgentMessaging(agentId: AgentId, stateManager: AgentStateManager): AgentMessaging {
  return new AgentMessaging(agentId, stateManager);
}
