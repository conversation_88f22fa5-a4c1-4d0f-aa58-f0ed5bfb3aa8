import { v4 as uuidv4 } from 'uuid';
import { 
  IterativeMessage, 
  IterativeCollaborationState,
  IterativeArtifact,
  Consultation,
  EnhancedReasoning,
  IterativeMessageType
} from '../types';
import { AgentId } from './agentTypes';
import { MessageBus } from './messageBus';
import { stateStore } from './stateStore';
import OpenAI from 'openai';

// Initialize MessageBus instance
const messageBus = new MessageBus();

// Initialize OpenAI client
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Collaboration threshold - agents will collaborate when decision confidence is below this threshold
const COLLABORATION_THRESHOLD = 0.7;

// Types for autonomous collaboration
interface CollaborationTypes {
  consultation?: AgentId[];
  feedback?: AgentId[];
  discussion?: {
    topic: string;
    participants: AgentId[];
    leadAgent: AgentId;
  };
}

interface CollaborationOptions {
  consultationQuestion?: string;
  feedbackPrompt?: string;
  discussionTopic?: string;
  collaborationTypes?: CollaborationTypes;
}

interface CollaborationEvaluation {
  shouldCollaborate: boolean;
  collaborationType: 'consultation' | 'feedback' | 'discussion' | null;
  targetAgents: AgentId[];
  reasoning: EnhancedReasoning;
}

interface CollaborationResult {
  collaborationInitiated: boolean;
  collaborationType?: 'consultation' | 'feedback' | 'discussion';
  results?: any[];
  result?: any;
  error?: string;
  reasoning: EnhancedReasoning;
}

// Type for message processor function
type MessageProcessor = (sessionId: string, message: IterativeMessage) => Promise<any>;

/**
 * Enhanced communication manager for agents to interact with each other
 */
export class AgentCommunicationManager {
  private sessionId: string;
  private processorFn: MessageProcessor;
  
  constructor(sessionId: string, processorFn: MessageProcessor) {
    this.sessionId = sessionId;
    this.processorFn = processorFn;
  }
  
  /**
   * Evaluate if an agent needs collaboration for a specific task
   * This will autonomously determine if the agent should consult with other agents
   */
  async evaluateCollaborationNeeds(
    fromAgent: AgentId,
    task: string,
    context: any,
    collaborationTypes: CollaborationTypes = {}
  ): Promise<CollaborationEvaluation> {
    console.log(`Evaluating collaboration needs for ${fromAgent} on task: ${task}`);
    
    // Get the session state
    const state = await stateStore.getState(this.sessionId);
    
    if (!state) {
      console.error(`Session ${this.sessionId} not found`);
      return {
        shouldCollaborate: false,
        collaborationType: null,
        targetAgents: [],
        reasoning: {
          process: 'Error evaluating collaboration needs',
          steps: ['Session state not found'],
          timestamp: new Date().toISOString(),
          thoughts: ['Cannot proceed without session state'],
          considerations: ['System error occurred'],
          decision: 'Unable to evaluate collaboration needs',
          confidence: 0
        }
      };
    }
    
    // Use AI to evaluate collaboration needs
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: `You are an AI collaboration evaluator that helps determine when an AI agent should collaborate with other agents.
            You'll analyze a task, its context, and determine if collaboration is needed and what type would be most effective.
            You must evaluate based on:
            1. Task complexity - Does the task require diverse expertise?
            2. Confidence level - Is the agent likely uncertain about aspects of the task?
            3. Specialization gaps - Is the task outside the agent's primary expertise?
            4. Potential value - Would collaboration significantly improve output quality?
            
            Context information about available agents:
            - market-research: Expertise in gathering and analyzing market trends, competitor analysis, and customer insights.
            - seo-keyword: Expertise in identifying optimal keywords, search intent, and SEO strategies.
            - content-strategy: Expertise in content planning, structure, tone, and audience targeting.
            - content-generation: Expertise in writing high-quality, engaging content based on outlines and requirements.
            - seo-optimization: Expertise in optimizing content for search engines, readability, and technical SEO factors.`
          },
          {
            role: 'user',
            content: `Task for '${fromAgent}' agent: ${task}

Task Context: ${JSON.stringify(context)}

Current state information: 
Content type: ${state.contentType || 'unknown'}
Topic: ${state.topic || 'unknown'}
Target audience: ${state.targetAudience || 'unknown'}
Tone: ${state.tone || 'unknown'}
Keywords: ${state.keywords?.join(', ') || 'none'}

Determine if this agent should collaborate with other agents to complete this task efficiently.
If collaboration is needed, specify which type (consultation, feedback, or discussion) and which agent(s) would be most helpful.

Respond ONLY with a JSON object with the following structure:
{
  "shouldCollaborate": boolean,
  "collaborationType": "consultation"|"feedback"|"discussion"|null,
  "targetAgents": [list of agent IDs],
  "reasoning": {
    "thoughts": [list of thought processes],
    "considerations": [list of key considerations],
    "decision": "final decision statement",
    "confidence": number between 0-1
  }
}`
          }
        ],
        response_format: { type: 'json_object' },
        temperature: 0.7,
      });
      
      // Extract the response content
      const responseContent = response.choices[0].message.content;
      if (!responseContent) {
        throw new Error('Empty response from AI collaboration evaluator');
      }
      
      // Parse the JSON response
      const result = JSON.parse(responseContent);
      
      // Add timestamp to reasoning
      result.reasoning.timestamp = new Date().toISOString();
      
      // Add process and steps to reasoning
      result.reasoning.process = `Evaluated collaboration needs for ${fromAgent} on task: ${task}`;
      result.reasoning.steps = [
        'Analyzed task requirements and complexity',
        'Assessed agent capabilities and specializations',
        'Evaluated potential value of collaboration',
        `Made decision: ${result.shouldCollaborate ? 'Collaboration recommended' : 'No collaboration needed'}`
      ];
      
      // Override target agents if explicitly provided in arguments
      if (result.collaborationType === 'consultation' && collaborationTypes.consultation) {
        result.targetAgents = collaborationTypes.consultation;
      } else if (result.collaborationType === 'feedback' && collaborationTypes.feedback) {
        result.targetAgents = collaborationTypes.feedback;
      } else if (result.collaborationType === 'discussion' && collaborationTypes.discussion) {
        result.targetAgents = collaborationTypes.discussion.participants;
      }
      
      // Log the decision
      console.log(`Collaboration decision for ${fromAgent}: ${result.shouldCollaborate ? 'YES' : 'NO'}, type: ${result.collaborationType}, targets: ${result.targetAgents.join(', ')}`);
      
      return result;
    } catch (error: any) {
      console.error('Error evaluating collaboration needs:', error);
      
      // Return a default response
      return {
        shouldCollaborate: false,
        collaborationType: null,
        targetAgents: [],
        reasoning: {
          process: 'Error occurred during collaboration evaluation',
          steps: [`Error: ${error.message}`],
          timestamp: new Date().toISOString(),
          thoughts: ['System error prevented proper evaluation'],
          considerations: ['Failing safely by proceeding without collaboration'],
          decision: 'Unable to determine collaboration needs due to error',
          confidence: 0
        }
      };
    }
  }
  
  /**
   * Autonomously initiate collaboration based on a task
   * This method will evaluate if collaboration is needed and automatically start it
   */
  async initiateAutonomousCollaboration(
    fromAgent: AgentId,
    task: string,
    taskContext: any,
    collaborationContext: any = {},
    collaborationOptions: CollaborationOptions = {}
  ): Promise<CollaborationResult> {
    // Evaluate if collaboration is needed
    const evaluation = await this.evaluateCollaborationNeeds(
      fromAgent,
      task,
      taskContext,
      collaborationOptions.collaborationTypes
    );
    
    // Record the decision in the state
    await stateStore.updateState(this.sessionId, (state) => {
      if (!state.decisions) {
        state.decisions = [];
      }
      
      state.decisions.push({
        id: uuidv4(),
        agent: fromAgent,
        timestamp: new Date().toISOString(),
        context: `Evaluating collaboration needs for task: ${task}`,
        reasoning: `${evaluation.reasoning.process}\n${evaluation.reasoning.thoughts?.join('\n') || ''}`,
        outcome: evaluation.shouldCollaborate 
          ? `Decided to ${evaluation.collaborationType} with ${evaluation.targetAgents.join(', ')}` 
          : 'Decided to proceed without collaboration',
        originalReasoning: evaluation.reasoning
      });
      
      return state;
    });
    
    // If no collaboration needed, return early
    if (!evaluation.shouldCollaborate) {
      return {
        collaborationInitiated: false,
        reasoning: evaluation.reasoning
      };
    }
    
    // Proceed with the appropriate collaboration type
    switch (evaluation.collaborationType) {
      case 'consultation':
        // Generate a question if not provided
        const consultationQuestion = collaborationOptions.consultationQuestion || 
          `I need your expertise on ${task}. Can you provide insights about ${taskContext.topic || task}?`;
        
        // Request consultation from all target agents
        const consultationResults = await Promise.all(
          evaluation.targetAgents.map(targetAgent => 
            this.requestConsultation(
              fromAgent,
              targetAgent,
              consultationQuestion,
              taskContext,
              evaluation.reasoning
            )
          )
        );
        
        // Filter out null results
        const validConsultationResults = consultationResults.filter(result => result !== null);
        
        return {
          collaborationInitiated: true,
          collaborationType: 'consultation',
          results: validConsultationResults,
          reasoning: evaluation.reasoning
        };
      
      case 'feedback':
        // Generate feedback prompt if not provided
        const feedbackPrompt = collaborationOptions.feedbackPrompt || 
          `I need feedback on my approach to: ${task}`;
        
        // For feedback, we need to ensure there's an artifact ID
        if (!collaborationContext.artifactId) {
          return {
            collaborationInitiated: false,
            error: 'Missing artifact ID for feedback',
            reasoning: evaluation.reasoning
          };
        }
        
        // Request feedback from all target agents
        const feedbackResults = await Promise.all(
          evaluation.targetAgents.map(targetAgent => 
            this.requestFeedback(
              fromAgent,
              targetAgent,
              collaborationContext.artifactId,
              feedbackPrompt,
              evaluation.reasoning
            )
          )
        );
        
        // Filter out null results
        const validFeedbackResults = feedbackResults.filter((result: any) => result !== null);
        
        return {
          collaborationInitiated: true,
          collaborationType: 'feedback',
          results: validFeedbackResults,
          reasoning: evaluation.reasoning
        };
      
      case 'discussion':
        // Generate discussion topic if not provided
        const discussionTopic = collaborationOptions.discussionTopic || 
          `Collaborative discussion about: ${task}`;
        
        // Start the discussion
        const discussionResult = await this.facilitateDiscussion(
          discussionTopic,
          evaluation.targetAgents.concat([fromAgent]),
          evaluation.targetAgents[0] || fromAgent,
          `Let's discuss ${discussionTopic} to collaborate on this task. Each agent should provide their perspective.`,
          evaluation.reasoning
        );
        
        return {
          collaborationInitiated: true,
          collaborationType: 'discussion',
          result: discussionResult,
          reasoning: evaluation.reasoning
        };
        
      default:
        return {
          collaborationInitiated: false,
          error: `Unknown collaboration type: ${evaluation.collaborationType}`,
          reasoning: evaluation.reasoning
        };
    }
  }
  
  /**
   * Request consultation from another agent
   */
  async requestConsultation(
    fromAgent: AgentId,
    toAgent: AgentId,
    question: string,
    context: any = {},
    reasoning?: any
  ): Promise<Consultation | null> {
    console.log(`${fromAgent} requesting consultation from ${toAgent}: ${question}`);
    
    const consultationId = uuidv4();
    const conversationId = uuidv4();
    
    // Create the consultation request message
    const message = messageBus.createDirectMessage(
      fromAgent,
      toAgent,
      IterativeMessageType.CONSULTATION_REQUEST,
      {
        consultationId,
        question,
        context
      },
      conversationId
    );
    
    // Add reasoning if provided
    if (reasoning) {
      message.reasoning = {
        process: reasoning.process || reasoning.thoughts?.join('\n') || 'Requesting consultation',
        steps: reasoning.steps || reasoning.considerations || [],
        timestamp: new Date().toISOString(),
        thoughts: reasoning.thoughts || ['Need specialized input from another agent'],
        considerations: reasoning.considerations || ['Value of expert consultation'],
        decision: reasoning.decision || 'Request consultation from specialized agent',
        confidence: reasoning.confidence || 0.9
      };
    }
    
    // Send the message
    const result = await messageBus.sendMessage(this.sessionId, message, this.processorFn);
    
    if (result && result.consultation) {
      // Store the consultation in the state
      await this.storeConsultation(
        consultationId,
        fromAgent,
        toAgent,
        question,
        result.consultation,
        context
      );
      
      return result.consultation;
    }
    
    return null;
  }

/**
 * Respond to a consultation request
 */
async respondToConsultation(
  fromAgent: AgentId,
  toAgent: AgentId,
  consultationId: string,
  response: any,
  inReplyTo: string,
  reasoning?: any
): Promise<any> {
  console.log(`${fromAgent} responding to consultation request from ${toAgent}`);
  
  const message = messageBus.createDirectMessage(
    fromAgent,
    toAgent,
    IterativeMessageType.CONSULTATION_RESPONSE,
    {
      consultationId,
      consultation: {
        id: consultationId,
        agentId: fromAgent,
        timestamp: new Date().toISOString(),
        question: '', // This would be extracted from the original request
        response
      }
    },
    this.sessionId,
    inReplyTo
  );
  
  // Add reasoning if provided
  if (reasoning) {
    message.reasoning = reasoning;
  }
  
  // Send the message
  return messageBus.sendMessage(this.sessionId, message, this.processorFn);
}
  
  /**
   * Request feedback on an artifact
   */
  async requestFeedback(
    fromAgent: AgentId,
    toAgent: AgentId,
    artifactId: string,
    prompt: string,
    reasoning?: any
  ): Promise<any> {
    console.log(`${fromAgent} requesting feedback from ${toAgent} on artifact ${artifactId}`);
    
    const message = messageBus.createDirectMessage(
      fromAgent,
      toAgent,
      IterativeMessageType.FEEDBACK_REQUEST,
      {
        artifactId,
        prompt
      },
      this.sessionId
    );
    
    // Add reasoning if provided
    if (reasoning) {
      message.reasoning = reasoning;
    }
    
    // Send the message
    return messageBus.sendMessage(this.sessionId, message, this.processorFn);
  }
  
  /**
   * Provide feedback on an artifact
   */
  async provideFeedback(
    fromAgent: AgentId,
    toAgent: AgentId,
    artifactId: string,
    feedback: string,
    inReplyTo: string,
    reasoning?: any
  ): Promise<any> {
    console.log(`${fromAgent} providing feedback to ${toAgent} on artifact ${artifactId}`);
    
    const message = messageBus.createDirectMessage(
      fromAgent,
      toAgent,
      IterativeMessageType.FEEDBACK,
      {
        artifactId,
        feedback
      },
      this.sessionId,
      inReplyTo
    );
    
    // Add reasoning if provided
    if (reasoning) {
      message.reasoning = reasoning;
    }
    
    // Send the message
    return messageBus.sendMessage(this.sessionId, message, this.processorFn);
  }
  
  /**
   * Broadcast an artifact to all agents
   */
  async broadcastArtifact(
    fromAgent: AgentId,
    artifact: IterativeArtifact,
    description: string,
    reasoning?: any
  ): Promise<any> {
    console.log(`${fromAgent} broadcasting artifact ${artifact.id} to all agents`);
    
    const message = messageBus.createBroadcastMessage(
      fromAgent,
      IterativeMessageType.ARTIFACT_BROADCAST,
      {
        artifactId: artifact.id,
        description,
        artifact
      },
      this.sessionId
    );
    
    // Add reasoning if provided
    if (reasoning) {
      message.reasoning = reasoning;
    }
    
    // Send the message
    return messageBus.sendMessage(this.sessionId, message, this.processorFn);
  }
  
  /**
   * Facilitate a multi-turn discussion between multiple agents on a specific topic
   * Implements a Divergence/Convergence pattern where agents first contribute perspectives,
   * then engage in back-and-forth exchanges, and finally converge on a synthesis
   * 
   * @param topic - The topic of discussion
   * @param involvedAgents - Array of agent IDs participating in the discussion
   * @param leadAgent - The agent responsible for leading and synthesizing the discussion
   * @param initialPrompt - Initial prompt to start the discussion
   * @param reasoning - Optional reasoning for why this discussion was initiated
   * @param maxTurns - Maximum number of discussion turns (default: 3)
   * @returns Promise with discussion results including synthesis and artifact ID
   */
  async facilitateDiscussion(
    topic: string,
    involvedAgents: AgentId[],
    leadAgent: AgentId,
    initialPrompt: string,
    reasoning?: any,
    maxTurns: number = 3
  ): Promise<any> {
    console.log(`Facilitating discussion on "${topic}" between ${involvedAgents.join(', ')} led by ${leadAgent}`);
    
    const discussionId = uuidv4();
    
    // Record the discussion in state
    await stateStore.updateState(this.sessionId, (state) => {
      if (!state.discussions) {
        state.discussions = {};
      }
      
      // Create discussion object with correct types from your IterativeCollaborationState
      state.discussions[discussionId] = {
        id: discussionId,
        topic,
        participants: involvedAgents,
        leadAgent,
        startedAt: new Date().toISOString(),
        messages: [],
        status: 'active',
        resolution: '',
        updatedAt: new Date().toISOString()
      };
      
      // Record decision
      if (!state.decisions) {
        state.decisions = [];
      }
      
      state.decisions.push({
        id: uuidv4(),
        agent: leadAgent,
        timestamp: new Date().toISOString(),
        context: `Starting discussion on "${topic}"`,
        reasoning: reasoning?.process || 'Facilitating agent discussion',
        outcome: 'pending',
        originalReasoning: reasoning
      });
      
      return state;
    });
    
    // Create the initial message to start the discussion
    const startMessage = messageBus.createDirectMessage(
      leadAgent,
      involvedAgents.filter(a => a !== leadAgent),
      IterativeMessageType.DISCUSSION_START,
      {
        discussionId,
        topic,
        prompt: initialPrompt
      },
      this.sessionId
    );
    
    // Add reasoning if provided
    if (reasoning) {
      startMessage.reasoning = reasoning;
    }
    
    // Send the start message
    await messageBus.sendMessage(this.sessionId, startMessage, this.processorFn);
    
    // Phase 1: Initial Perspectives Collection
    console.log(`[Discussion] Phase 1: Collecting initial perspectives for discussion '${discussionId}'`);
    
    // Collect perspectives from all participants except the lead agent
    const perspectives: Record<string, string> = {};
    const discussionTimeoutMs = 120000; // 2 minute timeout for the entire discussion
    const startTime = Date.now();
    
    try {
      // Request initial perspectives from all participants except lead
      const perspectiveRequests = involvedAgents
        .filter(agent => agent !== leadAgent)
        .map(async (agent) => {
          const perspectiveMessage = messageBus.createDirectMessage(
            leadAgent,
            agent,
            IterativeMessageType.DISCUSSION_PERSPECTIVE_REQUEST,
            {
              discussionId,
              topic,
              prompt: `${initialPrompt}\n\nPlease provide your initial perspective on this topic.`
            },
            this.sessionId
          );
          
          return messageBus.sendMessage(this.sessionId, perspectiveMessage, this.processorFn);
        });
      
      // Wait for all perspective requests to be sent
      const perspectiveResponses = await Promise.allSettled(perspectiveRequests);
      
      // Log any errors in sending perspective requests
      perspectiveResponses.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`[Discussion] Error requesting perspective from ${involvedAgents[index]}: ${result.reason}`);
        }
      });
      
      // Poll for perspectives until we have them all or timeout
      let allPerspectivesCollected = false;
      const perspectiveCollectionStart = Date.now();
      const perspectiveTimeoutMs = 60000; // 1 minute timeout for perspective collection
      
      while (!allPerspectivesCollected && (Date.now() - perspectiveCollectionStart < perspectiveTimeoutMs)) {
        // Get current state
        const state = await stateStore.getState(this.sessionId);
        if (!state) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }
        
        const discussion = state.discussions?.[discussionId];
        if (!discussion) {
          console.error(`[Discussion] Discussion ${discussionId} not found in state`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }
        
        // Collect perspectives from messages
        discussion.messages
          .filter(m => m.type === 'perspective')
          .forEach(m => {
            if (m.content && !perspectives[m.agentId]) {
              perspectives[m.agentId] = typeof m.content === 'string' ? m.content : JSON.stringify(m.content);
              console.log(`[Discussion] Received perspective from ${m.agentId}`);
            }
          });
        
        // Check if we have all perspectives
        const contributingAgents = Object.keys(perspectives);
        const missingPerspectives = involvedAgents
          .filter(a => a !== leadAgent && !contributingAgents.includes(a));
        
        if (missingPerspectives.length === 0) {
          allPerspectivesCollected = true;
          console.log(`[Discussion] All perspectives collected for discussion '${discussionId}'`);
          break;
        }
        
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      // Phase 2: Multi-turn Discussion
      console.log(`[Discussion] Phase 2: Starting multi-turn discussion for '${discussionId}'`);
      
      // If we have perspectives, start the multi-turn discussion
      if (Object.keys(perspectives).length > 0) {
        // Create a summary of perspectives for all agents to see
        const perspectiveSummary = Object.entries(perspectives)
          .map(([agentId, perspective]) => `${agentId}: ${perspective.substring(0, 200)}${perspective.length > 200 ? '...' : ''}`)
          .join('\n\n');
        
        // Conduct multiple turns of discussion
        for (let turn = 0; turn < maxTurns; turn++) {
          console.log(`[Discussion] Starting discussion turn ${turn + 1}/${maxTurns}`);
          
          // Request responses from each agent based on all perspectives
          const discussionRequests = involvedAgents.map(async (agent) => {
            const discussionMessage = messageBus.createDirectMessage(
              leadAgent,
              agent,
              IterativeMessageType.DISCUSSION_CONTRIBUTION,
              {
                discussionId,
                topic,
                turn: turn + 1,
                maxTurns,
                perspectives: perspectiveSummary,
                prompt: `Based on the perspectives shared, please provide your thoughts for discussion turn ${turn + 1}/${maxTurns}.`
              },
              this.sessionId
            );
            
            return messageBus.sendMessage(this.sessionId, discussionMessage, this.processorFn);
          });
          
          // Wait for all discussion requests to be sent
          await Promise.allSettled(discussionRequests);
          
          // Wait for responses (with timeout)
          const turnTimeoutMs = 30000; // 30 seconds per turn
          const turnStartTime = Date.now();
          const turnResponses: Record<string, string> = {};
          
          // Poll for turn responses
          while (Date.now() - turnStartTime < turnTimeoutMs) {
            const state = await stateStore.getState(this.sessionId);
            if (!state || !state.discussions?.[discussionId]) {
              await new Promise(resolve => setTimeout(resolve, 1000));
              continue;
            }
            
            // Collect turn responses
            state.discussions[discussionId].messages
              .filter(m => m.type === `turn-${turn + 1}`)
              .forEach(m => {
                if (m.content && !turnResponses[m.agentId]) {
                  turnResponses[m.agentId] = typeof m.content === 'string' ? m.content : JSON.stringify(m.content);
                  console.log(`[Discussion] Received turn ${turn + 1} response from ${m.agentId}`);
                }
              });
            
            // Check if we have enough responses to proceed
            if (Object.keys(turnResponses).length >= Math.ceil(involvedAgents.length / 2)) {
              console.log(`[Discussion] Received majority of responses for turn ${turn + 1}, proceeding`);
              break;
            }
            
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
          
          // Update the perspectives with the latest turn responses
          Object.entries(turnResponses).forEach(([agentId, response]) => {
            perspectives[agentId] = response;
          });
        }
      }
      
      // Phase 3: Synthesis by lead agent
      console.log(`[Discussion] Phase 3: Synthesizing discussion '${discussionId}'`);
      
      // Now the lead agent synthesizes the discussion
      const synthesisMessage = messageBus.createDirectMessage(
        leadAgent,
        involvedAgents,
        IterativeMessageType.DISCUSSION_SYNTHESIS_REQUEST,
        {
          discussionId,
          perspectives
        },
        this.sessionId
      );
      
      const synthesisResponse = await messageBus.sendMessage(this.sessionId, synthesisMessage, this.processorFn);
    
    if (synthesisResponse && synthesisResponse.content?.synthesis) {
      // Add the synthesis to the discussion in state
      await stateStore.updateState(this.sessionId, (state) => {
        if (state && state.discussions && state.discussions[discussionId]) {
          state.discussions[discussionId].messages.push({
            id: synthesisResponse.id,
            agentId: leadAgent,
            content: synthesisResponse.content.synthesis,
            type: 'synthesis',
            timestamp: synthesisResponse.timestamp
          });
          
          state.discussions[discussionId].status = 'completed';
          state.discussions[discussionId].resolution = synthesisResponse.content.synthesis;
          state.discussions[discussionId].updatedAt = new Date().toISOString();
        }
        
        // Update the decision record
        if (state && state.decisions) {
          const decisionIndex = state.decisions.findIndex(d => 
            d.context?.includes(`Starting discussion on "${topic}"`) && d.outcome === 'pending');
          
          if (decisionIndex >= 0) {
            state.decisions[decisionIndex].outcome = 'Discussion completed with synthesis';
            state.decisions[decisionIndex].timestamp = new Date().toISOString();
          }
        }
        
        return state;
      });
      
      // Optionally, create a discussion synthesis artifact
      const synthesisArtifact: IterativeArtifact = {
        id: uuidv4(),
        name: `Discussion Synthesis: ${topic}`,
        type: 'discussion-synthesis',
        createdBy: leadAgent,
        createdAt: new Date().toISOString(),
        currentVersion: 1,
        iterations: [
          {
            version: 1,
            timestamp: new Date().toISOString(),
            agent: leadAgent,
            content: {
              topic,
              synthesis: synthesisResponse.content.synthesis,
              perspectives
            },
            feedback: [],
            incorporatedConsultations: []
          }
        ],
        status: 'final',
        qualityScore: 0
      };
      
      // Add the artifact to the state
      await stateStore.updateState(this.sessionId, (state) => {
        if (!state) return state;
        if (!state.artifacts) {
          state.artifacts = {};
        }
        state.artifacts[synthesisArtifact.id] = synthesisArtifact;
        return state;
      });
      
      return {
        discussionId,
        synthesisArtifactId: synthesisArtifact.id,
        synthesis: synthesisResponse.content.synthesis
      };
    }
    
    return {
      discussionId,
      error: 'Failed to get synthesis from lead agent'
    };
    
    } catch (error: any) {
      // Handle errors in the discussion process
      console.error(`[Discussion] Error in discussion '${discussionId}':`, error);
      
      // Update discussion status to error
      await stateStore.updateState(this.sessionId, (state) => {
        if (state && state.discussions && state.discussions[discussionId]) {
          // Set status to completed but mark failure in resolution
          state.discussions[discussionId].status = 'completed';
          state.discussions[discussionId].updatedAt = new Date().toISOString();
        }
        
        // Update decision record
        if (state && state.decisions) {
          const decisionIndex = state.decisions.findIndex(d => 
            d.context?.includes(`Starting discussion on "${topic}"`) && d.outcome === 'pending');
          
          if (decisionIndex >= 0) {
            state.decisions[decisionIndex].outcome = `Discussion failed: ${error.message}`;
            state.decisions[decisionIndex].timestamp = new Date().toISOString();
          }
        }
        
        return state;
      });
      
      return {
        discussionId,
        error: `Discussion failed: ${error.message}`,
        errorDetails: error.stack
      };
    } finally {
      // Log completion of discussion regardless of outcome
      const endTime = Date.now();
      const durationMs = endTime - startTime;
      console.log(`[Discussion] Discussion '${discussionId}' completed in ${durationMs}ms`);
    }
  }
  
  /**
   * Store a consultation in the state
   */
  private async storeConsultation(
    consultationId: string,
    fromAgent: AgentId,
    toAgent: AgentId,
    question: string,
    response: any,
    context: any
  ): Promise<void> {
    await stateStore.updateState(this.sessionId, (state) => {
      if (!state) return state;
      if (!state.consultations) {
        state.consultations = {};
      }
      
      state.consultations[consultationId] = {
        id: consultationId,
        fromAgent: toAgent, // Switched because this is from the perspective of who's giving the consultation
        toAgent: fromAgent,
        artifactId: 'none',
        timestamp: new Date().toISOString(),
        feedback: '',
        suggestions: [],
        incorporated: false,
        requestId: uuidv4(),
        question,
        context: JSON.stringify(context),
        response
      };
      
      return state;
    });
  }
}

/**
 * Factory function to create a new communication manager
 */
export function createAgentCommunicationManager(
  sessionId: string,
  processorFn: MessageProcessor
): AgentCommunicationManager {
  return new AgentCommunicationManager(sessionId, processorFn);
}
