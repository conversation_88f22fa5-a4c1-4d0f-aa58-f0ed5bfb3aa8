import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeCollaborationState,
  IterativeMessageType
} from '../types';
import { stateStore } from './stateStore';
import logger from './logger';

/**
 * Configuration for circuit breaker
 */
export interface CircuitBreakerConfig {
  maxMessagesPerAgent: number;  // Maximum messages an agent can send in a time window
  timeWindowMs: number;         // Time window in milliseconds
  maxConsecutiveMessages: number; // Maximum consecutive messages between same agents
  cooldownPeriodMs: number;     // Cooldown period after circuit is broken
}

/**
 * Default circuit breaker configuration
 */
const DEFAULT_CIRCUIT_BREAKER: CircuitBreakerConfig = {
  maxMessagesPerAgent: 10,
  timeWindowMs: 60000, // 1 minute
  maxConsecutiveMessages: 5,
  cooldownPeriodMs: 30000 // 30 seconds
};

/**
 * Message bus with circuit breaker for handling agent-to-agent communication
 */
export class MessageBus {
  private circuitBreakerConfig: CircuitBreakerConfig;
  private messageCounters: Record<string, { count: number, lastReset: number }> = {};
  private consecutiveMessages: Record<string, number> = {};
  private brokenCircuits: Record<string, number> = {}; // Maps agent pair to time when circuit broke
  private sessionBuses: Record<string, boolean> = {}; // Track session-specific message buses
  private activeSessions: Map<string, {
    messageQueue: IterativeMessage[];
    messageHistory: IterativeMessage[];
    messageHandlers: Record<string, (message: IterativeMessage) => Promise<any>>;
  }> = new Map();

  constructor(config: Partial<CircuitBreakerConfig> = {}) {
    this.circuitBreakerConfig = { ...DEFAULT_CIRCUIT_BREAKER, ...config };
  }

  /**
   * Create a session-specific message bus
   * @param sessionId - The session ID
   * @returns A session-specific message bus object with queueMessage method
   */
  createSessionBus(sessionId: string): {
    queueMessage: (message: IterativeMessage) => Promise<void>;
    getHistory: () => IterativeMessage[];
  } {
    // Add the session to active sessions if it doesn't exist
    if (!this.activeSessions.has(sessionId)) {
      this.activeSessions.set(sessionId, {
        messageQueue: [],
        messageHistory: [],
        messageHandlers: {}
      });
    }

    // Create a session-specific message bus
    return {
      queueMessage: async (message: IterativeMessage) => {
        await this.queueMessage(message, sessionId);
      },
      getHistory: () => {
        const session = this.activeSessions.get(sessionId);
        return session ? session.messageHistory : [];
      }
    };
  }

  /**
   * Queue a message for processing
   * @param message - The message to queue
   * @param sessionId - The session ID
   */
  async queueMessage(message: IterativeMessage, sessionId: string): Promise<void> {
    console.log(`[MESSAGE_BUS] Queueing message ${message.id} for session ${sessionId}`);

    // Validate message type and set default if undefined
    if (!message.type) {
      console.warn('[MESSAGE_BUS] Message type is undefined in queueMessage, setting default type REQUEST');
      message.type = IterativeMessageType.REQUEST;
    }

    // Ensure message type is a valid IterativeMessageType
    const validTypes = Object.values(IterativeMessageType);
    if (!validTypes.includes(message.type as any)) {
      console.warn(`[MESSAGE_BUS] Invalid message type in queueMessage: ${message.type}, setting to REQUEST`);
      message.type = IterativeMessageType.REQUEST;
    }

    // Get the session
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.error(`[MESSAGE_BUS] No session found for ID ${sessionId}`);
      return;
    }

    // Add the message to the queue
    session.messageQueue.push(message);

    // Add the message to the history
    session.messageHistory.push(message);

    // Store the message in the state
    await this.addMessageToState(sessionId, message);

    console.log(`[MESSAGE_BUS] Message ${message.id} queued for session ${sessionId}`);
  }

  /**
   * Send a message from one agent to another
   */
  async sendMessage(
    sessionId: string,
    message: IterativeMessage,
    processorFn: (sessionId: string, message: IterativeMessage) => Promise<any>
  ): Promise<any> {
    console.log('====================================================================');
    console.log(`[MESSAGE_BUS] sendMessage: START - Message ID: ${message.id}`);
    console.log(`[MESSAGE_BUS] Session ID: ${sessionId}`);

    // Validate message type and set default if undefined
    if (!message.type) {
      console.warn('[MESSAGE_BUS] Message type is undefined, setting default type REQUEST');
      message.type = IterativeMessageType.REQUEST;
    }

    console.log(`[MESSAGE_BUS] Message Type: ${message.type}`);
    console.log(`[MESSAGE_BUS] From: ${message.from}, To: ${Array.isArray(message.to) ? message.to.join(', ') : message.to}`);

    // Validate message
    if (!message || !message.from || !message.to) {
      console.error('[MESSAGE_BUS] VALIDATION FAILED: Message is null or missing from/to fields');
      return null;
    }

    // Ensure message type is a valid IterativeMessageType
    const validTypes = Object.values(IterativeMessageType);
    if (!validTypes.includes(message.type as any)) {
      console.warn(`[MESSAGE_BUS] Invalid message type: ${message.type}, setting to REQUEST`);
      message.type = IterativeMessageType.REQUEST;
    }

    // Check if circuit is broken for this agent pair
    const toAgent = Array.isArray(message.to) ? message.to[0] : message.to;
    console.log(`[MESSAGE_BUS] Checking circuit breaker for ${message.from} -> ${toAgent}`);

    if (this.isCircuitBroken(message.from, toAgent)) {
      console.warn(`[MESSAGE_BUS] CIRCUIT BROKEN for message from ${message.from} to ${toAgent}`);
      console.log('[MESSAGE_BUS] Recording circuit breaker event in state store');

      // Update state with circuit breaker event
      await this.recordCircuitBreakerEvent(sessionId, message);

      console.log('[MESSAGE_BUS] sendMessage: END - Circuit broken, message rejected');
      return {
        error: 'Circuit breaker triggered',
        message: 'Too many messages between these agents in a short time period'
      };
    }

    // Update message counters and check rate limiting
    console.log(`[MESSAGE_BUS] Checking rate limiting for sender: ${message.from}`);
    if (this.isRateLimited(message.from)) {
      console.warn(`[MESSAGE_BUS] RATE LIMIT EXCEEDED for agent ${message.from}`);
      console.log('[MESSAGE_BUS] Recording rate limit event in state store');

      // Update state with rate limit event
      await this.recordRateLimitEvent(sessionId, message);

      console.log('[MESSAGE_BUS] sendMessage: END - Rate limit exceeded, message rejected');
      return {
        error: 'Rate limit exceeded',
        message: `Agent ${message.from} has sent too many messages in a short time period`
      };
    }

    // Check for consecutive messages between same agents
    console.log(`[MESSAGE_BUS] Checking consecutive message count between ${message.from} and ${toAgent}`);
    if (this.tooManyConsecutiveMessages(message.from, toAgent)) {
      console.warn(`[MESSAGE_BUS] TOO MANY CONSECUTIVE MESSAGES between ${message.from} and ${toAgent}`);
      console.log('[MESSAGE_BUS] Recording consecutive messages event in state store');

      // Update state with consecutive messages event
      await this.recordConsecutiveMessagesEvent(sessionId, message);

      console.log('[MESSAGE_BUS] sendMessage: END - Too many consecutive messages, message rejected');
      return {
        error: 'Too many consecutive messages',
        message: `Agents ${message.from} and ${toAgent} have exchanged too many consecutive messages`
      };
    }

    // Add message to state for tracking
    console.log(`[MESSAGE_BUS] Adding message ${message.id} to state store`);
    await this.addMessageToState(sessionId, message);

    // Process message
    console.log(`[MESSAGE_BUS] Processing message from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}`);
    if (message.content && typeof message.content === 'object') {
      if ('artifact' in message.content && message.content.artifact) {
        const artifact = message.content.artifact;
        console.log(`[MESSAGE_BUS] Message contains artifact - ID: ${artifact.id}, Type: ${artifact.type}`);
      } else if ('artifactId' in message.content) {
        console.log(`[MESSAGE_BUS] Message references artifact ID: ${message.content.artifactId}`);
      }
    }

    try {
      console.log(`[MESSAGE_BUS] Calling processor function with sessionId: ${sessionId}`);
      console.log(`[MESSAGE_BUS] Message details:`, {
        id: message.id,
        type: message.type,
        from: message.from,
        to: message.to,
        hasContent: !!message.content,
        contentKeys: message.content ? Object.keys(message.content) : []
      });

      const result = await processorFn(sessionId, message);
      console.log(`[MESSAGE_BUS] Processor function completed successfully`);
      console.log(`[MESSAGE_BUS] Result:`, {
        success: result?.success,
        hasResponse: !!result?.response,
        responseType: result?.response?.type,
        error: result?.error
      });

      // Update consecutive message counter
      this.updateConsecutiveCounter(message.from, toAgent);
      console.log(`[MESSAGE_BUS] Updated consecutive message counter for ${message.from} -> ${toAgent}`);
      console.log(`[MESSAGE_BUS] sendMessage: END - Message processed successfully`);

      return result;
    } catch (error) {
      console.error(`[MESSAGE_BUS] ERROR in processor function: ${error instanceof Error ? error.message : String(error)}`);
      console.log(`[MESSAGE_BUS] Error details:`, {
        messageId: message.id,
        messageType: message.type,
        from: message.from,
        to: message.to,
        errorName: error instanceof Error ? error.name : 'Unknown',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      console.log('[MESSAGE_BUS] sendMessage: END - Error occurred during processing');
      throw error;
    }
    // Note: No need for a return here as it's already handled in the try block
  }

  /**
   * Add message to state for tracking
   */
  private async addMessageToState(sessionId: string, message: IterativeMessage): Promise<void> {
    await stateStore.updateState(sessionId, (state: IterativeCollaborationState) => {
      if (!state.messages) {
        state.messages = [];
      }

      state.messages.push(message);
      return state;
    });
  }

  /**
   * Check if an agent is sending too many messages in the time window
   */
  private isRateLimited(agentId: string): boolean {
    const now = Date.now();
    const counter = this.messageCounters[agentId] || { count: 0, lastReset: now };

    // Reset counter if time window has passed
    if (now - counter.lastReset >= this.circuitBreakerConfig.timeWindowMs) {
      counter.count = 0;
      counter.lastReset = now;
    }

    // Increment counter
    counter.count++;
    this.messageCounters[agentId] = counter;

    // Check if rate limit exceeded
    return counter.count > this.circuitBreakerConfig.maxMessagesPerAgent;
  }

  /**
   * Check if there are too many consecutive messages between the same agents
   */
  private tooManyConsecutiveMessages(fromAgent: string, toAgent: string): boolean {
    const key = `${fromAgent}-${toAgent}`;
    const consecutiveCount = this.consecutiveMessages[key] || 0;
    return consecutiveCount >= this.circuitBreakerConfig.maxConsecutiveMessages;
  }

  /**
   * Update consecutive message counter between agents
   */
  private updateConsecutiveCounter(fromAgent: string, toAgent: string): void {
    const key = `${fromAgent}-${toAgent}`;
    const reverseKey = `${toAgent}-${fromAgent}`;

    // Reset counters for other agent pairs
    Object.keys(this.consecutiveMessages).forEach(k => {
      if (k !== key && k !== reverseKey) {
        this.consecutiveMessages[k] = 0;
      }
    });

    // Increment counter for this pair
    this.consecutiveMessages[key] = (this.consecutiveMessages[key] || 0) + 1;

    // If counter exceeds threshold, break the circuit
    if (this.consecutiveMessages[key] >= this.circuitBreakerConfig.maxConsecutiveMessages) {
      this.breakCircuit(fromAgent, toAgent);
    }
  }

  /**
   * Break the circuit between two agents for the cooldown period
   */
  private breakCircuit(fromAgent: string, toAgent: string): void {
    const key = this.getCircuitKey(fromAgent, toAgent);
    this.brokenCircuits[key] = Date.now() + this.circuitBreakerConfig.cooldownPeriodMs;
    console.warn(`Circuit broken between ${fromAgent} and ${toAgent} for ${this.circuitBreakerConfig.cooldownPeriodMs}ms`);
  }

  /**
   * Check if the circuit is currently broken between agents
   */
  private isCircuitBroken(fromAgent: string, toAgent: string): boolean {
    const key = this.getCircuitKey(fromAgent, toAgent);
    const brokenUntil = this.brokenCircuits[key] || 0;
    return Date.now() < brokenUntil;
  }

  /**
   * Generate a consistent key for a circuit between two agents
   */
  private getCircuitKey(agent1: string, agent2: string): string {
    // Sort agent IDs to ensure consistency regardless of which is from/to
    const agents = [agent1, agent2].sort();
    return `${agents[0]}-${agents[1]}`;
  }

  /**
   * Record a circuit breaker event in the state
   */
  private async recordCircuitBreakerEvent(sessionId: string, message: IterativeMessage): Promise<void> {
    await stateStore.updateState(sessionId, (state: IterativeCollaborationState) => {
      if (!state.events) {
        state.events = [];
      }

      state.events.push({
        type: 'CIRCUIT_BREAKER',
        timestamp: new Date().toISOString(),
        data: {
          from: message.from,
          to: message.to,
          messageType: message.type,
          reason: 'Too many consecutive messages between agents'
        }
      });

      return state;
    });
  }

  /**
   * Record a rate limit event in the state
   */
  private async recordRateLimitEvent(sessionId: string, message: IterativeMessage): Promise<void> {
    await stateStore.updateState(sessionId, (state: IterativeCollaborationState) => {
      if (!state.events) {
        state.events = [];
      }

      state.events.push({
        type: 'RATE_LIMIT',
        timestamp: new Date().toISOString(),
        data: {
          agent: message.from,
          messageType: message.type,
          reason: 'Too many messages in time window'
        }
      });

      return state;
    });
  }

  /**
   * Record a consecutive messages event in the state
   */
  private async recordConsecutiveMessagesEvent(sessionId: string, message: IterativeMessage): Promise<void> {
    await stateStore.updateState(sessionId, (state: IterativeCollaborationState) => {
      if (!state.events) {
        state.events = [];
      }

      state.events.push({
        type: 'CONSECUTIVE_MESSAGES',
        timestamp: new Date().toISOString(),
        data: {
          from: message.from,
          to: message.to,
          messageType: message.type,
          reason: 'Too many consecutive messages'
        }
      });

      return state;
    });
  }

  /**
   * Create a broadcast message to all agents
   */
  createBroadcastMessage(
    from: string,
    type: IterativeMessageType,
    content: any,
    conversationId: string
  ): IterativeMessage {
    // Validate message type
    if (!type) {
      console.warn('Creating broadcast message with undefined type, defaulting to REQUEST');
      type = IterativeMessageType.REQUEST;
    }

    // Ensure type is a valid IterativeMessageType
    const validTypes = Object.values(IterativeMessageType);
    if (!validTypes.includes(type)) {
      console.warn(`Invalid message type: ${type}, defaulting to REQUEST`);
      type = IterativeMessageType.REQUEST;
    }

    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from,
      to: 'all', // Special recipient for broadcast
      type,
      content,
      conversationId
    };
  }

  /**
   * Create a direct message to a specific agent
   */
  createDirectMessage(
    from: string,
    to: string | string[],
    type: IterativeMessageType,
    content: any,
    conversationId: string,
    inReplyTo?: string
  ): IterativeMessage {
    // Validate message type
    if (!type) {
      console.warn('Creating direct message with undefined type, defaulting to REQUEST');
      type = IterativeMessageType.REQUEST;
    }

    // Ensure type is a valid IterativeMessageType
    const validTypes = Object.values(IterativeMessageType);
    if (!validTypes.includes(type)) {
      console.warn(`Invalid message type: ${type}, defaulting to REQUEST`);
      type = IterativeMessageType.REQUEST;
    }

    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from,
      to,
      type,
      content,
      conversationId,
      inReplyTo
    };
  }

  /**
    }
  });

  // Increment counter for this pair
  this.consecutiveMessages[key] = (this.consecutiveMessages[key] || 0) + 1;

  // If counter exceeds threshold, break the circuit
  if (this.consecutiveMessages[key] >= this.circuitBreakerConfig.maxConsecutiveMessages) {
    this.breakCircuit(fromAgent, toAgent);
  }
}

/**
 * Break the circuit between two agents for the cooldown period
 */
private breakCircuit(fromAgent: string, toAgent: string): void {
  const key = this.getCircuitKey(fromAgent, toAgent);
  this.brokenCircuits[key] = Date.now() + this.circuitBreakerConfig.cooldownPeriodMs;
  console.warn(`Circuit broken between ${fromAgent} and ${toAgent} for ${this.circuitBreakerConfig.cooldownPeriodMs}ms`);
}

/**
 * Check if the circuit is currently broken between agents
 */
private isCircuitBroken(fromAgent: string, toAgent: string): boolean {
  const key = this.getCircuitKey(fromAgent, toAgent);
  const brokenUntil = this.brokenCircuits[key] || 0;
  return Date.now() < brokenUntil;
}

/**
 * Generate a consistent key for a circuit between two agents
 */
private getCircuitKey(agent1: string, agent2: string): string {
  // Sort agent IDs to ensure consistency regardless of which is from/to
  const agents = [agent1, agent2].sort();
  return `${agents[0]}-${agents[1]}`;
}

/**
 * Record a circuit breaker event in the state
 */
private async recordCircuitBreakerEvent(sessionId: string, message: IterativeMessage): Promise<void> {
  await stateStore.updateState(sessionId, (state: IterativeCollaborationState) => {
    if (!state.events) {
      state.events = [];
    }

    state.events.push({
      type: 'CIRCUIT_BREAKER',
      timestamp: new Date().toISOString(),
      data: {
        from: message.from,
        to: message.to,
        messageType: message.type,
        reason: 'Too many consecutive messages between agents'
      }
    });

    return state;
  });
}

/**
 * Record a rate limit event in the state
 */
private async recordRateLimitEvent(sessionId: string, message: IterativeMessage): Promise<void> {
  await stateStore.updateState(sessionId, (state: IterativeCollaborationState) => {
    if (!state.events) {
      state.events = [];
    }

    state.events.push({
      type: 'RATE_LIMIT',
      timestamp: new Date().toISOString(),
      data: {
        agent: message.from,
        messageType: message.type,
        reason: 'Too many messages in time window'
      }
    });

    return state;
  });
}

/**
 * Record a consecutive messages event in the state
 */
private async recordConsecutiveMessagesEvent(sessionId: string, message: IterativeMessage): Promise<void> {
  await stateStore.updateState(sessionId, (state: IterativeCollaborationState) => {
    if (!state.events) {
      state.events = [];
    }

    state.events.push({
      type: 'CONSECUTIVE_MESSAGES',
      timestamp: new Date().toISOString(),
      data: {
        from: message.from,
        to: message.to,
        messageType: message.type,
        reason: 'Too many consecutive messages'
      }
    });

    return state;
  });
}

/**
 * Create a broadcast message to all agents
 */
createBroadcastMessage(
  from: string,
  type: IterativeMessageType,
  content: any,
  conversationId: string
): IterativeMessage {
  return {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from,
    to: 'all', // Special recipient for broadcast
    type,
    content,
    conversationId
  };
}

/**
 * Create a direct message to a specific agent
 */
createDirectMessage(
  from: string,
  to: string | string[],
  type: IterativeMessageType,
  content: any,
  conversationId: string,
  inReplyTo?: string
): IterativeMessage {
  return {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from,
    to,
    type,
    content,
    conversationId,
    inReplyTo
  };
}

/**
 * Send a message and wait for a response (synchronous operation)
 */
async sendMessageAndWaitForResponse(
  sessionId: string,
  message: IterativeMessage,
  processorFn: (sessionId: string, message: IterativeMessage) => Promise<any>,
  timeoutMs: number = 15000
): Promise<IterativeMessage | null> {
  // Validate message type and set default if undefined
  if (!message.type) {
    console.warn('[MESSAGE_BUS] Message type is undefined in sendMessageAndWaitForResponse, setting default type REQUEST');
    message.type = IterativeMessageType.REQUEST;
  }

  // Ensure message type is a valid IterativeMessageType
  const validTypes = Object.values(IterativeMessageType);
  if (!validTypes.includes(message.type as any)) {
    console.warn(`[MESSAGE_BUS] Invalid message type in sendMessageAndWaitForResponse: ${message.type}, setting to REQUEST`);
    message.type = IterativeMessageType.REQUEST;
  }

  console.log(`[MESSAGE_BUS] Sending message:`, {
    id: message.id,
    type: message.type,
    from: message.from,
    to: message.to,
    timestamp: new Date().toISOString(),
    conversationId: message.conversationId,
    hasContent: !!message.content,
    hasArtifact: !!message.content?.artifact,
    artifactId: message.artifactId || message.content?.artifactId,
    inReplyTo: message.inReplyTo
  });

  if (message.content?.artifact) {
    console.log(`[MESSAGE_BUS] Artifact details:`, {
      id: message.content.artifact.id,
      type: message.content.artifact.type,
      name: message.content.artifact.name,
      createdBy: message.content.artifact.createdBy,
      status: message.content.artifact.status
    });
  }

  // Process the message
  const result = await this.sendMessage(sessionId, message, processorFn);

  if (!result || !result.response) {
    console.error('No response received from agent');
    return null;
  }

  console.log(`[MESSAGE_BUS] Received response from agent:`, {
    id: result.response.id,
    type: result.response.type,
    from: result.response.from,
    to: result.response.to,
    hasContent: !!result.response.content,
    hasArtifact: !!result.response.content?.artifact
  });

  // Save the response message to state
  await stateStore.updateState(sessionId, (state) => {
    if (state.messages) {
      state.messages.push(result.response);
    }
    return state;
  });

  return result.response;
}

async sendMessage(message: IterativeMessage, explicitSessionId?: string): Promise<IterativeMessage | null> {
  // Get the session ID from the message or use the explicitly provided one
  const sessionId = explicitSessionId || message.conversationId || message.sessionId;
  if (!sessionId) {
    console.error('[MESSAGE_BUS] Error: No session ID in message or provided explicitly');
    logger.error('Message missing session ID', {
      messageId: message.id,
      from: message.from,
      to: message.to,
      type: message.type
    });
    return null;
  }

  // Ensure the message has the sessionId set
  message.conversationId = sessionId;
  message.sessionId = sessionId;

  // Validate message type and set default if undefined
  if (!message.type) {
    console.warn('[MESSAGE_BUS] Message type is undefined, setting default type REQUEST');
    message.type = IterativeMessageType.REQUEST;
  }

  // Ensure message type is a valid IterativeMessageType
  const validTypes = Object.values(IterativeMessageType);
  if (!validTypes.includes(message.type as any)) {
    console.warn(`[MESSAGE_BUS] Invalid message type: ${message.type}, setting to REQUEST`);
    message.type = IterativeMessageType.REQUEST;
  }

  console.log(`[MESSAGE_BUS] Sending message:`, {
    id: message.id,
    type: message.type,
    from: message.from,
    to: message.to,
    timestamp: new Date().toISOString(),
    conversationId: message.conversationId,
    hasContent: !!message.content,
    hasArtifact: !!message.content?.artifact,
    artifactId: message.artifactId || message.content?.artifactId,
    inReplyTo: message.inReplyTo
  });

  if (message.content?.artifact) {
    console.log(`[MESSAGE_BUS] Artifact details:`, {
      id: message.content.artifact.id,
      type: message.content.artifact.type,
      name: message.content.artifact.name,
      createdBy: message.content.artifact.createdBy,
      status: message.content.artifact.status
    });
  }

  // Store the message in the global state
  await stateStore.storeMessage(sessionId, message);
  console.log(`[MESSAGE_BUS] Message ${message.id} stored in session ${sessionId}`);

  // Get the controller instance
  const controller = getControllerInstance();
  console.log(`[MESSAGE_BUS] Passing message to controller for processing`);

  // Process the message
  const response = await controller.processMessage(sessionId, message);

  if (response) {
    console.log(`[MESSAGE_BUS] Received response from controller:`, {
      id: response.id,
      type: response.type,
      from: response.from,
      to: response.to,
      hasContent: !!response.content,
      hasArtifact: !!response.content?.artifact
    });
  } else {
    console.log(`[MESSAGE_BUS] No response received from controller for message ${message.id}`);
  }

  return response;
  }
}

// Create a singleton instance
// Create and export a single instance of the MessageBus for use throughout the application
export const messageBus = new MessageBus();
