/**
 * Utility functions to map agent IDs to their roles
 */

import { AgentId } from '../types';

/**
 * Map an agent ID to its role type
 */
export function getAgentRoleType(agentId: AgentId): string {
  const roleMap: Record<AgentId, string> = {
    [AgentId.MARKET_RESEARCH]: 'researcher',
    [AgentId.SEO_KEYWORD]: 'researcher',
    [AgentId.CONTENT_STRATEGY]: 'strategist',
    [AgentId.CONTENT_GENERATION]: 'creator',
    [AgentId.SEO_OPTIMIZATION]: 'optimizer'
  };
  
  return roleMap[agentId] || 'unknown';
}

/**
 * Get all agents of a specific role
 */
export function getAgentsByRole(role: string): AgentId[] {
  switch (role) {
    case 'researcher':
      return [AgentId.MARKET_RESEARCH, AgentId.SEO_KEYWORD];
    case 'strategist':
      return [AgentId.CONTENT_STRATEGY];
    case 'creator':
      return [AgentId.CONTENT_GENERATION];
    case 'optimizer':
      return [AgentId.SEO_OPTIMIZATION];
    default:
      return [];
  }
}

/**
 * Convert agent ID to a more readable name
 */
export function getAgentName(agentId: AgentId): string {
  const nameMap: Record<AgentId, string> = {
    [AgentId.MARKET_RESEARCH]: 'Market Research Agent',
    [AgentId.SEO_KEYWORD]: 'SEO Keyword Agent',
    [AgentId.CONTENT_STRATEGY]: 'Content Strategy Agent',
    [AgentId.CONTENT_GENERATION]: 'Content Generation Agent',
    [AgentId.SEO_OPTIMIZATION]: 'SEO Optimization Agent'
  };
  
  return nameMap[agentId] || agentId;
}
