import {
  IterativeCollaborationState,
  Goal,
  GoalStatus,
  ArtifactStatus
} from '../types';
import { stateStore } from './stateStore';
import { artifactManager } from './artifact-manager';
import logger from './logger';

/**
 * Validation criteria for different phases
 */
export interface ValidationCriteria {
  requiredArtifactTypes: string[];
  qualityThresholds: Record<string, number>;
  requiredGoals: string[];
}

/**
 * Validation result
 */
export interface ValidationResult {
  isValid: boolean;
  missingArtifacts: string[];
  lowQualityArtifacts: string[];
  incompleteGoals: string[];
  details: string;
}

/**
 * Validation Manager
 * Handles validation of workflow phases and artifacts
 */
export class ValidationManager {
  /**
   * Validate a workflow phase
   */
  async validatePhase(
    sessionId: string,
    phase: string,
    criteria: ValidationCriteria
  ): Promise<ValidationResult> {
    try {
      // Get current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      // Check required artifacts
      const missingArtifacts: string[] = [];
      const lowQualityArtifacts: string[] = [];
      
      // Get all artifacts
      const artifacts = await artifactManager.getArtifacts(sessionId);
      
      // Check each required artifact type
      for (const artifactType of criteria.requiredArtifactTypes) {
        // Find artifact of this type
        const artifactOfType = Object.values(artifacts).find(a => a.type === artifactType);
        
        if (!artifactOfType) {
          missingArtifacts.push(artifactType);
          continue;
        }
        
        // Check quality threshold
        const threshold = criteria.qualityThresholds[artifactType] || 0.75;
        if (artifactOfType.qualityScore === undefined || artifactOfType.qualityScore < threshold) {
          lowQualityArtifacts.push(artifactType);
        }
      }
      
      // Check required goals
      const incompleteGoals: string[] = [];
      
      // Check each required goal
      for (const goalId of criteria.requiredGoals) {
        // Find goal
        const goal = state.goals?.find(g => g.id === goalId);
        
        if (!goal || goal.status !== GoalStatus.COMPLETED) {
          incompleteGoals.push(goalId);
        }
      }
      
      // Determine overall validity
      const isValid = missingArtifacts.length === 0 && 
                      lowQualityArtifacts.length === 0 && 
                      incompleteGoals.length === 0;
      
      // Create result details
      let details = isValid 
        ? `Phase ${phase} validation passed successfully.` 
        : `Phase ${phase} validation failed.`;
      
      if (missingArtifacts.length > 0) {
        details += ` Missing artifacts: ${missingArtifacts.join(', ')}.`;
      }
      
      if (lowQualityArtifacts.length > 0) {
        details += ` Low quality artifacts: ${lowQualityArtifacts.join(', ')}.`;
      }
      
      if (incompleteGoals.length > 0) {
        details += ` Incomplete goals: ${incompleteGoals.join(', ')}.`;
      }
      
      // Log validation result
      logger.info(`Phase validation ${isValid ? 'passed' : 'failed'}`, {
        sessionId,
        phase,
        isValid,
        missingArtifacts,
        lowQualityArtifacts,
        incompleteGoals,
        timestamp: new Date().toISOString()
      });
      
      return {
        isValid,
        missingArtifacts,
        lowQualityArtifacts,
        incompleteGoals,
        details
      };
    } catch (error) {
      const err = error as Error;
      logger.error(`Error validating phase`, {
        sessionId,
        phase,
        error: err.message || String(error),
        stack: err.stack
      });
      
      return {
        isValid: false,
        missingArtifacts: [],
        lowQualityArtifacts: [],
        incompleteGoals: [],
        details: `Error validating phase: ${err.message}`
      };
    }
  }
  
  /**
   * Validate research phase
   */
  async validateResearchPhase(sessionId: string): Promise<ValidationResult> {
    const criteria: ValidationCriteria = {
      requiredArtifactTypes: [
        'market-research-report',
        'keyword-analysis',
        'content-strategy'
      ],
      qualityThresholds: {
        'market-research-report': 0.75,
        'keyword-analysis': 0.75,
        'content-strategy': 0.80
      },
      requiredGoals: [
        'market-research-goal',
        'keyword-research-goal',
        'content-strategy-goal'
      ]
    };
    
    return this.validatePhase(sessionId, 'research', criteria);
  }
  
  /**
   * Validate content generation phase
   */
  async validateContentGenerationPhase(sessionId: string): Promise<ValidationResult> {
    const criteria: ValidationCriteria = {
      requiredArtifactTypes: [
        'content-draft'
      ],
      qualityThresholds: {
        'content-draft': 0.85
      },
      requiredGoals: [
        'content-generation-goal'
      ]
    };
    
    return this.validatePhase(sessionId, 'content-generation', criteria);
  }
  
  /**
   * Validate review phase
   */
  async validateReviewPhase(sessionId: string): Promise<ValidationResult> {
    const criteria: ValidationCriteria = {
      requiredArtifactTypes: [
        'seo-optimized-content'
      ],
      qualityThresholds: {
        'seo-optimized-content': 0.85
      },
      requiredGoals: [
        'seo-optimization-goal'
      ]
    };
    
    return this.validatePhase(sessionId, 'review', criteria);
  }
  
  /**
   * Update goal status based on artifact validation
   */
  async updateGoalStatus(
    sessionId: string,
    goalId: string,
    status: GoalStatus,
    details?: string
  ): Promise<void> {
    try {
      // Get current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      // Find goal
      const goalIndex = state.goals?.findIndex(g => g.id === goalId);
      
      if (goalIndex === undefined || goalIndex === -1 || !state.goals) {
        throw new Error(`Goal ${goalId} not found`);
      }
      
      // Update goal
      const updatedGoals = [...state.goals];
      updatedGoals[goalIndex] = {
        ...updatedGoals[goalIndex],
        status,
        completedAt: status === GoalStatus.COMPLETED ? new Date().toISOString() : undefined,
        details: details || updatedGoals[goalIndex].details
      };
      
      // Store updated goals
      await stateStore.updateState(sessionId, {
        goals: updatedGoals
      });
      
      logger.info(`Goal status updated`, {
        sessionId,
        goalId,
        status,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error updating goal status`, {
        sessionId,
        goalId,
        status,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Check if all dependencies for a goal are completed
   */
  async checkGoalDependencies(
    sessionId: string,
    goalId: string
  ): Promise<boolean> {
    try {
      // Get current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      // Find goal
      const goal = state.goals?.find(g => g.id === goalId);
      
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }
      
      // If no dependencies, return true
      if (!goal.dependencies || goal.dependencies.length === 0) {
        return true;
      }
      
      // Check each dependency
      for (const dependencyId of goal.dependencies) {
        const dependency = state.goals?.find(g => g.id === dependencyId);
        
        if (!dependency || dependency.status !== GoalStatus.COMPLETED) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error checking goal dependencies`, {
        sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }
}

// Create singleton instance
export const validationManager = new ValidationManager();
