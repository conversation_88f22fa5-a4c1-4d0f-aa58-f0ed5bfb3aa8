import { v4 as uuidv4 } from 'uuid';
import { 
  IterativeMessage, 
  IterativeCollaborationState, 
  IterativeMessageType,
  IterativeArtifact,
  AgentId
} from '../types';
import { EnhancedIterativeMessage, createEnhancedIterativeMessage } from '../types/enhanced-message';
import { convertToIterativeMessage } from './message-adapter';
import { stateStore } from './stateStore';
import logger from './logger';

/**
 * Configuration for circuit breaker
 */
export interface CircuitBreakerConfig {
  maxMessagesPerAgent: number;  // Maximum messages an agent can send in a time window
  timeWindowMs: number;         // Time window in milliseconds
  maxConsecutiveMessages: number; // Maximum consecutive messages between same agents
  cooldownPeriodMs: number;     // Cooldown period after circuit is broken
}

/**
 * Default circuit breaker configuration
 */
const DEFAULT_CIRCUIT_BREAKER: CircuitBreakerConfig = {
  maxMessagesPerAgent: 20,
  timeWindowMs: 60000, // 1 minute
  maxConsecutiveMessages: 5,
  cooldownPeriodMs: 30000 // 30 seconds
};

/**
 * Enhanced message bus with A2A protocol compatibility
 */
export class EnhancedMessageBus {
  private circuitBreakerConfig: CircuitBreakerConfig;
  private messageCounters: Record<string, { count: number, lastReset: number }> = {};
  private consecutiveMessages: Record<string, number> = {};
  private brokenCircuits: Record<string, number> = {}; // Maps agent pair to time when circuit broke
  private activeSessions: Map<string, {
    messageQueue: EnhancedIterativeMessage[];
    messageHistory: EnhancedIterativeMessage[];
    messageHandlers: Record<string, (message: EnhancedIterativeMessage) => Promise<any>>;
  }> = new Map();
  
  constructor(config: Partial<CircuitBreakerConfig> = {}) {
    this.circuitBreakerConfig = { ...DEFAULT_CIRCUIT_BREAKER, ...config };
  }
  
  /**
   * Create a session-specific message bus
   */
  async createSessionBus(sessionId: string): Promise<{
    queueMessage: (message: EnhancedIterativeMessage) => Promise<void>;
    getHistory: () => EnhancedIterativeMessage[];
    createBroadcastMessage: (
      from: string,
      type: IterativeMessageType,
      content: any
    ) => EnhancedIterativeMessage;
    createDirectMessage: (
      from: string,
      to: string | string[],
      type: IterativeMessageType,
      content: any,
      inReplyTo?: string
    ) => EnhancedIterativeMessage;
  }> {
    // Add the session to active sessions if it doesn't exist
    if (!this.activeSessions.has(sessionId)) {
      this.activeSessions.set(sessionId, {
        messageQueue: [],
        messageHistory: [],
        messageHandlers: {}
      });
    }
    
    // Return session-specific methods
    return {
      queueMessage: async (message: EnhancedIterativeMessage) => {
        await this.queueMessage(message, sessionId);
      },
      getHistory: () => {
        const session = this.activeSessions.get(sessionId);
        return session ? session.messageHistory : [];
      },
      createBroadcastMessage: (
        from: string,
        type: IterativeMessageType,
        content: any
      ): EnhancedIterativeMessage => {
        return createEnhancedIterativeMessage(
          from,
          'all',
          type,
          content,
          {
            sessionId,
            conversationId: sessionId
          }
        );
      },
      createDirectMessage: (
        from: string,
        to: string | string[],
        type: IterativeMessageType,
        content: any,
        inReplyTo?: string
      ): EnhancedIterativeMessage => {
        return createEnhancedIterativeMessage(
          from,
          to,
          type,
          content,
          {
            sessionId,
            conversationId: sessionId,
            inReplyTo
          }
        );
      }
    };
  }
  
  /**
   * Queue a message for processing
   */
  async queueMessage(message: EnhancedIterativeMessage, sessionId: string): Promise<void> {
    console.log(`[ENHANCED_MESSAGE_BUS] Queueing message ${message.id} for session ${sessionId}`);
    
    // Validate message type
    if (!message.type) {
      console.warn('[ENHANCED_MESSAGE_BUS] Message type is undefined, setting default type SYSTEM_MESSAGE');
      message.type = IterativeMessageType.SYSTEM_MESSAGE;
    }
    
    // Get session data
    const sessionData = this.activeSessions.get(sessionId);
    if (!sessionData) {
      throw new Error(`Session ${sessionId} not found in active sessions`);
    }
    
    // Add message to queue
    sessionData.messageQueue.push(message);
    
    // Process queue
    this.processQueue(sessionId);
  }
  
  /**
   * Process the message queue for a session
   */
  private async processQueue(sessionId: string): Promise<void> {
    const sessionData = this.activeSessions.get(sessionId);
    if (!sessionData || sessionData.messageQueue.length === 0) {
      return;
    }
    
    // Get the next message
    const message = sessionData.messageQueue.shift()!;
    
    // Add to history
    sessionData.messageHistory.push(message);
    
    // Store in state
    await this.addMessageToState(sessionId, message);
    
    // Process message
    await this.processMessage(sessionId, message);
  }
  
  /**
   * Add a message to the session state
   */
  private async addMessageToState(sessionId: string, message: EnhancedIterativeMessage): Promise<void> {
    try {
      // Get current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        console.warn(`[ENHANCED_MESSAGE_BUS] Session ${sessionId} not found in state store`);
        return;
      }
      
      // Convert to standard IterativeMessage for state storage
      const iterativeMessage = convertToIterativeMessage(message);
      
      // Add message to state
      await stateStore.updateState(sessionId, {
        messages: [...(state.messages || []), iterativeMessage]
      });
    } catch (error) {
      console.error(`[ENHANCED_MESSAGE_BUS] Error adding message to state:`, error);
    }
  }
  
  /**
   * Process a message
   */
  private async processMessage(sessionId: string, message: EnhancedIterativeMessage): Promise<void> {
    try {
      // Check circuit breaker
      if (this.isCircuitBroken(message.from, Array.isArray(message.to) ? message.to[0] : message.to)) {
        console.log(`[ENHANCED_MESSAGE_BUS] Circuit breaker active for ${message.from} -> ${message.to}`);
        return;
      }
      
      // Update message counters
      this.updateMessageCounters(message.from, Array.isArray(message.to) ? message.to[0] : message.to);
      
      // Get session data
      const sessionData = this.activeSessions.get(sessionId);
      if (!sessionData) {
        throw new Error(`Session ${sessionId} not found in active sessions`);
      }
      
      // Handle broadcast messages
      if (message.to === 'all') {
        console.log(`[ENHANCED_MESSAGE_BUS] Broadcasting message ${message.id} from ${message.from}`);
        
        // Get all agent handlers
        const handlers = Object.values(sessionData.messageHandlers);
        
        // Call each handler
        for (const handler of handlers) {
          try {
            await handler(message);
          } catch (error) {
            console.error(`[ENHANCED_MESSAGE_BUS] Error in broadcast handler:`, error);
          }
        }
        
        return;
      }
      
      // Handle direct messages
      const recipients = Array.isArray(message.to) ? message.to : [message.to];
      
      for (const recipient of recipients) {
        const handler = sessionData.messageHandlers[recipient];
        
        if (handler) {
          try {
            await handler(message);
          } catch (error) {
            console.error(`[ENHANCED_MESSAGE_BUS] Error in handler for ${recipient}:`, error);
          }
        } else {
          console.warn(`[ENHANCED_MESSAGE_BUS] No handler registered for ${recipient}`);
        }
      }
    } catch (error) {
      console.error(`[ENHANCED_MESSAGE_BUS] Error processing message:`, error);
    }
  }
  
  /**
   * Register a message handler for an agent
   */
  registerHandler(
    sessionId: string,
    agentId: string,
    handler: (message: EnhancedIterativeMessage) => Promise<any>
  ): void {
    // Get session data
    let sessionData = this.activeSessions.get(sessionId);
    
    // Create session if it doesn't exist
    if (!sessionData) {
      sessionData = {
        messageQueue: [],
        messageHistory: [],
        messageHandlers: {}
      };
      this.activeSessions.set(sessionId, sessionData);
    }
    
    // Register handler
    sessionData.messageHandlers[agentId] = handler;
  }
  
  /**
   * Send a message
   */
  async sendMessage(sessionId: string, message: EnhancedIterativeMessage): Promise<void> {
    await this.queueMessage(message, sessionId);
  }
  
  /**
   * Check if circuit breaker is active
   */
  private isCircuitBroken(from: string, to: string): boolean {
    const key = `${from}-${to}`;
    
    // Check if circuit is broken
    if (this.brokenCircuits[key]) {
      const breakTime = this.brokenCircuits[key];
      const currentTime = Date.now();
      
      // Check if cooldown period has passed
      if (currentTime - breakTime > this.circuitBreakerConfig.cooldownPeriodMs) {
        // Reset circuit
        delete this.brokenCircuits[key];
        return false;
      }
      
      return true;
    }
    
    return false;
  }
  
  /**
   * Update message counters for circuit breaker
   */
  private updateMessageCounters(from: string, to: string): void {
    const key = `${from}-${to}`;
    const currentTime = Date.now();
    
    // Update message count
    if (!this.messageCounters[key]) {
      this.messageCounters[key] = {
        count: 1,
        lastReset: currentTime
      };
    } else {
      // Check if time window has passed
      if (currentTime - this.messageCounters[key].lastReset > this.circuitBreakerConfig.timeWindowMs) {
        // Reset counter
        this.messageCounters[key] = {
          count: 1,
          lastReset: currentTime
        };
      } else {
        // Increment counter
        this.messageCounters[key].count++;
        
        // Check if limit exceeded
        if (this.messageCounters[key].count > this.circuitBreakerConfig.maxMessagesPerAgent) {
          // Break circuit
          this.brokenCircuits[key] = currentTime;
          console.log(`[ENHANCED_MESSAGE_BUS] Circuit breaker activated for ${from} -> ${to}`);
        }
      }
    }
    
    // Update consecutive messages
    if (!this.consecutiveMessages[key]) {
      this.consecutiveMessages[key] = 1;
    } else {
      this.consecutiveMessages[key]++;
      
      // Check if limit exceeded
      if (this.consecutiveMessages[key] > this.circuitBreakerConfig.maxConsecutiveMessages) {
        // Break circuit
        this.brokenCircuits[key] = currentTime;
        console.log(`[ENHANCED_MESSAGE_BUS] Circuit breaker activated for ${from} -> ${to} (consecutive messages)`);
      }
    }
  }
}

// Create singleton instance
export const enhancedMessageBus = new EnhancedMessageBus();
