import { v4 as uuidv4 } from 'uuid';
import {
  Consultation,
  IterativeMessageType,
  AgentId,
  IterativeArtifact
} from '../types';
import { EnhancedIterativeMessage, createEnhancedIterativeMessage } from '../types/enhanced-message';
import { stateStore } from './stateStore';
import { enhancedMessageBus } from './enhanced-message-bus';
import logger from './logger';

/**
 * Consultation Manager
 * Handles the creation, tracking, and resolution of consultations between agents
 */
export class ConsultationManager {
  /**
   * Request a consultation from another agent
   */
  async requestConsultation(
    sessionId: string,
    fromAgent: AgentId | string,
    toAgent: AgentId | string,
    question: string,
    context: any,
    artifactId?: string
  ): Promise<string> {
    try {
      // Create consultation ID
      const consultationId = `consultation-${uuidv4()}`;
      
      // Create consultation record
      const consultation: Consultation = {
        id: consultationId,
        fromAgent: fromAgent as AgentId,
        toAgent: toAgent as AgentId,
        question,
        context,
        timestamp: new Date().toISOString(),
        artifactId,
        incorporated: false
      };
      
      // Store consultation in state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      await stateStore.updateState(sessionId, {
        consultations: {
          ...(state.consultations || {}),
          [consultationId]: consultation
        }
      });
      
      // Create consultation request message
      const consultationMessage = createEnhancedIterativeMessage(
        fromAgent,
        toAgent,
        IterativeMessageType.CONSULTATION_REQUEST,
        {
          consultationId,
          question,
          context,
          artifactId
        },
        {
          sessionId,
          reasoning: {
            thoughts: [`Need input from ${toAgent} on specific aspect of work`],
            considerations: [
              'Specialized expertise is required',
              'Collaboration improves quality',
              'Feedback should be incorporated into final product'
            ],
            decision: `Request consultation from ${toAgent} to improve quality`,
            confidence: 0.9
          },
          metadata: {
            consultationType: artifactId ? 'artifact-feedback' : 'general-consultation'
          }
        }
      );
      
      // Send consultation request
      await enhancedMessageBus.sendMessage(sessionId, consultationMessage);
      
      logger.info(`Consultation requested`, {
        sessionId,
        consultationId,
        fromAgent,
        toAgent,
        artifactId,
        timestamp: new Date().toISOString()
      });
      
      return consultationId;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error requesting consultation`, {
        sessionId,
        fromAgent,
        toAgent,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Respond to a consultation request
   */
  async respondToConsultation(
    sessionId: string,
    consultationId: string,
    response: any,
    feedback?: string,
    suggestions?: Array<{
      area: string;
      suggestion: string;
      priority: 'high' | 'medium' | 'low';
    }>
  ): Promise<void> {
    try {
      // Get consultation from state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      const consultation = state.consultations?.[consultationId];
      if (!consultation) {
        throw new Error(`Consultation ${consultationId} not found`);
      }
      
      // Update consultation with response
      const updatedConsultation: Consultation = {
        ...consultation,
        response,
        feedback,
        suggestions
      };
      
      // Store updated consultation
      await stateStore.updateState(sessionId, {
        consultations: {
          ...(state.consultations || {}),
          [consultationId]: updatedConsultation
        }
      });
      
      // Create consultation response message
      const responseMessage = createEnhancedIterativeMessage(
        consultation.toAgent,
        consultation.fromAgent,
        IterativeMessageType.CONSULTATION_RESPONSE,
        {
          consultationId,
          response,
          feedback,
          suggestions
        },
        {
          sessionId,
          reasoning: {
            thoughts: [`Providing feedback to ${consultation.fromAgent}`],
            considerations: [
              'Feedback should be constructive and actionable',
              'Prioritize suggestions by impact',
              'Provide clear rationale for recommendations'
            ],
            decision: `Provide detailed consultation response with ${suggestions?.length || 0} suggestions`,
            confidence: 0.9
          },
          metadata: {
            consultationType: consultation.artifactId ? 'artifact-feedback' : 'general-consultation'
          }
        }
      );
      
      // Send response message
      await enhancedMessageBus.sendMessage(sessionId, responseMessage);
      
      logger.info(`Consultation response sent`, {
        sessionId,
        consultationId,
        fromAgent: consultation.toAgent,
        toAgent: consultation.fromAgent,
        artifactId: consultation.artifactId,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error responding to consultation`, {
        sessionId,
        consultationId,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Mark a consultation as incorporated
   */
  async markConsultationIncorporated(
    sessionId: string,
    consultationId: string,
    artifactId: string
  ): Promise<void> {
    try {
      // Get consultation from state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      const consultation = state.consultations?.[consultationId];
      if (!consultation) {
        throw new Error(`Consultation ${consultationId} not found`);
      }
      
      // Update consultation
      const updatedConsultation: Consultation = {
        ...consultation,
        incorporated: true
      };
      
      // Store updated consultation
      await stateStore.updateState(sessionId, {
        consultations: {
          ...(state.consultations || {}),
          [consultationId]: updatedConsultation
        }
      });
      
      // Update artifact to reference incorporated consultation
      const artifact = state.artifacts?.[artifactId];
      if (artifact) {
        // Find the latest iteration
        const latestIteration = artifact.iterations?.[artifact.iterations.length - 1];
        
        if (latestIteration) {
          // Update iteration with incorporated consultation
          const updatedIteration = {
            ...latestIteration,
            incorporatedConsultations: [
              ...(latestIteration.incorporatedConsultations || []),
              consultationId
            ]
          };
          
          // Update artifact
          const updatedArtifact: IterativeArtifact = {
            ...artifact,
            iterations: [
              ...artifact.iterations.slice(0, -1),
              updatedIteration
            ]
          };
          
          // Store updated artifact
          await stateStore.updateState(sessionId, {
            artifacts: {
              ...(state.artifacts || {}),
              [artifactId]: updatedArtifact
            }
          });
        }
      }
      
      logger.info(`Consultation marked as incorporated`, {
        sessionId,
        consultationId,
        artifactId,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error marking consultation as incorporated`, {
        sessionId,
        consultationId,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Get all consultations for a session
   */
  async getConsultations(sessionId: string): Promise<Record<string, Consultation>> {
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    return state.consultations || {};
  }
  
  /**
   * Get a specific consultation
   */
  async getConsultation(sessionId: string, consultationId: string): Promise<Consultation | null> {
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    return state.consultations?.[consultationId] || null;
  }
}

// Create singleton instance
export const consultationManager = new ConsultationManager();
