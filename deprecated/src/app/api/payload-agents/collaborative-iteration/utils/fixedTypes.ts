/**
 * This file reconciles types and interfaces for agent collaboration utilities
 * to fix TypeScript errors across the codebase
 */
import { 
  IterativeMessage, 
  IterativeArtifact, 
  Consultation,
  AgentState,
  AgentId,
  IterativeMessageType,
  IterativeCollaborationState
} from '../types';
import { Reasoning } from '../../a2atypes';

// Export necessary types from the main types file
export {
  AgentId,
  IterativeMessageType,
  IterativeMessage,
  AgentState,
  IterativeCollaborationState
};

/**
 * Add missing updatedAt property to IterativeArtifact interface
 */
export interface EnhancedIterativeArtifact extends IterativeArtifact {
  updatedAt: string;
  version?: number;
  agent?: string; // For compatibility with some code paths
}

/**
 * Add missing properties to Consultation interface
 */
export interface EnhancedConsultation extends Consultation {
  requester: string; // For compatibility with some code paths
  provider?: string;
}

/**
 * StandardizedHandlerResult provides a consistent structure for all agent handler results
 * This makes it easier to integrate with the central collaboration state
 */
export interface StandardizedHandlerResult {
  response: IterativeMessage;
  stateUpdates?: Record<string, any>;
  artifactUpdates?: {
    new?: Record<string, EnhancedIterativeArtifact>;
    updated?: Record<string, EnhancedIterativeArtifact>;
  };
  consultationUpdates?: {
    new?: Record<string, EnhancedConsultation>;
  };
}

/**
 * Enhanced feedback structure
 */
export interface EnhancedFeedback {
  artifactId: string;
  from: string;
  content: string;
  timestamp: string;
}

/**
 * Extended agent state with required feedback structure
 */
export interface EnhancedAgentState extends AgentState {
  feedback: EnhancedFeedback[];
}

/**
 * Compatibility types for message system
 */
export const ExtendedIterativeMessageType = {
  ...IterativeMessageType,
  DISCUSSION_CONTRIBUTION: 'DISCUSSION_CONTRIBUTION',
  DISCUSSION_SYNTHESIS_REQUEST: 'DISCUSSION_SYNTHESIS_REQUEST'
} as const;

/**
 * Export type for enhanced reasoning
 */
export interface EnhancedReasoning extends Reasoning {
  process?: string;
  steps?: string[];
  thoughts?: string[];
  considerations?: string[];
  alternatives?: string[];
}
