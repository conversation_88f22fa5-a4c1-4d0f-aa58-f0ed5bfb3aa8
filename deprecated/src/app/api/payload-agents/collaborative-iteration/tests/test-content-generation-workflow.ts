// src/app/(payload)/api/agents/collaborative-iteration/tests/test-content-generation-workflow.ts

import { v4 as uuidv4 } from 'uuid';
import { initiateResearchPhase } from '../workflows/research-phase-workflow.js';
import { initiateContentGenerationPhase } from '../workflows/content-generation-workflow.js';
import { stateStore } from '../utils/stateStore.js';
import logger from '../utils/logger.js';

/**
 * Test the content generation workflow
 *
 * This function initiates the research phase workflow first, then the content generation workflow,
 * and monitors the progress to ensure it's working correctly.
 */
async function testContentGenerationWorkflow() {
  try {
    // Generate a unique session ID for this test
    const sessionId = `test-${uuidv4()}`;

    // Test parameters
    const topic = 'Artificial Intelligence in Healthcare';
    const contentType = 'blog-article';
    const targetAudience = 'healthcare professionals';
    const tone = 'informative';
    const additionalParams = {
      keywords: ['AI in healthcare', 'medical AI', 'healthcare technology'],
      testMode: true
    };

    logger.info(`Starting content generation workflow test with session ID: ${sessionId}`, {
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone
    });

    // Step 1: Initiate the research phase workflow
    logger.info(`Step 1: Initiating research phase workflow`, { sessionId });
    const researchSuccess = await initiateResearchPhase(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );

    if (!researchSuccess) {
      logger.error(`Failed to initiate research phase workflow`, { sessionId });
      return;
    }

    logger.info(`Research phase workflow initiated successfully`, { sessionId });

    // Step 2: Wait for research phase to complete
    logger.info(`Step 2: Waiting for research phase to complete`, { sessionId });
    await waitForResearchPhaseCompletion(sessionId);

    // Step 3: Initiate the content generation phase workflow
    logger.info(`Step 3: Initiating content generation phase workflow`, { sessionId });
    const contentGenerationSuccess = await initiateContentGenerationPhase(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );

    if (!contentGenerationSuccess) {
      logger.error(`Failed to initiate content generation phase workflow`, { sessionId });
      return;
    }

    logger.info(`Content generation phase workflow initiated successfully`, { sessionId });

    // Step 4: Monitor the content generation workflow progress
    logger.info(`Step 4: Monitoring content generation workflow progress`, { sessionId });
    await monitorContentGenerationProgress(sessionId);

  } catch (error) {
    const err = error as Error;
    logger.error(`Error in test script`, {
      error: err.message || String(error),
      stack: err.stack
    });
  }
}

/**
 * Wait for the research phase to complete
 *
 * This function periodically checks the state to see if the research phase has completed.
 */
async function waitForResearchPhaseCompletion(sessionId: string, maxChecks: number = 20): Promise<boolean> {
  let checkCount = 0;
  let complete = false;

  while (!complete && checkCount < maxChecks) {
    // Wait for a few seconds between checks
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Check if the research phase is complete
    if (
      state.workflowProgress?.marketResearchComplete &&
      state.workflowProgress?.keywordResearchComplete &&
      state.workflowProgress?.contentStrategyComplete
    ) {
      complete = true;
      logger.info(`Research phase completed successfully!`, {
        sessionId,
        workflowProgress: state.workflowProgress
      });
      return true;
    }

    logger.info(`Research phase progress check #${checkCount + 1}`, {
      sessionId,
      workflowProgress: state.workflowProgress
    });

    checkCount++;
  }

  if (!complete) {
    logger.warn(`Research phase did not complete within the expected time`, {
      sessionId,
      checksPerformed: checkCount
    });
    return false;
  }

  return true;
}

/**
 * Monitor the progress of the content generation workflow
 *
 * This function periodically checks the state to monitor the progress
 * of the workflow and logs relevant information.
 */
async function monitorContentGenerationProgress(sessionId: string, maxChecks: number = 20) {
  let checkCount = 0;
  let complete = false;

  while (!complete && checkCount < maxChecks) {
    // Wait for a few seconds between checks
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return;
    }

    // Extract relevant information
    const {
      workflowProgress,
      currentPhase,
      metadata,
      generatedArtifacts = []
    } = state;

    // Count artifacts by type
    const artifactCounts: Record<string, number> = {};
    for (const id of generatedArtifacts) {
      const type = state.artifacts[id]?.type;
      if (type) {
        artifactCounts[type] = (artifactCounts[type] || 0) + 1;
      }
    }

    // Log the current status
    logger.info(`Content generation workflow progress check #${checkCount + 1}`, {
      sessionId,
      currentPhase,
      workflowProgress,
      artifactCounts,
      contentGenerationWorkflowState: metadata?.contentGenerationWorkflowState || null
    });

    // Check if the workflow is complete
    if (
      workflowProgress?.contentGenerationComplete &&
      workflowProgress?.seoOptimizationComplete
    ) {
      complete = true;
      logger.info(`Content generation workflow completed successfully!`, {
        sessionId,
        artifactCounts,
        totalArtifacts: generatedArtifacts.length
      });

      // Log details of each artifact
      for (const id of generatedArtifacts) {
        const artifact = state.artifacts[id];
        if (artifact) {
          logger.info(`Artifact: ${artifact.title}`, {
            id: artifact.id,
            type: artifact.type,
            status: artifact.status,
            createdAt: artifact.createdAt
          });
        }
      }
    }

    checkCount++;
  }

  if (!complete) {
    logger.warn(`Content generation workflow did not complete within the expected time`, {
      sessionId,
      checksPerformed: checkCount
    });
  }
}

// Run the test
testContentGenerationWorkflow().catch(error => {
  logger.error(`Unhandled error in test script`, {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined
  });
});
