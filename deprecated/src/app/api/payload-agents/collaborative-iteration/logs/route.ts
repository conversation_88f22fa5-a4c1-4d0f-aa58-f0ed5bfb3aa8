import { NextRequest, NextResponse } from 'next/server';
import logger from '../../collaborative-iteration/utils/logger';

// In-memory storage for logs (in a production environment, this would be in a database)
const logStorage: Record<string, Array<{
  id: string;
  timestamp: string;
  level: string;
  message: string;
  agent?: string;
  phase?: string;
  context?: any;
}>> = {};

// Maximum logs to keep per session
const MAX_LOGS_PER_SESSION = 1000;

/**
 * Add a log entry to storage
 */
export function storeLog(sessionId: string, log: any) {
  if (!sessionId) {
    console.error('No sessionId provided to storeLog');
    return null;
  }
  
  if (!logStorage[sessionId]) {
    logStorage[sessionId] = [];
  }
  
  // Add logging to debug agent visibility
  console.log(`Storing log for agent: ${log.agent || 'unknown'} with message: ${log.message?.substring(0, 50)}...`);
  
  // Generate a unique ID for the log
  const logId = `log_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  // Add the log with timestamp and ID
  logStorage[sessionId].push({
    id: logId,
    timestamp: new Date().toISOString(),
    ...log
  });
  
  // Log the total counts by agent
  const agentCounts = logStorage[sessionId].reduce((acc, log) => {
    const agent = log.agent || 'unknown';
    acc[agent] = (acc[agent] || 0) + 1;
    return acc;
  }, {});
  
  console.log(`Current log counts by agent: ${JSON.stringify(agentCounts)}`);
  
  // Trim logs if there are too many
  if (logStorage[sessionId].length > MAX_LOGS_PER_SESSION) {
    logStorage[sessionId] = logStorage[sessionId].slice(-MAX_LOGS_PER_SESSION);
  }
  
  return logId;
}

/**
 * API handler for fetching logs
 */
export async function GET(request: NextRequest) {
  // Get query parameters
  const searchParams = request.nextUrl.searchParams;
  const sessionId = searchParams.get('sessionId');
  const after = searchParams.get('after');
  const limit = parseInt(searchParams.get('limit') || '50', 10);
  
  // Validate session ID
  if (!sessionId) {
    return NextResponse.json(
      { error: 'Session ID is required' },
      { status: 400 }
    );
  }
  
  try {
    // Get logs for the session
    const sessionLogs = logStorage[sessionId] || [];
    
    // Filter logs after a specific log ID if provided
    let filteredLogs = sessionLogs;
    if (after) {
      const afterIndex = sessionLogs.findIndex(log => log.id === after);
      if (afterIndex !== -1) {
        filteredLogs = sessionLogs.slice(afterIndex + 1);
      }
    }
    
    // Limit the number of logs returned
    const limitedLogs = filteredLogs.slice(-limit);
    
    return NextResponse.json({ 
      logs: limitedLogs,
      total: sessionLogs.length,
      returned: limitedLogs.length
    });
  } catch (error) {
    console.error('Error fetching logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch logs' },
      { status: 500 }
    );
  }
}

/**
 * API handler for creating logs
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, level, message, agent, phase, context } = body;
    
    // Validate required fields
    if (!sessionId || !level || !message) {
      return NextResponse.json(
        { error: 'sessionId, level, and message are required' },
        { status: 400 }
      );
    }
    
    // Store the log
    const logId = storeLog(sessionId, { level, message, agent, phase, context });
    if (!logId) {
      return NextResponse.json(
        { error: 'Failed to store log' },
        { status: 500 }
      );
    }
    
    // Also send to the server logger
    switch (level) {
      case 'info':
        logger.info(message, { sessionId, agent, phase, ...context });
        break;
      case 'debug':
        logger.debug(message, { sessionId, agent, phase, ...context });
        break;
      case 'warn':
        logger.warn(message, { sessionId, agent, phase, ...context });
        break;
      case 'error':
        logger.error(message, { sessionId, agent, phase, ...context });
        break;
      default:
        logger.info(message, { sessionId, agent, phase, ...context });
    }
    
    return NextResponse.json({ success: true, logId });
  } catch (error) {
    console.error('Error creating log:', error);
    return NextResponse.json(
      { error: 'Failed to create log' },
      { status: 500 }
    );
  }
}

/**
 * Utility to clear logs for a session
 */
export async function DELETE(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const sessionId = searchParams.get('sessionId');
  
  if (!sessionId) {
    return NextResponse.json(
      { error: 'Session ID is required' },
      { status: 400 }
    );
  }
  
  try {
    // Clear logs for the session
    if (logStorage[sessionId]) {
      delete logStorage[sessionId];
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error clearing logs:', error);
    return NextResponse.json(
      { error: 'Failed to clear logs' },
      { status: 500 }
    );
  }
}
