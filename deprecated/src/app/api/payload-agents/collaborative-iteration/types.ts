// src/app/(payload)/api/agents/collaborative-iteration/types.ts

import { Reasoning } from '../a2atypes';

/**
 * Agent identifiers for specialized roles
 */
export enum AgentId {
  MARKET_RESEARCH = 'market-research',
  SEO_KEYWORD = 'seo-keyword',
  CONTENT_STRATEGY = 'content-strategy',
  CONTENT_GENERATION = 'content-generation',
  SEO_OPTIMIZATION = 'seo-optimization'
}

/**
 * Agent role types
 */
export type AgentRole =
  | 'researcher'
  | 'strategist'
  | 'creator'
  | 'optimizer'
  | 'coordinator';

/**
 * Artifact status in the iterative process
 */
export enum ArtifactStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  IN_REVIEW = 'in-review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  REVISED = 'revised',
  FINAL = 'final'
}

/**
 * Message types for iterative agent collaboration
 */
export enum IterativeMessageType {
  // Basic message types
  REQUEST = 'REQUEST',                                               // Generic request
  RESPONSE = 'RESPONSE',                                             // Generic response
  UPDATE = 'UPDATE',                                                // Status update
  QUESTION = 'QUESTION',                                            // Question to another agent
  ANSWER = 'ANSWER',                                                // Answer to a question
  SUGGESTION = 'SUGGESTION',                                        // Suggestion for improvement
  CONFIRMATION = 'CONFIRMATION',                                    // Confirmation message

  // Workflow message types
  INITIAL_REQUEST = 'INITIAL_REQUEST',                              // Initial content generation request
  FINAL_OUTPUT = 'FINAL_OUTPUT',                                    // Final content after iterations
  SYSTEM_MESSAGE = 'SYSTEM_MESSAGE',                                // System message
  ERROR = 'ERROR',                                                  // Error message

  // Feedback related message types
  FEEDBACK = 'FEEDBACK',                                            // Direct feedback on content without explicit consultation
  FEEDBACK_REQUEST = 'FEEDBACK_REQUEST',                            // Request for feedback on content

  // Consultation related message types
  CONSULTATION_REQUEST = 'CONSULTATION_REQUEST',                     // Request for feedback on content
  CONSULTATION_RESPONSE = 'CONSULTATION_RESPONSE',                   // Provide feedback on content

  // Iteration related message types
  ITERATION_REQUEST = 'ITERATION_REQUEST',                          // Request for a new iteration of content
  ITERATION_RESPONSE = 'ITERATION_RESPONSE',                        // Provide a new iteration of content

  // Artifact related message types
  ARTIFACT_DELIVERY = 'ARTIFACT_DELIVERY',                          // Deliver an artifact to another agent
  ARTIFACT_REQUEST = 'ARTIFACT_REQUEST',                            // Request an artifact from another agent
  ARTIFACT_BROADCAST = 'ARTIFACT_BROADCAST',                        // Broadcast an artifact to all agents

  // Discussion related message types
  DISCUSSION_START = 'DISCUSSION_START',                            // Initiate a new discussion among agents
  DISCUSSION_PERSPECTIVE_REQUEST = 'DISCUSSION_PERSPECTIVE_REQUEST', // Request for perspective in a discussion
  DISCUSSION_SYNTHESIS_REQUEST = 'DISCUSSION_SYNTHESIS_REQUEST',     // Request for synthesis of perspectives
  DISCUSSION_SYNTHESIS = 'DISCUSSION_SYNTHESIS',                     // Synthesis of discussion contributions
  DISCUSSION_CONTRIBUTION = 'DISCUSSION_CONTRIBUTION',               // Contribution to an ongoing discussion

  // Acknowledgment
  ACKNOWLEDGMENT = 'ACKNOWLEDGMENT',                                 // Acknowledge receipt of a message

  // User interaction types
  USER_MESSAGE = 'USER_MESSAGE',                                     // Message from user to agents
  USER_FEEDBACK = 'USER_FEEDBACK'                                    // Feedback from user to agents
}

/**
 * Artifact status in the iterative process
 */
// ArtifactStatus is defined above

/**
 * Consultation record - tracks feedback between agents
 */
export interface Consultation {
  id: string;
  fromAgent: string;
  toAgent: string;
  artifactId: string;
  version?: number;
  timestamp: string;
  feedback: string;
  suggestions: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  incorporated: boolean;
  requestId: string;
  question?: string;
  context?: string;
  response?: any;
}

/**
 * Iteration - represents a version of content with its history
 */
export interface Iteration {
  version: number;
  timestamp: string;
  agent: string; // The agent that created this iteration
  content: any;
  feedback: string[];
  incorporatedConsultations: string[]; // IDs of consultations that influenced this version
  changes?: string; // Description of changes made in this iteration
  reasoning?: Reasoning; // Reasoning behind the changes
  consultations?: string[]; // Consultations used for this iteration
}

/**
 * Enhanced artifact with iteration history
 *
 * This interface has been expanded to include all properties required by specialized agents,
 * eliminating the need for separate ExtendedArtifact interfaces in each agent implementation.
 */
export interface IterativeArtifact {
  // Core properties
  id: string;
  name: string;
  type: string;
  createdBy: string;
  createdAt: string;
  currentVersion: number;
  iterations: Iteration[];
  status: ArtifactStatus;
  qualityScore: number;

  // Extended properties for all agents
  content?: string | Record<string, any>; // Content of the artifact (text, HTML, JSON, etc.)
  metadata?: Record<string, any>;        // Metadata about the artifact (reasoning, analysis, etc.)
  updatedAt?: string;                    // Last update timestamp
  data?: any;                            // Additional structured data specific to the artifact type

  // Content Strategy specific properties
  strategyContent?: {
    overview?: string;
    targetAudience?: string;
    contentType?: string;
    tone?: string;
    keywords?: string[];
    sections?: Array<{ title: string; content: string }>;
    distribution?: string;
    callToAction?: string;
    generatedAt?: string;
  };

  // SEO Optimization specific properties
  seoAnalysis?: {
    score?: number;
    recommendations?: Array<{ type: string; description: string; priority: string }>;
    keywords?: { primary: string; secondary: string[] };
    metaTags?: { title?: string; description?: string };
    headingStructure?: Array<{ level: number; text: string }>;
    contentDensity?: number;
    readabilityScore?: number;
  };

  // Content Generation specific properties
  generatedContent?: {
    title?: string;
    body?: string;
    sections?: Array<{ heading: string; content: string }>;
    summary?: string;
    format?: string;
    wordCount?: number;
  };

  // Market Research specific properties
  marketResearch?: {
    insights?: string[];
    demographics?: Record<string, any>;
    competitors?: Array<{ name: string; strengths: string[]; weaknesses: string[] }>;
    trends?: string[];
    opportunities?: string[];
  };

  // SEO Keyword specific properties
  keywordAnalysis?: {
    primary?: string;
    secondary?: string[];
    related?: string[];
    volumeData?: Record<string, number>;
    difficulty?: Record<string, number>;
    intent?: Record<string, string>;
  };

  // Reasoning and decision-making properties
  reasoning?: Reasoning;                 // Reasoning behind the artifact creation/modification
}

/**
 * Agent state for tracking interactions
 */
export interface AgentState {
  id: string;
  processedRequests?: string[];
  generatedArtifacts?: string[];
  consultationsProvided?: string[];
  consultationsReceived?: string[];
  lastUpdated?: string;
  feedback?: Array<{
    artifactId: string;
    from: string;
    content: string;
    timestamp: string;
  }>;
  feedbackReceived?: Array<{
    artifactId: string;
    from: string;
    content: string;
    timestamp: string;
  }>;
  agentStates?: {
    [agentName: string]: {
      processedRequests?: string[];
      generatedArtifacts?: string[];
      consultationsProvided?: string[];
      consultationsReceived?: string[];
      feedbackReceived?: any[];
      lastUpdated?: string;
      [key: string]: any;
    };
  };
}

/**
 * Workflow progress tracking
 */
export interface WorkflowProgress {
  marketResearchComplete: boolean;
  keywordResearchComplete: boolean;
  contentStrategyComplete: boolean;
  contentGenerationComplete: boolean;
  seoOptimizationComplete: boolean;
}

/**
 * Decision - represents an agent's reasoning and decision-making process
 */
export interface Decision {
  id: string;
  agent: string;
  timestamp: string;
  context?: string;
  reasoning?: string;
  outcome?: string;
  confidence?: number;
  originalReasoning?: any; // Store the original reasoning object
  [key: string]: any; // Allow for additional properties
}

/**
 * Discussion - represents a structured discussion between agents
 */
export type Discussion = {
  id: string;
  topic: string;
  participants: string[];
  leadAgent: string;
  status: 'active' | 'completed';
  messages: DiscussionMessage[];
  resolution?: string;
  createdAt: string;
  updatedAt: string;
};

export type DiscussionMessage = {
  id: string;
  agentId: string;
  content: any;
  type: 'perspective' | 'synthesis' | 'response';
  timestamp: string;
};

/**
 * Enhanced reasoning structure for agent decisions
 */
export type EnhancedReasoning = {
  process: string;
  steps: string[];
  timestamp: string;
  thoughts?: string[];
  considerations?: string[];
  decision?: string;
  confidence?: number;
  alternatives?: string[];
};

/**
 * Event - represents system events like circuit breakers
 */
export interface Event {
  type: 'CIRCUIT_BREAKER' | 'RATE_LIMIT' | 'CONSECUTIVE_MESSAGES' | 'SYSTEM';
  timestamp: string;
  data: any;
}

/**
 * Goal status in the collaborative process
 */
export type GoalStatus =
  | 'pending'
  | 'in-progress'
  | 'completed'
  | 'blocked'
  | 'failed'
  | 'inactive';

/**
 * Goal structure for tracking collaborative tasks
 */
export interface Goal {
  id: string;
  title: string;
  description: string;
  agent?: AgentId;
  status: GoalStatus;
  dependencies?: string[];
  dependentGoals?: string[];
  artifacts?: string[];
  createdAt: string;
  completedAt?: string;
  completionTime?: string;
  activationTime?: string;
  priority: 'high' | 'medium' | 'low';
  metadata?: Record<string, any>;
  condition?: {
    type: 'artifact' | 'message' | 'consultation' | 'time';
    artifactType?: string;
    messageType?: string;
    fromAgent?: string;
    toAgent?: string;
  };
}

/**
 * Iterative collaboration state
 */
export interface IterativeCollaborationState {
  id: string;
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  status: 'active' | 'paused' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  lastUpdateTime?: string;
  artifacts: Record<string, IterativeArtifact>;
  generatedArtifacts: string[]; // Array of artifact IDs for tracking
  consultations: Record<string, Consultation>;
  agentStates: Record<string, AgentState>;
  currentPhase?: 'planning' | 'research' | 'creation' | 'review' | 'refinement' | 'finalization' | 'error';
  collaborationState?: 'planning' | 'research' | 'creation' | 'review' | 'refinement' | 'finalization' | 'error';
  messages: any[];
  iterations: number;
  maxIterations: number;
  workflowProgress?: WorkflowProgress;
  decisions?: Decision[];
  discussions?: Record<string, Discussion>;
  events?: Event[];
  goals?: Goal[];
  sessionId?: string;
  clientName?: string;
  additionalInstructions?: string;
  agents?: Record<string, { active: boolean; [key: string]: any }>;
}

// Enhanced reasoning is already defined above

/**
 * Basic message structure for iterative collaboration
 */
export interface IterativeMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string | string[];
  type: IterativeMessageType;
  content: any;
  artifactId?: string;
  version?: number;
  conversationId: string;
  reasoning?: Reasoning | EnhancedReasoning;
  inReplyTo?: string; // Reference to the message this is replying to
  reasoningId?: string; // Reference to stored reasoning
  sessionId?: string;
  metadata?: {
    [key: string]: any;
    additionalInstructions?: string;
  };
}

/**
 * Consultation request message
 */
export interface ConsultationRequest extends IterativeMessage {
  type: IterativeMessageType.CONSULTATION_REQUEST;
  artifactId: string;
  version: number;
  questions: string[];
  focusAreas: string[];
}

/**
 * Consultation response message
 */
export interface ConsultationResponse extends IterativeMessage {
  type: IterativeMessageType.CONSULTATION_RESPONSE;
  artifactId: string;
  version: number;
  feedback: string;
  suggestions: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  consultationId: string;
}

/**
 * Iteration request message
 */
export interface IterationRequest extends IterativeMessage {
  type: IterativeMessageType.ITERATION_REQUEST;
  artifactId: string;
  currentVersion: number;
  feedbackIncorporated: string[]; // Consultation IDs
  requiredQualityScore: number;
}

/**
 * Iteration response message
 */
export interface IterationResponse extends IterativeMessage {
  type: IterativeMessageType.ITERATION_RESPONSE;
  artifactId: string;
  newVersion: number;
  changes: string;
  qualityScore: number;
  convergenceStatus: 'continuing' | 'converged';
}

/**
 * Configuration for the iterative collaboration
 */
export const ITERATIVE_CONFIG = {
  maxIterations: 5,
  qualityThreshold: 0.8,
  maxMessagesPerPhase: 10,
  circuitBreakerThreshold: 5,
  timeout: 60000 // ms
};

/**
 * Quality thresholds for different artifact types
 */
export const QUALITY_THRESHOLDS = {
  MARKET_RESEARCH: 0.75,
  KEYWORD_RESEARCH: 0.75,
  CONTENT_STRATEGY: 0.80,
  CONTENT_GENERATION: 0.75,
  SEO_OPTIMIZATION: 0.80,
  FEEDBACK: 0.70
};

/**
 * StandardizedHandlerResult - common return type for all agent message handlers
 */
export interface StandardizedHandlerResult {
  response?: IterativeMessage;
  success?: boolean;
  message?: string;
  error?: string;
  artifactUpdates?: {
    new?: Record<string, IterativeArtifact>;
    updated?: Record<string, IterativeArtifact>;
  };
  consultationUpdates?: {
    new?: Record<string, Consultation>;
    updated?: Record<string, Consultation>;
  };
  stateUpdates?: {
    [key: string]: any;
  };
  goals?: {
    new?: Goal[];
    completed?: string[];
  };
}

/**
 * Agent interface for collaborative agents
 */
export interface Agent {
  agentId: string;
  shouldAct(sessionId: string): Promise<boolean>;
  act(sessionId: string): Promise<void>;
  processMessage(sessionId: string, message: IterativeMessage): Promise<StandardizedHandlerResult | null>;
}
