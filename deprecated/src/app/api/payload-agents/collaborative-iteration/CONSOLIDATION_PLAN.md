# Agent System Consolidation Plan

## Current State
- `/agents/` - Clean, modular implementation with consistent structure
- `/server-based/` - Currently referenced by API routes but inconsistent

## Consolidation Strategy
We will combine both implementations into a single unified codebase by:

1. Using the `/agents/` directory structure as our target pattern
2. Copying the enhanced implementations from both directories as needed
3. Updating imports and references to maintain functionality
4. Removing all redundant code and deprecated implementations

## Implementation Steps

### Phase 1: Immediate Consolidation (Current)
1. Move all agent implementations to follow the `/agents/` pattern:
   - index.ts (main agent class)
   - handlers.ts (message handlers)
   - methods.ts (core logic with structured reasoning)

2. Update the main export file to reference the new consolidated agents

3. Delete all duplicate, deprecated, and redundant implementations

### Phase 2: API Route Updates (Next)
1. Update all API route imports to reference the consolidated agents
2. Ensure all agent references use the new consolidated structure
3. Validate functionality with integration tests

## Final Structure
After consolidation, we'll have a single, clean implementation for each agent:

```
/api/agents/collaborative-iteration/
├── agents/
│   ├── content-generation/
│   │   ├── index.ts
│   │   ├── handlers.ts
│   │   └── methods.ts
│   ├── content-strategy/
│   │   ├── index.ts
│   │   ├── handlers.ts
│   │   └── methods.ts
│   ├── market-research/
│   │   ├── index.ts
│   │   ├── handlers.ts
│   │   └── methods.ts
│   ├── seo-keyword/
│   │   ├── index.ts
│   │   ├── handlers.ts
│   │   └── methods.ts
│   └── seo-optimization/
│       ├── index.ts
│       ├── handlers.ts
│       └── methods.ts
├── core/
│   └── ... (core utilities)
├── utils/
│   └── ... (shared utilities)
└── index.ts (main export file)
```

This consolidated structure will maintain all enhanced functionality, including structured reasoning patterns, while eliminating redundancy and improving maintainability.
