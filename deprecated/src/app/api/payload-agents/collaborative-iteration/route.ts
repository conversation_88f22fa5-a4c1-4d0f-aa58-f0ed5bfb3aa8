// src/app/(payload)/api/agents/collaborative-iteration/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { IterativeMessage, IterativeMessageType } from './types';
import { collaborationController } from './utils/controllerAdapter';
import logger from './utils/logger';

// Import enhanced A2A integration
import {
  initiateEnhancedCollaborativeWorkflow,
  createEnhancedIterativeMessage
} from './enhanced-a2a-integration';

/**
 * POST - Start or continue an iterative collaboration session
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    logger.info('POST request received', { body: JSON.stringify(body) });

    // Check if this is a new session or continuing an existing one
    if (body.sessionId) {
      // Continue existing session with a message
      const { message, sessionId } = body;

      if (!message) {
        return NextResponse.json(
          { success: false, error: 'Message is required for existing sessions' },
          { status: 400 }
        );
      }

      logger.debug('Processing message for session', { sessionId });

      // Get current session state
      const session = await collaborationController.getSession(sessionId);

      if (!session) {
        return NextResponse.json(
          { success: false, error: 'Session not found' },
          { status: 404 }
        );
      }

      // Send the message
      const result = await collaborationController.sendMessage(sessionId, message);

      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.error || 'Failed to send message' },
          { status: 400 }
        );
      }

      // Get the updated session state
      const updatedSession = await collaborationController.getSession(sessionId);

      // Get message history
      const messageHistory = await collaborationController.getMessageHistory(sessionId);

      return NextResponse.json({
        success: true,
        sessionId,
        session: updatedSession,
        messageHistory: messageHistory.slice(-10) // Last 10 messages
      });
    } else {
      // Start a new session
      const {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords,
        additionalInstructions,
        clientName
      } = body;

      // Validate required fields
      if (!topic || !contentType || !targetAudience) {
        return NextResponse.json(
          { success: false, error: 'Missing required fields: topic, contentType, and targetAudience' },
          { status: 400 }
        );
      }

      // Create a new session
      const { sessionId, state } = await collaborationController.startSession(
        topic,
        contentType,
        targetAudience,
        tone || 'professional',
        keywords || [],
        additionalInstructions,
        clientName
      );

      logger.info('Created new collaboration session', { sessionId });

      // Create initial message to start the content creation process using enhanced A2A protocol
      const initialMessage = createEnhancedIterativeMessage(
        'user',
        'system',
        IterativeMessageType.SYSTEM_MESSAGE,
        {
          event: 'COLLABORATION_START',
          details: {
            topic,
            contentType,
            targetAudience,
            tone: tone || 'professional',
            keywords: keywords || []
          }
        },
        {
          sessionId,
          conversationId: sessionId,
          metadata: {
            clientName,
            additionalInstructions,
            useEnhancedA2A: true
          }
        }
      );

      // Start the enhanced collaborative workflow
      await initiateEnhancedCollaborativeWorkflow(
        sessionId,
        topic,
        {
          contentType,
          targetAudience,
          tone: tone || 'professional',
          keywords: keywords || [],
          additionalInstructions,
          clientName
        }
      );

      // Also send the initial message through the controller for backward compatibility
      await collaborationController.sendMessage(sessionId, initialMessage);

      // Get the current session state
      const updatedSession = await collaborationController.getSession(sessionId);

      return NextResponse.json({
        success: true,
        sessionId,
        session: updatedSession,
        initialMessage
      });
    }
  } catch (error) {
    logger.error('Error processing request', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    let errorMessage = 'An unexpected error occurred';

    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * GET - Retrieve a session state
 */
export async function GET(request: NextRequest) {
  try {
    // Get session ID from the URL
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('id');

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    logger.debug('Retrieving session', { sessionId });

    // Retrieve the session
    const session = await collaborationController.getSession(sessionId);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Session not found' },
        { status: 404 }
      );
    }

    // Get message history
    const messageHistory = await collaborationController.getMessageHistory(sessionId);

    return NextResponse.json({
      success: true,
      session,
      messageHistory: messageHistory.slice(-20) // Get the last 20 messages
    });
  } catch (error) {
    logger.error('Error retrieving session', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      { success: false, error: 'Failed to retrieve session' },
      { status: 500 }
    );
  }
}
