# Agent Implementation Standards

This document outlines the standardized implementation pattern for collaborative agents in the AuthenCIO system.

## Agent Structure

Each agent should follow a modular three-file structure:

### 1. `index.ts` (Main Agent Class)

- Contains the main agent class implementing the `Agent` interface
- Handles agent initialization, state management, and messaging
- Exports a singleton instance for use in the application
- Manages agent lifecycle and registration

```typescript
// Example index.ts structure
import { Agent, AgentId } from '../../types';
import { AgentHandler } from '../../core/AgentHandler';
import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';
import { handlers } from './handlers';

export class ExampleAgent implements Agent {
  agentId: AgentId = 'example-agent';
  private handler: AgentHandler;
  private stateManager: AgentStateManager;
  private messaging: AgentMessaging;
  
  constructor() {
    // Initialize agent components
    this.handler = new AgentHandler(this.agentId);
    this.stateManager = new AgentStateManager(this.agentId);
    this.messaging = new AgentMessaging();
    
    // Register message handlers
    this.registerHandlers();
  }
  
  private registerHandlers() {
    // Register all handlers from handlers.ts
  }
  
  // Required Agent interface methods
  async processMessage(sessionId: string, message: any) {
    // Process incoming messages
  }
}

// Export singleton instance
export const exampleAgent = new ExampleAgent();
```

### 2. `handlers.ts` (Message Handlers)

- Contains all message handler implementations
- Each handler processes a specific message type
- Follows a consistent structure for request processing
- Returns standardized handler results

```typescript
// Example handlers.ts structure
import { IterativeMessage, IterativeCollaborationState } from '../../types';
import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';
import * as methods from './methods';

// Handler for initial requests
export async function handleInitialRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
) {
  // Implementation details
}

// Handler for artifact requests
export async function handleArtifactRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
) {
  // Implementation details
}

// Export all handlers
export const handlers = {
  handleInitialRequest,
  handleArtifactRequest,
  // Other handlers...
};
```

### 3. `methods.ts` (Core Logic and LLM Integration)

- Contains the core business logic for the agent
- Implements LangGraph-inspired structured reasoning patterns
- Manages interactions with external APIs (OpenAI, etc.)
- Implements error handling and fallback strategies

```typescript
// Example methods.ts structure
import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';
import { IterativeArtifact, EnhancedReasoning } from '../../types';
import { createChainOfThoughtReasoning } from '../../utils/reasoningUtils';

// Initialize OpenAI if API key is available
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null;

// Core method example with structured reasoning
export async function analyzeContent(
  content: string,
  parameters: any = {}
): Promise<{
  analysis: string;
  reasoning?: EnhancedReasoning;
}> {
  // Implementation with structured reasoning
  const reasoningSteps = [
    "Step 1: Analyze raw data",
    "Step 2: Process information",
    "Step 3: Generate conclusions"
  ];
  
  try {
    // Logic implementation
    
    // Create structured reasoning
    const reasoning = createChainOfThoughtReasoning(
      "Analysis question",
      { context: "data" },
      reasoningSteps,
      "Final decision",
      "agent-id"
    );
    
    return {
      analysis: "Result",
      reasoning
    };
  } catch (error) {
    // Error handling with fallback reasoning
  }
}
```

## Structured Reasoning Implementation

All agent implementations should incorporate LangGraph-inspired structured reasoning following these patterns:

1. **Define Clear Reasoning Steps**: Each analysis should have explicit steps
2. **Collect Evidence for Each Step**: Gather supporting evidence during analysis
3. **Implement Error Handling**: Include fallback reasoning for error scenarios
4. **Use Chain-of-Thought Utilities**: Utilize the `createChainOfThoughtReasoning` utility
5. **Preserve Context**: Include full context in the reasoning structure

## Migration Path

Current implementations in `server-based/` will be gradually migrated to follow the structure in `agents/`. When implementing a new agent or updating an existing one, follow the pattern established in the `agents/` directory.
