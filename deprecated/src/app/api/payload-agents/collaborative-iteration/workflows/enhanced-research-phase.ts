import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessageType,
  AgentId,
  ArtifactStatus,
  Goal,
  GoalStatus
} from '../types';
import { EnhancedIterativeMessage, createEnhancedIterativeMessage } from '../types/enhanced-message';
import { stateStore } from '../utils/stateStore';
import { enhancedMessageBus } from '../utils/enhanced-message-bus';
import { validationManager } from '../utils/validation-manager';
import logger from '../utils/logger';
import { QUALITY_THRESHOLDS } from './enhanced-workflow-orchestrator';
import {
  marketResearchAgent,
  seoKeywordAgent,
  contentStrategyAgent
} from '../agents';

/**
 * Initiate the enhanced research phase
 */
export async function initiateEnhancedResearchPhase(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Update current phase
    await stateStore.updateState(sessionId, {
      currentPhase: 'research',
      workflowProgress: {
        ...state.workflowProgress,
        currentPhase: 'research'
      }
    });
    
    // Create initial research request message for Market Research agent
    const marketResearchRequest = createEnhancedIterativeMessage(
      'system',
      AgentId.MARKET_RESEARCH,
      IterativeMessageType.INITIAL_REQUEST,
      {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords: additionalParams.keywords || [],
        instructions: 'Conduct comprehensive market research analysis'
      },
      {
        sessionId,
        conversationId: additionalParams.discussionId || sessionId,
        reasoning: {
          thoughts: ['Beginning research phase with market research'],
          considerations: [
            'Need to understand target audience demographics and preferences',
            'Should identify market trends relevant to the topic',
            'Must analyze competitive landscape'
          ],
          decision: 'Request market research analysis as first step',
          confidence: 0.95
        },
        metadata: {
          workflowPhase: 'research',
          goalId: 'market-research-goal'
        }
      }
    );
    
    // Send market research request
    await enhancedMessageBus.sendMessage(sessionId, marketResearchRequest);
    
    // Update goal status
    await validationManager.updateGoalStatus(
      sessionId,
      'market-research-goal',
      GoalStatus.IN_PROGRESS,
      'Market research analysis requested'
    );
    
    logger.info(`Research phase initiated`, {
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      timestamp: new Date().toISOString()
    });
    
    // Schedule validation of research phase results
    setTimeout(() => {
      validateEnhancedResearchPhase(
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone,
        additionalParams
      ).catch((err: Error) => {
        logger.error(`Error validating research phase`, {
          sessionId,
          phase: 'research-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 60000); // Check after 1 minute
    
    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating research phase`, {
      sessionId,
      phase: 'research',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validate the enhanced research phase
 */
export async function validateEnhancedResearchPhase(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Skip if not in research phase
    if (state.currentPhase !== 'research') {
      logger.info(`Skipping research phase validation - current phase is ${state.currentPhase}`, {
        sessionId,
        currentPhase: state.currentPhase,
        timestamp: new Date().toISOString()
      });
      return false;
    }
    
    // Validate research phase
    const validationResult = await validationManager.validateResearchPhase(sessionId);
    
    if (validationResult.isValid) {
      logger.info(`Research phase validation passed`, {
        sessionId,
        timestamp: new Date().toISOString()
      });
      
      // Transition to content generation phase
      return await transitionToContentGeneration(
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone,
        additionalParams
      );
    } else {
      logger.info(`Research phase validation failed`, {
        sessionId,
        details: validationResult.details,
        timestamp: new Date().toISOString()
      });
      
      // Check if market research is complete
      if (!validationResult.missingArtifacts.includes('market-research-report') &&
          !validationResult.lowQualityArtifacts.includes('market-research-report')) {
        
        // If market research is complete but keyword research is not started, request it
        if (validationResult.missingArtifacts.includes('keyword-analysis')) {
          await requestKeywordResearch(
            sessionId,
            topic,
            contentType,
            targetAudience,
            tone,
            additionalParams
          );
        }
      }
      
      // Check if market research and keyword research are complete
      if (!validationResult.missingArtifacts.includes('market-research-report') &&
          !validationResult.lowQualityArtifacts.includes('market-research-report') &&
          !validationResult.missingArtifacts.includes('keyword-analysis') &&
          !validationResult.lowQualityArtifacts.includes('keyword-analysis')) {
        
        // If both are complete but content strategy is not started, request it
        if (validationResult.missingArtifacts.includes('content-strategy')) {
          await requestContentStrategy(
            sessionId,
            topic,
            contentType,
            targetAudience,
            tone,
            additionalParams
          );
        }
      }
      
      // Schedule another validation check
      setTimeout(() => {
        validateEnhancedResearchPhase(
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          additionalParams
        ).catch((err: Error) => {
          logger.error(`Error in scheduled research phase validation`, {
            sessionId,
            phase: 'research-validation',
            error: err.message || String(err),
            stack: err.stack
          });
        });
      }, 30000); // Check again after 30 seconds
      
      return false;
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating research phase`, {
      sessionId,
      phase: 'research-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Request keyword research
 */
async function requestKeywordResearch(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Create keyword research request
    const keywordResearchRequest = createEnhancedIterativeMessage(
      'system',
      AgentId.SEO_KEYWORD,
      IterativeMessageType.INITIAL_REQUEST,
      {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords: additionalParams.keywords || [],
        instructions: 'Conduct SEO keyword research and analysis'
      },
      {
        sessionId,
        conversationId: additionalParams.discussionId || sessionId,
        reasoning: {
          thoughts: ['Market research is complete, proceeding to keyword research'],
          considerations: [
            'Need to identify high-value keywords for SEO',
            'Should analyze keyword competition and search volume',
            'Must align keywords with target audience and content goals'
          ],
          decision: 'Request SEO keyword analysis as next step',
          confidence: 0.9
        },
        metadata: {
          workflowPhase: 'research',
          goalId: 'keyword-research-goal'
        }
      }
    );
    
    // Send keyword research request
    await enhancedMessageBus.sendMessage(sessionId, keywordResearchRequest);
    
    // Update goal status
    await validationManager.updateGoalStatus(
      sessionId,
      'keyword-research-goal',
      GoalStatus.IN_PROGRESS,
      'SEO keyword analysis requested'
    );
    
    logger.info(`Keyword research requested`, {
      sessionId,
      topic,
      timestamp: new Date().toISOString()
    });
    
    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error requesting keyword research`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Request content strategy
 */
async function requestContentStrategy(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Create content strategy request
    const contentStrategyRequest = createEnhancedIterativeMessage(
      'system',
      AgentId.CONTENT_STRATEGY,
      IterativeMessageType.INITIAL_REQUEST,
      {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords: additionalParams.keywords || [],
        instructions: 'Develop comprehensive content strategy'
      },
      {
        sessionId,
        conversationId: additionalParams.discussionId || sessionId,
        reasoning: {
          thoughts: ['Market research and keyword analysis are complete, proceeding to content strategy'],
          considerations: [
            'Need to develop content structure and outline',
            'Should incorporate market research insights',
            'Must optimize for identified keywords',
            'Should align with target audience preferences'
          ],
          decision: 'Request content strategy development as final research phase step',
          confidence: 0.9
        },
        metadata: {
          workflowPhase: 'research',
          goalId: 'content-strategy-goal'
        }
      }
    );
    
    // Send content strategy request
    await enhancedMessageBus.sendMessage(sessionId, contentStrategyRequest);
    
    // Update goal status
    await validationManager.updateGoalStatus(
      sessionId,
      'content-strategy-goal',
      GoalStatus.IN_PROGRESS,
      'Content strategy development requested'
    );
    
    logger.info(`Content strategy requested`, {
      sessionId,
      topic,
      timestamp: new Date().toISOString()
    });
    
    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error requesting content strategy`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Transition to content generation phase
 * This is imported by the workflow orchestrator
 */
async function transitionToContentGeneration(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Import here to avoid circular dependency
    const { transitionToContentGeneration: orchestratorTransition } = require('./enhanced-workflow-orchestrator');
    
    return await orchestratorTransition(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );
  } catch (error) {
    const err = error as Error;
    logger.error(`Error transitioning to content generation phase`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}
