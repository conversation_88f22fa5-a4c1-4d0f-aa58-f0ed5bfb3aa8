/**
 * Server-based components for the collaborative agent system
 *
 * This file exports server-side singleton instances of various components used
 * throughout the collaborative agent system. This allows for consistent state
 * management and communication between agents.
 */

import { v4 as uuidv4 } from 'uuid';
import { AgentIdEnum, type AgentId } from './utils/agentTypes';
import { stateStore } from './utils/stateStore'; // Import state store
import { enhancedMessageBus as messageBus } from './utils/enhancedMessageBus';
import logger from './utils/logger';

// Import all agent classes
import { ContentGenerationAgent } from './agents/content-generation';
import { MarketResearchAgent } from './agents/market-research';
import { SeoKeywordAgent } from './agents/seo-keyword';
import { ContentStrategyAgent } from './agents/content-strategy';
import { SeoOptimizationAgent } from './agents/seo-optimization';

// Create and export singleton instances of all components
export { stateStore }; // Use the imported state store

// Export the enhanced message bus
export { messageBus };

// Create helper to get a session-specific message bus
export function getSessionMessageBus(sessionId: string) {
  if (!sessionId) {
    logger.error('Attempted to create session message bus with undefined sessionId');
    throw new Error('Session ID is required');
  }

  logger.debug(`Creating session message bus for ${sessionId}`);
  return messageBus.createSessionBus(sessionId);
}

// Create and export singleton instances of all agents
export const contentGenerationAgent = new ContentGenerationAgent();
export const marketResearchAgent = new MarketResearchAgent();
export const seoKeywordAgent = new SeoKeywordAgent();
export const contentStrategyAgent = new ContentStrategyAgent();
export const seoOptimizationAgent = new SeoOptimizationAgent();

// Re-export agent types for use in other files
export { AgentIdEnum, type AgentId };
