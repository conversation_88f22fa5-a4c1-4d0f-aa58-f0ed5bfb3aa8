// src/app/(payload)/api/agents/collaborative-iteration/core/AgentMessaging.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  IterativeMessage, 
  IterativeArtifact, 
  Consultation,
  AgentId,
  IterativeMessageType
} from '../types';
import { AgentStateManager } from './AgentStateManager';
import { stateStore } from '../utils/stateStore';

/**
 * Agent Messaging
 * Handles message creation and delivery for agents
 */
export class AgentMessaging {
  private agentId: AgentId;
  private stateManager: AgentStateManager;
  
  constructor(agentId: AgentId, stateManager: AgentStateManager) {
    this.agentId = agentId;
    this.stateManager = stateManager;
  }
  
  /**
   * Create a standard message
   */
  createMessage(
    toAgent: AgentId | AgentId[],
    type: IterativeMessageType,
    content: any,
    conversationId: string,
    parentId?: string
  ): IterativeMessage {
    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: this.agentId,
      to: toAgent,
      type,
      content,
      conversationId,
      parentId
    };
  }
  
  /**
   * Send a message and store it in the state
   */
  async sendMessage(
    sessionId: string,
    message: IterativeMessage
  ): Promise<IterativeMessage> {
    await stateStore.updateState(sessionId, (state) => {
      if (!state.messages) {
        state.messages = [];
      }
      
      state.messages.push(message);
      return state;
    });
    
    return message;
  }
  
  /**
   * Create and send a message
   */
  async send(
    sessionId: string,
    toAgent: AgentId | AgentId[],
    type: IterativeMessageType,
    content: any,
    conversationId: string,
    parentId?: string
  ): Promise<IterativeMessage> {
    const message = this.createMessage(toAgent, type, content, conversationId, parentId);
    return await this.sendMessage(sessionId, message);
  }
  
  /**
   * Send an acknowledgment message
   */
  async sendAcknowledgment(
    sessionId: string,
    toAgent: AgentId,
    originalMessageId: string,
    message: string,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      toAgent,
      'ACKNOWLEDGMENT',
      {
        message,
        originalMessageId
      },
      conversationId,
      originalMessageId
    );
  }
  
  /**
   * Send an error message
   */
  async sendErrorMessage(
    sessionId: string,
    toAgent: AgentId,
    errorMessage: string,
    originalMessageId: string | undefined,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      toAgent,
      'ERROR',
      {
        message: errorMessage,
        originalMessageId
      },
      conversationId,
      originalMessageId
    );
  }
  
  /**
   * Send an artifact request
   */
  async sendArtifactRequest(
    sessionId: string,
    toAgent: AgentId,
    artifactType: string,
    description: string,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      toAgent,
      'ARTIFACT_REQUEST',
      {
        artifactType,
        description
      },
      conversationId
    );
  }
  
  /**
   * Send an artifact delivery
   */
  async sendArtifactDelivery(
    sessionId: string,
    toAgent: AgentId | AgentId[],
    artifact: IterativeArtifact,
    originalMessageId: string | undefined,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      toAgent,
      'ARTIFACT_DELIVERY',
      {
        artifactId: artifact.id,
        artifactType: artifact.type,
        originalMessageId
      },
      conversationId,
      originalMessageId
    );
  }
  
  /**
   * Send feedback
   */
  async sendFeedback(
    sessionId: string,
    toAgent: AgentId,
    feedbackContent: string,
    artifactId: string | undefined,
    originalMessageId: string | undefined,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      toAgent,
      'FEEDBACK',
      {
        feedback: feedbackContent,
        artifactId,
        originalMessageId
      },
      conversationId,
      originalMessageId
    );
  }
  
  /**
   * Send a consultation request
   */
  async sendConsultationRequest(
    sessionId: string,
    toAgent: AgentId,
    question: string,
    context: Record<string, any> | undefined,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      toAgent,
      'CONSULTATION_REQUEST',
      {
        question,
        context: context || {}
      },
      conversationId
    );
  }
  
  /**
   * Send a consultation response
   */
  async sendConsultationResponse(
    sessionId: string,
    toAgent: AgentId,
    consultation: Consultation,
    originalMessageId: string,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      toAgent,
      'CONSULTATION_RESPONSE',
      {
        consultationId: consultation.id,
        response: consultation.response,
        originalMessageId
      },
      conversationId,
      originalMessageId
    );
  }
  
  /**
   * Start a discussion
   */
  async startDiscussion(
    sessionId: string,
    participants: AgentId[],
    topic: string,
    initialContent: any,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      participants,
      'DISCUSSION_START',
      {
        topic,
        content: initialContent
      },
      conversationId
    );
  }
  
  /**
   * Contribute to a discussion
   */
  async contributeToDiscussion(
    sessionId: string,
    participants: AgentId[],
    discussionId: string,
    contribution: any,
    originalMessageId: string,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      participants,
      'DISCUSSION_CONTRIBUTION',
      {
        discussionId,
        contribution
      },
      conversationId,
      originalMessageId
    );
  }
  
  /**
   * Request discussion synthesis
   */
  async requestDiscussionSynthesis(
    sessionId: string,
    toAgent: AgentId,
    discussionId: string,
    conversationId: string
  ): Promise<IterativeMessage> {
    return await this.send(
      sessionId,
      toAgent,
      'DISCUSSION_SYNTHESIS_REQUEST',
      {
        discussionId
      },
      conversationId
    );
  }
  
  /**
   * Send a message and wait for a response
   */
  async sendMessageAndWaitForResponse(
    sessionId: string,
    message: IterativeMessage,
    timeoutMs: number = 30000
  ): Promise<IterativeMessage | null> {
    // Send the message
    await this.sendMessage(sessionId, message);
    
    // Wait for a response
    const startTime = Date.now();
    while (Date.now() - startTime < timeoutMs) {
      // Get the current state
      const state = await this.stateManager.getSessionState(sessionId);
      if (!state || !state.messages) {
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      // Look for a response message
      const responses = state.messages.filter(m => 
        (m.to === this.agentId || (Array.isArray(m.to) && m.to.includes(this.agentId))) &&
        m.from === message.to &&
        m.conversationId === message.conversationId &&
        m.parentId === message.id
      );
      
      if (responses.length > 0) {
        return responses[responses.length - 1];
      }
      
      // Wait a short time before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // No response received within timeout
    return null;
  }
}
