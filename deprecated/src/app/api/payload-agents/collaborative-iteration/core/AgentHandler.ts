// src/app/(payload)/api/agents/collaborative-iteration/core/AgentHandler.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeCollaborationState,
  IterativeArtifact,
  Consultation,
  AgentId,
  StandardizedHandlerResult,
  IterativeMessageType,
  ArtifactStatus
} from '../types';
import { AgentStateManager } from './AgentStateManager';
import { AgentMessaging } from './AgentMessaging';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';

/**
 * Message handler type
 */
export type MessageHandler = (
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
) => Promise<StandardizedHandlerResult>;

/**
 * Agent Handler
 * Processes incoming messages and manages agent-specific logic
 */
export class AgentHandler {
  private agentId: AgentId;
  private stateManager: AgentStateManager;
  private messaging: AgentMessaging;
  private handlers: Record<string, MessageHandler>;

  constructor(agentId: AgentId) {
    this.agentId = agentId;
    this.stateManager = new AgentStateManager(agentId);
    this.messaging = new AgentMessaging(agentId, this.stateManager);
    this.handlers = {};
  }

  /**
   * Register a message handler for a specific message type
   */
  registerHandler(messageType: IterativeMessageType, handler: MessageHandler): void {
    this.handlers[messageType] = handler;
  }

  /**
   * Process an incoming message
   */
  async processMessage(
    sessionId: string,
    message: IterativeMessage
  ): Promise<StandardizedHandlerResult | null> {
    // Get the current state
    const state = await this.stateManager.getSessionState(sessionId);

    if (!state) {
      console.error(`No state found for session ${sessionId}`);
      return null;
    }

    // Check if this message is intended for this agent
    if (typeof message.to === 'string' && message.to !== this.agentId && message.to !== 'all') {
      return null;
    }

    if (Array.isArray(message.to) && !message.to.includes(this.agentId) && !message.to.includes('all')) {
      return null;
    }

    // Check if this message has already been processed
    const isProcessed = await this.stateManager.hasProcessedMessage(sessionId, message.id);

    if (isProcessed) {
      return null;
    }

    // Validate message type - ensure it's not undefined
    if (!message.type) {
      logger.warn(`Message type is undefined for message ${message.id} from ${message.from} to ${this.agentId}`, {
        sessionId,
        messageId: message.id,
        from: message.from,
        to: this.agentId
      });

      // Set a default type for messages with undefined type
      message.type = IterativeMessageType.REQUEST;
      logger.info(`Setting default message type 'REQUEST' for message ${message.id}`, {
        sessionId,
        messageId: message.id
      });
    }

    // Get the handler for this message type
    const handler = this.handlers[message.type];

    // Try to use a default handler if no specific handler is found
    if (!handler) {
      logger.warn(`No handler registered for message type ${message.type} in agent ${this.agentId}`, {
        sessionId,
        messageId: message.id,
        messageType: message.type,
        agentId: this.agentId
      });

      // Try to use the REQUEST handler as a fallback if available
      const defaultHandler = this.handlers[IterativeMessageType.REQUEST];
      if (defaultHandler) {
        logger.info(`Using default REQUEST handler for message type ${message.type} in agent ${this.agentId}`, {
          sessionId,
          messageId: message.id,
          messageType: message.type,
          agentId: this.agentId
        });

        try {
          // Process the message with the default handler
          const result = await defaultHandler(message, state, this.stateManager, this.messaging);

          // Track this message as processed
          await this.stateManager.trackProcessedMessage(sessionId, message.id);

          return result;
        } catch (error) {
          const err = error as Error;
          logger.error(`Error processing message ${message.id} with default handler:`, {
            sessionId,
            messageId: message.id,
            error: err.message,
            stack: err.stack
          });
        }
      }

      // Create a default acknowledgment if no default handler or it failed
      const response = await this.messaging.sendAcknowledgment(
        sessionId,
        message.from,
        message.id,
        `No handler available for message type ${message.type}`,
        message.conversationId
      );

      // Track this message as processed
      await this.stateManager.trackProcessedMessage(sessionId, message.id);

      return {
        success: false,
        message: `No handler registered for message type ${message.type}`,
        error: null,
        response
      };
    }

    try {
      // Process the message with the handler
      const result = await handler(message, state, this.stateManager, this.messaging);

      // Track this message as processed
      await this.stateManager.trackProcessedMessage(sessionId, message.id);

      return result;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing message ${message.id} by agent ${this.agentId}:`, {
        sessionId,
        messageId: message.id,
        agentId: this.agentId,
        error: err.message,
        stack: err.stack
      });

      // Create an error response
      const response = await this.messaging.sendErrorMessage(
        sessionId,
        message.from,
        `Error processing message: ${err.message}`,
        message.id,
        message.conversationId
      );

      // Track this message as processed
      await this.stateManager.trackProcessedMessage(sessionId, message.id);

      return {
        success: false,
        message: `Error processing message: ${err.message}`,
        error: err.message,
        response
      };
    }
  }

  /**
   * Create a new artifact with standardized structure
   * @param name The name of the artifact
   * @param type The type of the artifact (e.g., 'market-research', 'seo-keywords')
   * @param content The content of the artifact
   * @param status The initial status of the artifact
   * @param metadata Additional metadata for the artifact
   * @returns The created artifact
   */
  createArtifact(
    name: string,
    type: string,
    content: any,
    status: ArtifactStatus = ArtifactStatus.DRAFT,
    metadata: Record<string, any> = {}
  ): IterativeArtifact {
    const timestamp = new Date().toISOString();
    const id = uuidv4();

    const artifact: IterativeArtifact = {
      id,
      name,
      type,
      createdBy: this.agentId,
      createdAt: timestamp,
      updatedAt: timestamp,
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp,
          agent: this.agentId,
          content,
          feedback: [],
          incorporatedConsultations: []
        }
      ],
      status,
      qualityScore: 0.8, // Default quality score
      content,
      metadata: {
        ...metadata,
        createdAt: timestamp,
        createdBy: this.agentId
      }
    };

    return artifact;
  }

  /**
   * Create a new consultation
   */
  createConsultation(
    toAgent: AgentId,
    question: string,
    context?: any,
    response?: any
  ): Consultation {
    return {
      id: uuidv4(),
      fromAgent: this.agentId,
      toAgent,
      question,
      context: context || {},
      response,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get the state manager
   */
  getStateManager(): AgentStateManager {
    return this.stateManager;
  }

  /**
   * Get the messaging utility
   */
  getMessaging(): AgentMessaging {
    return this.messaging;
  }

  /**
   * Get the agent ID
   */
  getAgentId(): AgentId {
    return this.agentId;
  }
}

/**
 * Create an agent handler for a specific agent
 */
export function createAgentHandler(agentId: AgentId): AgentHandler {
  return new AgentHandler(agentId);
}
