// src/app/(payload)/api/agents/collaborative-iteration/core/AgentBase.ts

import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';
import {
  Agent,
  AgentId,
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  StandardizedHandlerResult,
  Goal,
  IterativeArtifact,
  ArtifactStatus,
  EnhancedReasoning
} from '../types';
import { AgentHandler } from './AgentHandler';
import { AgentStateManager } from './AgentStateManager';
import { AgentMessaging } from './AgentMessaging';
import { stateStore } from '../utils/stateStore';
import { createEnhancedReasoning } from '../utils/reasoningUtils';
import logger from '../utils/logger';

/**
 * Message handler function type
 */
export type MessageHandler = (
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
) => Promise<StandardizedHandlerResult>;

/**
 * Base class for all collaborative agents
 * Provides common functionality and standardized implementation
 */
export abstract class AgentBase implements Agent {
  agentId: AgentId;
  protected handler: AgentHandler;
  protected stateManager: AgentStateManager;
  protected messaging: AgentMessaging;
  protected openai: OpenAI | null = null;

  /**
   * Constructor for the agent base class
   * @param agentId The ID of the agent
   */
  constructor(agentId: AgentId) {
    this.agentId = agentId;
    this.handler = new AgentHandler(agentId);
    this.stateManager = new AgentStateManager(agentId);
    this.messaging = new AgentMessaging(agentId, this.stateManager);

    // Initialize OpenAI if API key is available
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      });
    }

    // Register message handlers
    this.registerHandlers();
  }

  /**
   * Register message handlers for this agent
   * Must be implemented by derived classes
   */
  protected abstract registerHandlers(): void;

  /**
   * Process an incoming message
   * @param sessionId The session ID
   * @param message The message to process
   * @returns The result of processing the message
   */
  async processMessage(
    sessionId: string,
    message: IterativeMessage
  ): Promise<StandardizedHandlerResult | null> {
    logger.info(`${this.agentId} Agent: Processing message of type ${message.type} from ${message.from}`, {
      sessionId,
      messageId: message.id,
      messageType: message.type
    });

    // Validate message type
    if (!message.type || !Object.values(IterativeMessageType).includes(message.type as IterativeMessageType)) {
      logger.warn(`Invalid message type: ${message.type}. Setting to REQUEST as default.`, {
        sessionId,
        messageId: message.id,
        originalType: message.type
      });
      message.type = IterativeMessageType.REQUEST;
    }

    return await this.handler.processMessage(sessionId, message);
  }

  /**
   * Determine if this agent should act in the current turn
   * @param sessionId The session ID
   * @returns True if the agent should act, false otherwise
   */
  async shouldAct(sessionId: string): Promise<boolean> {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) return false;

    // Check if there are any goals assigned to this agent
    const hasAssignedGoals = state.goals?.some(
      (goal) => goal.assignedTo === this.agentId && goal.status === 'active'
    ) || false;

    // Check if there are any messages addressed to this agent that haven't been processed
    const hasUnprocessedMessages = state.messages?.some(
      (message) =>
        (message.to === this.agentId || (Array.isArray(message.to) && message.to.includes(this.agentId))) &&
        !this.stateManager.hasProcessedMessageSync(sessionId, message.id)
    ) || false;

    return hasAssignedGoals || hasUnprocessedMessages;
  }

  /**
   * Act on the current state
   * @param sessionId The session ID
   */
  async act(sessionId: string): Promise<void> {
    logger.info(`${this.agentId} Agent: Acting on session ${sessionId}`, {
      sessionId,
      agent: this.agentId
    });

    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`${this.agentId} Agent: No state found for session ${sessionId}`, {
        sessionId,
        agent: this.agentId
      });
      return;
    }

    // Process any active goals assigned to this agent
    const activeGoals = state.goals?.filter(
      (goal) => goal.assignedTo === this.agentId && goal.status === 'active'
    ) || [];

    for (const goal of activeGoals) {
      await this.processGoal(sessionId, goal);
    }

    // Process any unprocessed messages addressed to this agent
    const unprocessedMessages = state.messages?.filter(
      (message) =>
        (message.to === this.agentId || (Array.isArray(message.to) && message.to.includes(this.agentId))) &&
        !this.stateManager.hasProcessedMessageSync(sessionId, message.id)
    ) || [];

    for (const message of unprocessedMessages) {
      await this.processMessage(sessionId, message);
    }
  }

  /**
   * Process a goal assigned to this agent
   * @param sessionId The session ID
   * @param goal The goal to process
   */
  protected async processGoal(sessionId: string, goal: Goal): Promise<void> {
    logger.info(`${this.agentId} Agent: Processing goal - ${goal.name}`, {
      sessionId,
      goalId: goal.id,
      agent: this.agentId
    });

    // Create a message to handle the goal
    const goalMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'orchestrator',
      to: this.agentId,
      type: IterativeMessageType.REQUEST,
      content: {
        goalId: goal.id,
        name: goal.name,
        description: goal.description,
        parameters: goal.metadata || {}
      },
      conversationId: sessionId
    };

    // Process the goal as a message
    await this.processMessage(sessionId, goalMessage);
  }

  /**
   * Create a standardized artifact
   * @param name The name of the artifact
   * @param type The type of the artifact
   * @param content The content of the artifact
   * @param status The status of the artifact
   * @param metadata Additional metadata for the artifact
   * @returns The created artifact
   */
  protected createArtifact(
    name: string,
    type: string,
    content: any,
    status: ArtifactStatus = ArtifactStatus.COMPLETED,
    metadata: Record<string, any> = {}
  ): IterativeArtifact {
    const timestamp = new Date().toISOString();
    const id = uuidv4();

    return {
      id,
      name,
      type,
      createdBy: this.agentId,
      createdAt: timestamp,
      updatedAt: timestamp,
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp,
          agent: this.agentId,
          content,
          feedback: [],
          incorporatedConsultations: []
        }
      ],
      status,
      qualityScore: 0.8, // Default quality score
      content,
      metadata: {
        ...metadata,
        createdAt: timestamp,
        createdBy: this.agentId
      }
    };
  }

  /**
   * Create enhanced reasoning for agent decisions
   * @param context The context for the reasoning
   * @param thoughts The agent's thoughts
   * @param conclusion The conclusion reached
   * @param confidence The confidence level (0-1)
   * @returns Enhanced reasoning object
   */
  protected createReasoning(
    context: Record<string, any>,
    thoughts: string[],
    conclusion: string,
    confidence: number = 0.8
  ): EnhancedReasoning {
    return createEnhancedReasoning(
      context,
      thoughts,
      conclusion,
      this.agentId,
      {
        confidence,
        steps: thoughts.map(t => t.substring(0, 50)) // Create steps from thoughts
      }
    );
  }
}
