// src/app/(payload)/api/agents/research.ts
import { ChatOpenAI } from "@langchain/openai";
import { StateGraph, END, START } from "@langchain/langgraph";
import { RunnableLambda } from "@langchain/core/runnables";
import { SystemMessage } from "@langchain/core/messages";
import { JsonOutputParser } from "@langchain/core/output_parsers";
import { stat } from "fs";

// Define the state interface for our research agent
export interface ResearchAgentState {
  productName: string;
  category: string;
  researchDepth: 'basic' | 'standard' | 'comprehensive';
  sellerName?: string;
  existingSeller?: string | null;
  productResult?: any;
  sellerResult?: any;
  featuresResult?: any;
  pricingPlansResult?: any;
  faqsResult?: any;
  error?: string;
}

// Helper to create OpenAI model instances
function createModel(temp: number = 0) {
  return new ChatOpenAI({
    temperature: temp,
    modelName: "gpt-3.5-turbo-1106",
  });
}

// Create the base model with output parsing for consistent JSON results
const baseModel = createModel(0.7).pipe(new JsonOutputParser());

// Configure the depth settings based on research level
const GENERATION_CONFIG = {
  basic: {
    featureCount: 4,
    pricingPlans: 2,
    faqCount: 4,
    detailLevel: "brief",
  },
  standard: {
    featureCount: 7,
    pricingPlans: 3,
    faqCount: 7,
    detailLevel: "moderate",
  },
  comprehensive: {
    featureCount: 12,
    pricingPlans: 4,
    faqCount: 10,
    detailLevel: "detailed",
  }
};

// Feature categories map based on industry
const FEATURE_CATEGORIES = {
  crm: [
    'find-attract-leads',
    'close-more-deals',
    'support-retain-customers',
    'understand-whats-working',
    'automate-save-time'
  ],
  marketing: [
    'find-attract-leads',
    'automate-save-time',
    'customize-connect',
    'understand-whats-working',
    'collaborate-teams'
  ],
  sales: [
    'close-more-deals',
    'understand-whats-working',
    'automate-save-time',
    'collaborate-teams',
    'customize-connect'
  ]
};

// FAQ categories
const FAQ_CATEGORIES = [
  'general',
  'features',
  'pricing',
  'support',
  'implementation',
  'alternatives'
];

// Define the state channel for our graph
const researchAgentState = {
  agentState: {
    value: (x: ResearchAgentState, y: ResearchAgentState) => (y ? Object.assign({}, x, y) : x),
    default: () => ({
      productName: "",
      category: "crm",
      researchDepth: "standard"
    }),
  },
};

// Research agents for each component of the research process

// 1. Product Research Agent
async function productResearchAgent(state: {
  agentState: ResearchAgentState;
}): Promise<{ agentState: ResearchAgentState }> {
  console.log("productResearchAgent", state.agentState);
  try {
    const config = GENERATION_CONFIG[state.agentState.researchDepth];
    
    const productPrompt = `
You are an expert product researcher for ${state.agentState.category.toUpperCase()} software products. I need you to generate detailed information about a product named "${state.agentState.productName}".

Provide the following information in a JSON format:
1. A comprehensive overview of the product, including its key value propositions and main use cases
2. Expert view with strengths (3-5), considerations (2-3), and weaknesses (1-2)
3. Tags related to this product (4-7 tags)

The JSON should have this structure:
{
  "overview": "...",
  "expertView": {
    "strengths": [{"text": "..."}, ...],
    "considerations": [{"text": "..."}, ...],
    "weaknesses": [{"text": "..."}, ...]
  },
  "meta": {
    "tags": [{"tag": "..."}, ...]
  }
}

Detail level should be ${config.detailLevel}. Ensure all information sounds realistic and professional.
`;

    const response = await baseModel.invoke([
      new SystemMessage(
        "You are a product research expert with deep knowledge of software products."
      ),
      new SystemMessage(productPrompt)
    ]);

    return {
      agentState: {
        ...state.agentState,
        productResult: response,
      },
    };
  } catch (error) {
    return {
      agentState: {
        ...state.agentState,
        error: `Error in product research: ${error instanceof Error ? error.message : String(error)}`,
      },
    };
  }
}

// 2. Seller Research Agent
async function sellerResearchAgent(state: {
  agentState: ResearchAgentState;
}): Promise<{ agentState: ResearchAgentState }> {
  try {
    // Skip if we're using an existing seller
    if (state.agentState.existingSeller) {
      return { agentState: state.agentState };
    }
    
    // Skip if no seller name provided
    if (!state.agentState.sellerName) {
      return { agentState: state.agentState };
    }
    
    const config = GENERATION_CONFIG[state.agentState.researchDepth];
    
    const sellerPrompt = `
You are an expert company researcher. I need you to generate detailed information about a company named "${state.agentState.sellerName}" that makes the software product "${state.agentState.productName}" in the ${state.agentState.category} industry.

Provide the following information in a JSON format:
1. A concise but informative company description
2. Year founded (a realistic year, not too recent and not too old)
3. Headquarters location (city and country)
4. Company size category
5. 3-5 key facts about the company
6. 2-3 industries the company serves

The JSON should have this structure:
{
  "description": "...",
  "foundedYear": YYYY,
  "headquarters": "City, Country",
  "companySize": "startup|small|medium|large|enterprise",
  "keyFacts": [{"fact": "..."}, ...],
  "industries": [{"industry": "..."}, ...]
}

Detail level should be ${config.detailLevel}. Ensure all information sounds realistic and professional.
`;

    const response = await baseModel.invoke([
      new SystemMessage(
        "You are a corporate research expert with deep knowledge of the tech industry."
      ),
      new SystemMessage(sellerPrompt)
    ]);

    return {
      agentState: {
        ...state.agentState,
        sellerResult: response,
      },
    };
  } catch (error) {
    return {
      agentState: {
        ...state.agentState,
        error: `Error in seller research: ${error instanceof Error ? error.message : String(error)}`,
      },
    };
  }
}

// 3. Features Research Agent
async function featuresResearchAgent(state: {
  agentState: ResearchAgentState;
}): Promise<{ agentState: ResearchAgentState }> {
  try {
    const config = GENERATION_CONFIG[state.agentState.researchDepth];
    const featureCategories = FEATURE_CATEGORIES[state.agentState.category as keyof typeof FEATURE_CATEGORIES] || 
                             FEATURE_CATEGORIES.crm;
    
    // Use the product overview from the product research step
    const productOverview = state.agentState.productResult?.overview || "";
    
    const featuresPrompt = `
You are a product feature expert for ${state.agentState.category.toUpperCase()} software. I need you to generate ${config.featureCount} features for a product named "${state.agentState.productName}".

The product overview is: "${productOverview.substring(0, 300)}..."

For each feature, provide:
1. A concise feature title
2. A detailed description of the feature and its benefits
3. A category for the feature (choose from: ${featureCategories.join(', ')})
4. An appropriate icon name from this list: Star, MessageSquare, Zap, FileText, Users, Shield, Clock, Calendar, CheckCircle2, Database, Settings, CloudCog, TrendingUp

The response should be a JSON array with this structure:
[
  {
    "title": "...",
    "description": "...",
    "category": "...",
    "icon": "..."
  },
  ...
]

Ensure the features are varied across different categories. Detail level should be ${config.detailLevel}.
`;

    const response = await baseModel.invoke([
      new SystemMessage(
        "You are a product feature specialist with deep knowledge of software products."
      ),
      new SystemMessage(featuresPrompt)
    ]);

    // Add theme colors to the features
    const themeColors = ['blue', 'purple', 'indigo', 'violet', 'sky', 'navy', 'teal'];
    const enhancedFeatures = Array.isArray(response) ? 
      response.map(feature => ({
        ...feature, 
        themeColor: themeColors[Math.floor(Math.random() * themeColors.length)]
      })) : [];

    return {
      agentState: {
        ...state.agentState,
        featuresResult: enhancedFeatures,
      },
    };
  } catch (error) {
    return {
      agentState: {
        ...state.agentState,
        error: `Error in features research: ${error instanceof Error ? error.message : String(error)}`,
      },
    };
  }
}

// 4. Pricing Plans Research Agent
async function pricingPlansResearchAgent(state: {
  agentState: ResearchAgentState;
}): Promise<{ agentState: ResearchAgentState }> {
  try {
    const config = GENERATION_CONFIG[state.agentState.researchDepth];
    
    // Use the product overview from the product research step
    const productOverview = state.agentState.productResult?.overview || "";
    
    const pricingPrompt = `
You are a pricing strategy expert for ${state.agentState.category.toUpperCase()} software. I need you to generate ${config.pricingPlans} pricing plans for a product named "${state.agentState.productName}".

The product overview is: "${productOverview.substring(0, 300)}..."

For each pricing plan, provide:
1. A title (e.g., "Basic", "Pro", "Enterprise")
2. A short subtitle
3. A realistic price (usually monthly per user)
4. Billing frequency text
5. A short description of who this plan is best for
6. 4-6 key features included in this plan

The response should be a JSON array with this structure:
[
  {
    "title": "...",
    "subtitle": "...",
    "price": "$XX",
    "billingFrequency": "/user/month",
    "description": "...",
    "features": [{"feature": "..."}, ...],
    "popular": true/false
  },
  ...
]

Make one plan marked as "popular" (usually the middle tier). Ensure the pricing strategy makes sense for ${state.agentState.category} software and shows logical progression between tiers.
`;

    const response = await baseModel.invoke([
      new SystemMessage(
        "You are a SaaS pricing specialist with deep knowledge of software business models."
      ),
      new SystemMessage(pricingPrompt)
    ]);

    return {
      agentState: {
        ...state.agentState,
        pricingPlansResult: response,
      },
    };
  } catch (error) {
    return {
      agentState: {
        ...state.agentState,
        error: `Error in pricing plans research: ${error instanceof Error ? error.message : String(error)}`,
      },
    };
  }
}

// 5. FAQs Research Agent
async function faqsResearchAgent(state: {
  agentState: ResearchAgentState;
}): Promise<{ agentState: ResearchAgentState }> {
  console.log("faqsResearchAgent", state.agentState);
  try {
    const config = GENERATION_CONFIG[state.agentState.researchDepth];
    
    // Use the product overview from the product research step
    const productOverview = state.agentState.productResult?.overview || "";
    
    // Mix up categories based on FAQ count
    const categoriesMap: {[key: string]: number} = {};
    const totalCategories = FAQ_CATEGORIES.length;
    
    // Distribute FAQs across categories
    for (let i = 0; i < config.faqCount; i++) {
      const categoryIndex = i % totalCategories;
      const category = FAQ_CATEGORIES[categoryIndex];
      categoriesMap[category] = (categoriesMap[category] || 0) + 1;
    }
    
    const faqPrompt = `
You are a content specialist for ${state.agentState.category.toUpperCase()} software documentation. I need you to generate ${config.faqCount} frequently asked questions (FAQs) for a product named "${state.agentState.productName}".

The product overview is: "${productOverview.substring(0, 300)}..."

For each FAQ, provide:
1. A question from a potential customer's perspective
2. A clear, helpful answer
3. A category from this list: ${FAQ_CATEGORIES.join(', ')}

The distribution of FAQs across categories should roughly be:
${Object.entries(categoriesMap).map(([cat, count]) => `- ${cat}: ${count}`).join('\n')}

The response should be a JSON array with this structure:
[
  {
    "question": "...",
    "answer": "...",
    "category": "..."
  },
  ...
]

Ensure the FAQs cover common customer questions about the product, pricing, support, etc. The answers should be informative but concise.
`;

    const response = await baseModel.invoke([
      new SystemMessage(
        "You are a technical writer specializing in software documentation."
      ),
      new SystemMessage(faqPrompt)
    ]);

    // Format FAQs for rich text storage
    const formattedFaqs = Array.isArray(response) ? 
      response.map((faq, index) => ({
        question: faq.question,
        answer: { 
          root: {
            type: 'root',
            children: [{ 
              type: 'paragraph', 
              children: [{ text: faq.answer }],
              direction: null,
              format: '',
              indent: 0,
              version: 1
            }],
            direction: null,
            format: '',
            indent: 0,
            version: 1
          }
        },
        category: faq.category,
        order: index,
        featured: index < 2 // Make the first two FAQs featured
      })) : [];

    return {
      agentState: {
        ...state.agentState,
        faqsResult: formattedFaqs,
      },
    };
  } catch (error) {
    return {
      agentState: {
        ...state.agentState,
        error: `Error in FAQs research: ${error instanceof Error ? error.message : String(error)}`,
      },
    };
  }
}

// Build the research graph
const researchGraph = new StateGraph<Record<string, any>>({
  channels: researchAgentState,
})
  .addNode("productResearch", new RunnableLambda({ func: productResearchAgent }) as any)
  .addNode("sellerResearch", new RunnableLambda({ func: sellerResearchAgent }) as any)
  .addNode("featuresResearch", new RunnableLambda({ func: featuresResearchAgent }) as any)
  .addNode("pricingPlansResearch", new RunnableLambda({ func: pricingPlansResearchAgent }) as any)
  .addNode("faqsResearch", new RunnableLambda({ func: faqsResearchAgent }) as any)
  .addEdge(START, "productResearch")
  .addEdge("productResearch", "sellerResearch")
  .addEdge("sellerResearch", "featuresResearch")
  .addEdge("featuresResearch", "pricingPlansResearch")
  .addEdge("pricingPlansResearch", "faqsResearch")
  .addEdge("faqsResearch", END);

// Compile the graph into an executable
export const researchExecutor = researchGraph.compile();

// Main function to execute the research process
export async function executeResearch(data: {
  productName: string;
  category: string;
  generateSeller: boolean;
  sellerName?: string;
  existingSeller?: string | null;
  researchDepth: 'basic' | 'standard' | 'comprehensive';
  generateFeatures: boolean;
  generatePricing: boolean;
  generateFAQs: boolean;
  preview: boolean;
}): Promise<any> {
  try {
    // Prepare input state for the graph
    const inputs = {
      agentState: {
        productName: data.productName,
        category: data.category,
        researchDepth: data.researchDepth,
        sellerName: data.sellerName || "",
        existingSeller: data.existingSeller || null
      },
    };

    // Execute the research process
    const result = await researchExecutor.invoke(inputs);
    
    // Format the final results
    const finalResults = {
      product: {
        name: data.productName,
        slug: data.productName.toLowerCase().replace(/ /g, '-'),
        overview: result.agentState.productResult?.overview || "",
        meta: {
          industryCategory: data.category,
          tags: result.agentState.productResult?.meta?.tags || []
        },
        expertView: result.agentState.productResult?.expertView || {}
      },
      seller: data.generateSeller ? 
        (data.existingSeller ? { id: data.existingSeller } : result.agentState.sellerResult) : null,
      features: data.generateFeatures ? result.agentState.featuresResult : [],
      pricingPlans: data.generatePricing ? result.agentState.pricingPlansResult : [],
      faqs: data.generateFAQs ? result.agentState.faqsResult : []
    };

    return {
      success: true,
      message: 'Research completed successfully',
      preview: finalResults,
      error: result.agentState.error
    };
  } catch (error) {
    return {
      success: false,
      error: `Research execution failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}