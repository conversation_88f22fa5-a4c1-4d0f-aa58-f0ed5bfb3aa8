/**
 * Dynamic Collaboration System Types (V2)
 *
 * This file contains type definitions for the new dynamic agent collaboration system.
 */

import { v4 as uuidv4 } from 'uuid';
import { IterativeArtifact, IterativeMessage, Reasoning } from '../collaborative-iteration/types';

/**
 * Content generation request parameters
 */
export interface ContentGenerationParams {
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  additionalParams?: Record<string, any>;
}

/**
 * Dynamic message types for agent communication
 */
export enum DynamicMessageType {
  REQUEST_INFORMATION = 'request_information',
  PROVIDE_INFORMATION = 'provide_information',
  REQUEST_COLLABORATION = 'request_collaboration',
  PROVIDE_COLLABORATION = 'provide_collaboration',
  REQUEST_FEEDBACK = 'request_feedback',
  PROVIDE_FEEDBACK = 'provide_feedback',
  SYSTEM_MESSAGE = 'system_message',
  SYSTEM_NOTIFICATION = 'system_notification',
  GOAL_UPDATE = 'goal_update',
  ARTIFACT_UPDATE = 'artifact_update',
  ARTIFACT_CREATED = 'artifact_created',
  ARTIFACT_UPDATED = 'artifact_updated',
  WORKFLOW_TRANSITION = 'workflow_transition',
  USER_MESSAGE = 'user_message',
  AGENT_MESSAGE = 'agent_message',
  FEEDBACK_REQUEST = 'feedback_request',
  FEEDBACK_RESPONSE = 'feedback_response',
  FEEDBACK = 'feedback'
}

/**
 * Dynamic agent message for communication
 */
export interface DynamicAgentMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string | string[];
  type: DynamicMessageType;
  content: any;
  replyTo?: string;
  conversationId: string;
  reasoning?: Reasoning;
  metadata?: Record<string, any>;
}

/**
 * Goal types for content generation
 */
export enum GoalType {
  MARKET_RESEARCH = 'market_research',
  KEYWORD_ANALYSIS = 'keyword_analysis',
  CONTENT_STRATEGY = 'content_strategy',
  CONTENT_CREATION = 'content_creation',
  SEO_OPTIMIZATION = 'seo_optimization',
  QUALITY_ASSESSMENT = 'quality_assessment'
}

/**
 * Goal status in the dynamic workflow
 */
export enum GoalStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  BLOCKED = 'blocked',
  FAILED = 'failed'
}

/**
 * Content goal for dynamic collaboration
 */
export interface ContentGoal {
  id: string;
  description: string;
  type: GoalType;
  status: GoalStatus;
  assignedTo?: string;
  dependencies: string[];
  progress: number;
  createdAt: string;
  completedAt?: string;
  artifactId?: string;
  criteria: string[];
}

/**
 * Feedback data for artifact evaluation
 */
export interface FeedbackData {
  strengths: string[];
  weaknesses: string[];
  suggestions: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  overallRating: number; // 1-10
  reasoning: Reasoning;
}

/**
 * Artifact decision for determining what to create
 */
export interface ArtifactDecision {
  shouldCreate: boolean;
  artifactType?: string;
  reasoning: Reasoning;
  dependencies?: string[];
}

/**
 * Dynamic workflow phase
 */
export enum DynamicWorkflowPhase {
  PLANNING = 'planning',
  RESEARCH = 'research',
  CREATION = 'creation',
  REVIEW = 'review',
  FINALIZATION = 'finalization'
}

/**
 * Dynamic collaboration state
 */
export interface DynamicCollaborationState {
  id: string;
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  status: 'active' | 'paused' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  currentPhase: DynamicWorkflowPhase;
  goals: Record<string, ContentGoal>;
  activeGoals: string[];
  completedGoals: string[];
  artifacts: Record<string, IterativeArtifact>;
  generatedArtifacts: string[];
  messages: IterativeMessage[];
  dynamicMessages: Record<string, DynamicAgentMessage>;
  conversations: Record<string, string[]>; // conversationId -> messageIds
  feedbackCycles: Record<string, FeedbackCycle>;
  evaluations: Record<string, any>; // evaluationId -> evaluation
  comparisonSessionId?: string; // ID of the fixed workflow session for comparison
}

/**
 * Feedback cycle for tracking feedback on artifacts
 */
export interface FeedbackCycle {
  artifactId: string;
  cycles: Array<{
    requestId: string;
    requestTimestamp: string;
    fromAgent: string;
    toAgent: string;
    feedbackId?: string;
    feedbackTimestamp?: string;
    incorporated: boolean;
    incorporatedTimestamp?: string;
    rating?: number;
  }>;
}

/**
 * Create a new dynamic collaboration state
 */
export function createDynamicCollaborationState(
  sessionId: string,
  params: ContentGenerationParams
): DynamicCollaborationState {
  return {
    id: sessionId,
    topic: params.topic,
    contentType: params.contentType,
    targetAudience: params.targetAudience,
    tone: params.tone,
    keywords: params.keywords,
    status: 'active',
    startTime: new Date().toISOString(),
    currentPhase: DynamicWorkflowPhase.PLANNING,
    goals: {},
    activeGoals: [],
    completedGoals: [],
    artifacts: {},
    generatedArtifacts: [],
    messages: [],
    dynamicMessages: {},
    conversations: {},
    feedbackCycles: {},
    evaluations: {}
  };
}
