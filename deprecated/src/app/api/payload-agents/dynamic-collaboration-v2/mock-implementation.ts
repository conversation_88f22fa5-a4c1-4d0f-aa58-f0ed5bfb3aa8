import { v4 as uuidv4 } from 'uuid';
import { 
  DynamicCollaborationState, 
  ContentGenerationParams, 
  DynamicWorkflowPhase,
  DynamicMessageType,
  DynamicAgentMessage,
  GoalType,
  GoalStatus,
  ContentGoal
} from './types';
import { ArtifactStatus } from '../collaborative-iteration/types';

// In-memory storage for session states
const sessionStates: Record<string, DynamicCollaborationState> = {};

/**
 * Mock implementation of the dynamic workflow orchestrator
 * This is used for development and testing purposes
 */
export class MockDynamicWorkflowOrchestrator {
  private sessionId: string;
  
  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }
  
  /**
   * Initialize a new collaboration session
   * @param params The content generation parameters
   * @returns Promise<boolean> indicating success or failure
   */
  async initializeCollaboration(params: ContentGenerationParams): Promise<boolean> {
    try {
      // Create initial state
      const initialState: DynamicCollaborationState = {
        id: this.sessionId,
        topic: params.topic,
        contentType: params.contentType,
        targetAudience: params.targetAudience,
        tone: params.tone,
        keywords: params.keywords,
        status: 'active',
        startTime: new Date().toISOString(),
        currentPhase: DynamicWorkflowPhase.RESEARCH,
        goals: {},
        activeGoals: [],
        completedGoals: [],
        artifacts: {},
        generatedArtifacts: [],
        messages: [],
        dynamicMessages: {},
        conversations: {},
        feedbackCycles: {}
      };
      
      // Create initial goals
      const researchGoalId = uuidv4();
      const keywordGoalId = uuidv4();
      
      const researchGoal: ContentGoal = {
        id: researchGoalId,
        type: GoalType.MARKET_RESEARCH,
        description: `Research the topic: ${params.topic}`,
        criteria: [
          'Identify target audience needs and preferences',
          'Analyze competitor content',
          'Determine key market trends'
        ],
        status: GoalStatus.IN_PROGRESS,
        progress: 0,
        assignedTo: ['market-research'],
        dependencies: [],
        artifacts: [],
        startTime: new Date().toISOString()
      };
      
      const keywordGoal: ContentGoal = {
        id: keywordGoalId,
        type: GoalType.KEYWORD_RESEARCH,
        description: `Identify SEO keywords for: ${params.topic}`,
        criteria: [
          'Find high-volume, low-competition keywords',
          'Identify long-tail keyword opportunities',
          'Analyze keyword relevance to topic'
        ],
        status: GoalStatus.PENDING,
        progress: 0,
        assignedTo: ['seo-keyword'],
        dependencies: [researchGoalId],
        artifacts: []
      };
      
      // Add goals to state
      initialState.goals[researchGoalId] = researchGoal;
      initialState.goals[keywordGoalId] = keywordGoal;
      initialState.activeGoals.push(researchGoalId);
      
      // Create initial system message
      const systemMessage: DynamicAgentMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: 'market-research',
        type: DynamicMessageType.GOAL_UPDATE,
        content: {
          action: 'GOAL_ASSIGNED',
          goalId: researchGoalId,
          description: researchGoal.description,
          criteria: researchGoal.criteria,
          type: researchGoal.type
        },
        conversationId: uuidv4(),
        reasoning: {
          thoughts: [
            'Starting the research phase with market research',
            'Market research should be completed before keyword research'
          ],
          considerations: [
            'Topic complexity and scope',
            'Target audience specificity'
          ],
          decision: `Assign market research goal for topic "${params.topic}"`,
          confidence: 0.9
        }
      };
      
      // Add message to state
      initialState.dynamicMessages[systemMessage.id] = systemMessage;
      initialState.conversations[systemMessage.conversationId] = [systemMessage.id];
      
      // Store the state
      sessionStates[this.sessionId] = initialState;
      
      return true;
    } catch (error) {
      console.error('Error initializing collaboration:', error);
      return false;
    }
  }
  
  /**
   * Get the current state of the session
   * @returns Promise<DynamicCollaborationState | null>
   */
  async getState(): Promise<DynamicCollaborationState | null> {
    return sessionStates[this.sessionId] || null;
  }
  
  /**
   * Process a user message
   * @param message The user message
   * @returns Promise<string> The message ID
   */
  async processUserMessage(message: DynamicAgentMessage): Promise<string> {
    try {
      const state = sessionStates[this.sessionId];
      if (!state) {
        throw new Error('Session not found');
      }
      
      // Add the message to the state
      state.dynamicMessages[message.id] = message;
      
      // Create a conversation if it doesn't exist
      if (!state.conversations[message.conversationId]) {
        state.conversations[message.conversationId] = [];
      }
      
      // Add the message to the conversation
      state.conversations[message.conversationId].push(message.id);
      
      // Create a response message
      const responseMessage: DynamicAgentMessage = {
        id: uuidv4(),
        timestamp: new Date(Date.now() + 1000).toISOString(), // 1 second later
        from: 'system',
        to: 'user',
        type: DynamicMessageType.AGENT_MESSAGE,
        content: `Thank you for your message: "${message.content}". The agents are working on your request.`,
        conversationId: message.conversationId,
        replyTo: message.id,
        reasoning: {
          thoughts: [
            'User has provided input',
            'Need to acknowledge receipt'
          ],
          considerations: [
            'Maintaining user engagement',
            'Setting appropriate expectations'
          ],
          decision: 'Send acknowledgement response to user',
          confidence: 0.95
        }
      };
      
      // Add the response to the state
      state.dynamicMessages[responseMessage.id] = responseMessage;
      state.conversations[message.conversationId].push(responseMessage.id);
      
      return message.id;
    } catch (error) {
      console.error('Error processing user message:', error);
      throw error;
    }
  }
  
  /**
   * Get the communication hub
   * @returns Object with sendMessage method
   */
  getCommunicationHub() {
    return {
      sendMessage: async (message: DynamicAgentMessage): Promise<string> => {
        try {
          const state = sessionStates[this.sessionId];
          if (!state) {
            throw new Error('Session not found');
          }
          
          // Add the message to the state
          state.dynamicMessages[message.id] = message;
          
          // Create a conversation if it doesn't exist
          if (!state.conversations[message.conversationId]) {
            state.conversations[message.conversationId] = [];
          }
          
          // Add the message to the conversation
          state.conversations[message.conversationId].push(message.id);
          
          return message.id;
        } catch (error) {
          console.error('Error sending message:', error);
          throw error;
        }
      }
    };
  }
  
  /**
   * Get the artifact decision framework
   * @returns Object with createArtifact method
   */
  getArtifactDecisionFramework() {
    return {
      createArtifact: async (artifact: any): Promise<string> => {
        try {
          const state = sessionStates[this.sessionId];
          if (!state) {
            throw new Error('Session not found');
          }
          
          // Add the artifact to the state
          state.artifacts[artifact.id] = artifact;
          state.generatedArtifacts.push(artifact.id);
          
          // Create a notification message
          const notificationMessage: DynamicAgentMessage = {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'all',
            type: DynamicMessageType.ARTIFACT_UPDATE,
            content: {
              action: 'CREATED',
              artifactId: artifact.id,
              name: artifact.name,
              type: artifact.type
            },
            conversationId: uuidv4(),
            reasoning: {
              thoughts: [
                `New artifact "${artifact.name}" has been created`,
                'Notifying all agents about the new artifact'
              ],
              considerations: [
                'Artifact visibility and accessibility',
                'Workflow progression'
              ],
              decision: 'Broadcast artifact creation notification',
              confidence: 0.9
            }
          };
          
          // Add the notification to the state
          state.dynamicMessages[notificationMessage.id] = notificationMessage;
          state.conversations[notificationMessage.conversationId] = [notificationMessage.id];
          
          return artifact.id;
        } catch (error) {
          console.error('Error creating artifact:', error);
          throw error;
        }
      }
    };
  }
  
  /**
   * Pause the collaboration
   * @returns Promise<boolean> indicating success or failure
   */
  async pauseCollaboration(): Promise<boolean> {
    try {
      const state = sessionStates[this.sessionId];
      if (!state) {
        throw new Error('Session not found');
      }
      
      state.status = 'paused';
      
      return true;
    } catch (error) {
      console.error('Error pausing collaboration:', error);
      return false;
    }
  }
  
  /**
   * Resume the collaboration
   * @returns Promise<boolean> indicating success or failure
   */
  async resumeCollaboration(): Promise<boolean> {
    try {
      const state = sessionStates[this.sessionId];
      if (!state) {
        throw new Error('Session not found');
      }
      
      state.status = 'active';
      
      return true;
    } catch (error) {
      console.error('Error resuming collaboration:', error);
      return false;
    }
  }
  
  /**
   * Cancel the collaboration
   * @returns Promise<boolean> indicating success or failure
   */
  async cancelCollaboration(): Promise<boolean> {
    try {
      const state = sessionStates[this.sessionId];
      if (!state) {
        throw new Error('Session not found');
      }
      
      state.status = 'failed';
      state.endTime = new Date().toISOString();
      
      return true;
    } catch (error) {
      console.error('Error canceling collaboration:', error);
      return false;
    }
  }
  
  /**
   * Complete the collaboration
   * @returns Promise<boolean> indicating success or failure
   */
  async completeCollaboration(): Promise<boolean> {
    try {
      const state = sessionStates[this.sessionId];
      if (!state) {
        throw new Error('Session not found');
      }
      
      state.status = 'completed';
      state.endTime = new Date().toISOString();
      state.currentPhase = DynamicWorkflowPhase.COMPLETED;
      
      return true;
    } catch (error) {
      console.error('Error completing collaboration:', error);
      return false;
    }
  }
}
