import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { DynamicWorkflowOrchestrator } from '../../../dynamic-workflow-orchestrator';
import { ArtifactStatus } from '../../../../../../api/agents/collaborative-iteration/types';
import logger from '../../../../../../api/agents/collaborative-iteration/utils/logger';

/**
 * API handler for publishing content from a dynamic collaboration session
 *
 * @param req The Next.js request object
 * @param params The route parameters containing the session ID
 * @returns NextResponse with the result of the publishing
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const {
      title,
      content,
      status = 'published'
    } = body;

    // Validate required parameters
    if (!title) {
      return NextResponse.json(
        { error: 'Missing required parameter: title' },
        { status: 400 }
      );
    }

    if (!content) {
      return NextResponse.json(
        { error: 'Missing required parameter: content' },
        { status: 400 }
      );
    }

    // Create a new orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

    // Get the artifact decision framework
    const artifactFramework = orchestrator.getArtifactDecisionFramework();

    // Create a final published artifact
    const artifactId = await artifactFramework.createArtifact({
      id: uuidv4(),
      type: 'published-article',
      name: title,
      content,
      creator: 'user',
      status: ArtifactStatus.COMPLETED,
      timestamp: new Date().toISOString(),
      data: {
        publishStatus: status,
        publishedAt: new Date().toISOString()
      }
    });

    // Update the session status to completed
    await orchestrator.completeCollaboration();

    logger.info(`Published content from dynamic collaboration session`, {
      sessionId,
      artifactId,
      title
    });

    // In a real implementation, you would integrate with a CMS or publishing system here
    // For now, we'll just return a success response

    return NextResponse.json({
      success: true,
      artifactId,
      message: 'Content published successfully',
      publishStatus: status
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error publishing content from dynamic collaboration session`, {
      sessionId: params.sessionId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: err.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
