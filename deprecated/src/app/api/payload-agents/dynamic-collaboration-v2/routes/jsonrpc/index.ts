/**
 * JSONRPC API handler for dynamic collaboration
 *
 * This file contains the implementation of the JSONRPC API for dynamic collaboration.
 * It provides a unified interface for all operations related to dynamic collaboration.
 */

import { v4 as uuidv4 } from 'uuid';
import { DynamicWorkflowOrchestrator } from '../../dynamic-workflow-orchestrator';
import { stateStore } from '../../../../../api/agents/collaborative-iteration/utils/stateStore';
import {
  DynamicWorkflowPhase,
  DynamicMessageType,
  ContentGenerationParams
} from '../../types';
import { ArtifactStatus } from '../../../../../api/agents/collaborative-iteration/types';

// JSONRPC interface types
interface JsonRpcRequest {
  jsonrpc: string;
  method: string;
  params: any;
  id: string | number;
}

interface JsonRpcSuccessResponse {
  jsonrpc: string;
  result: any;
  id: string | number;
}

interface JsonRpcErrorResponse {
  jsonrpc: string;
  error: {
    code: number;
    message: string;
    data?: any;
  };
  id: string | number | null;
}

type JsonRpcResponse = JsonRpcSuccessResponse | JsonRpcErrorResponse;

/**
 * Process a JSONRPC request
 */
export async function processJsonRpcRequest(request: JsonRpcRequest): Promise<JsonRpcResponse> {
  const { jsonrpc, method, params, id } = request;

  // Validate JSONRPC request
  if (jsonrpc !== '2.0' || !method) {
    return {
      jsonrpc: '2.0',
      error: { code: -32600, message: 'Invalid Request' },
      id: id || null
    };
  }

  try {
    let result;

    // Session methods
    if (method === 'session.create') {
      result = await handleSessionCreate(params);
    } else if (method === 'session.get') {
      result = await handleSessionGet(params);
    } else if (method === 'session.list') {
      result = await handleSessionList();
    } else if (method === 'session.update') {
      result = await handleSessionUpdate(params);
    } else if (method === 'session.delete') {
      result = await handleSessionDelete(params);
    }
    // Message methods
    else if (method === 'message.send') {
      result = await handleMessageSend(params);
    } else if (method === 'message.list') {
      result = await handleMessageList(params);
    }
    // Artifact methods
    else if (method === 'artifact.create') {
      result = await handleArtifactCreate(params);
    } else if (method === 'artifact.list') {
      result = await handleArtifactList(params);
    }
    // Content methods
    else if (method === 'content.publish') {
      result = await handleContentPublish(params);
    }
    // Unknown method
    else {
      return {
        jsonrpc: '2.0',
        error: { code: -32601, message: 'Method not found' },
        id
      };
    }

    return {
      jsonrpc: '2.0',
      result,
      id
    };
  } catch (error) {
    const err = error as Error;
    return {
      jsonrpc: '2.0',
      error: {
        code: -32603,
        message: err.message || 'Internal error',
        data: { stack: err.stack }
      },
      id
    };
  }
}

/**
 * Process a batch of JSONRPC requests
 */
export async function processJsonRpcBatch(requests: JsonRpcRequest[]): Promise<JsonRpcResponse[]> {
  const responses: JsonRpcResponse[] = [];

  for (const request of requests) {
    responses.push(await processJsonRpcRequest(request));
  }

  return responses;
}

// Handler implementations

/**
 * Create a new session
 */
async function handleSessionCreate(params: any) {
  const {
    topic,
    contentType = 'blog-article',
    targetAudience = 'general',
    tone = 'professional',
    keywords = [],
    additionalInstructions = ''
  } = params;

  // Validate required parameters
  if (!topic) {
    throw new Error('Missing required parameter: topic');
  }

  // Generate a new session ID
  const sessionId = uuidv4();

  // Create parameters object
  const contentParams: ContentGenerationParams = {
    topic,
    contentType,
    targetAudience,
    tone,
    keywords,
    additionalParams: {
      additionalInstructions
    }
  };

  // Initialize the collaboration
  const success = await DynamicWorkflowOrchestrator.initiate(sessionId, contentParams);

  if (!success) {
    throw new Error('Failed to initialize collaboration session');
  }

  return {
    success: true,
    sessionId,
    message: 'Dynamic collaboration session created successfully'
  };
}

/**
 * Get a session by ID
 */
async function handleSessionGet(params: any) {
  const { sessionId } = params;

  // Validate session ID
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Get the session state from the state store
  const sessionState = await stateStore.getState(sessionId);

  if (!sessionState) {
    throw new Error('Session not found');
  }

  return sessionState;
}

/**
 * List all sessions
 */
async function handleSessionList() {
  // Get all sessions
  const sessions = await stateStore.listSessions();
  const sessionStates = {};

  // Get details for each session
  for (const sessionId of sessions) {
    const state = await stateStore.getState(sessionId);
    if (state && state.currentPhase !== undefined) {
      sessionStates[sessionId] = {
        id: sessionId,
        topic: state.topic,
        status: state.status,
        startTime: state.startTime,
        endTime: state.endTime,
        currentPhase: state.currentPhase
      };
    }
  }

  return {
    success: true,
    sessions: Object.values(sessionStates)
  };
}

/**
 * Update a session
 */
async function handleSessionUpdate(params: any) {
  const { sessionId, action, phase } = params;

  // Validate session ID
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Create a new orchestrator instance
  const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

  // Perform the requested action
  let success = false;

  switch (action) {
    case 'transition-phase':
      if (!phase) {
        throw new Error('Missing required parameter: phase');
      }
      success = await orchestrator.transitionToPhase(phase as DynamicWorkflowPhase);
      break;

    case 'pause':
      // Implement pause functionality
      await stateStore.updateState(sessionId, (currentState) => {
        if (!currentState) return currentState;
        return {
          ...currentState,
          status: 'paused'
        };
      });
      success = true;
      break;

    case 'resume':
      // Implement resume functionality
      await stateStore.updateState(sessionId, (currentState) => {
        if (!currentState) return currentState;
        return {
          ...currentState,
          status: 'active'
        };
      });
      success = true;
      break;

    case 'cancel':
      // Implement cancel functionality
      await stateStore.updateState(sessionId, (currentState) => {
        if (!currentState) return currentState;
        return {
          ...currentState,
          status: 'failed',
          endTime: new Date().toISOString()
        };
      });
      success = true;
      break;

    default:
      throw new Error(`Unknown action: ${action}`);
  }

  if (!success) {
    throw new Error(`Failed to perform action: ${action}`);
  }

  // Get the updated session state
  const updatedState = await stateStore.getState(sessionId);

  return {
    success: true,
    message: `Action ${action} performed successfully`,
    state: updatedState
  };
}

/**
 * Delete a session
 */
async function handleSessionDelete(params: any) {
  const { sessionId } = params;

  // Validate session ID
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Delete the session state from the state store
  const success = await stateStore.deleteState(sessionId);

  if (!success) {
    throw new Error('Failed to delete session');
  }

  return {
    success: true,
    message: 'Session deleted successfully'
  };
}

/**
 * Send a message
 */
async function handleMessageSend(params: any) {
  const {
    sessionId,
    content,
    type = DynamicMessageType.USER_MESSAGE,
    to = 'system'
  } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  if (!content) {
    throw new Error('Missing required parameter: content');
  }

  // Create a new orchestrator instance
  const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

  // Create the message
  const messageId = uuidv4();
  const conversationId = uuidv4();
  const timestamp = new Date().toISOString();

  // Add the message to the state
  await stateStore.updateState(sessionId, (currentState) => {
    if (!currentState) return currentState;

    const newMessage = {
      id: messageId,
      timestamp,
      from: 'user',
      to,
      type,
      content,
      conversationId
    };

    const updatedDynamicMessages = {
      ...currentState.dynamicMessages,
      [messageId]: newMessage
    };

    const updatedConversations = { ...currentState.conversations };
    if (!updatedConversations[conversationId]) {
      updatedConversations[conversationId] = [];
    }
    updatedConversations[conversationId].push(messageId);

    return {
      ...currentState,
      dynamicMessages: updatedDynamicMessages,
      conversations: updatedConversations
    };
  });

  // Process the message
  await orchestrator.processUserMessage(messageId);

  return {
    success: true,
    messageId,
    message: 'Message sent successfully'
  };
}

/**
 * List messages for a session
 */
async function handleMessageList(params: any) {
  const { sessionId } = params;

  // Validate session ID
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Get the session state from the state store
  const sessionState = await stateStore.getState(sessionId);

  if (!sessionState) {
    throw new Error('Session not found');
  }

  // Extract messages
  const messages = sessionState.dynamicMessages || {};

  return {
    success: true,
    messages: Object.values(messages)
  };
}

/**
 * Create an artifact
 */
async function handleArtifactCreate(params: any) {
  const {
    sessionId,
    type,
    name,
    content,
    creator = 'user',
    status = ArtifactStatus.COMPLETED,
    data = {}
  } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  if (!type) {
    throw new Error('Missing required parameter: type');
  }

  if (!name) {
    throw new Error('Missing required parameter: name');
  }

  // Create the artifact
  const artifactId = uuidv4();
  const timestamp = new Date().toISOString();

  // Add the artifact to the state
  await stateStore.updateState(sessionId, (currentState) => {
    if (!currentState) return currentState;

    const newArtifact = {
      id: artifactId,
      type,
      name,
      content,
      creator,
      status,
      timestamp,
      data
    };

    return {
      ...currentState,
      artifacts: {
        ...currentState.artifacts,
        [artifactId]: newArtifact
      },
      generatedArtifacts: [...(currentState.generatedArtifacts || []), artifactId]
    };
  });

  // Create a notification message
  const messageId = uuidv4();
  const conversationId = uuidv4();

  await stateStore.updateState(sessionId, (currentState) => {
    if (!currentState) return currentState;

    const notificationMessage = {
      id: messageId,
      timestamp,
      from: 'system',
      to: 'all',
      type: DynamicMessageType.ARTIFACT_UPDATE,
      content: {
        action: 'CREATED',
        artifactId,
        name,
        type
      },
      conversationId
    };

    const updatedDynamicMessages = {
      ...currentState.dynamicMessages,
      [messageId]: notificationMessage
    };

    const updatedConversations = { ...currentState.conversations };
    if (!updatedConversations[conversationId]) {
      updatedConversations[conversationId] = [];
    }
    updatedConversations[conversationId].push(messageId);

    return {
      ...currentState,
      dynamicMessages: updatedDynamicMessages,
      conversations: updatedConversations
    };
  });

  return {
    success: true,
    artifactId,
    message: 'Artifact created successfully'
  };
}

/**
 * List artifacts for a session
 */
async function handleArtifactList(params: any) {
  const { sessionId } = params;

  // Validate session ID
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Get the session state from the state store
  const sessionState = await stateStore.getState(sessionId);

  if (!sessionState) {
    throw new Error('Session not found');
  }

  // Extract artifacts
  const artifacts = sessionState.artifacts || {};
  const generatedArtifacts = sessionState.generatedArtifacts || [];

  return {
    success: true,
    artifacts: Object.values(artifacts).filter(artifact =>
      generatedArtifacts.includes(artifact.id)
    )
  };
}

/**
 * Publish content
 */
async function handleContentPublish(params: any) {
  const {
    sessionId,
    title,
    content,
    status = 'published'
  } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  if (!title) {
    throw new Error('Missing required parameter: title');
  }

  if (!content) {
    throw new Error('Missing required parameter: content');
  }

  // Create the artifact
  const artifactId = uuidv4();
  const timestamp = new Date().toISOString();

  // Add the artifact to the state
  await stateStore.updateState(sessionId, (currentState) => {
    if (!currentState) return currentState;

    const newArtifact = {
      id: artifactId,
      type: 'published-article',
      name: title,
      content,
      creator: 'user',
      status: ArtifactStatus.COMPLETED,
      timestamp,
      data: {
        publishStatus: status,
        publishedAt: timestamp
      }
    };

    return {
      ...currentState,
      artifacts: {
        ...currentState.artifacts,
        [artifactId]: newArtifact
      },
      generatedArtifacts: [...(currentState.generatedArtifacts || []), artifactId],
      status: 'completed',
      endTime: timestamp,
      currentPhase: DynamicWorkflowPhase.COMPLETED
    };
  });

  // Create a notification message
  const messageId = uuidv4();
  const conversationId = uuidv4();

  await stateStore.updateState(sessionId, (currentState) => {
    if (!currentState) return currentState;

    const notificationMessage = {
      id: messageId,
      timestamp,
      from: 'system',
      to: 'all',
      type: DynamicMessageType.SYSTEM_NOTIFICATION,
      content: {
        message: `Article "${title}" has been published`,
        artifactId,
        status
      },
      conversationId
    };

    const updatedDynamicMessages = {
      ...currentState.dynamicMessages,
      [messageId]: notificationMessage
    };

    const updatedConversations = { ...currentState.conversations };
    if (!updatedConversations[conversationId]) {
      updatedConversations[conversationId] = [];
    }
    updatedConversations[conversationId].push(messageId);

    return {
      ...currentState,
      dynamicMessages: updatedDynamicMessages,
      conversations: updatedConversations
    };
  });

  return {
    success: true,
    artifactId,
    message: 'Content published successfully',
    publishStatus: status
  };
}
