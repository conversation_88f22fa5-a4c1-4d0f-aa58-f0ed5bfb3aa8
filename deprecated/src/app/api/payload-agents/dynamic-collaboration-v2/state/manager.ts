/**
 * Dynamic Collaboration State Manager
 *
 * This file implements a state manager that provides higher-level operations
 * on top of the state store for the dynamic collaboration system.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../collaborative-iteration/utils/logger';
import {
  DynamicCollaborationState,
  ContentGoal,
  DynamicAgentMessage,
  DynamicArtifact,
  DynamicMessageType,
  GoalStatus,
  GoalType,
  ArtifactStatus,
  ContentGenerationParams,
  createDynamicCollaborationState,
  DynamicWorkflowPhase,
  Reasoning
} from './models';
import { dynamicStateStore } from './store';

/**
 * Dynamic Collaboration State Manager
 *
 * This class provides higher-level operations for managing dynamic collaboration state,
 * including goal management, message handling, and artifact creation.
 */
export class DynamicStateManager {
  private sessionId: string;

  /**
   * Constructor
   * @param sessionId The session ID to manage
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }

  /**
   * Initialize a new session
   * @param params Content generation parameters
   * @returns Promise<boolean> indicating success
   */
  public async initializeSession(params: ContentGenerationParams): Promise<boolean> {
    try {
      logger.info(`Initializing dynamic collaboration session`, {
        sessionId: this.sessionId,
        topic: params.topic
      });

      // Create initial state
      const initialState = createDynamicCollaborationState(this.sessionId, params);

      // Store the state
      await dynamicStateStore.setState(this.sessionId, initialState);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error initializing session`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Get the current state
   * @returns The current state or null if not found
   */
  public async getState(): Promise<DynamicCollaborationState | null> {
    return await dynamicStateStore.getState(this.sessionId);
  }

  /**
   * Update the workflow phase
   * @param phase The new phase
   * @returns Promise<boolean> indicating success
   */
  public async updatePhase(phase: DynamicWorkflowPhase): Promise<boolean> {
    try {
      logger.info(`Updating phase to ${phase}`, {
        sessionId: this.sessionId,
        phase
      });

      await dynamicStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        return {
          ...currentState,
          currentPhase: phase
        };
      });

      // Create a system message about the phase transition
      const messageId = uuidv4();
      const conversationId = uuidv4();
      const timestamp = new Date().toISOString();

      await this.addSystemMessage({
        id: messageId,
        timestamp,
        from: 'system',
        to: 'all',
        type: DynamicMessageType.WORKFLOW_TRANSITION,
        content: {
          message: `Transitioned to phase: ${phase}`,
          phase
        },
        conversationId
      });

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error updating phase`, {
        sessionId: this.sessionId,
        phase,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Define goals for the session
   * @param goalTemplates Array of goal templates
   * @returns Array of created goal IDs
   */
  public async defineGoals(goalTemplates: Array<{
    description: string;
    type: GoalType;
    dependencies: string[];
    criteria: string[];
  }>): Promise<string[]> {
    try {
      // Create goals from templates
      const goals: Record<string, ContentGoal> = {};
      const goalIds: string[] = [];

      for (const template of goalTemplates) {
        const goalId = uuidv4();
        goals[goalId] = {
          id: goalId,
          description: template.description,
          type: template.type,
          status: GoalStatus.PENDING,
          dependencies: template.dependencies,
          progress: 0,
          createdAt: new Date().toISOString(),
          criteria: template.criteria
        };
        goalIds.push(goalId);
      }

      // Update state with goals
      await dynamicStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        return {
          ...currentState,
          goals: {
            ...currentState.goals,
            ...goals
          }
        };
      });

      logger.info(`Defined ${goalIds.length} goals for session ${this.sessionId}`, {
        sessionId: this.sessionId,
        goalCount: goalIds.length,
        goalTypes: Object.values(goals).map(g => g.type)
      });

      return goalIds;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error defining goals`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return [];
    }
  }

  /**
   * Assign a goal to an agent
   * @param goalId Goal ID to assign
   * @param agentId Agent ID to assign the goal to
   * @returns Promise<boolean> indicating success
   */
  public async assignGoal(goalId: string, agentId: string): Promise<boolean> {
    try {
      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      const goal = state.goals[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Update goal status and assignee
      await dynamicStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        const updatedGoals = { ...currentState.goals };
        updatedGoals[goalId] = {
          ...updatedGoals[goalId],
          status: GoalStatus.IN_PROGRESS,
          assignedTo: agentId,
          startTime: new Date().toISOString()
        };

        // Add to active goals
        const activeGoals = [...currentState.activeGoals];
        if (!activeGoals.includes(goalId)) {
          activeGoals.push(goalId);
        }

        return {
          ...currentState,
          goals: updatedGoals,
          activeGoals
        };
      });

      // Create goal assignment message
      await this.addSystemMessage({
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: agentId,
        type: DynamicMessageType.GOAL_UPDATE,
        content: {
          action: 'GOAL_ASSIGNED',
          goalId,
          description: goal.description,
          criteria: goal.criteria,
          type: goal.type
        },
        conversationId: uuidv4(),
        reasoning: {
          thoughts: [
            `Agent ${agentId} has the expertise needed for this goal`,
            `Goal dependencies are satisfied`
          ],
          considerations: [
            `Agent workload and specialization`,
            `Goal priority and dependencies`
          ],
          decision: `Assign goal "${goal.description}" to agent ${agentId}`,
          confidence: 0.9
        }
      });

      logger.info(`Assigned goal ${goalId} to agent ${agentId}`, {
        sessionId: this.sessionId,
        goalId,
        agentId,
        goalDescription: goal.description
      });

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error assigning goal`, {
        sessionId: this.sessionId,
        goalId,
        agentId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Add a message to the state
   * @param message The message to add
   * @returns The message ID
   */
  public async addMessage(message: DynamicAgentMessage): Promise<string> {
    try {
      // Ensure message has an ID
      if (!message.id) {
        message.id = uuidv4();
      }

      // Ensure message has a timestamp
      if (!message.timestamp) {
        message.timestamp = new Date().toISOString();
      }

      // Ensure message has a conversation ID
      if (!message.conversationId) {
        message.conversationId = uuidv4();
      }

      // Add message to state
      await dynamicStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        // Add to dynamic messages
        const dynamicMessages = {
          ...currentState.dynamicMessages,
          [message.id]: message
        };

        // Add to conversations
        const conversations = { ...currentState.conversations };
        if (!conversations[message.conversationId]) {
          conversations[message.conversationId] = [];
        }
        conversations[message.conversationId].push(message.id);

        // Add to messages array
        const messages = [...currentState.messages, message];

        return {
          ...currentState,
          dynamicMessages,
          conversations,
          messages
        };
      });

      logger.debug(`Added message ${message.id} to session ${this.sessionId}`, {
        sessionId: this.sessionId,
        messageId: message.id,
        from: message.from,
        to: message.to,
        type: message.type
      });

      return message.id;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error adding message`, {
        sessionId: this.sessionId,
        messageId: message.id,
        error: err.message || String(error),
        stack: err.stack
      });
      return message.id;
    }
  }

  /**
   * Add a system message
   * @param message The message to add
   * @returns The message ID
   */
  public async addSystemMessage(message: DynamicAgentMessage): Promise<string> {
    // Ensure from is 'system'
    message.from = 'system';
    return this.addMessage(message);
  }

  /**
   * Add an artifact to the state
   * @param artifact The artifact to add
   * @returns The artifact ID
   */
  public async addArtifact(artifact: DynamicArtifact): Promise<string> {
    try {
      // Ensure artifact has an ID
      if (!artifact.id) {
        artifact.id = uuidv4();
      }

      // Ensure artifact has timestamps
      if (!artifact.createdAt) {
        artifact.createdAt = new Date().toISOString();
      }
      if (!artifact.updatedAt) {
        artifact.updatedAt = artifact.createdAt;
      }

      // Ensure artifact has a version
      if (!artifact.version) {
        artifact.version = 1;
      }

      // Add artifact to state
      await dynamicStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        // Add to artifacts
        const artifacts = {
          ...currentState.artifacts,
          [artifact.id]: artifact
        };

        // Add to generated artifacts
        const generatedArtifacts = [...currentState.generatedArtifacts];
        if (!generatedArtifacts.includes(artifact.id)) {
          generatedArtifacts.push(artifact.id);
        }

        return {
          ...currentState,
          artifacts,
          generatedArtifacts
        };
      });

      // Create artifact created message
      await this.addSystemMessage({
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: 'all',
        type: DynamicMessageType.ARTIFACT_CREATED,
        content: {
          artifactId: artifact.id,
          artifactType: artifact.type,
          title: artifact.title
        },
        conversationId: uuidv4()
      });

      logger.info(`Added artifact ${artifact.id} to session ${this.sessionId}`, {
        sessionId: this.sessionId,
        artifactId: artifact.id,
        type: artifact.type,
        title: artifact.title
      });

      return artifact.id;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error adding artifact`, {
        sessionId: this.sessionId,
        artifactId: artifact.id,
        error: err.message || String(error),
        stack: err.stack
      });
      return artifact.id;
    }
  }

  /**
   * Track goal progress
   * @param goalId Goal ID to update
   * @param progress Progress percentage (0-100)
   * @returns Promise<boolean> indicating success
   */
  public async trackGoalProgress(goalId: string, progress: number): Promise<boolean> {
    try {
      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      const goal = state.goals[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Ensure progress is between 0 and 100
      const validProgress = Math.max(0, Math.min(100, progress));

      // Update goal progress
      await dynamicStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        const updatedGoals = { ...currentState.goals };
        updatedGoals[goalId] = {
          ...updatedGoals[goalId],
          progress: validProgress
        };

        return {
          ...currentState,
          goals: updatedGoals
        };
      });

      // Create progress update message
      await this.addSystemMessage({
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: 'all',
        type: DynamicMessageType.GOAL_UPDATE,
        content: {
          action: 'GOAL_PROGRESS',
          goalId,
          description: goal.description,
          progress: validProgress
        },
        conversationId: uuidv4()
      });

      logger.info(`Updated progress for goal ${goalId} to ${validProgress}%`, {
        sessionId: this.sessionId,
        goalId,
        progress: validProgress
      });

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error tracking goal progress`, {
        sessionId: this.sessionId,
        goalId,
        progress,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Complete a goal
   * @param goalId Goal ID to complete
   * @param artifactId Optional artifact ID associated with the goal
   * @returns Promise<boolean> indicating success
   */
  public async completeGoal(goalId: string, artifactId?: string): Promise<boolean> {
    try {
      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      const goal = state.goals[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // If an artifact is provided, evaluate it against the goal criteria
      if (artifactId) {
        const artifact = state.artifacts[artifactId];
        if (!artifact) {
          throw new Error(`Artifact ${artifactId} not found`);
        }

        // Import the ArtifactEvaluationService
        const { ArtifactEvaluationService } = await import('../artifact-evaluation-service');

        // Evaluate the artifact against the goal criteria
        const evaluation = ArtifactEvaluationService.evaluateArtifact(artifact, goal);

        // Store the evaluation
        await this.storeArtifactEvaluation(artifactId, evaluation);

        // If the goal is not met, return false or request improvements
        if (!evaluation.goalMet) {
          // Create feedback message
          await this.addSystemMessage({
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: goal.assignedTo || 'all',
            type: DynamicMessageType.FEEDBACK,
            content: {
              artifactId,
              goalId,
              evaluation,
              message: `Artifact does not meet goal criteria. Please improve based on feedback: ${evaluation.feedback}`
            },
            conversationId: uuidv4()
          });

          logger.info(`Goal ${goalId} not completed due to insufficient artifact quality`, {
            sessionId: this.sessionId,
            goalId,
            artifactId,
            evaluationScore: evaluation.overallScore,
            feedback: evaluation.feedback
          });

          return false;
        }

        // Log successful evaluation
        logger.info(`Artifact evaluation successful for goal ${goalId}`, {
          sessionId: this.sessionId,
          goalId,
          artifactId,
          evaluationScore: evaluation.overallScore
        });
      }

      // Update goal status and completion details
      await dynamicStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        const updatedGoals = { ...currentState.goals };
        updatedGoals[goalId] = {
          ...updatedGoals[goalId],
          status: GoalStatus.COMPLETED,
          progress: 100,
          completedAt: new Date().toISOString(),
          artifactId
        };

        // Move from active to completed goals
        const activeGoals = currentState.activeGoals.filter((id: string) => id !== goalId);
        const completedGoals = [...currentState.completedGoals];
        if (!completedGoals.includes(goalId)) {
          completedGoals.push(goalId);
        }

        return {
          ...currentState,
          goals: updatedGoals,
          activeGoals,
          completedGoals
        };
      });

      // Create goal completion message
      await this.addSystemMessage({
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: 'all',
        type: DynamicMessageType.GOAL_UPDATE,
        content: {
          action: 'GOAL_COMPLETED',
          goalId,
          description: goal.description,
          artifactId
        },
        conversationId: uuidv4()
      });

      logger.info(`Completed goal ${goalId}`, {
        sessionId: this.sessionId,
        goalId,
        artifactId
      });

      // Check if any blocked goals can now be unblocked
      await this.checkForUnblockedGoals();

      // Check if we need to transition to the next phase
      await this.checkPhaseTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error completing goal`, {
        sessionId: this.sessionId,
        goalId,
        artifactId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Store an artifact evaluation
   * @param artifactId Artifact ID
   * @param evaluation Evaluation result
   */
  private async storeArtifactEvaluation(artifactId: string, evaluation: any): Promise<void> {
    try {
      await dynamicStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        // Initialize evaluations if not exists
        const evaluations = currentState.evaluations || {};

        // Store evaluation by ID
        evaluations[evaluation.id] = evaluation;

        // Update artifact with evaluation ID
        const artifacts = { ...currentState.artifacts };
        if (artifacts[artifactId]) {
          const updatedArtifact = { ...artifacts[artifactId] };

          // Initialize evaluationIds array if not exists
          updatedArtifact.evaluationIds = updatedArtifact.evaluationIds || [];

          // Add evaluation ID if not already present
          if (!updatedArtifact.evaluationIds.includes(evaluation.id)) {
            updatedArtifact.evaluationIds.push(evaluation.id);
          }

          // Update quality score based on evaluation
          updatedArtifact.qualityScore = Math.round(evaluation.overallScore * 100);

          artifacts[artifactId] = updatedArtifact;
        }

        return {
          ...currentState,
          artifacts,
          evaluations
        };
      });

      logger.info(`Stored evaluation for artifact ${artifactId}`, {
        sessionId: this.sessionId,
        artifactId,
        evaluationId: evaluation.id,
        score: evaluation.overallScore
      });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error storing artifact evaluation`, {
        sessionId: this.sessionId,
        artifactId,
        error: err.message || String(error),
        stack: err.stack
      });
    }
  }

  /**
   * Check if we need to transition to the next phase based on goal completion
   */
  private async checkPhaseTransition(): Promise<void> {
    try {
      // Create an orchestrator instance to check for phase transitions
      const orchestrator = new (await import('../dynamic-workflow-orchestrator-v2')).DynamicWorkflowOrchestratorV2(this.sessionId);

      // Check if we need to transition to the next phase
      await orchestrator.checkGoalCompletionAndTransition();
    } catch (error) {
      const err = error as Error;
      logger.error(`Error checking phase transition`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
    }
  }

  /**
   * Check for goals that were blocked but can now be unblocked
   */
  private async checkForUnblockedGoals(): Promise<void> {
    try {
      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Find blocked goals
      const blockedGoals = Object.values(state.goals).filter(
        goal => goal.status === GoalStatus.BLOCKED
      );

      for (const goal of blockedGoals) {
        // Check if all dependencies are completed
        const dependencies = Object.values(state.goals).filter(g =>
          goal.dependencies.includes(g.id)
        );

        const allDependenciesCompleted = dependencies.every(dep => dep.status === GoalStatus.COMPLETED);

        if (allDependenciesCompleted) {
          // Unblock the goal
          await dynamicStateStore.updateState(this.sessionId, (currentState) => {
            if (!currentState) return currentState;

            const updatedGoals = { ...currentState.goals };
            updatedGoals[goal.id] = {
              ...updatedGoals[goal.id],
              status: GoalStatus.PENDING
            };

            return {
              ...currentState,
              goals: updatedGoals
            };
          });

          logger.info(`Unblocked goal ${goal.id}`, {
            sessionId: this.sessionId,
            goalId: goal.id
          });

          // Create goal unblocked message
          await this.addSystemMessage({
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'all',
            type: DynamicMessageType.GOAL_UPDATE,
            content: {
              action: 'GOAL_UNBLOCKED',
              goalId: goal.id,
              description: goal.description
            },
            conversationId: uuidv4()
          });
        }
      }
    } catch (error) {
      const err = error as Error;
      logger.error(`Error checking for unblocked goals`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
    }
  }

  /**
   * Determine which agent should handle a goal
   */
  private determineAgentForGoal(goal: ContentGoal): string {
    // If goal already has an assignee, use that
    if (goal.assignedTo && typeof goal.assignedTo === 'string') {
      return goal.assignedTo;
    }

    // Otherwise, determine based on goal type
    switch (goal.type) {
      case GoalType.MARKET_RESEARCH:
        return 'market-research';
      case GoalType.KEYWORD_ANALYSIS:
        return 'seo-keyword';
      case GoalType.CONTENT_STRATEGY:
        return 'content-strategy';
      case GoalType.CONTENT_CREATION:
        return 'content-generation';
      case GoalType.SEO_OPTIMIZATION:
        return 'seo-optimization';
      case GoalType.QUALITY_ASSESSMENT:
        return 'quality-assessment';
      default:
        return 'system';
    }
  }
}

// Export a factory function to create state managers
export function createStateManager(sessionId: string): DynamicStateManager {
  return new DynamicStateManager(sessionId);
}
