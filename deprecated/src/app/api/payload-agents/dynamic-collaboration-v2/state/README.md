# Dynamic Collaboration State Module

This module provides a dedicated state management system for the Dynamic Collaboration feature. It includes models, a specialized state store, and a state manager for higher-level operations.

## Components

### Models (`models.ts`)

Defines the data structures used in the dynamic collaboration system:

- `DynamicCollaborationState`: The main state model
- `ContentGoal`: Represents a goal in the content creation process
- `DynamicAgentMessage`: Represents a message between agents
- `DynamicArtifact`: Represents an artifact created during collaboration

### State Store (`store.ts`)

Provides methods for storing, retrieving, and updating state:

- Uses in-memory caching for fast access
- Uses Redis for persistence
- Implements a singleton pattern

### State Manager (`manager.ts`)

Provides higher-level operations for state manipulation:

- Session initialization
- Phase transitions
- Goal management
- Message handling
- Artifact management

## Usage

```typescript
import { createStateManager, DynamicWorkflowPhase } from './state';

// Create state manager
const stateManager = createStateManager('session-123');

// Get current state
const state = await stateManager.getState();

// Update phase
await stateManager.updatePhase(DynamicWorkflowPhase.RESEARCH);

// Add a message
await stateManager.addMessage({
  id: 'msg-123',
  timestamp: new Date().toISOString(),
  from: 'user',
  to: 'system',
  type: 'user_message',
  content: 'Can you add more information about CRM benefits?',
  conversationId: 'conv-123'
});
```

## Benefits

- **Type Safety**: Full TypeScript type checking
- **Performance**: No runtime type conversions
- **Maintainability**: Clear separation of concerns
- **Scalability**: Easier to extend with new features
- **Consistency**: Single source of truth for state

## Implementation Details

### State Persistence

The state is persisted in both memory and Redis:

- In-memory cache provides fast access
- Redis provides durable storage with expiration (7 days by default)

### Error Handling

All methods include comprehensive error handling with detailed logging.

### Logging

The system uses a structured logging approach with context information.

## For More Information

See the full documentation in `docs/dynamic-collaboration-state-management.md`.
