/**
 * Dynamic Collaboration State Store
 *
 * This file implements a specialized state store for the dynamic collaboration system.
 * It provides methods for storing, retrieving, and updating state specific to dynamic collaboration.
 */

import { Redis } from '@upstash/redis';
import logger from '../../collaborative-iteration/utils/logger';
import { DynamicCollaborationState } from './models';

/**
 * Redis client configuration
 */
const REDIS_KEY_PREFIX = 'dynamic-collab:';
const REDIS_EXPIRY = 60 * 60 * 24 * 7; // 7 days in seconds

/**
 * Dynamic Collaboration State Store
 *
 * This class provides methods for managing dynamic collaboration state
 * with both in-memory caching and Redis persistence.
 */
export class DynamicCollaborationStateStore {
  private redis: Redis | null = null;
  private inMemoryCache: Map<string, DynamicCollaborationState> = new Map();
  private static instance: DynamicCollaborationStateStore;

  /**
   * Get the singleton instance of the state store
   */
  public static getInstance(): DynamicCollaborationStateStore {
    if (!DynamicCollaborationStateStore.instance) {
      DynamicCollaborationStateStore.instance = new DynamicCollaborationStateStore();
    }
    return DynamicCollaborationStateStore.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    this.initializeRedis();
  }

  /**
   * Initialize Redis connection
   */
  private initializeRedis(): void {
    try {
      this.redis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL || '',
        token: process.env.UPSTASH_REDIS_REST_TOKEN || ''
      });
      logger.info('Dynamic Collaboration State Store initialized with Redis');

      // Test Redis connection
      this.redis.ping().then((result) => {
        logger.info(`Redis connection test: ${result}`);
      });
    } catch (error) {
      logger.error('Failed to initialize Redis connection', { error });
      this.redis = null;
    }
  }

  /**
   * Get state for a session
   * @param sessionId The session ID
   * @returns The session state or null if not found
   */
  public async getState(sessionId: string): Promise<DynamicCollaborationState | null> {
    try {
      // Check in-memory cache first
      if (this.inMemoryCache.has(sessionId)) {
        logger.debug(`Retrieved state from in-memory cache for session ${sessionId}`, { sessionId });
        return this.inMemoryCache.get(sessionId) || null;
      }

      // If not in cache, try to get from Redis
      if (this.redis) {
        const redisKey = `${REDIS_KEY_PREFIX}${sessionId}`;
        const rawState = await this.redis.json.get<any>(redisKey, "$");

        if (rawState) {
          // Handle the nested array structure from Redis
          let state: DynamicCollaborationState;

          // If the result is an array with one element (common with Upstash Redis JSON)
          if (Array.isArray(rawState) && rawState.length === 1) {
            state = rawState[0] as DynamicCollaborationState;
          } else {
            state = rawState as DynamicCollaborationState;
          }

          // Update in-memory cache
          this.inMemoryCache.set(sessionId, state);
          logger.debug(`Retrieved state from Redis for session ${sessionId}`, { sessionId });
          return state;
        }
      }

      logger.debug(`No state found for session ${sessionId}`, { sessionId });
      return null;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error getting state for session ${sessionId}`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return null;
    }
  }

  /**
   * Set state for a session
   * @param sessionId The session ID
   * @param state The state to set
   */
  public async setState(sessionId: string, state: DynamicCollaborationState): Promise<void> {
    try {
      // Update in-memory cache
      this.inMemoryCache.set(sessionId, state);
      logger.debug(`Saved state to in-memory cache for session ${sessionId}`, { sessionId });

      // Persist to Redis if available
      if (this.redis) {
        const redisKey = `${REDIS_KEY_PREFIX}${sessionId}`;

        // Store the state in Redis using JSON.SET
        const result = await this.redis.json.set(redisKey, "$", state as unknown as Record<string, unknown>);
        logger.info('Redis save result status', { status: result });

        // Set expiration
        const expireResult = await this.redis.expire(redisKey, REDIS_EXPIRY);
        logger.info('Redis expire result status', { status: expireResult });
      }

      const artifactsCount = Object.keys(state.artifacts || {}).length;
      const messagesCount = Object.keys(state.dynamicMessages || {}).length;
      const decisionsCount = Object.keys(state.goals || {}).length;

      logger.debug(`Successfully saved state for session ${sessionId}`, {
        sessionId,
        artifactsCount,
        messagesCount,
        decisionsCount
      });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error setting state for session ${sessionId}`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
    }
  }

  /**
   * Update state for a session using an update function
   * @param sessionId The session ID
   * @param updateFn Function that takes the current state and returns the updated state
   */
  public async updateState(
    sessionId: string,
    updateFn: (state: DynamicCollaborationState | null) => DynamicCollaborationState | null
  ): Promise<void> {
    try {
      // Get current state
      const currentState = await this.getState(sessionId);

      // Apply update function
      const updatedState = updateFn(currentState);

      // If update function returns null, don't update
      if (!updatedState) return;

      // Save updated state
      await this.setState(sessionId, updatedState);
    } catch (error) {
      const err = error as Error;
      logger.error(`Error updating state for session ${sessionId}`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
    }
  }

  /**
   * Delete state for a session
   * @param sessionId The session ID
   */
  public async deleteState(sessionId: string): Promise<void> {
    try {
      // Remove from in-memory cache
      this.inMemoryCache.delete(sessionId);

      // Remove from Redis if available
      if (this.redis) {
        const redisKey = `${REDIS_KEY_PREFIX}${sessionId}`;
        await this.redis.del(redisKey);
      }

      logger.info(`Deleted state for session ${sessionId}`, { sessionId });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error deleting state for session ${sessionId}`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
    }
  }

  /**
   * List all session IDs
   * @returns Array of session IDs
   */
  public async listSessions(): Promise<string[]> {
    try {
      if (this.redis) {
        // Use scan instead of keys for production environments
        // This is a simplified implementation
        const keys = await this.redis.keys(`${REDIS_KEY_PREFIX}*`);
        return keys.map(key => key.replace(REDIS_KEY_PREFIX, ''));
      }

      // If Redis is not available, return in-memory cache keys
      return Array.from(this.inMemoryCache.keys());
    } catch (error) {
      const err = error as Error;
      logger.error('Error listing sessions', {
        error: err.message || String(error),
        stack: err.stack
      });
      return [];
    }
  }
}

// Export singleton instance
export const dynamicStateStore = DynamicCollaborationStateStore.getInstance();
