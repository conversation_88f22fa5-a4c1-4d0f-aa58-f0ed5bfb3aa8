/**
 * Goal Manager
 *
 * This class manages content goals in the dynamic collaboration system,
 * defining, assigning, tracking, and adjusting goals as needed.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../collaborative-iteration/utils/logger';
import {
  GoalType,
  GoalStatus,
  ContentGoal,
  createStateManager
} from './state';

export class GoalManager {
  private sessionId: string;
  private stateManager: ReturnType<typeof createStateManager>;

  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = createStateManager(sessionId);
  }

  /**
   * Define initial goals for the content creation process
   * @returns Array of created goal IDs
   */
  public async defineGoals(): Promise<string[]> {
    try {
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Define goal templates based on content type
      const goalTemplates = this.getGoalTemplates(state.contentType);

      // Create goals from templates
      const goals: Record<string, ContentGoal> = {};
      const goalIds: string[] = [];

      for (const template of goalTemplates) {
        const goalId = uuidv4();
        goals[goalId] = {
          id: goalId,
          description: template.description,
          type: template.type,
          status: GoalStatus.PENDING,
          dependencies: template.dependencies,
          progress: 0,
          createdAt: new Date().toISOString(),
          criteria: template.criteria
        };
        goalIds.push(goalId);
      }

      // Define goals using the state manager's defineGoals method
      const goalTemplatesForManager = goalTemplates.map(template => ({
        description: template.description,
        type: template.type,
        dependencies: template.dependencies,
        criteria: template.criteria
      }));

      await this.stateManager.defineGoals(goalTemplatesForManager);

      logger.info(`Defined ${goalIds.length} goals for session ${this.sessionId}`, {
        sessionId: this.sessionId,
        goalCount: goalIds.length,
        goalTypes: Object.values(goals).map(g => g.type)
      });

      return goalIds;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error defining goals`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return [];
    }
  }

  /**
   * Assign a goal to an agent
   * @param goalId Goal ID to assign
   * @param agentId Agent ID to assign the goal to
   * @returns True if assignment was successful
   */
  public async assignGoal(goalId: string, agentId: string): Promise<boolean> {
    try {
      // Use the state manager to assign the goal
      return await this.stateManager.assignGoal(goalId, agentId);
    } catch (error) {
      const err = error as Error;
      logger.error(`Error assigning goal`, {
        sessionId: this.sessionId,
        goalId,
        agentId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Track goal progress
   * @param goalId Goal ID to update
   * @param progress Progress percentage (0-100)
   */
  public async trackGoalProgress(goalId: string, progress: number): Promise<void> {
    try {
      // Use the state manager to track goal progress
      await this.stateManager.trackGoalProgress(goalId, progress);

    } catch (error) {
      const err = error as Error;
      logger.error(`Error tracking goal progress`, {
        sessionId: this.sessionId,
        goalId,
        progress,
        error: err.message || String(error),
        stack: err.stack
      });
    }
  }

  /**
   * Complete a goal
   * @param goalId Goal ID to complete
   * @param artifactId Optional artifact ID associated with the goal completion
   * @returns True if completion was successful
   */
  public async completeGoal(goalId: string, artifactId?: string): Promise<boolean> {
    try {
      // Use the state manager to complete the goal
      return await this.stateManager.completeGoal(goalId, artifactId);
    } catch (error) {
      const err = error as Error;
      logger.error(`Error completing goal`, {
        sessionId: this.sessionId,
        goalId,
        artifactId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }



  /**
   * Get goal templates based on content type
   */
  private getGoalTemplates(contentType: string): Array<{
    description: string;
    type: GoalType;
    dependencies: string[];
    criteria: string[];
  }> {
    // Base templates that apply to all content types
    const baseTemplates = [
      {
        description: 'Conduct market research and audience analysis',
        type: GoalType.MARKET_RESEARCH,
        dependencies: [],
        criteria: [
          'Identify target audience demographics and behaviors',
          'Analyze market trends relevant to the topic',
          'Determine content gaps and opportunities',
          'Identify audience pain points and interests'
        ]
      },
      {
        description: 'Identify SEO keywords and search intent',
        type: GoalType.KEYWORD_ANALYSIS,
        dependencies: [],
        criteria: [
          'Research primary and secondary keywords',
          'Analyze search intent for target keywords',
          'Evaluate keyword competition and difficulty',
          'Prioritize keywords based on value and relevance'
        ]
      },
      {
        description: 'Develop content strategy and structure',
        type: GoalType.CONTENT_STRATEGY,
        dependencies: ['Conduct market research and audience analysis', 'Identify SEO keywords and search intent'],
        criteria: [
          'Create logical content structure with clear sections',
          'Define tone and style appropriate for audience',
          'Incorporate keywords strategically',
          'Plan multimedia elements and enhancements'
        ]
      },
      {
        description: 'Generate initial content draft',
        type: GoalType.CONTENT_CREATION,
        dependencies: ['Develop content strategy and structure'],
        criteria: [
          'Create compelling introduction and conclusion',
          'Develop comprehensive section content',
          'Incorporate keywords naturally',
          'Ensure proper formatting and readability'
        ]
      },
      {
        description: 'Perform SEO optimization',
        type: GoalType.SEO_OPTIMIZATION,
        dependencies: ['Generate initial content draft'],
        criteria: [
          'Evaluate keyword distribution and placement',
          'Check metadata optimization',
          'Assess content length and comprehensiveness',
          'Suggest internal linking opportunities'
        ]
      },
      {
        description: 'Conduct quality assessment',
        type: GoalType.QUALITY_ASSESSMENT,
        dependencies: ['Perform SEO optimization'],
        criteria: [
          'Evaluate content accuracy and relevance',
          'Check grammar, spelling, and readability',
          'Assess alignment with content strategy',
          'Verify SEO best practices implementation'
        ]
      }
    ];

    // Add content type specific templates or modify base templates
    switch (contentType) {
      case 'blog-article':
        // Blog articles might need additional goals or criteria
        return baseTemplates;

      case 'product-page':
        // Product pages need specific goals
        const productTemplates = [...baseTemplates];
        // Modify content strategy criteria for product pages
        productTemplates[2].criteria.push('Include product features and benefits');
        productTemplates[2].criteria.push('Add clear call-to-action elements');
        return productTemplates;

      case 'buying-guide':
        // Buying guides need specific goals
        const guideTemplates = [...baseTemplates];
        // Modify content strategy criteria for buying guides
        guideTemplates[2].criteria.push('Include comparison criteria');
        guideTemplates[2].criteria.push('Add product recommendations');
        return guideTemplates;

      default:
        return baseTemplates;
    }
  }
}
