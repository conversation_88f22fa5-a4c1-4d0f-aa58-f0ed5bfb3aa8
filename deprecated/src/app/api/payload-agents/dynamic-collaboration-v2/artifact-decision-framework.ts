/**
 * Artifact Decision Framework
 * 
 * This class enables agents to determine which artifacts to create
 * based on content needs and manage artifact creation and updates.
 */

import { v4 as uuidv4 } from 'uuid';
import { stateStore } from '../collaborative-iteration/utils/stateStore';
import logger from '../collaborative-iteration/utils/logger';
import { 
  ArtifactDecision, 
  ContentGoal, 
  DynamicAgentMessage, 
  DynamicMessageType,
  GoalType
} from './types';
import { IterativeArtifact, ArtifactStatus, Reasoning } from '../collaborative-iteration/types';

export class ArtifactDecisionFramework {
  private sessionId: string;
  
  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }
  
  /**
   * Decide whether an agent should create a new artifact
   * @param agentId The agent making the decision
   * @param goalId The goal the agent is working on
   * @returns Decision about artifact creation
   */
  public async decideArtifactCreation(
    agentId: string,
    goalId: string
  ): Promise<ArtifactDecision> {
    try {
      const state = await stateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }
      
      const goal = state.goals?.[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }
      
      // Get existing artifacts
      const existingArtifacts = state.generatedArtifacts?.map(id => state.artifacts?.[id]).filter(Boolean) || [];
      
      // Determine if an artifact should be created based on goal type
      const artifactType = this.getArtifactTypeForGoal(goal);
      
      // Check if a similar artifact already exists
      const similarArtifacts = existingArtifacts.filter(artifact => 
        artifact.type === artifactType
      );
      
      // If no similar artifact exists, create a new one
      if (similarArtifacts.length === 0) {
        return {
          shouldCreate: true,
          artifactType,
          reasoning: {
            thoughts: [
              `No existing ${artifactType} artifact found`,
              `A ${artifactType} artifact is needed for the ${goal.type} goal`
            ],
            considerations: [
              'Artifact is required for workflow progress',
              'Creating a new artifact is more efficient than modifying existing ones'
            ],
            decision: `Create a new ${artifactType} artifact`,
            confidence: 0.9
          }
        };
      }
      
      // If similar artifacts exist, determine if a new one should be created
      // or an existing one should be updated
      const latestArtifact = similarArtifacts.reduce((latest, current) => {
        const latestTimestamp = new Date(latest.createdAt).getTime();
        const currentTimestamp = new Date(current.createdAt).getTime();
        return currentTimestamp > latestTimestamp ? current : latest;
      }, similarArtifacts[0]);
      
      // If the latest artifact is complete and high quality, no need for a new one
      if (
        latestArtifact.status === 'completed' && 
        (latestArtifact.qualityScore || 0) > 80
      ) {
        return {
          shouldCreate: false,
          artifactType,
          reasoning: {
            thoughts: [
              `Existing high-quality ${artifactType} artifact found`,
              `The existing artifact meets the requirements for the ${goal.type} goal`
            ],
            considerations: [
              'Creating a duplicate artifact would be inefficient',
              'Existing artifact has high quality score'
            ],
            decision: `Use existing ${artifactType} artifact instead of creating a new one`,
            confidence: 0.85
          },
          dependencies: [latestArtifact.id]
        };
      }
      
      // Otherwise, create a new artifact
      return {
        shouldCreate: true,
        artifactType,
        reasoning: {
          thoughts: [
            `Existing ${artifactType} artifacts are not sufficient`,
            `A new ${artifactType} artifact would better serve the ${goal.type} goal`
          ],
          considerations: [
            'Existing artifacts may be outdated or low quality',
            'Fresh perspective could improve content quality'
          ],
          decision: `Create a new ${artifactType} artifact despite existing ones`,
          confidence: 0.75
        },
        dependencies: similarArtifacts.map(a => a.id)
      };
    } catch (error) {
      const err = error as Error;
      logger.error(`Error deciding artifact creation`, {
        sessionId: this.sessionId,
        agentId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      
      // Default to creating a new artifact in case of error
      return {
        shouldCreate: true,
        artifactType: 'unknown',
        reasoning: {
          thoughts: [
            'Error occurred during decision process',
            'Defaulting to creating a new artifact'
          ],
          considerations: [
            'Error handling requires a safe default',
            'Creating a new artifact is the safest option'
          ],
          decision: 'Create a new artifact due to error in decision process',
          confidence: 0.5
        }
      };
    }
  }
  
  /**
   * Create a new artifact
   * @param agentId The agent creating the artifact
   * @param artifactType The type of artifact to create
   * @param content The artifact content
   * @param metadata Additional metadata for the artifact
   * @returns The ID of the created artifact
   */
  public async createArtifact(
    agentId: string,
    artifactType: string,
    content: any,
    metadata: Record<string, any> = {},
    goalId?: string
  ): Promise<string> {
    try {
      // Generate a new artifact ID
      const artifactId = uuidv4();
      
      // Create the artifact
      const artifact: IterativeArtifact = {
        id: artifactId,
        name: this.getArtifactName(artifactType, metadata.topic || ''),
        type: artifactType,
        status: 'completed' as ArtifactStatus,
        createdBy: agentId,
        createdAt: new Date().toISOString(),
        currentVersion: 1,
        iterations: [
          {
            version: 1,
            timestamp: new Date().toISOString(),
            agent: agentId,
            content,
            feedback: [],
            incorporatedConsultations: []
          }
        ],
        qualityScore: metadata.qualityScore || 75,
        metadata: {
          ...metadata,
          goalId
        }
      };
      
      // Add the artifact to state
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;
        
        // Add the artifact to the artifacts object
        const updatedArtifacts = {
          ...currentState.artifacts,
          [artifactId]: artifact
        };
        
        // Add the artifact ID to the generatedArtifacts array
        const updatedGeneratedArtifacts = [
          ...(currentState.generatedArtifacts || []),
          artifactId
        ];
        
        return {
          ...currentState,
          artifacts: updatedArtifacts,
          generatedArtifacts: updatedGeneratedArtifacts
        };
      });
      
      // Create artifact creation message
      const artifactMessage: DynamicAgentMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: agentId,
        to: 'all',
        type: DynamicMessageType.ARTIFACT_CREATED,
        content: {
          artifactId,
          artifactType,
          name: artifact.name,
          summary: this.generateArtifactSummary(content, artifactType)
        },
        conversationId: uuidv4(),
        reasoning: {
          thoughts: [
            `Created a new ${artifactType} artifact`,
            `This artifact supports the workflow goals`
          ],
          considerations: [
            'Quality and comprehensiveness of the artifact',
            'Relevance to the overall content objectives'
          ],
          decision: `Share the new ${artifactType} artifact with all agents`,
          confidence: 0.9
        }
      };
      
      // Store the message
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;
        
        const dynamicMessages = { ...currentState.dynamicMessages };
        dynamicMessages[artifactMessage.id] = artifactMessage;
        
        return {
          ...currentState,
          dynamicMessages
        };
      });
      
      logger.info(`Created artifact ${artifactId} of type ${artifactType}`, {
        sessionId: this.sessionId,
        artifactId,
        artifactType,
        agentId,
        goalId
      });
      
      // If a goal ID was provided, update the goal with the artifact ID
      if (goalId) {
        await stateStore.updateState(this.sessionId, (currentState) => {
          if (!currentState || !currentState.goals || !currentState.goals[goalId]) {
            return currentState;
          }
          
          const updatedGoals = { ...currentState.goals };
          updatedGoals[goalId] = {
            ...updatedGoals[goalId],
            artifactId
          };
          
          return {
            ...currentState,
            goals: updatedGoals
          };
        });
      }
      
      return artifactId;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error creating artifact`, {
        sessionId: this.sessionId,
        agentId,
        artifactType,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Update an existing artifact
   * @param artifactId The ID of the artifact to update
   * @param agentId The agent updating the artifact
   * @param content The new content
   * @param reasoning The reasoning for the update
   * @returns True if the update was successful
   */
  public async updateArtifact(
    artifactId: string,
    agentId: string,
    content: any,
    reasoning: Reasoning
  ): Promise<boolean> {
    try {
      const state = await stateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }
      
      const artifact = state.artifacts?.[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }
      
      // Create a new iteration
      const newVersion = artifact.currentVersion + 1;
      const newIteration = {
        version: newVersion,
        timestamp: new Date().toISOString(),
        agent: agentId,
        content,
        feedback: [],
        incorporatedConsultations: []
      };
      
      // Update the artifact
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState || !currentState.artifacts || !currentState.artifacts[artifactId]) {
          return currentState;
        }
        
        const updatedArtifacts = { ...currentState.artifacts };
        updatedArtifacts[artifactId] = {
          ...updatedArtifacts[artifactId],
          currentVersion: newVersion,
          iterations: [...updatedArtifacts[artifactId].iterations, newIteration]
        };
        
        return {
          ...currentState,
          artifacts: updatedArtifacts
        };
      });
      
      // Create artifact update message
      const updateMessage: DynamicAgentMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: agentId,
        to: 'all',
        type: DynamicMessageType.ARTIFACT_UPDATED,
        content: {
          artifactId,
          artifactType: artifact.type,
          name: artifact.name,
          version: newVersion,
          summary: this.generateArtifactSummary(content, artifact.type)
        },
        conversationId: uuidv4(),
        reasoning
      };
      
      // Store the message
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;
        
        const dynamicMessages = { ...currentState.dynamicMessages };
        dynamicMessages[updateMessage.id] = updateMessage;
        
        return {
          ...currentState,
          dynamicMessages
        };
      });
      
      logger.info(`Updated artifact ${artifactId} to version ${newVersion}`, {
        sessionId: this.sessionId,
        artifactId,
        version: newVersion,
        agentId
      });
      
      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error updating artifact`, {
        sessionId: this.sessionId,
        artifactId,
        agentId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }
  
  /**
   * Get the appropriate artifact type for a goal
   * @param goal The goal to get the artifact type for
   * @returns The artifact type
   */
  private getArtifactTypeForGoal(goal: ContentGoal): string {
    switch (goal.type) {
      case GoalType.MARKET_RESEARCH:
        return 'market-research';
      case GoalType.KEYWORD_ANALYSIS:
        return 'seo-keyword-analysis';
      case GoalType.CONTENT_STRATEGY:
        return 'content-strategy';
      case GoalType.CONTENT_CREATION:
        return 'content-draft';
      case GoalType.SEO_OPTIMIZATION:
        return 'seo-optimized-content';
      case GoalType.QUALITY_ASSESSMENT:
        return 'quality-assessment';
      default:
        return 'generic-artifact';
    }
  }
  
  /**
   * Generate a name for an artifact
   * @param artifactType The type of artifact
   * @param topic The content topic
   * @returns A descriptive name for the artifact
   */
  private getArtifactName(artifactType: string, topic: string): string {
    const topicSuffix = topic ? `: ${topic}` : '';
    
    switch (artifactType) {
      case 'market-research':
        return `Market Research Report${topicSuffix}`;
      case 'seo-keyword-analysis':
        return `SEO Keyword Analysis${topicSuffix}`;
      case 'content-strategy':
        return `Content Strategy Plan${topicSuffix}`;
      case 'content-draft':
        return `Content Draft${topicSuffix}`;
      case 'seo-optimized-content':
        return `SEO-Optimized Content${topicSuffix}`;
      case 'quality-assessment':
        return `Quality Assessment Report${topicSuffix}`;
      default:
        return `${artifactType.charAt(0).toUpperCase() + artifactType.slice(1)}${topicSuffix}`;
    }
  }
  
  /**
   * Generate a summary of an artifact's content
   * @param content The artifact content
   * @param artifactType The type of artifact
   * @returns A brief summary of the artifact
   */
  private generateArtifactSummary(content: any, artifactType: string): string {
    // If content is a string, summarize it
    if (typeof content === 'string') {
      const words = content.split(/\s+/);
      if (words.length > 50) {
        return words.slice(0, 50).join(' ') + '...';
      }
      return content;
    }
    
    // If content is an object, create a summary based on artifact type
    switch (artifactType) {
      case 'market-research':
        return `Market research covering ${content.targetAudience || 'audience'} analysis and ${content.marketTrends?.length || 0} market trends.`;
      case 'seo-keyword-analysis':
        return `SEO keyword analysis with ${content.primaryKeywords?.length || 0} primary and ${content.secondaryKeywords?.length || 0} secondary keywords.`;
      case 'content-strategy':
        return `Content strategy with ${content.sections?.length || 0} sections and ${content.keypoints?.length || 0} key points.`;
      case 'content-draft':
        return `Content draft with approximately ${content.wordCount || 'unknown'} words.`;
      case 'seo-optimized-content':
        return `SEO-optimized content with a quality score of ${content.seoScore || 'unknown'}.`;
      default:
        return `${artifactType} artifact created.`;
    }
  }
}
