// src/app/(payload)/api/agents/dynamic-collaboration/enhanced-collaboration-controller.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  CollaborationState,
  CollaborationMessage,
  EnhancedA2AMessage,
  Artifact,
  Decision,
  Reasoning,
  TextPart,
  MessageIntention,
  Part
} from '../a2atypes';
import { MessageBus } from './enhanced-message-bus';
import { ArtifactManager } from './artifact-manager';
import { DecisionManager } from './decision-manager';
import { GoalWorkflowEngine, Goal } from './goal-workflow-engine';
import { GoalProgressMonitor } from './goal-progress-monitor';
import { FinalOutputGenerator } from './final-output-generator';
import { ContentGenerationRequest } from './types';

/**
 * Enhanced Collaboration Controller
 * 
 * This class manages the entire collaboration process using the enhanced A2A
 * protocol with reasoning, artifacts, and collaborative decision-making.
 */
export class EnhancedCollaborationController {
  private sessions: Map<string, {
    state: CollaborationState;
    messageBus: MessageBus;
    artifactManager: ArtifactManager;
    decisionManager: DecisionManager;
    workflowEngine: GoalWorkflowEngine;
    progressMonitor: GoalProgressMonitor;
    finalOutputGenerator: FinalOutputGenerator;
  }> = new Map();
  
  // Flag to prevent recursive processing
  private isProcessingSystemMessage = false;
  
  private baseUrl: string;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_API_URL || 
      `http://${process.env.VERCEL_URL || 'localhost:3000'}`;
  }

  /**
   * Start a new collaborative content generation session
   */
  async startCollaboration(request: ContentGenerationRequest): Promise<CollaborationState> {
    // Create a new collaboration state
    const state = this.createCollaborationState(request);
    
    // Add duration property required by CollaborationState interface
    const stateWithDuration = {
      ...state,
      duration: '0s' // Will be updated when session ends
    };
    
    // Create artifact manager
    const artifactManager = new ArtifactManager();
    
    // Create decision manager
    const decisionManager = new DecisionManager();
    
    // Create enhanced message bus
    const messageBus = new MessageBus(stateWithDuration, this.baseUrl);
    
    // Create goal workflow engine
    const workflowEngine = new GoalWorkflowEngine(
      messageBus as any, // Type cast to avoid type mismatch
      artifactManager,
      decisionManager,
      stateWithDuration
    );
    
    // Create goal progress monitor
    const progressMonitor = new GoalProgressMonitor(
      artifactManager,
      messageBus
    );
    
    // Create final output generator
    const finalOutputGenerator = new FinalOutputGenerator(
      artifactManager,
      decisionManager,
      messageBus,
      stateWithDuration
    );
    
    // Store the session
    this.sessions.set(stateWithDuration.id, {
      state: stateWithDuration,
      messageBus,
      artifactManager,
      decisionManager,
      workflowEngine,
      progressMonitor,
      finalOutputGenerator
    });
    
    // Initialize the workflow
    await workflowEngine.initializeWorkflow();
    
    // Return the state
    return messageBus.getState();
  }
  
  /**
   * Get a collaboration session by ID
   */
  getSession(sessionId: string): {
    state: CollaborationState;
    messageBus: MessageBus;
    artifactManager: ArtifactManager;
    decisionManager: DecisionManager;
    workflowEngine: GoalWorkflowEngine;
    progressMonitor: GoalProgressMonitor;
    finalOutputGenerator: FinalOutputGenerator;
  } | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Get a collaboration session state by ID
   */
  getSessionState(sessionId: string): CollaborationState | null {
    const session = this.sessions.get(sessionId);
    return session ? session.messageBus.getState() : null;
  }
  
  /**
   * Get all collaboration session states
   */
  getAllSessionStates(): CollaborationState[] {
    return Array.from(this.sessions.values()).map(session => 
      session.messageBus.getState()
    );
  }
  
  /**
   * Orchestrate the full workflow from start to finish in a synchronized manner
   * This demonstrates the complete flow of agent collaboration, content evaluation,
   * and final output generation
   */
  async orchestrateFullWorkflow(request: ContentGenerationRequest): Promise<{
    sessionId: string;
    finalOutput: any;
    state: CollaborationState;
  }> {
    console.log('Starting orchestrated workflow for topic:', request.topic);
    
    // Step 1: Start the collaboration session
    const initialState = await this.startCollaboration(request);
    const sessionId = initialState.id;
    const session = this.getSession(sessionId);
    
    if (!session) {
      throw new Error(`Failed to create session for topic: ${request.topic}`);
    }
    
    console.log(`Session created with ID: ${sessionId}`);
    
    // Step 2: Wait for initial goals to be created and active
    let goalCompletionPromise = new Promise<void>((resolve, reject) => {
      // Poll for goal completion status
      const checkGoals = async () => {
        const currentState = session.messageBus.getState();
        const allGoalsComplete = currentState.goals.every(goal => goal.status === 'completed');
        const hasFailedGoals = currentState.goals.some(goal => goal.status === 'blocked');
        
        if (allGoalsComplete) {
          console.log('All goals completed successfully');
          resolve();
          return;
        }
        
        if (hasFailedGoals) {
          console.error('Some goals are blocked/failed');
          reject(new Error('Workflow failed - some goals could not be completed'));
          return;
        }
        
        // Continue checking while there are active goals
        if (currentState.goals.some(goal => goal.status === 'in-progress')) {
          console.log(`Goals in progress: ${currentState.goals.filter(g => g.status === 'in-progress').map(g => g.description).join(', ')}`);
          setTimeout(checkGoals, 2000); // Check every 2 seconds
        } else {
          console.log('No active goals found, but not all goals are complete. Workflow may be stuck.');
          resolve(); // Move on anyway to see what we can evaluate
        }
      };
      
      // Start checking goals
      setTimeout(checkGoals, 2000);
    });
    
    // Wait for goals to be completed (or timeout after 5 minutes)
    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => reject(new Error('Workflow timed out after 5 minutes')), 5 * 60 * 1000);
    });
    
    try {
      await Promise.race([goalCompletionPromise, timeoutPromise]);
    } catch (error) {
      console.error('Error during goal completion:', error);
      // Continue anyway to see what we can evaluate
    }
    
    // Step 3: Trigger content evaluation
    console.log('Starting content evaluation process');
    try {
      const contentEvaluationDecision = await session.finalOutputGenerator.initializeContentEvaluation();
      console.log('Content evaluation initiated with decision ID:', contentEvaluationDecision.id);
      
      // Step 4: Wait for evaluation results (poll for decision status)
      let evaluationComplete = false;
      let evaluationAttempts = 0;
      const MAX_EVALUATION_ATTEMPTS = 20; // Check up to 20 times
      
      while (!evaluationComplete && evaluationAttempts < MAX_EVALUATION_ATTEMPTS) {
        evaluationAttempts++;
        await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds between checks
        
        const updatedDecision = session.decisionManager.getDecisionById(contentEvaluationDecision.id);
        if (updatedDecision?.status === 'decided') {
          evaluationComplete = true;
          console.log('Content evaluation complete with decision:', updatedDecision.decision);
        } else {
          console.log(`Waiting for content evaluation (attempt ${evaluationAttempts}/${MAX_EVALUATION_ATTEMPTS})...`);
        }
      }
      
      // Step 5: Process the evaluation results to get final output
      let finalOutput;
      if (evaluationComplete) {
        console.log('Processing final output based on evaluations');
        finalOutput = await session.finalOutputGenerator.processFinalDecision();
      } else {
        console.log('Content evaluation did not complete in time, proceeding with best available output');
        finalOutput = await session.finalOutputGenerator.generateBestEffortOutput();
      }
      
      console.log('Final output generated successfully');
      
      // Step 6: Mark the collaboration as completed
      session.messageBus.updateState(state => ({
        ...state,
        status: 'completed',
        endTime: new Date().toISOString()
      }));
      
      // Return the complete package
      return {
        sessionId,
        finalOutput,
        state: session.messageBus.getState()
      };
      
    } catch (error) {
      console.error('Error during orchestration workflow:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Workflow orchestration failed: ${errorMessage}`);
    }
  }

  /**
   * Send a message to a collaboration session
   */
  async sendMessage(sessionId: string, message: EnhancedA2AMessage): Promise<void> {
    const session = this.sessions.get(sessionId);
    
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }
    
    // Detect system-generated goal completion or workflow messages to prevent infinite loops
    const isSystemGoalMessage = message.from === 'system' && 
      message.parts?.some(part => 
        part.type === 'text' && (
          part.text.includes('goal completed') || 
          part.text.includes('workflow') ||
          part.text.includes('GOAL_COMPLETE')
        )
      );
    
    // Send the message
    await session.messageBus.sendMessage(message);
    
    // Skip recursive processing for system-generated goal messages
    if (isSystemGoalMessage && this.isProcessingSystemMessage) {
      console.log('Skipping recursive processing of system goal message');
      return;
    }
    
    try {
      // Set flag to prevent recursive processing of system messages
      if (isSystemGoalMessage) {
        this.isProcessingSystemMessage = true;
      }
      
      // Process the message for goal tracking
      await session.workflowEngine.processMessage(message);
      
      // Use the progress monitor to detect goal progress and completion
      const { goalUpdates, artifactsCreated } = await session.progressMonitor.processMessage(
        message, 
        session.state.goals
      );
      
      // Apply goal updates
      if (Object.keys(goalUpdates).length > 0) {
        for (const [goalId, update] of Object.entries(goalUpdates)) {
          if (update.completed) {
            await session.workflowEngine.completeGoal(goalId);
          } else if (update.progress > 0) {
            session.workflowEngine.updateGoalProgress(goalId, update.progress);
          }
        }
      }
      
      // Register any newly created artifacts
      for (const artifact of artifactsCreated) {
        // Add artifact reference to the message if not already present
        if (!message.artifactReferences) {
          message.artifactReferences = [];
        }
        if (!message.artifactReferences.includes(artifact.id)) {
          message.artifactReferences.push(artifact.id);
        }
      }
    } finally {
      // Reset flag
      if (isSystemGoalMessage) {
        this.isProcessingSystemMessage = false;
      }
    }
  }
  
  /**
   * Send a legacy format message to a collaboration session
   */
  async sendLegacyMessage(sessionId: string, message: CollaborationMessage): Promise<void> {
    const session = this.sessions.get(sessionId);
    
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }
    
    // Convert to enhanced format
    const enhancedMessage = this.convertLegacyMessage(message);
    
    // Send the message
    await this.sendMessage(sessionId, enhancedMessage);
  }
  
  /**
   * Convert a legacy message to enhanced format
   */
  private convertLegacyMessage(message: CollaborationMessage): EnhancedA2AMessage {
    // Create reasoning from message content
    const reasoning: Reasoning = {
      thoughts: ['Processing message from legacy format'],
      considerations: ['Legacy messages may have limited context'],
      decision: 'Convert message to enhanced format for compatibility',
      confidence: 0.8
    };
    
    // Create text part
    const textPart: TextPart = {
      type: 'text',
      text: message.content
    };
    
    // Determine intentions based on message type
    let intentions: string[] = ['inform'];
    
    switch (message.type) {
      case 'REQUEST_INFO':
      case 'REQUEST_FEEDBACK':
      case 'REQUEST_CONTENT':
      case 'ASK_FOLLOWUP':
        intentions = ['request'];
        break;
      case 'PROVIDE_FEEDBACK':
      case 'SUGGEST_REVISION':
        intentions = ['critique', 'suggest'];
        break;
      case 'PROVIDE_INFO':
      case 'PROVIDE_CONTENT':
      case 'ANSWER_FOLLOWUP':
        intentions = ['inform'];
        break;
      case 'ACCEPT_REVISION':
      case 'REJECT_REVISION':
        intentions = ['decide'];
        break;
      case 'SYSTEM_MESSAGE':
        intentions = ['inform'];
        break;
    }
    
    // Create enhanced message
    return {
      id: message.id,
      timestamp: message.timestamp,
      from: message.from,
      to: message.to,
      role: message.from === 'system' ? 'system' : 'agent',
      parts: [textPart],
      conversationId: message.conversationId,
      reasoning,
      intentions: intentions as any[],
      replyTo: message.replyTo,
      metadata: {
        convertedFromLegacy: true,
        originalType: message.type
      }
    };
  }
  
  /**
   * Initialize a goal for a collaboration session
   */
  async initializeGoal(sessionId: string, goalDescription: string): Promise<Goal | null> {
    const session = this.sessions.get(sessionId);
    
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }
    
    // Find the goal
    const goal = session.state.goals.find(g => g.description === goalDescription);
    
    if (!goal) {
      console.error(`Goal not found: ${goalDescription}`);
      return null;
    }
    
    // Start the goal
    await session.workflowEngine.startGoal(goal.id);
    
    // Return the updated goal
    return session.state.goals.find(g => g.id === goal.id) || null;
  }
  
  /**
   * Create a new collaboration state
   */
  private createCollaborationState(request: ContentGenerationRequest): CollaborationState {
    // Ensure required fields have default values
    const contentType = request.contentType || 'blog-article' as const;
    const tone = request.tone || 'professional';
    const keywords = request.keywords || [];
    
    return {
      id: uuidv4(), // Generate a new ID for the collaboration state
      topic: request.topic,
      contentType,
      targetAudience: request.targetAudience,
      tone,
      keywords,
      currentGoal: '',
      goals: [],
      progress: 0,
      activeAgents: [
        'market-research',
        'seo-keyword',
        'content-strategy',
        'content-generation',
        'seo-optimization'
      ],
      conversations: {},
      messageIndex: {},
      artifactIndex: {},
      decisionIndex: {},
      startTime: new Date().toISOString(),
      status: 'active'
    };
  }
  
  /**
   * Format a collaboration state for UI display
   */
  formatStateForUI(state: CollaborationState): any {
    // Extract all messages
    const allMessages = Object.values(state.messageIndex);
    
    // Sort messages by timestamp
    const sortedMessages = allMessages.sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    // Format messages for UI
    const uiMessages = sortedMessages.map(message => ({
      id: message.id,
      timestamp: message.timestamp,
      from: message.from,
      to: Array.isArray(message.to) ? message.to.join(', ') : message.to,
      intentions: message.intentions,
      content: message.parts?.find(p => p.type === 'text')?.text || '',
      replyTo: message.replyTo,
      hasReasoning: !!message.reasoning,
      reasoning: message.reasoning ? {
        thoughts: message.reasoning.thoughts,
        considerations: message.reasoning.considerations,
        decision: message.reasoning.decision,
        confidence: message.reasoning.confidence
      } : undefined
    }));
    
    // Format artifacts for UI with enhanced information
    const artifacts = Object.values(state.artifactIndex).map(artifact => {
      // Get text content from parts
      const textContent = artifact.parts?.find(p => p.type === 'text')?.text || '';
      
      // Extract structured content if available
      let structuredContent = null;
      const dataPart = artifact.parts?.find(p => p.type === 'data');
      if (dataPart && dataPart.type === 'data') {
        const data = dataPart.data;
        if (data.sectionsContent || data.metadata) {
          structuredContent = {
            title: data.metadata?.title || '',
            sections: data.sectionsContent ? Object.entries(data.sectionsContent).map(([heading, content]) => ({
              heading,
              content: typeof content === 'string' ? content : JSON.stringify(content)
            })) : [],
            metadata: data.metadata || {}
          };
        }
      }
      
      // Calculate word count and completeness
      const wordCount = textContent.split(/\s+/).filter(Boolean).length;
      
      // Determine if artifact has a complete structure
      const hasTitle = !!structuredContent?.title;
      const hasSections = structuredContent?.sections && structuredContent.sections.length > 0;
      const hasMetadata = !!structuredContent?.metadata;
      
      // Calculate completeness score (0-100)
      let completeness = 0;
      if (hasTitle) completeness += 20;
      if (hasSections) completeness += 40;
      if (hasMetadata) completeness += 10;
      if (wordCount > 300) completeness += 30;
      
      return {
        id: artifact.id,
        name: artifact.name,
        description: artifact.description,
        type: artifact.type,
        version: artifact.version,
        status: artifact.status,
        contributors: artifact.contributors,
        qualityScore: artifact.qualityScore,
        content: textContent,
        structuredContent,
        wordCount,
        completeness,
        lastUpdated: artifact.metadata?.updatedAt || artifact.metadata?.createdAt,
        creator: artifact.metadata?.createdBy
      };
    });
    
    // Format decisions for UI
    const decisions = Object.values(state.decisionIndex).map(decision => ({
      id: decision.id,
      topic: decision.topic,
      description: decision.description,
      status: decision.status,
      outcome: decision.decision,
      options: decision.options.map(option => ({
        id: option.id,
        description: option.description,
        proposedBy: option.proposedBy,
        votes: option.votes.length,
        confidence: option.votes.reduce((sum, vote) => sum + vote.confidence, 0) / 
          (option.votes.length || 1)
      }))
    }));
    
    // Format goals for UI with enhanced information
    const goals = state.goals.map(goal => {
      // Get goal artifacts
      const goalArtifacts = goal.artifacts
        .map(artifactId => state.artifactIndex[artifactId])
        .filter(Boolean);
      
      // Calculate average quality score of artifacts
      const avgQualityScore = goalArtifacts.length > 0
        ? goalArtifacts.reduce((sum, artifact) => sum + (artifact.qualityScore || 0), 0) / goalArtifacts.length
        : 0;
      
      return {
        id: goal.id,
        description: goal.description,
        criteria: goal.criteria,
        assignedTo: goal.assignedTo,
        status: goal.status,
        progress: goal.progress,
        artifactCount: goal.artifacts.length,
        decisionCount: goal.decisions.length,
        completedAt: goal.status === 'completed' ? goal.completedAt || new Date().toISOString() : undefined,
        avgArtifactQuality: Math.round(avgQualityScore),
        artifacts: goal.artifacts.map(id => ({
          id,
          name: state.artifactIndex[id]?.name || 'Unknown artifact'
        }))
      };
    });
    
    // Return formatted state with enhanced information
    return {
      id: state.id,
      topic: state.topic,
      contentType: state.contentType,
      targetAudience: state.targetAudience,
      tone: state.tone,
      keywords: state.keywords,
      progress: state.progress,
      currentGoal: state.currentGoal,
      status: state.status,
      startTime: state.startTime,
      endTime: state.endTime,
      duration: this.formatDuration(state.startTime, state.endTime || new Date().toISOString()),
      messages: uiMessages,
      artifacts,
      decisions,
      goals,
      // Add summary statistics
      stats: {
        messageCount: uiMessages.length,
        artifactCount: artifacts.length,
        completedGoals: goals.filter(g => g.status === 'completed').length,
        totalGoals: goals.length,
        averageArtifactQuality: artifacts.length > 0
          ? Math.round(artifacts.reduce((sum, a) => sum + (a.qualityScore || 0), 0) / artifacts.length)
          : 0
      }
    };
  }
  
  /**
   * Format a duration between timestamps
   */
  private formatDuration(startTime: string, endTime: string): string {
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    const durationMs = end - start;
    
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    
    return `${minutes}m ${seconds}s`;
  }
}
