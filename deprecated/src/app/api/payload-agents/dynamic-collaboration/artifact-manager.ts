// src/app/(payload)/api/agents/dynamic-collaboration/artifact-manager.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  EnhancedArtifact, 
  ArtifactType, 
  Reasoning, 
  Part, 
  Feedback,
  ArtifactVersion
} from '../a2atypes';

/**
 * Artifact Manager
 * 
 * This class manages the lifecycle of artifacts in the collaborative agent system.
 * It provides methods for creating, updating, and accessing artifacts with versioning,
 * attribution, and feedback mechanisms.
 */
export class ArtifactManager {
  private artifacts: Map<string, EnhancedArtifact> = new Map();
  
  /**
   * Get all artifacts
   */
  getAllArtifacts(): EnhancedArtifact[] {
    return Array.from(this.artifacts.values());
  }
  
  /**
   * Get artifacts by type
   */
  getArtifactsByType(type: ArtifactType): EnhancedArtifact[] {
    return this.getAllArtifacts().filter(artifact => artifact.type === type);
  }
  
  /**
   * Get artifact by ID
   */
  getArtifactById(id: string): EnhancedArtifact | undefined {
    return this.artifacts.get(id);
  }
  
  /**
   * Create a new artifact
   */
  createArtifact(
    name: string,
    description: string,
    type: ArtifactType,
    parts: Part[],
    creator: string,
    reasoning?: Reasoning,
    dependencies?: string[]
  ): EnhancedArtifact {
    const timestamp = new Date().toISOString();
    
    // Create new artifact
    const artifact: EnhancedArtifact = {
      id: uuidv4(),
      name,
      description,
      type,
      parts,
      creator,
      timestamp,
      data: {}, // Required by Artifact interface
      metadata: { 
        createdAt: timestamp,
        createdBy: creator
      },
      index: 0,
      version: 1,
      contributors: [creator],
      feedback: [],
      history: [
        {
          version: 1,
          timestamp,
          agent: creator,
          changes: 'Initial creation',
          parts,
          reasoning
        }
      ],
      dependencies,
      status: 'draft',
      qualityScore: 0
    };
    
    // Store artifact
    this.artifacts.set(artifact.id, artifact);
    
    return artifact;
  }
  
  /**
   * Update an existing artifact
   */
  updateArtifact(
    id: string,
    parts: Part[],
    updater: string,
    changes: string,
    reasoning?: Reasoning
  ): EnhancedArtifact | undefined {
    const artifact = this.getArtifactById(id);
    
    if (!artifact) {
      console.error(`Artifact not found: ${id}`);
      return undefined;
    }
    
    const timestamp = new Date().toISOString();
    
    // Create a new version
    const newVersion = artifact.version + 1;
    
    // Create version history entry
    const versionEntry: ArtifactVersion = {
      version: newVersion,
      timestamp,
      agent: updater,
      changes,
      parts,
      reasoning
    };
    
    // Add contributor if not already present
    if (!artifact.contributors.includes(updater)) {
      artifact.contributors.push(updater);
    }
    
    // Update artifact
    const updatedArtifact: EnhancedArtifact = {
      ...artifact,
      parts,
      version: newVersion,
      history: [...artifact.history, versionEntry],
      metadata: {
        ...artifact.metadata,
        updatedAt: timestamp,
        updatedBy: updater
      }
    };
    
    // Store updated artifact
    this.artifacts.set(id, updatedArtifact);
    
    return updatedArtifact;
  }
  
  /**
   * Add feedback to an artifact
   */
  addFeedback(
    artifactId: string,
    agent: string,
    category: 'improvement' | 'suggestion' | 'issue' | 'praise',
    content: string,
    priority: number,
    reasoning?: Reasoning,
    location?: string
  ): Feedback | undefined {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact) {
      console.error(`Artifact not found: ${artifactId}`);
      return undefined;
    }
    
    // Create feedback
    const feedback: Feedback = {
      id: uuidv4(),
      agent,
      timestamp: new Date().toISOString(),
      category,
      content,
      priority,
      location,
      reasoning,
      status: 'pending'
    };
    
    // Add feedback to artifact
    artifact.feedback.push(feedback);
    
    // Store updated artifact
    this.artifacts.set(artifactId, artifact);
    
    return feedback;
  }
  
  /**
   * Update feedback status
   */
  updateFeedbackStatus(
    artifactId: string,
    feedbackId: string,
    status: 'pending' | 'accepted' | 'rejected' | 'implemented',
    resolution?: string
  ): Feedback | undefined {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact) {
      console.error(`Artifact not found: ${artifactId}`);
      return undefined;
    }
    
    // Find feedback
    const feedbackIndex = artifact.feedback.findIndex(f => f.id === feedbackId);
    
    if (feedbackIndex === -1) {
      console.error(`Feedback not found: ${feedbackId}`);
      return undefined;
    }
    
    // Update feedback
    const updatedFeedback: Feedback = {
      ...artifact.feedback[feedbackIndex],
      status,
      resolution
    };
    
    // Replace feedback in artifact
    artifact.feedback[feedbackIndex] = updatedFeedback;
    
    // Store updated artifact
    this.artifacts.set(artifactId, artifact);
    
    return updatedFeedback;
  }
  
  /**
   * Calculate quality score for an artifact based on feedback and content
   */
  calculateQualityScore(artifactId: string): number {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact) {
      console.error(`Artifact not found: ${artifactId}`);
      return 0;
    }
    
    // Base score
    let score = 70;
    
    // Add score based on positive feedback
    const positiveFeedback = artifact.feedback.filter(f => 
      f.category === 'praise' || 
      (f.category === 'suggestion' && f.status === 'implemented') ||
      (f.category === 'improvement' && f.status === 'implemented')
    );
    
    score += Math.min(15, positiveFeedback.length * 2);
    
    // Deduct score based on unaddressed issues
    const unresolvedIssues = artifact.feedback.filter(f => 
      f.category === 'issue' && 
      (f.status === 'pending' || f.status === 'accepted')
    );
    
    score -= Math.min(15, unresolvedIssues.length * 3);
    
    // Add score based on version history (more revisions = better quality)
    score += Math.min(10, (artifact.version - 1) * 2);
    
    // Add score based on number of contributors (more diverse input = better quality)
    score += Math.min(5, artifact.contributors.length);
    
    // Cap score at 0-100
    return Math.max(0, Math.min(100, score));
  }
  
  /**
   * Update artifact status
   */
  updateArtifactStatus(
    artifactId: string,
    status: 'draft' | 'review' | 'approved' | 'published'
  ): EnhancedArtifact | undefined {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact) {
      console.error(`Artifact not found: ${artifactId}`);
      return undefined;
    }
    
    // Update status
    artifact.status = status;
    
    // If moving to review or approved, calculate quality score
    if (status === 'review' || status === 'approved') {
      artifact.qualityScore = this.calculateQualityScore(artifactId);
    }
    
    // Store updated artifact
    this.artifacts.set(artifactId, artifact);
    
    return artifact;
  }
  
  /**
   * Check if an artifact depends on another
   */
  hasDependency(artifactId: string, dependencyId: string): boolean {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact || !artifact.dependencies) {
      return false;
    }
    
    return artifact.dependencies.includes(dependencyId);
  }
  
  /**
   * Find artifacts that depend on a given artifact
   */
  findDependents(artifactId: string): EnhancedArtifact[] {
    return this.getAllArtifacts().filter(artifact => 
      artifact.dependencies && artifact.dependencies.includes(artifactId)
    );
  }
  
  /**
   * Get artifact content as text (joining all text parts)
   */
  getArtifactText(artifactId: string): string {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact || !artifact.parts) {
      return '';
    }
    
    // Extract text from all text parts
    const textParts = artifact.parts
      .filter(part => part.type === 'text')
      .map(part => (part.type === 'text' ? part.text : ''));
    
    return textParts.join('\n\n');
  }
  
  /**
   * Export artifact data for storage or transmission
   */
  exportArtifacts(): Record<string, EnhancedArtifact> {
    const artifactRecord: Record<string, EnhancedArtifact> = {};
    
    this.artifacts.forEach((artifact, id) => {
      artifactRecord[id] = artifact;
    });
    
    return artifactRecord;
  }
  
  /**
   * Import artifacts from external source
   */
  importArtifacts(artifactRecord: Record<string, EnhancedArtifact>): void {
    Object.entries(artifactRecord).forEach(([id, artifact]) => {
      this.artifacts.set(id, artifact);
    });
  }

  /**
   * Extract structured content from an artifact
   * This is useful for content artifacts that contain structured data like sections, headings, etc.
   */
  extractStructuredContent(artifactId: string): {
    title?: string;
    sections: Array<{ heading: string; content: string }>;
    metadata?: Record<string, any>;
  } {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact || !artifact.parts) {
      return { sections: [] };
    }
    
    // Default result structure
    const result: {
      title?: string;
      sections: Array<{ heading: string; content: string }>;
      metadata?: Record<string, any>;
    } = {
      sections: []
    };
    
    // Look for data parts that might contain structured content
    const dataParts = artifact.parts.filter(part => part.type === 'data');
    if (dataParts.length > 0 && dataParts[0].type === 'data') {
      const data = dataParts[0].data;
      
      // Check if data contains sectionsContent
      if (data.sectionsContent && typeof data.sectionsContent === 'object') {
        // Convert sectionsContent object to array of sections
        result.sections = Object.entries(data.sectionsContent).map(([heading, content]) => ({
          heading,
          content: typeof content === 'string' ? content : JSON.stringify(content)
        }));
      }
      
      // Extract title if available
      if (data.metadata?.title) {
        result.title = data.metadata.title;
      }
      
      // Extract other metadata
      if (data.metadata) {
        result.metadata = data.metadata;
      }
      
      return result;
    }
    
    // If no structured data found, try to parse from text content
    const fullText = this.getArtifactText(artifactId);
    
    // Try to extract title (assuming it's an H1 or H2 at the beginning)
    const titleMatch = fullText.match(/^\s*#\s+(.+?)\s*$|^\s*<h1>(.*?)<\/h1>\s*|^\s*<h2>(.*?)<\/h2>\s*/m);
    if (titleMatch) {
      result.title = titleMatch[1] || titleMatch[2] || titleMatch[3];
    }
    
    // Try to extract sections based on markdown or HTML headings
    const sectionMatches = fullText.matchAll(/^\s*##\s+(.+?)\s*$|^\s*<h2>(.*?)<\/h2>\s*/gm);
    
    let lastIndex = 0;
    let lastHeading = '';
    
    for (const match of sectionMatches) {
      const heading = match[1] || match[2];
      const startIndex = match.index || 0;
      
      // If we have a previous heading, extract its content
      if (lastHeading && lastIndex < startIndex) {
        const content = fullText.substring(lastIndex, startIndex).trim();
        result.sections.push({ heading: lastHeading, content });
      }
      
      lastHeading = heading;
      lastIndex = startIndex + match[0].length;
    }
    
    // Add the last section if there is one
    if (lastHeading && lastIndex < fullText.length) {
      const content = fullText.substring(lastIndex).trim();
      result.sections.push({ heading: lastHeading, content });
    }
    
    return result;
  }
  
  /**
   * Merge multiple artifacts into a new artifact
   * Useful for combining different parts of content from multiple agents
   */
  mergeArtifacts(
    artifactIds: string[],
    name: string,
    description: string,
    type: ArtifactType,
    creator: string,
    reasoning?: Reasoning
  ): EnhancedArtifact | undefined {
    if (artifactIds.length === 0) {
      console.error('No artifacts to merge');
      return undefined;
    }
    
    // Get all artifacts
    const artifacts = artifactIds
      .map(id => this.getArtifactById(id))
      .filter((a): a is EnhancedArtifact => !!a);
    
    if (artifacts.length === 0) {
      console.error('No valid artifacts found to merge');
      return undefined;
    }
    
    // Collect all parts
    const allParts: Part[] = [];
    const contributors: string[] = [];
    const dependencies: string[] = artifactIds;
    
    // Process each artifact
    artifacts.forEach(artifact => {
      // Add parts
      if (artifact.parts) {
        allParts.push(...artifact.parts);
      }
      
      // Add contributors
      if (artifact.contributors) {
        artifact.contributors.forEach(contributor => {
          if (!contributors.includes(contributor)) {
            contributors.push(contributor);
          }
        });
      }
    });
    
    // Add the creator if not already included
    if (!contributors.includes(creator)) {
      contributors.push(creator);
    }
    
    // Create the merged artifact
    return this.createArtifact(
      name,
      description,
      type,
      allParts,
      creator,
      reasoning,
      dependencies
    );
  }
  
  /**
   * Create a new version of an artifact with structured content
   * This is particularly useful for content generation artifacts
   */
  createStructuredContentVersion(
    artifactId: string,
    updater: string,
    structuredContent: {
      title?: string;
      sections: Array<{ heading: string; content: string }>;
      metadata?: Record<string, any>;
    },
    reasoning?: Reasoning
  ): EnhancedArtifact | undefined {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact) {
      console.error(`Artifact not found: ${artifactId}`);
      return undefined;
    }
    
    // Create text part with full content
    const textContent = [
      structuredContent.title ? `# ${structuredContent.title}\n\n` : '',
      ...structuredContent.sections.map(section => `## ${section.heading}\n\n${section.content}\n\n`)
    ].join('');
    
    const textPart: Part = {
      type: 'text',
      text: textContent
    };
    
    // Create data part with structured content
    const dataPart: Part = {
      type: 'data',
      data: {
        title: structuredContent.title,
        sectionsContent: structuredContent.sections.reduce((acc, section) => {
          acc[section.heading] = section.content;
          return acc;
        }, {} as Record<string, string>),
        metadata: structuredContent.metadata || {}
      }
    };
    
    // Update the artifact with the new parts
    return this.updateArtifact(
      artifactId,
      [textPart, dataPart],
      updater,
      'Updated structured content',
      reasoning
    );
  }
  
  /**
   * Analyze an artifact for quality and completeness
   */
  analyzeArtifact(artifactId: string): {
    qualityScore: number;
    completeness: number;
    wordCount: number;
    readabilityScore: number;
    suggestions: string[];
  } {
    const artifact = this.getArtifactById(artifactId);
    
    if (!artifact) {
      return {
        qualityScore: 0,
        completeness: 0,
        wordCount: 0,
        readabilityScore: 0,
        suggestions: ['Artifact not found']
      };
    }
    
    // Get text content
    const textContent = this.getArtifactText(artifactId);
    
    // Calculate word count
    const wordCount = textContent.split(/\s+/).filter(Boolean).length;
    
    // Simple readability score (0-100)
    // Based on average sentence length and word length
    const sentences = textContent.split(/[.!?]\s+/).filter(Boolean);
    const avgSentenceLength = sentences.length > 0 ? wordCount / sentences.length : 0;
    const words = textContent.split(/\s+/).filter(Boolean);
    const avgWordLength = words.length > 0 
      ? words.join('').length / words.length 
      : 0;
    
    // Readability formula (lower avg sentence length and word length = more readable)
    // Scaled to 0-100 where 100 is most readable
    const readabilityScore = Math.max(0, Math.min(100, 
      100 - (avgSentenceLength - 15) * 2 - (avgWordLength - 4.5) * 10
    ));
    
    // Calculate completeness based on structured content
    const structuredContent = this.extractStructuredContent(artifactId);
    const hasTitle = !!structuredContent.title;
    const hasSections = structuredContent.sections.length > 0;
    const hasMetadata = !!structuredContent.metadata;
    
    // Completeness score (0-100)
    let completeness = 0;
    if (hasTitle) completeness += 20;
    if (hasSections) completeness += 40;
    if (hasMetadata) completeness += 10;
    if (wordCount > 300) completeness += 30;
    
    // Generate suggestions
    const suggestions: string[] = [];
    
    if (!hasTitle) {
      suggestions.push('Add a clear title to the content');
    }
    
    if (!hasSections || structuredContent.sections.length < 3) {
      suggestions.push('Add more sections to structure the content better');
    }
    
    if (wordCount < 300) {
      suggestions.push('Expand the content to provide more value');
    }
    
    if (readabilityScore < 60) {
      suggestions.push('Improve readability by using shorter sentences and simpler words');
    }
    
    // Use the quality score from the artifact or calculate a new one
    const qualityScore = artifact.qualityScore || this.calculateQualityScore(artifactId);
    
    return {
      qualityScore,
      completeness,
      wordCount,
      readabilityScore,
      suggestions
    };
  }
}
