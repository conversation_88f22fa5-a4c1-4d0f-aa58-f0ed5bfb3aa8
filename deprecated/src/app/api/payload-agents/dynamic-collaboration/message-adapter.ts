/**
 * Message Adapter for Dynamic Collaboration System
 * 
 * This adapter converts between the new dynamic collaboration message format
 * and the legacy A2A message format expected by the agent routes.
 */

import { CollaborationMessage } from './types';

/**
 * Convert a dynamic collaboration message to the legacy A2A format
 * @param message The collaboration message to convert
 * @returns A message in the format expected by agent routes
 */
export function adaptMessageToLegacyFormat(message: CollaborationMessage): any {
  // Extract content from the message
  const content = message.content;
  
  // Create a task object based on message type
  let task: any = {
    id: message.id,
    timestamp: message.timestamp
  };
  
  // Map message types to task types
  switch (message.type) {
    case 'REQUEST_INFO':
      task.type = 'REQUEST_INFORMATION';
      task.query = typeof content === 'string' ? content : content.query;
      break;
    case 'PROVIDE_INFO':
      task.type = 'PROVIDE_INFORMATION';
      task.information = content;
      break;
    case 'REQUEST_FEEDBACK':
      task.type = 'REQUEST_FEEDBACK';
      task.content = content;
      break;
    case 'PROVIDE_FEEDBACK':
      task.type = 'PROVIDE_FEEDBACK';
      task.feedback = content;
      break;
    case 'COLLABORATION_REQUEST':
      task.type = 'REQUEST_COLLABORATION';
      task.context = content.context;
      task.content = content.content;
      break;
    case 'COLLABORATION_RESPONSE':
      task.type = 'COLLABORATION_RESPONSE';
      task.result = content;
      break;
    default:
      task.type = message.type;
      task.content = content;
  }
  
  // Create the legacy format message
  return {
    id: message.id,
    from: message.from,
    to: message.to,
    timestamp: message.timestamp,
    task: task,
    replyTo: message.replyTo
  };
}

/**
 * Convert a legacy A2A message to the dynamic collaboration format
 * @param legacyMessage The legacy message to convert
 * @returns A message in the dynamic collaboration format
 */
export function adaptLegacyMessageToNewFormat(legacyMessage: any): CollaborationMessage {
  const task = legacyMessage.task;
  let type = '';
  let content: any = {};
  
  // Map task types to message types
  switch (task.type) {
    case 'REQUEST_INFORMATION':
      type = 'REQUEST_INFO';
      content = { query: task.query };
      break;
    case 'PROVIDE_INFORMATION':
      type = 'PROVIDE_INFO';
      content = task.information;
      break;
    case 'REQUEST_FEEDBACK':
      type = 'REQUEST_FEEDBACK';
      content = task.content;
      break;
    case 'PROVIDE_FEEDBACK':
      type = 'PROVIDE_FEEDBACK';
      content = task.feedback;
      break;
    case 'REQUEST_COLLABORATION':
      type = 'COLLABORATION_REQUEST';
      content = {
        context: task.context,
        content: task.content
      };
      break;
    case 'COLLABORATION_RESPONSE':
      type = 'COLLABORATION_RESPONSE';
      content = task.result;
      break;
    default:
      type = task.type;
      content = task.content;
  }
  
  // Create the new format message
  return {
    id: legacyMessage.id,
    from: legacyMessage.from,
    to: legacyMessage.to,
    timestamp: legacyMessage.timestamp,
    type: type,
    content: content,
    replyTo: legacyMessage.replyTo,
    conversationId: legacyMessage.conversationId || legacyMessage.id,
    metadata: {
      phase: 'discussion', // Default phase
      priority: 'medium',
      tags: []
    }
  };
}
