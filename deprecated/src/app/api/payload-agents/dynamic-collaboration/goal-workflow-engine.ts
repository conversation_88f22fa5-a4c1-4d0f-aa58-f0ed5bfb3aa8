// src/app/(payload)/api/agents/dynamic-collaboration/goal-workflow-engine.ts

import { v4 as uuidv4 } from 'uuid';
import { MessageBus } from './message-bus';
import { ArtifactManager } from './artifact-manager';
import { DecisionManager } from './decision-manager';
import { CollaborationState, CollaborationMessage } from './types';

/**
 * Pending action for a goal
 */
export interface PendingAction {
  id: string;
  agent: string;
  action: string;
  completed: boolean;
}

/**
 * Goal in the content creation workflow
 */
export interface Goal {
  id: string;
  description: string;
  criteria: string[];
  dependencies: string[];
  assignedTo: string[];
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  artifacts: string[];
  decisions: string[];
  pendingActions?: PendingAction[];
  completedAt?: string; // ISO timestamp when the goal was completed
}

/**
 * Goal-Oriented Workflow Engine
 * 
 * This class replaces the previous phase-based workflow with a more flexible
 * goal-oriented approach where agents dynamically collaborate to achieve
 * content creation objectives.
 */
export class GoalWorkflowEngine {
  private messageBus: MessageBus;
  private artifactManager: ArtifactManager;
  private decisionManager: DecisionManager;
  private collaborationState: CollaborationState;
  
  // Goal templates for content creation
  private static CONTENT_CREATION_GOALS: Array<Omit<Goal, 'id' | 'status' | 'progress' | 'artifacts' | 'decisions'>> = [
    {
      description: 'Conduct market research and audience analysis',
      criteria: [
        'Identify target audience demographics and behaviors',
        'Analyze market trends relevant to the topic',
        'Determine content gaps and opportunities',
        'Identify audience pain points and interests'
      ],
      dependencies: [],
      assignedTo: ['market-research']
    },
    {
      description: 'Identify SEO keywords and search intent',
      criteria: [
        'Research primary and secondary keywords',
        'Analyze search intent for target keywords',
        'Evaluate keyword competition and difficulty',
        'Prioritize keywords based on value and relevance'
      ],
      dependencies: [],
      assignedTo: ['seo-keyword']
    },
    {
      description: 'Develop content strategy and structure',
      criteria: [
        'Create logical content structure with clear sections',
        'Define tone and style appropriate for audience',
        'Incorporate keywords strategically',
        'Plan multimedia elements and enhancements'
      ],
      dependencies: ['Conduct market research and audience analysis', 'Identify SEO keywords and search intent'],
      assignedTo: ['content-strategy']
    },
    {
      description: 'Generate initial content draft',
      criteria: [
        'Create compelling introduction and conclusion',
        'Develop comprehensive section content',
        'Incorporate keywords naturally',
        'Ensure proper formatting and readability'
      ],
      dependencies: ['Develop content strategy and structure'],
      assignedTo: ['content-generation']
    },
    {
      description: 'Perform SEO optimization',
      criteria: [
        'Evaluate keyword distribution and placement',
        'Check metadata optimization',
        'Assess content length and comprehensiveness',
        'Suggest internal linking opportunities'
      ],
      dependencies: ['Generate initial content draft'],
      assignedTo: ['seo-optimization']
    },
    {
      description: 'Refine and finalize content',
      criteria: [
        'Address SEO optimization feedback',
        'Improve readability and flow',
        'Enhance visuals and formatting',
        'Conduct final quality assessment'
      ],
      dependencies: ['Perform SEO optimization'],
      assignedTo: ['content-generation', 'content-strategy']
    }
  ];

  constructor(
    messageBus: MessageBus,
    artifactManager: ArtifactManager,
    decisionManager: DecisionManager,
    collaborationState: CollaborationState
  ) {
    this.messageBus = messageBus;
    this.artifactManager = artifactManager;
    this.decisionManager = decisionManager;
    this.collaborationState = collaborationState;
  }

  /**
   * Initialize the content creation workflow
   */
  async initializeWorkflow(): Promise<void> {
    // Create goals based on templates
    const goals = GoalWorkflowEngine.CONTENT_CREATION_GOALS.map(template => ({
      id: uuidv4(),
      description: template.description,
      criteria: template.criteria,
      dependencies: template.dependencies,
      assignedTo: template.assignedTo,
      status: 'pending' as const,
      progress: 0,
      artifacts: [],
      decisions: []
    }));
    
    // Set up initial current goal - the one with no dependencies
    const initialGoals = goals.filter(goal => goal.dependencies.length === 0);
    
    // Update collaboration state
    this.collaborationState.goals = goals;
    this.collaborationState.status = 'active';
    this.collaborationState.currentGoal = initialGoals[0]?.description || '';
    
    // Notify agents of the workflow initialization
    const initMessage = this.createSystemMessage(
      'all',
      `Content creation workflow initialized. Current goals: ${initialGoals.map(g => g.description).join(', ')}`,
      {
        thoughts: [
          'A goal-oriented workflow provides more flexibility than phases',
          'Multiple agents can work in parallel on independent goals',
          'Goals have clear criteria for completion'
        ],
        considerations: [
          'Agents need clear instructions about their responsibilities',
          'Progress tracking should be transparent'
        ],
        decision: 'Initialize workflow with parallel initial goals',
        confidence: 0.95
      }
    );
    
    await this.messageBus.sendMessage(initMessage);
    
    // Start the first goals
    for (const goal of initialGoals) {
      await this.startGoal(goal.id);
    }
  }
  
  /**
   * Get active goals (in-progress)
   */
  getActiveGoals(): Goal[] {
    return this.collaborationState.goals.filter(goal => goal.status === 'in-progress');
  }
  
  /**
   * Get pending goals (not started)
   */
  getPendingGoals(): Goal[] {
    return this.collaborationState.goals.filter(goal => goal.status === 'pending');
  }
  
  /**
   * Get completed goals
   */
  getCompletedGoals(): Goal[] {
    return this.collaborationState.goals.filter(goal => goal.status === 'completed');
  }
  
  /**
   * Get blocked goals
   */
  getBlockedGoals(): Goal[] {
    return this.collaborationState.goals.filter(goal => goal.status === 'blocked');
  }
  
  /**
   * Start a goal
   */
  async startGoal(goalId: string): Promise<void> {
    // Find the goal
    const goalIndex = this.collaborationState.goals.findIndex(goal => goal.id === goalId);
    
    if (goalIndex === -1) {
      console.error(`Goal not found: ${goalId}`);
      return;
    }
    
    const goal = this.collaborationState.goals[goalIndex];
    
    // Check if all dependencies are completed
    const dependencies = this.collaborationState.goals.filter(g => 
      goal.dependencies.includes(g.description)
    );
    
    const allDependenciesCompleted = dependencies.every(dep => dep.status === 'completed');
    
    if (!allDependenciesCompleted) {
      // Mark as blocked
      this.collaborationState.goals[goalIndex].status = 'blocked';
      console.log(`Goal "${goal.description}" is blocked by incomplete dependencies`);
      return;
    }
    
    // Update goal status
    this.collaborationState.goals[goalIndex].status = 'in-progress';
    
    // Set as current goal if no current goal is set
    if (!this.collaborationState.currentGoal) {
      this.collaborationState.currentGoal = goal.description;
    }
    
    // Notify assigned agents
    const startMessage = this.createSystemMessage(
      goal.assignedTo,
      `Goal started: ${goal.description}. Criteria: ${goal.criteria.join(', ')}`,
      {
        thoughts: [
          `Agents ${goal.assignedTo.join(', ')} are responsible for this goal`,
          'Clear criteria will help measure goal completion',
          'Collaborative work may be required between assigned agents'
        ],
        considerations: [
          'Previous goals may provide useful context and artifacts',
          'Regular progress updates will help with coordination'
        ],
        decision: `Initiate goal "${goal.description}" with assigned agents`,
        confidence: 0.9
      }
    );
    
    await this.messageBus.sendMessage(startMessage);
    
    // Request specific actions from agents
    await this.requestInitialActionsForGoal(goal);
  }
  
  /**
   * Update goal progress
   */
  updateGoalProgress(goalId: string, progress: number): void {
    // Find the goal
    const goalIndex = this.collaborationState.goals.findIndex(goal => goal.id === goalId);
    
    if (goalIndex === -1) {
      console.error(`Goal not found: ${goalId}`);
      return;
    }
    
    // Update progress
    this.collaborationState.goals[goalIndex].progress = Math.min(100, Math.max(0, progress));
    
    // Calculate overall progress
    this.updateOverallProgress();
  }
  
  /**
   * Update overall collaboration progress
   */
  private updateOverallProgress(): void {
    const totalGoals = this.collaborationState.goals.length;
    const totalProgress = this.collaborationState.goals.reduce((sum, goal) => sum + goal.progress, 0);
    
    this.collaborationState.progress = Math.round(totalProgress / totalGoals);
  }
  
  /**
   * Mark a goal as completed
   */
  async completeGoal(goalId: string): Promise<void> {
    // Find the goal
    const goalIndex = this.collaborationState.goals.findIndex(goal => goal.id === goalId);
    
    if (goalIndex === -1) {
      console.error(`Goal not found: ${goalId}`);
      return;
    }
    
    const goal = this.collaborationState.goals[goalIndex];
    
    // Update goal status
    this.collaborationState.goals[goalIndex].status = 'completed';
    this.collaborationState.goals[goalIndex].progress = 100;
    
    // Update overall progress
    this.updateOverallProgress();
    
    // Notify all agents
    const completeMessage = this.createSystemMessage(
      'all',
      `Goal completed: ${goal.description}`,
      {
        thoughts: [
          'Goal completion unblocks dependent goals',
          'Artifacts created for this goal may be useful for subsequent goals',
          'Completion criteria have been met'
        ],
        considerations: [
          'Dependent goals should be started if possible',
          'The completion of this goal may influence the overall content strategy'
        ],
        decision: `Mark goal "${goal.description}" as completed and proceed with workflow`,
        confidence: 0.95
      }
    );
    
    await this.messageBus.sendMessage(completeMessage);
    
    // Check for newly unblocked goals
    await this.startUnblockedGoals();
    
    // If all goals are completed, finalize the collaboration
    if (this.getCompletedGoals().length === this.collaborationState.goals.length) {
      await this.finalizeCollaboration();
    } else {
      // Update current goal
      const activeGoals = this.getActiveGoals();
      if (activeGoals.length > 0) {
        this.collaborationState.currentGoal = activeGoals[0].description;
      }
    }
  }
  
  /**
   * Check if a goal is ready to be completed based on pending actions
   * @param goalId The ID of the goal to check
   * @returns True if the goal is ready to be completed
   */
  async checkGoalCompletion(goalId: string): Promise<boolean> {
    // Find the goal
    const goalIndex = this.collaborationState.goals.findIndex(goal => goal.id === goalId);
    
    if (goalIndex === -1) {
      console.error(`Goal not found: ${goalId}`);
      return false;
    }
    
    const goal = this.collaborationState.goals[goalIndex];
    
    // If the goal is not in progress, it's not ready to be completed
    if (goal.status !== 'in-progress') {
      return false;
    }
    
    // If there are no pending actions, the goal might be ready to be completed
    if (!goal.pendingActions || goal.pendingActions.length === 0) {
      return true;
    }
    
    // Check if all pending actions are completed
    const allActionsCompleted = goal.pendingActions.every(action => action.completed);
    
    if (allActionsCompleted) {
      // All actions are completed, so the goal is ready to be completed
      return true;
    }
    
    return false;
  }
  
  /**
   * Mark an action as completed
   * @param actionId The ID of the action to mark as completed
   * @returns True if the goal is now completed
   */
  async completeAction(actionId: string): Promise<boolean> {
    // Find the goal with this action
    let goalCompleted = false;
    let goalId: string | null = null;
    
    for (let i = 0; i < this.collaborationState.goals.length; i++) {
      const goal = this.collaborationState.goals[i];
      
      if (goal.pendingActions) {
        const actionIndex = goal.pendingActions.findIndex(action => action.id === actionId);
        
        if (actionIndex !== -1) {
          // Mark the action as completed
          this.collaborationState.goals[i].pendingActions![actionIndex].completed = true;
          goalId = goal.id;
          
          // Check if all actions for this goal are completed
          const allActionsCompleted = goal.pendingActions.every(action => action.completed);
          
          if (allActionsCompleted && goal.status === 'in-progress') {
            // All actions are completed, so complete the goal
            await this.completeGoal(goal.id);
            goalCompleted = true;
          } else {
            // Update the goal progress based on completed actions
            const completedCount = goal.pendingActions.filter(action => action.completed).length;
            const totalCount = goal.pendingActions.length;
            const progress = Math.round((completedCount / totalCount) * 100);
            
            this.updateGoalProgress(goal.id, progress);
          }
          
          break;
        }
      }
    }
    
    return goalCompleted;
  }
  
  /**
   * Start any goals that are no longer blocked
   */
  private async startUnblockedGoals(): Promise<void> {
    const blockedGoals = this.getBlockedGoals();
    
    for (const goal of blockedGoals) {
      // Check if all dependencies are now completed
      const dependencies = this.collaborationState.goals.filter(g => 
        goal.dependencies.includes(g.description)
      );
      
      const allDependenciesCompleted = dependencies.every(dep => dep.status === 'completed');
      
      if (allDependenciesCompleted) {
        await this.startGoal(goal.id);
      }
    }
  }
  
  /**
   * Finalize the collaboration process
   */
  private async finalizeCollaboration(): Promise<void> {
    // Update collaboration state
    this.collaborationState.status = 'completed';
    this.collaborationState.endTime = new Date().toISOString();
    
    // Create a summary of the collaboration
    const completedArtifacts = this.artifactManager.getAllArtifacts();
    const decisions = this.decisionManager.getAllDecisions();
    
    // Ensure we have a final content artifact
    const finalContentArtifacts = completedArtifacts.filter(a => a.type === 'final-content');
    
    if (finalContentArtifacts.length === 0) {
      // If no final content artifact exists, create one from the latest draft
      const draftArtifacts = completedArtifacts.filter(a => a.type === 'draft-content');
      
      if (draftArtifacts.length > 0) {
        // Sort by version to get the latest draft
        const latestDraft = draftArtifacts.sort((a, b) => b.version - a.version)[0];
        
        // Create a final content artifact based on the latest draft
        const finalArtifact = this.artifactManager.createArtifact(
          'Final Content',
          'Final version of content created from the latest draft',
          'final-content',
          latestDraft.parts,
          'system',
          {
            thoughts: ['All goals completed but no explicit final content created'],
            considerations: ['Latest draft should be considered the final content'],
            decision: 'Convert latest draft to final content',
            confidence: 0.9
          },
          [latestDraft.id]
        );
        
        console.log(`Created final content artifact from latest draft: ${finalArtifact.id}`);
      }
    }
    
    const summary = `
      Content Generation Completed
      ---------------------------
      Topic: ${this.collaborationState.topic}
      Content Type: ${this.collaborationState.contentType}
      Target Audience: ${this.collaborationState.targetAudience}
      Completion Time: ${this.getFormattedDuration()}
      
      Artifacts Created: ${completedArtifacts.length}
      Decisions Made: ${decisions.length}
      Goals Completed: ${this.getCompletedGoals().length}
      
      Final Content Artifacts:
      - ${completedArtifacts
          .filter(a => a.type === 'final-content')
          .map(a => a.name)
          .join('\n- ')}
    `;
    
    // Send final message
    const finalMessage = this.createSystemMessage(
      'all',
      summary,
      {
        thoughts: [
          'All goals have been completed',
          'The final content meets the requirements and criteria',
          'The collaboration has been successful'
        ],
        considerations: [
          'The artifacts and decisions are available for review',
          'The content should be ready for publication'
        ],
        decision: 'Finalize the collaboration and prepare content for publication',
        confidence: 0.95
      }
    );
    
    await this.messageBus.sendMessage(finalMessage);
  }
  
  /**
   * Get formatted duration of the collaboration
   */
  private getFormattedDuration(): string {
    const startTime = new Date(this.collaborationState.startTime).getTime();
    const endTime = this.collaborationState.endTime 
      ? new Date(this.collaborationState.endTime).getTime() 
      : Date.now();
    
    const durationMs = endTime - startTime;
    const durationMinutes = Math.floor(durationMs / 60000);
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    
    return `${hours}h ${minutes}m`;
  }
  
  /**
   * Request initial actions for a goal from assigned agents
   */
  private async requestInitialActionsForGoal(goal: Goal): Promise<void> {
    // Different action requests based on goal and agent
    for (const agent of goal.assignedTo) {
      let actions: RequestedAction[] = [];
      
      switch (agent) {
        case 'market-research':
          actions = [
            {
              agent,
              action: 'AnalyzeAudience',
              priority: 8,
              rationale: 'Understanding the target audience is critical for content relevance',
              parameters: { topic: this.collaborationState.topic }
            },
            {
              agent,
              action: 'IdentifyTrends',
              priority: 7,
              rationale: 'Current market trends will shape content strategy',
              parameters: { topic: this.collaborationState.topic }
            }
          ];
          break;
          
        case 'seo-keyword':
          actions = [
            {
              agent,
              action: 'ResearchKeywords',
              priority: 9,
              rationale: 'Identifying optimal keywords is essential for content visibility',
              parameters: { 
                topic: this.collaborationState.topic,
                targetAudience: this.collaborationState.targetAudience
              }
            },
            {
              agent,
              action: 'AnalyzeSearchIntent',
              priority: 8,
              rationale: 'Understanding search intent ensures content meets user needs',
              parameters: { topic: this.collaborationState.topic }
            }
          ];
          break;
          
        case 'content-strategy':
          // Only send if market research and keyword research are complete
          const marketResearchComplete = this.getCompletedGoals().some(g => 
            g.description === 'Conduct market research and audience analysis'
          );
          const keywordResearchComplete = this.getCompletedGoals().some(g => 
            g.description === 'Identify SEO keywords and search intent'
          );
          
          if (marketResearchComplete && keywordResearchComplete) {
            actions = [
              {
                agent,
                action: 'DevelopContentStructure',
                priority: 9,
                rationale: 'A well-structured outline is the foundation for quality content',
                parameters: { 
                  topic: this.collaborationState.topic,
                  contentType: this.collaborationState.contentType
                }
              }
            ];
          } else {
            actions = [
              {
                agent,
                action: 'MonitorProgress',
                priority: 5,
                rationale: 'Preparing for content structure development',
                parameters: { dependencies: ['market-research', 'seo-keyword'] }
              }
            ];
          }
          break;
          
        case 'content-generation':
          // Only send if content strategy is complete
          const contentStrategyComplete = this.getCompletedGoals().some(g => 
            g.description === 'Develop content strategy and structure'
          );
          
          if (contentStrategyComplete) {
            actions = [
              {
                agent,
                action: 'GenerateContent',
                priority: 10,
                rationale: 'Creating high-quality content based on strategy and research',
                parameters: { 
                  topic: this.collaborationState.topic,
                  contentType: this.collaborationState.contentType,
                  targetAudience: this.collaborationState.targetAudience,
                  tone: this.collaborationState.tone
                }
              }
            ];
          } else {
            actions = [
              {
                agent,
                action: 'PrepareForContent',
                priority: 5,
                rationale: 'Waiting for content strategy before generation',
                parameters: { dependencies: ['content-strategy'] }
              }
            ];
          }
          break;
          
        case 'seo-optimization':
          // Only send if content draft is complete
          const contentDraftComplete = this.getCompletedGoals().some(g => 
            g.description === 'Generate initial content draft'
          );
          
          if (contentDraftComplete) {
            actions = [
              {
                agent,
                action: 'OptimizeContent',
                priority: 8,
                rationale: 'Ensuring content is fully optimized for search',
                parameters: { 
                  keywords: this.collaborationState.keywords
                }
              }
            ];
          } else {
            actions = [
              {
                agent,
                action: 'PrepareForOptimization',
                priority: 5,
                rationale: 'Waiting for content draft before optimization',
                parameters: { dependencies: ['content-generation'] }
              }
            ];
          }
          break;
      }
      
      // Send action requests to agent
      for (const action of actions) {
        const actionMessage = this.createSystemMessage(
          agent,
          `Action Request: ${action.action}`,
          {
            thoughts: [
              `This action is needed for the goal "${goal.description}"`,
              `${agent} is the best agent for this action based on capabilities`,
              `Priority level ${action.priority} reflects importance to workflow`
            ],
            considerations: [
              'Action may require collaboration with other agents',
              'Artifacts from previous goals may provide context'
            ],
            decision: `Request ${action.action} from ${agent} with priority ${action.priority}`,
            confidence: 0.9
          },
          [action]
        );
        
        await this.messageBus.sendMessage(actionMessage);
      }
    }
  }
  
  /**
   * Process a message to update goal progress
   */
  async processMessage(message: EnhancedA2AMessage): Promise<void> {
    // Track artifact references
    if (message.artifactReferences && message.artifactReferences.length > 0) {
      // Find the goal this artifact relates to
      const relatedGoal = this.findGoalForMessage(message);
      
      if (relatedGoal) {
        // Add artifacts to goal if not already present
        for (const artifactId of message.artifactReferences) {
          if (!relatedGoal.artifacts.includes(artifactId)) {
            relatedGoal.artifacts.push(artifactId);
          }
        }
        
        // Update goal progress based on artifacts
        this.updateGoalProgressBasedOnArtifacts(relatedGoal);
      }
    }
    
    // Track decision references
    if (message.decisionReferences && message.decisionReferences.length > 0) {
      // Find the goal this decision relates to
      const relatedGoal = this.findGoalForMessage(message);
      
      if (relatedGoal) {
        // Add decisions to goal if not already present
        for (const decisionId of message.decisionReferences) {
          if (!relatedGoal.decisions.includes(decisionId)) {
            relatedGoal.decisions.push(decisionId);
          }
        }
      }
    }
    
    // Check for goal completion indicators
    if (message.parts && message.parts.length > 0) {
      for (const part of message.parts) {
        if (part.type === 'text' && typeof part.text === 'string') {
          if (
            part.text.includes('GOAL_COMPLETE') || 
            part.text.includes('Task completed') ||
            part.text.includes('Goal accomplished')
          ) {
            // Find the goal this message relates to
            const relatedGoal = this.findGoalForMessage(message);
            
            if (relatedGoal && relatedGoal.status === 'in-progress') {
              // Complete the goal
              await this.completeGoal(relatedGoal.id);
            }
          }
        }
      }
    }
  }
  
  /**
   * Find the goal related to a message
   */
  private findGoalForMessage(message: EnhancedA2AMessage): Goal | undefined {
    // First try to find by agent assignment and status
    let relatedGoal = this.collaborationState.goals.find(goal => 
      goal.status === 'in-progress' && 
      goal.assignedTo.includes(message.from)
    );
    
    // If not found, look for the current goal
    if (!relatedGoal) {
      relatedGoal = this.collaborationState.goals.find(goal => 
        goal.description === this.collaborationState.currentGoal
      );
    }
    
    return relatedGoal;
  }
  
  /**
   * Update goal progress based on artifacts
   */
  private updateGoalProgressBasedOnArtifacts(goal: Goal): void {
    // Different progress calculation based on goal
    let progress = 0;
    
    switch (goal.description) {
      case 'Conduct market research and audience analysis':
        // Check for audience-analysis artifact
        const audienceAnalysisArtifacts = goal.artifacts.filter(id => {
          const artifact = this.artifactManager.getArtifactById(id);
          return artifact && artifact.type === 'audience-analysis';
        });
        
        // Check for market-research artifact
        const marketResearchArtifacts = goal.artifacts.filter(id => {
          const artifact = this.artifactManager.getArtifactById(id);
          return artifact && artifact.type === 'market-research';
        });
        
        if (audienceAnalysisArtifacts.length > 0 && marketResearchArtifacts.length > 0) {
          progress = 100; // Both required artifacts are present
        } else if (audienceAnalysisArtifacts.length > 0 || marketResearchArtifacts.length > 0) {
          progress = 50; // Only one artifact type is present
        }
        break;
        
      case 'Identify SEO keywords and search intent':
        // Check for keyword-set artifact
        const keywordArtifacts = goal.artifacts.filter(id => {
          const artifact = this.artifactManager.getArtifactById(id);
          return artifact && artifact.type === 'keyword-set';
        });
        
        if (keywordArtifacts.length > 0) {
          progress = 100;
        }
        break;
        
      case 'Develop content strategy and structure':
        // Check for content-structure artifact
        const structureArtifacts = goal.artifacts.filter(id => {
          const artifact = this.artifactManager.getArtifactById(id);
          return artifact && artifact.type === 'content-structure';
        });
        
        if (structureArtifacts.length > 0) {
          progress = 100;
        }
        break;
        
      case 'Generate initial content draft':
        // Check for draft-content artifact
        const draftArtifacts = goal.artifacts.filter(id => {
          const artifact = this.artifactManager.getArtifactById(id);
          return artifact && artifact.type === 'draft-content';
        });
        
        if (draftArtifacts.length > 0) {
          progress = 100;
        }
        break;
        
      case 'Perform SEO optimization':
        // Check for seo-analysis artifact
        const seoArtifacts = goal.artifacts.filter(id => {
          const artifact = this.artifactManager.getArtifactById(id);
          return artifact && artifact.type === 'seo-analysis';
        });
        
        if (seoArtifacts.length > 0) {
          progress = 100;
        }
        break;
        
      case 'Refine and finalize content':
        // Check for final-content artifact
        const finalContentArtifacts = goal.artifacts.filter(id => {
          const artifact = this.artifactManager.getArtifactById(id);
          return artifact && artifact.type === 'final-content';
        });
        
        if (finalContentArtifacts.length > 0) {
          progress = 100;
        }
        break;
    }
    
    // Update goal progress
    this.updateGoalProgress(goal.id, progress);
  }
  
  /**
   * Create a system message
   */
  private createSystemMessage(
    to: string | string[],
    content: string,
    reasoning: Reasoning,
    requestedActions: RequestedAction[] = []
  ): EnhancedA2AMessage {
    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'system',
      to,
      role: 'system',
      parts: [
        {
          type: 'text',
          text: content
        }
      ],
      conversationId: uuidv4(),
      reasoning,
      requestedActions,
      intentions: ['inform'],
      metadata: {
        systemMessage: true,
        timestamp: new Date().toISOString()
      }
    };
  }
}
