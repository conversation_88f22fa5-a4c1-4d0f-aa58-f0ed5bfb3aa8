// src/app/(payload)/api/agents/dynamic-collaboration/final-output-generator.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  Artifact as BaseArtifact, 
  Decision, 
  EnhancedA2AMessage, 
  CollaborationState as BaseCollaborationState,
  Reasoning,
  MessageIntention as BaseMessageIntention
} from '../a2atypes';

/**
 * Extended Artifact type with additional properties needed for content evaluation
 */
interface Artifact extends BaseArtifact {
  status?: 'draft' | 'revised' | 'final';
  qualityScore?: number;
  decisions?: string[];
  history?: Array<{
    version: number;
    timestamp: string;
    agent: string;
    changes: string;
    parts: any[];
    reasoning?: Reasoning;
  }>;
  contributors?: string[];
}

/**
 * Extended CollaborationState type with additional properties
 */
interface CollaborationState extends BaseCollaborationState {
  duration: string;
}

/**
 * Extended MessageIntention type with additional values
 */
type MessageIntention = BaseMessageIntention | 'instruct';
import { DecisionManager } from './decision-manager';
import { ArtifactManager } from './artifact-manager';
import { MessageBus } from './enhanced-message-bus';

/**
 * Evaluation criteria for content
 */
interface EvaluationCriteria {
  id: string;
  name: string;
  description: string;
  weight: number; // 1-10 scale
}

/**
 * Evaluation result for a specific artifact
 */
interface ArtifactEvaluation {
  artifactId: string;
  criteria: Record<string, number>; // criteriaId -> score (1-10)
  totalScore: number;
  normalizedScore: number; // 0-100 scale
  feedback: string;
  evaluator: string;
  timestamp: string;
}

/**
 * Final output decision
 */
interface FinalOutputDecision {
  id: string;
  selectedArtifactId: string;
  evaluations: ArtifactEvaluation[];
  rationale: string;
  timestamp: string;
  qualityThreshold: number; // 0-100 scale
  metQualityThreshold: boolean;
  requiredRevisions: string[] | null;
}

/**
 * Final Output Generator
 * 
 * This class manages the evaluation and selection of final content outputs
 * from the collaborative agent system.
 */
export class FinalOutputGenerator {
  private artifactManager: ArtifactManager;
  private decisionManager: DecisionManager;
  private messageBus: MessageBus;
  private state: CollaborationState;
  
  // Default evaluation criteria
  private defaultCriteria: EvaluationCriteria[] = [
    {
      id: 'relevance',
      name: 'Relevance',
      description: 'How relevant the content is to the topic and audience',
      weight: 10
    },
    {
      id: 'completeness',
      name: 'Completeness',
      description: 'Whether the content covers all necessary aspects of the topic',
      weight: 8
    },
    {
      id: 'clarity',
      name: 'Clarity',
      description: 'How clear and understandable the content is',
      weight: 9
    },
    {
      id: 'seo',
      name: 'SEO Optimization',
      description: 'How well optimized the content is for search engines',
      weight: 7
    },
    {
      id: 'engagement',
      name: 'Engagement',
      description: 'How engaging and interesting the content is',
      weight: 8
    }
  ];
  
  // Quality threshold for acceptable content (0-100)
  private qualityThreshold: number = 70;
  
  constructor(
    artifactManager: ArtifactManager,
    decisionManager: DecisionManager,
    messageBus: MessageBus,
    state: CollaborationState
  ) {
    this.artifactManager = artifactManager;
    this.decisionManager = decisionManager;
    this.messageBus = messageBus;
    this.state = state;
  }
  
  /**
   * Set quality threshold
   */
  setQualityThreshold(threshold: number): void {
    if (threshold < 0 || threshold > 100) {
      throw new Error('Quality threshold must be between 0 and 100');
    }
    this.qualityThreshold = threshold;
  }
  
  /**
   * Initialize the content evaluation process
   * This creates a decision for agents to evaluate content artifacts
   */
  async initializeContentEvaluation(): Promise<Decision> {
    // Get content artifacts to evaluate
    const contentArtifacts = this.artifactManager
      .getAllArtifacts()
      .filter(artifact => 
        artifact.type === 'draft-content' || 
        artifact.type === 'revised-content'
      );
    
    if (contentArtifacts.length === 0) {
      throw new Error('No content artifacts found to evaluate');
    }
    
    // Create a decision for content evaluation
    const decisionOptions = contentArtifacts.map(artifact => ({
      description: `Select "${artifact.name}" as the final content`,
      rationale: `This artifact was created by ${artifact.contributors.join(', ')}`,
      pros: [
        `Created at ${new Date(artifact.metadata.createdAt).toLocaleString()}`
      ],
      cons: []
    }));
    
    const decision = this.decisionManager.createDecision(
      'Content Selection',
      'Evaluate and select the best content version for publication',
      'system',
      decisionOptions,
      {
        thoughts: [
          'Multiple content versions have been created',
          'Need to select the best version for publication',
          'Agents should evaluate based on quality criteria'
        ],
        considerations: [
          'Content relevance to the target audience',
          'SEO optimization and keyword usage',
          'Clarity and readability',
          'Comprehensiveness of topic coverage',
          'Engagement and interest level'
        ],
        decision: 'Create a decision process for content evaluation',
        confidence: 1.0
      }
    );
    
    // Send a message to all agents to evaluate content
    await this.messageBus.sendMessage(
      this.createEvaluationRequestMessage(decision.id, contentArtifacts)
    );
    
    return decision;
  }
  
  /**
   * Process content evaluations and make final decision
   * @param decisionId Optional decision ID. If not provided, will use the latest content evaluation decision
   */
  async processFinalDecision(decisionId?: string): Promise<FinalOutputDecision | null> {
    // If no decision ID is provided, find the latest content evaluation decision
    if (!decisionId) {
      const decisions = this.decisionManager.getDecisions();
      const contentEvaluationDecisions = decisions.filter(d => 
        d.topic === 'Content Evaluation' && d.status === 'decided'
      );
      
      if (contentEvaluationDecisions.length === 0) {
        console.log('No content evaluation decisions found');
        return await this.generateBestEffortOutput();
      }
      
      // Sort by timestamp (newest first) and take the first one
      const latestDecision = contentEvaluationDecisions.sort((a, b) => {
        return new Date(b.decidedAt || '').getTime() - new Date(a.decidedAt || '').getTime();
      })[0];
      
      decisionId = latestDecision.id;
    }
    const decision = this.decisionManager.getDecisionById(decisionId);
    
    if (!decision) {
      console.error(`Decision not found: ${decisionId}`);
      return null;
    }
    
    if (decision.status !== 'decided') {
      console.error(`Decision is not yet resolved: ${decisionId}`);
      return null;
    }
    
    // Gather all evaluations
    const evaluations: ArtifactEvaluation[] = [];
    
    decision.options.forEach(option => {
      // Extract artifact ID from option description
      const artifactMatch = option.description.match(/Select "([^"]+)" as the final content/);
      if (!artifactMatch) return;
      
      const artifactName = artifactMatch[1];
      const artifact = this.artifactManager.getAllArtifacts().find(a => a.name === artifactName);
      if (!artifact) return;
      
      // Process votes as evaluations
      option.votes.forEach(vote => {
        try {
          // Try to parse evaluation data from the vote reasoning
          const evaluationData = JSON.parse(vote.reasoning);
          
          if (evaluationData.criteria && typeof evaluationData.criteria === 'object') {
            // Calculate total and normalized score
            let totalWeightedScore = 0;
            let totalPossibleScore = 0;
            
            Object.entries(evaluationData.criteria).forEach(([criteriaId, score]) => {
              const criterion = this.defaultCriteria.find(c => c.id === criteriaId);
              if (criterion) {
                totalWeightedScore += (score as number) * criterion.weight;
                totalPossibleScore += 10 * criterion.weight;
              }
            });
            
            const normalizedScore = totalPossibleScore > 0 
              ? (totalWeightedScore / totalPossibleScore) * 100 
              : 0;
            
            evaluations.push({
              artifactId: artifact.id,
              criteria: evaluationData.criteria as Record<string, number>,
              totalScore: evaluationData.totalScore || totalWeightedScore,
              normalizedScore,
              feedback: evaluationData.feedback || vote.reasoning,
              evaluator: vote.agent,
              timestamp: vote.timestamp
            });
          }
        } catch (e) {
          // If vote reasoning isn't parseable JSON, create a simple evaluation
          evaluations.push({
            artifactId: artifact.id,
            criteria: {},
            totalScore: vote.confidence * 10, // Convert 0-1 confidence to 0-10 scale
            normalizedScore: vote.confidence * 100, // Convert to 0-100 scale
            feedback: vote.reasoning,
            evaluator: vote.agent,
            timestamp: vote.timestamp
          });
        }
      });
    });
    
    if (evaluations.length === 0) {
      console.error('No valid evaluations found');
      return null;
    }
    
    // Get the selected artifact ID from the winning decision
    const winningOptionMatch = decision.decision?.match(/Select "([^"]+)" as the final content/);
    if (!winningOptionMatch) {
      console.error('Could not determine winning artifact from decision');
      return null;
    }
    
    const winningArtifactName = winningOptionMatch[1];
    const winningArtifact = this.artifactManager.getAllArtifacts().find(a => a.name === winningArtifactName);
    
    if (!winningArtifact) {
      console.error(`Winning artifact not found: ${winningArtifactName}`);
      return null;
    }
    
    // Calculate average normalized score for winning artifact
    const winningEvaluations = evaluations.filter(e => e.artifactId === winningArtifact.id);
    const avgNormalizedScore = winningEvaluations.length > 0
      ? winningEvaluations.reduce((sum, e) => sum + e.normalizedScore, 0) / winningEvaluations.length
      : 0;
    
    // Determine if quality threshold is met
    const metQualityThreshold = avgNormalizedScore >= this.qualityThreshold;
    
    // Create final output decision
    const finalDecision: FinalOutputDecision = {
      id: uuidv4(),
      selectedArtifactId: winningArtifact.id,
      evaluations,
      rationale: decision.reasoning?.decision || 'Based on agent evaluations',
      timestamp: new Date().toISOString(),
      qualityThreshold: this.qualityThreshold,
      metQualityThreshold,
      requiredRevisions: metQualityThreshold ? null : this.generateRevisionRequirements(winningEvaluations)
    };
    
    // If quality threshold is met, finalize the content
    if (metQualityThreshold) {
      await this.finalizeContent(winningArtifact, finalDecision);
    } else {
      // Otherwise, request revisions
      await this.requestRevisions(winningArtifact, finalDecision);
    }
    
    return finalDecision;
  }
  
  /**
   * Generate revision requirements based on evaluations
   */
  private generateRevisionRequirements(evaluations: ArtifactEvaluation[]): string[] {
    const revisions: string[] = [];
    
    // Aggregate feedback from all evaluations
    const allFeedback = evaluations.map(e => e.feedback).join('\n');
    
    // Find low-scoring criteria
    const criteriaCounts: Record<string, { sum: number, count: number }> = {};
    
    evaluations.forEach(evaluation => {
      Object.entries(evaluation.criteria).forEach(([criteriaId, score]) => {
        if (!criteriaCounts[criteriaId]) {
          criteriaCounts[criteriaId] = { sum: 0, count: 0 };
        }
        criteriaCounts[criteriaId].sum += score;
        criteriaCounts[criteriaId].count += 1;
      });
    });
    
    // Identify criteria that need improvement
    const criteriaToImprove = Object.entries(criteriaCounts)
      .map(([criteriaId, data]) => ({
        criteriaId,
        avgScore: data.sum / data.count,
        criterion: this.defaultCriteria.find(c => c.id === criteriaId)
      }))
      .filter(item => item.avgScore < 7 && item.criterion) // Low scoring criteria (below 7/10)
      .sort((a, b) => a.avgScore - b.avgScore); // Sort by score ascending
    
    // Add specific revision requirements
    criteriaToImprove.forEach(item => {
      revisions.push(`Improve ${item.criterion?.name}: ${item.criterion?.description}`);
    });
    
    // Extract suggestions from feedback using simple text analysis
    const suggestionPatterns = [
      /should improve (.*?)(?:\.|$)/gi,
      /needs to be (.*?)(?:\.|$)/gi,
      /recommend (.*?)(?:\.|$)/gi,
      /suggest (.*?)(?:\.|$)/gi
    ];
    
    suggestionPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(allFeedback)) !== null) {
        if (match[1].trim()) {
          revisions.push(match[1].trim());
        }
      }
    });
    
    // Remove duplicates and return
    return [...new Set(revisions)];
  }
  
  /**
   * Finalize content by publishing the selected artifact
   */
  private async finalizeContent(artifact: Artifact, finalDecision: FinalOutputDecision): Promise<void> {
    // Update artifact status
    artifact.status = 'final';
    artifact.qualityScore = Math.round(
      finalDecision.evaluations
        .filter(e => e.artifactId === artifact.id)
        .reduce((sum, e) => sum + e.normalizedScore, 0) / 
        finalDecision.evaluations.filter(e => e.artifactId === artifact.id).length
    );
    
    // Add decision reference to artifact
    if (!artifact.decisions) {
      artifact.decisions = [];
    }
    artifact.decisions.push(finalDecision.id);
    
    // Create final version in artifact history
    if (!artifact.history) {
      artifact.history = [];
    }
    artifact.history.push({
      version: artifact.history.length + 1,
      timestamp: new Date().toISOString(),
      agent: 'system',
      changes: 'Finalized content based on agent evaluations',
      parts: artifact.parts,
      reasoning: {
        thoughts: [
          'Content has been evaluated by all agents',
          `Quality score: ${artifact.qualityScore}/100`,
          'Content meets quality threshold for publication'
        ],
        considerations: [
          'Agent evaluations and feedback',
          'Quality criteria scores',
          'Collaborative decision process'
        ],
        decision: 'Finalize content for publication',
        confidence: finalDecision.metQualityThreshold ? 0.9 : 0.7
      }
    });
    
    // Update state with final content
    this.state.status = 'completed';
    this.state.endTime = new Date().toISOString();
    
    // Calculate duration
    const startTime = new Date(this.state.startTime).getTime();
    const endTime = new Date(this.state.endTime).getTime();
    const durationMs = endTime - startTime;
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    this.state.duration = `${minutes}m ${seconds}s`;
    
    // Update progress
    this.state.progress = 100;
    
    // Send completion message
    await this.messageBus.sendMessage(this.createCompletionMessage(artifact, finalDecision));
  }
  
  /**
   * Request revisions for content that didn't meet quality threshold
   */
  private async requestRevisions(artifact: Artifact, finalDecision: FinalOutputDecision): Promise<void> {
    // Create revision request message
    await this.messageBus.sendMessage(this.createRevisionRequestMessage(artifact, finalDecision));
  }
  
  /**
   * Create a message requesting agents to evaluate content
   */
  private createEvaluationRequestMessage(decisionId: string, artifacts: Artifact[]): EnhancedA2AMessage {
    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'system',
      to: 'all',
      role: 'system',
      conversationId: uuidv4(),
      parts: [
        {
          type: 'text',
          text: `## Content Evaluation Required

All agents should evaluate the available content drafts and vote on the best version.

**Decision ID:** ${decisionId}

**Available Content Artifacts:**
${artifacts.map(a => `- ${a.name} (Created by: ${a.contributors.join(', ')})`).join('\n')}

**Evaluation Criteria:**
${this.defaultCriteria.map(c => `- ${c.name} (Weight: ${c.weight}/10): ${c.description}`).join('\n')}

Each agent should:
1. Review each content artifact
2. Score each artifact on the evaluation criteria (1-10 scale)
3. Provide feedback and improvement suggestions
4. Cast a vote for the best artifact using the decision framework

Please include your evaluation data as JSON in your vote reasoning.`
        }
      ],
      intentions: ['request', 'instruct'],
      reasoning: {
        thoughts: [
          'Need to evaluate multiple content drafts',
          'Require input from all specialized agents',
          'Should use consistent evaluation criteria'
        ],
        considerations: [
          'Different agents have different expertise',
          'Need structured evaluation format',
          'Will use decision framework for final selection'
        ],
        decision: 'Request structured content evaluations from all agents',
        confidence: 0.95
      }
    };
  }
  
  /**
   * Create a completion message when content is finalized
   */
  private createCompletionMessage(artifact: Artifact, finalDecision: FinalOutputDecision): EnhancedA2AMessage {
    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'system',
      to: 'all',
      role: 'system',
      conversationId: uuidv4(),
      parts: [
        {
          type: 'text',
          text: `## Content Generation Complete

The collaborative content generation process has been completed successfully.

**Selected Content:** ${artifact.name}
**Quality Score:** ${artifact.qualityScore}/100
**Contributors:** ${artifact.contributors.join(', ')}

**Evaluation Summary:**
${finalDecision.evaluations
  .filter(e => e.artifactId === artifact.id)
  .map(e => `- ${e.evaluator}: Score ${Math.round(e.normalizedScore)}/100 - "${e.feedback.substring(0, 100)}${e.feedback.length > 100 ? '...' : ''}"`)
  .join('\n')}

The content has met our quality threshold of ${finalDecision.qualityThreshold}/100 and is ready for publication.

Thank you to all agents for your contributions!`
        }
      ],
      intentions: ['inform'],
      reasoning: {
        thoughts: [
          'Content generation process is complete',
          'Final content has been selected and evaluated',
          'Quality threshold has been met'
        ],
        considerations: [
          'Agents should be informed of the outcome',
          'Summary of evaluations provides transparency',
          'Acknowledging contributions maintains collaborative atmosphere'
        ],
        decision: 'Send completion notification with content selection details',
        confidence: 0.95
      },
      artifactReferences: [artifact.id]
    };
  }
  
  /**
   * Generate a best effort output when no formal evaluation is available
   * This is a fallback method when the evaluation process fails or times out
   */
  async generateBestEffortOutput(): Promise<FinalOutputDecision | null> {
    console.log('Generating best effort output');
    
    // Find all content artifacts
    const allArtifacts = this.artifactManager.getAllArtifacts();
    const contentArtifacts = allArtifacts.filter(a => 
      a.type === 'final-content' || a.type === 'content-outline' || a.type === 'draft-content'
    );
    
    if (contentArtifacts.length === 0) {
      console.log('No content artifacts found for best effort output');
      return null;
    }
    
    // Prioritize artifacts: final-content > draft-content > content-outline
    const priorityOrder: Record<string, number> = {
      'final-content': 3,
      'draft-content': 2,
      'content-outline': 1
    };
    
    // Sort by priority (highest first) and timestamp (newest first)
    const sortedArtifacts = contentArtifacts.sort((a, b) => {
      const priorityDiff = (priorityOrder[a.type] || 0) - (priorityOrder[b.type] || 0);
      if (priorityDiff !== 0) return -priorityDiff; // Negative to sort in descending order
      
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
    
    // Take the best artifact
    const bestArtifact = sortedArtifacts[0];
    
    // Create a basic evaluation
    const evaluation: ArtifactEvaluation = {
      artifactId: bestArtifact.id,
      criteria: {
        'relevance': 7,
        'completeness': 7,
        'clarity': 7,
        'seo': 7,
        'engagement': 7
      },
      totalScore: 35, // 7 * 5 criteria
      normalizedScore: 70, // 35/50 * 100
      feedback: 'This is an auto-generated evaluation for the best available artifact. No formal evaluation was conducted.',
      evaluator: 'system',
      timestamp: new Date().toISOString()
    };
    
    // Create a final decision
    const finalDecision: FinalOutputDecision = {
      id: uuidv4(),
      selectedArtifactId: bestArtifact.id,
      evaluations: [evaluation],
      rationale: 'This is the best available content based on artifact type and creation time. No formal evaluation was conducted.',
      timestamp: new Date().toISOString(),
      qualityThreshold: this.qualityThreshold,
      metQualityThreshold: true, // Assume it meets the threshold to avoid revision loop
      requiredRevisions: null
    };
    
    // Finalize the content
    await this.finalizeContent(bestArtifact as Artifact, finalDecision);
    
    return finalDecision;
  }
  
  /**
   * Create a revision request message when content needs improvement
   */
  private createRevisionRequestMessage(artifact: Artifact, finalDecision: FinalOutputDecision): EnhancedA2AMessage {
    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'system',
      to: ['content-generation', 'content-strategy'],
      role: 'system',
      conversationId: uuidv4(),
      parts: [
        {
          type: 'text',
          text: `## Content Revision Required

The content draft "${artifact.name}" has been selected, but requires revisions before it can be finalized.

**Current Quality Score:** ${Math.round(finalDecision.evaluations
  .filter(e => e.artifactId === artifact.id)
  .reduce((sum, e) => sum + e.normalizedScore, 0) / 
  finalDecision.evaluations.filter(e => e.artifactId === artifact.id).length)}/100
**Quality Threshold:** ${finalDecision.qualityThreshold}/100

**Required Revisions:**
${finalDecision.requiredRevisions ? finalDecision.requiredRevisions.map(r => `- ${r}`).join('\n') : 'No specific revisions required'}

**Evaluation Feedback:**
${finalDecision.evaluations
  .filter(e => e.artifactId === artifact.id)
  .map(e => `- ${e.evaluator}: "${e.feedback.substring(0, 150)}${e.feedback.length > 150 ? '...' : ''}"`)
  .join('\n')}

Please revise the content to address these issues and improve the overall quality.`
        }
      ],
      intentions: ['request', 'instruct'],
      reasoning: {
        thoughts: [
          'Content needs improvement before finalization',
          'Specific revision requirements identified',
          'Quality threshold not yet met'
        ],
        considerations: [
          'Content generation and strategy agents best suited for revisions',
          'Clear feedback will lead to better improvements',
          'Specific issues should be highlighted'
        ],
        decision: 'Request targeted content revisions with specific feedback',
        confidence: 0.9
      },
      artifactReferences: [artifact.id],
      requestedActions: [
        {
          agent: 'content-generation',
          action: 'ReviseContent',
          priority: 9,
          rationale: 'Content requires revisions based on agent evaluations',
          parameters: {
            artifactId: artifact.id,
            revisionRequirements: finalDecision.requiredRevisions
          }
        }
      ]
    };
  }
}
