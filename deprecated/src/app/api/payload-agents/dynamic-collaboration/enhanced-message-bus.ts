// src/app/(payload)/api/agents/dynamic-collaboration/enhanced-message-bus.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  EnhancedA2AMessage, 
  CollaborationState as BaseCollaborationState,
  Reasoning,
  MessageIntention,
  RequestedAction,
  TextPart
} from '../a2atypes';

/**
 * Extended CollaborationState type with additional properties
 */
interface CollaborationState extends BaseCollaborationState {
  duration: string;
}

/**
 * Enhanced Message Bus for agent communications
 * 
 * This class extends the original MessageBus functionality with support for
 * the enhanced A2A protocol, including reasoning, intention signaling,
 * and artifact/decision references.
 */
export class MessageBus {
  private messageIndex: Record<string, EnhancedA2AMessage> = {};
  private conversations: Record<string, string[]> = {}; // conversationId -> messageIds
  private subscribers: Map<string, (message: EnhancedA2AMessage) => Promise<void>> = new Map();
  private messageQueue: EnhancedA2AMessage[] = [];
  private isProcessing: boolean = false;
  private baseUrl: string;
  private state: CollaborationState;
  
  // Message exchange tracking to prevent infinite loops
  private agentMessageCounts: Record<string, Record<string, number>> = {}; // from -> to -> count
  private lastProcessedTime: Record<string, number> = {}; // conversationId -> timestamp
  private MAX_AGENT_EXCHANGES = 5; // Maximum number of back-and-forth exchanges between same agents
  private AGENT_TIMEOUT_MS = 10000; // 10 seconds timeout between agent responses

  constructor(state: CollaborationState, baseUrl: string = '') {
    this.state = state;
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_API_URL || 
      `http://${process.env.VERCEL_URL || 'localhost:3000'}`;
  }

  /**
   * Get the current collaboration state
   */
  getState(): CollaborationState {
    return { ...this.state };
  }

  /**
   * Update the collaboration state
   */
  updateState(updater: (state: CollaborationState) => CollaborationState): void {
    this.state = updater(this.state);
  }

  /**
   * Get all messages
   */
  getAllMessages(): EnhancedA2AMessage[] {
    return Object.values(this.messageIndex);
  }
  
  /**
   * Get messages in a conversation
   */
  getConversationMessages(conversationId: string): EnhancedA2AMessage[] {
    const messageIds = this.conversations[conversationId] || [];
    return messageIds.map(id => this.messageIndex[id]).filter(Boolean);
  }
  
  /**
   * Add a message to the message index and conversation history
   */
  private addMessageToHistory(message: EnhancedA2AMessage): void {
    // Add to message index
    this.messageIndex[message.id] = message;
    
    // Initialize conversation if it doesn't exist
    if (!this.conversations[message.conversationId]) {
      this.conversations[message.conversationId] = [];
    }
    
    // Add to conversation
    this.conversations[message.conversationId].push(message.id);
    
    // Update state with the new message and conversation
    this.state.messageIndex[message.id] = message;
    this.state.conversations[message.conversationId] = 
      this.conversations[message.conversationId];
  }

  // Store the last N message timestamps to detect rapid message sequences
  private lastMessageTimestamps: number[] = [];
  private MAX_MESSAGES_PER_SECOND = 5;
  private RAPID_MESSAGE_WINDOW_MS = 1000; // 1 second window
  private GLOBAL_MESSAGE_LIMIT = 100; // Absolute maximum messages for a collaboration
  private loopDetectionActive = false;

  /**
   * Send a message to the bus
   */
  async sendMessage(message: EnhancedA2AMessage): Promise<void> {
    // HARD LIMIT: Enforce absolute maximum message count to prevent runaway processes
    const totalMessages = Object.keys(this.messageIndex).length;
    if (totalMessages >= this.GLOBAL_MESSAGE_LIMIT && this.state.status !== 'completed') {
      console.log(`⚠️ EMERGENCY CUTOFF: Total messages (${totalMessages}) exceeded global limit`);
      this.state.status = 'completed';
      this.state.endTime = new Date().toISOString();
      
      // Create emergency cutoff message
      const emergencyMessage = this.createSystemMessage(
        `EMERGENCY CUTOFF: The collaboration was automatically terminated because the total message count (${totalMessages}) exceeded the global limit (${this.GLOBAL_MESSAGE_LIMIT}).

This is likely due to an infinite loop in agent communication. The system will now stop all agent processing to prevent resource exhaustion.

Please check your agent implementations for circular dependencies or mutual calls that could lead to endless loops.`,
        'all',
        'error'
      );
      
      // Only add to state, skip all other processing
      this.messageIndex[emergencyMessage.id] = emergencyMessage;
      this.state.messageIndex[emergencyMessage.id] = emergencyMessage;
      
      if (!this.conversations[emergencyMessage.conversationId]) {
        this.conversations[emergencyMessage.conversationId] = [];
        this.state.conversations[emergencyMessage.conversationId] = [];
      }
      
      this.conversations[emergencyMessage.conversationId].push(emergencyMessage.id);
      this.state.conversations[emergencyMessage.conversationId].push(emergencyMessage.id);
      
      // Clear all message counts and the queue
      this.agentMessageCounts = {};
      this.messageQueue = [];
      this.isProcessing = false;
      
      return;
    }
    
    // RATE LIMITING: Detect rapid message sequences
    const now = Date.now();
    this.lastMessageTimestamps.push(now);
    
    // Keep only timestamps within the window
    this.lastMessageTimestamps = this.lastMessageTimestamps.filter(
      ts => now - ts < this.RAPID_MESSAGE_WINDOW_MS
    );
    
    // If we have too many messages in the time window, we're likely in a loop
    if (this.lastMessageTimestamps.length > this.MAX_MESSAGES_PER_SECOND && !this.loopDetectionActive) {
      this.loopDetectionActive = true;
      console.log(`⚠️ Loop detected! ${this.lastMessageTimestamps.length} messages in ${this.RAPID_MESSAGE_WINDOW_MS}ms`);
      
      // Create loop detection message
      const loopDetectionMessage = this.createSystemMessage(
        `LOOP DETECTED: The system detected a high message rate (${this.lastMessageTimestamps.length} messages in ${this.RAPID_MESSAGE_WINDOW_MS}ms) which indicates a potential infinite loop in agent communication.

Processing has been paused for 3 seconds to break the cycle. After this pause, agents will resume with limited communication patterns.`,
        'all',
        'warning'
      );
      
      // Add to state
      this.messageIndex[loopDetectionMessage.id] = loopDetectionMessage;
      this.state.messageIndex[loopDetectionMessage.id] = loopDetectionMessage;
      
      if (!this.conversations[loopDetectionMessage.conversationId]) {
        this.conversations[loopDetectionMessage.conversationId] = [];
        this.state.conversations[loopDetectionMessage.conversationId] = [];
      }
      
      this.conversations[loopDetectionMessage.conversationId].push(loopDetectionMessage.id);
      this.state.conversations[loopDetectionMessage.conversationId].push(loopDetectionMessage.id);
      
      // Reset all agent message counts to break existing loops
      this.agentMessageCounts = {};
      
      // Wait 3 seconds to break the loop cycle, then reset
      setTimeout(() => {
        this.loopDetectionActive = false;
        this.lastMessageTimestamps = [];
        console.log('Loop detection reset after pause');
      }, 3000);
      
      // Continue with adding the current message but don't process it further
      this.messageIndex[message.id] = message;
      this.state.messageIndex[message.id] = message;
      
      if (!this.conversations[message.conversationId]) {
        this.conversations[message.conversationId] = [];
        this.state.conversations[message.conversationId] = [];
      }
      
      this.conversations[message.conversationId].push(message.id);
      this.state.conversations[message.conversationId].push(message.id);
      
      return;
    }
    
    // Prevent recursion if we're already in the process of forcing completion
    if (message.from === 'system' && message.parts.some(p => 
        p.type === 'text' && ((p as TextPart).text.includes('COLLABORATION COMPLETE') ||
        (p as TextPart).text.includes('EMERGENCY CUTOFF') ||
        (p as TextPart).text.includes('LOOP DETECTED')))) {
      console.log('Received system control message, adding to state without further processing');
      
      // Still add the message to state
      this.messageIndex[message.id] = message;
      this.state.messageIndex[message.id] = message;
      
      // Add to conversation
      if (!this.conversations[message.conversationId]) {
        this.conversations[message.conversationId] = [];
        this.state.conversations[message.conversationId] = [];
      }
      
      this.conversations[message.conversationId].push(message.id);
      this.state.conversations[message.conversationId].push(message.id);
      
      return; // Skip further processing to avoid infinite loop
    }
    
    console.log(`Sending message from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}`);
    
    // Add message to state
    this.messageIndex[message.id] = message;
    this.state.messageIndex[message.id] = message;
    
    // Add to conversation
    if (!this.conversations[message.conversationId]) {
      this.conversations[message.conversationId] = [];
      this.state.conversations[message.conversationId] = [];
    }
    
    this.conversations[message.conversationId].push(message.id);
    this.state.conversations[message.conversationId].push(message.id);
    
    // Check if we've reached the maximum session duration
    const startTime = new Date(this.state.startTime).getTime();
    const currentTime = Date.now();
    const elapsedTime = currentTime - startTime;
    const MAX_DURATION_MS = 5 * 60 * 1000; // 5 minutes maximum duration
    
    // Only check time-based completion when not already heading to completion
    if (elapsedTime >= MAX_DURATION_MS && 
        (this.state.status === 'active' || this.state.status === 'paused')) {
      console.log(`Forcing completion due to time limit: ${Math.round(elapsedTime/1000)} seconds elapsed`);
      await this.forceCompletionWithFinalArticle();
      return;
    }
    
    // Add to queue for processing
    this.messageQueue.push(message);
    
    // Start processing if not already processing
    if (!this.isProcessing) {
      await this.processQueue();
    }
  }

  /**
   * Process the message queue
   */
  private async processQueue(): Promise<void> {
    this.isProcessing = true;
    
    // Don't process any more messages if the collaboration is completed
    if (this.state.status === 'completed' || this.state.status === 'failed' || this.state.status !== 'active') {
      console.log('Collaboration is already completed. Clearing message queue.');
      this.messageQueue = [];
      this.isProcessing = false;
      return;
    }
    
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!;
      
      // Skip processing system completion messages to avoid infinite loops
      if (message.from === 'system' && message.parts.some(p => 
          p.type === 'text' && (p as TextPart).text.includes('COLLABORATION COMPLETE'))) {
        console.log('Skipping processing of completion message in queue');
        continue;
      }
      
      // Skip processing if collaboration is not active
      if (this.state.status !== 'active' && this.state.status !== 'paused') {
        console.log('Collaboration completed during message processing. Stopping further processing.');
        break;
      }
      
      await this.routeMessage(message);
    }
    
    this.isProcessing = false;
  }

  /**
   * Route a message to the appropriate agent(s)
   */
  private async routeMessage(message: EnhancedA2AMessage): Promise<void> {
    // If the message is to 'all', broadcast to all agents
    if (message.to === 'all') {
      await this.broadcastMessage(message);
      return;
    }
    
    // If to is an array, send to each recipient
    if (Array.isArray(message.to)) {
      for (const recipient of message.to) {
        await this.sendToAgent(message, recipient);
      }
      return;
    }
    
    // Single recipient
    await this.sendToAgent(message, message.to);
  }
  
  /**
   * Broadcast a message to all agents
   */
  private async broadcastMessage(message: EnhancedA2AMessage): Promise<void> {
    const validAgents = [
      'market-research',
      'seo-keyword',
      'content-strategy',
      'seo-optimization',
      'content-generation'
    ];
    
    // For each agent, create a copy of the message with the specific agent as recipient
    for (const agent of validAgents) {
      // Skip sending to the originator
      if (agent === message.from) continue;
      
      // Create a copy with the specific agent as recipient
      const agentMessage: EnhancedA2AMessage = {
        ...message,
        to: agent
      };
      
      await this.sendToAgent(agentMessage, agent);
    }
  }
  
  /**
   * Send a message to a specific agent
   */
  private async sendToAgent(message: EnhancedA2AMessage, recipient: string): Promise<void> {
    // Skip if recipient is 'system'
    if (recipient === 'system') {
      return;
    }
    
    // Check for potential infinite loops between agents
    const fromAgent = message.from;
    const toAgent = recipient;
    
    // Initialize tracking if needed
    if (!this.agentMessageCounts[fromAgent]) {
      this.agentMessageCounts[fromAgent] = {};
    }
    if (!this.agentMessageCounts[fromAgent][toAgent]) {
      this.agentMessageCounts[fromAgent][toAgent] = 0;
    }
    
    // Increment message count
    this.agentMessageCounts[fromAgent][toAgent]++;
    
    // Check if we're in a potential loop
    const currentExchangeCount = this.agentMessageCounts[fromAgent][toAgent];
    
    // Also check for bidirectional exchanges (A->B, B->A pattern)
    let bidirectionalCount = 0;
    if (this.agentMessageCounts[toAgent] && this.agentMessageCounts[toAgent][fromAgent]) {
      bidirectionalCount = this.agentMessageCounts[toAgent][fromAgent];
    }
    
    // Get current time
    const now = Date.now();
    const lastProcessed = this.lastProcessedTime[message.conversationId] || 0;
    const timeSinceLastProcessed = now - lastProcessed;
    
    // Update last processed time
    this.lastProcessedTime[message.conversationId] = now;
    
    // Check for potential infinite loops or processing happening too fast
    if ((currentExchangeCount > this.MAX_AGENT_EXCHANGES || 
        bidirectionalCount > this.MAX_AGENT_EXCHANGES) && 
        timeSinceLastProcessed < this.AGENT_TIMEOUT_MS) {
      console.log(`Potential infinite loop detected between ${fromAgent} and ${toAgent}. ` +
                 `Exchange count: ${currentExchangeCount}, Bidirectional: ${bidirectionalCount}, ` +
                 `Time since last: ${timeSinceLastProcessed}ms`);
                 
      // Create a system message about the loop
      const loopDetectionMessage = this.createSystemMessage(
        `Detected potential message loop between ${fromAgent} and ${toAgent}. ` +
        `Processing has been paused to prevent infinite recursion. ` +
        `Please check agent implementations for mutual dependencies or circular message patterns.`,
        'system',
        'warning'
      );
      
      // Add the message to history so it's visible in the UI
      this.addMessageToHistory(loopDetectionMessage);
      
      // Reset counters to allow future exchanges after a break
      this.agentMessageCounts[fromAgent][toAgent] = 0;
      if (this.agentMessageCounts[toAgent] && this.agentMessageCounts[toAgent][fromAgent]) {
        this.agentMessageCounts[toAgent][fromAgent] = 0;
      }
      
      return; // Don't send this message
    }
    
    // List of valid agents
    const validAgents = [
      'market-research',
      'seo-keyword',
      'content-strategy',
      'seo-optimization',
      'content-generation'
    ];
    
    // Check if the recipient is valid
    if (!validAgents.includes(recipient)) {
      console.error(`Unknown agent: ${recipient}`);
      return;
    }
    
    try {
      // Construct the full URL for the agent endpoint
      // We've modified to include "with-reasoning" to indicate enhanced protocol
      const agentUrl = `${this.baseUrl}/api/agents/${recipient}/with-reasoning`;
      
      console.log(`Sending enhanced message to ${recipient}`);
      
      // Send the message directly (no conversion needed with new protocol)
      const response = await fetch(agentUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          state: this.state
        }),
      });
      
      // Handle response errors
      if (!response.ok) {
        // Try fallback to legacy endpoint
        console.log(`Enhanced endpoint not available for ${recipient}, trying legacy endpoint`);
        await this.sendToLegacyAgent(message, recipient);
        return;
      }
      
      // Process the response
      const responseData = await response.json();
      
      // If the agent sent a response message, add it to the queue
      if (responseData.message) {
        this.messageQueue.push(responseData.message);
      }
      
      // If the agent updated the state, merge the changes
      if (responseData.stateUpdates) {
        this.mergeStateUpdates(responseData.stateUpdates);
      }
    } catch (error) {
      console.error(`Error sending message to ${recipient}:`, error);
      
      // Try fallback to legacy endpoint
      console.log(`Error with enhanced endpoint for ${recipient}, trying legacy endpoint`);
      await this.sendToLegacyAgent(message, recipient);
    }
  }
  
  /**
   * Fallback to send to legacy agent endpoint
   */
  private async sendToLegacyAgent(message: EnhancedA2AMessage, recipient: string): Promise<void> {
    try {
      // Construct the full URL for the legacy agent endpoint
      const agentUrl = `${this.baseUrl}/api/agents/${recipient}`;
      
      // Convert enhanced message to legacy format
      const legacyMessage = this.convertToLegacyFormat(message);
      
      // Send the message
      const response = await fetch(agentUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: legacyMessage,
          state: this.extractLegacyState()
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to send message to ${recipient}: ${response.statusText}`);
      }
      
      // Process the response
      const responseData = await response.json();
      
      // If the agent sent a response message, convert it to enhanced format and add to queue
      if (responseData.message) {
        const enhancedMessage = this.convertFromLegacyFormat(responseData.message, recipient);
        this.messageQueue.push(enhancedMessage);
      }
      
      // If the agent updated the state, merge the changes
      if (responseData.stateUpdates) {
        this.mergeStateUpdates(responseData.stateUpdates);
      }
    } catch (error) {
      console.error(`Error sending legacy message to ${recipient}:`, error);
      
      // Add an error message to the history
      const errorMessage = this.createSystemMessage(
        `Error sending message to ${recipient}: ${error}`,
        message.from,
        'error'
      );
      
      this.addMessageToHistory(errorMessage);
    }
  }
  
  /**
   * Convert enhanced message to legacy format
   */
  private convertToLegacyFormat(message: EnhancedA2AMessage): any {
    // Extract text content from parts
    let textContent = '';
    for (const part of message.parts) {
      if (part.type === 'text') {
        textContent += part.text;
      }
    }
    
    // Include reasoning in the message content if available
    if (message.reasoning) {
      textContent += '\n\n--- Reasoning ---\n';
      if (message.reasoning.thoughts.length > 0) {
        textContent += '\nThoughts:\n' + message.reasoning.thoughts.map(t => `- ${t}`).join('\n');
      }
      if (message.reasoning.considerations.length > 0) {
        textContent += '\nConsiderations:\n' + message.reasoning.considerations.map(c => `- ${c}`).join('\n');
      }
      if (message.reasoning.alternatives && message.reasoning.alternatives.length > 0) {
        textContent += '\nAlternatives:\n' + message.reasoning.alternatives.map(a => `- ${a}`).join('\n');
      }
      textContent += `\nDecision: ${message.reasoning.decision}`;
    }
    
    // Convert to legacy format
    return {
      id: message.id,
      timestamp: message.timestamp,
      from: message.from,
      to: Array.isArray(message.to) ? message.to[0] : message.to,
      type: message.intentions && message.intentions.length > 0 ? message.intentions[0].toUpperCase() : 'INFORM',
      content: textContent,
      replyTo: message.replyTo,
      metadata: {
        ...message.metadata,
        original: 'enhanced'
      }
    };
  }
  
  /**
   * Convert legacy message to enhanced format
   */
  private convertFromLegacyFormat(legacyMessage: any, recipient: string): EnhancedA2AMessage {
    // Extract potential reasoning from content
    let textContent = legacyMessage.content;
    let thoughts: string[] = [];
    let considerations: string[] = [];
    let alternatives: string[] = [];
    let decision = '';
    
    // Check if content contains reasoning
    const reasoningMatch = textContent.match(/--- Reasoning ---\n([\s\S]*?)(?:\n\n|$)/);
    if (reasoningMatch) {
      // Extract reasoning text
      const reasoningText = reasoningMatch[1];
      
      // Remove reasoning from content
      textContent = textContent.replace(/--- Reasoning ---\n[\s\S]*?(?:\n\n|$)/, '');
      
      // Extract thoughts
      const thoughtsMatch = reasoningText.match(/Thoughts:\n([\s\S]*?)(?:Considerations:|Alternatives:|Decision:|$)/);
      if (thoughtsMatch) {
        thoughts = thoughtsMatch[1].split('\n')
          .filter((line: string) => line.trim().startsWith('- '))
          .map((line: string) => line.trim().substring(2));
      }
      
      // Extract considerations
      const considerationsMatch = reasoningText.match(/Considerations:\n([\s\S]*?)(?:Alternatives:|Decision:|$)/);
      if (considerationsMatch) {
        considerations = considerationsMatch[1].split('\n')
          .filter((line: string) => line.trim().startsWith('- '))
          .map((line: string) => line.trim().substring(2));
      }
      
      // Extract alternatives
      const alternativesMatch = reasoningText.match(/Alternatives:\n([\s\S]*?)(?:Decision:|$)/);
      if (alternativesMatch) {
        alternatives = alternativesMatch[1].split('\n')
          .filter((line: string) => line.trim().startsWith('- '))
          .map((line: string) => line.trim().substring(2));
      }
      
      // Extract decision
      const decisionMatch = reasoningText.match(/Decision: (.*?)(?:\n|$)/);
      if (decisionMatch) {
        decision = decisionMatch[1];
      }
    }
    
    // Determine message intention from type
    let intentions: MessageIntention[] = ['inform'];
    if (legacyMessage.type) {
      const type = legacyMessage.type.toLowerCase();
      if (type.includes('request')) intentions = ['request'];
      else if (type.includes('feedback')) intentions = ['critique'];
      else if (type.includes('question')) intentions = ['request', 'clarify'];
      else if (type.includes('suggest')) intentions = ['suggest'];
    }
    
    // Create text part
    const textPart: TextPart = {
      type: 'text',
      text: textContent.trim()
    };
    
    // Create enhanced message
    const enhancedMessage: EnhancedA2AMessage = {
      id: legacyMessage.id || uuidv4(),
      timestamp: legacyMessage.timestamp || new Date().toISOString(),
      from: legacyMessage.from,
      to: legacyMessage.to,
      role: 'agent',
      parts: [textPart],
      conversationId: legacyMessage.metadata?.conversationId || uuidv4(),
      intentions,
      replyTo: legacyMessage.replyTo,
      metadata: {
        ...legacyMessage.metadata,
        convertedFromLegacy: true
      }
    };
    
    // Add reasoning if found
    if (thoughts.length > 0 || considerations.length > 0 || decision) {
      enhancedMessage.reasoning = {
        thoughts,
        considerations,
        alternatives,
        decision: decision || 'Provide response based on available information',
        confidence: 0.8 // Default confidence
      };
    }
    
    return enhancedMessage;
  }
  
  /**
   * Extract relevant state data for legacy agents
   */
  private extractLegacyState(): any {
    return {
      id: this.state.id,
      topic: this.state.topic,
      contentType: this.state.contentType,
      targetAudience: this.state.targetAudience,
      tone: this.state.tone,
      keywords: this.state.keywords,
      phase: this.mapGoalToPhase(),
      startTime: this.state.startTime,
      endTime: this.state.endTime,
      // Extract data from artifacts
      marketResearch: this.extractMarketResearch(),
      seoKeywords: this.extractSeoKeywords(),
      contentStrategy: this.extractContentStrategy(),
      content: this.extractContent(),
      seoOptimization: this.extractSeoOptimization(),
      // Legacy conversation format
      conversations: this.extractLegacyConversations(),
      decisions: {
        planning: [],
        discussion: [],
        execution: [],
        review: [],
        refinement: []
      },
      iterations: []
    };
  }
  
  /**
   * Map current goal to legacy phase
   */
  private mapGoalToPhase(): string {
    const currentGoal = this.state.currentGoal;
    
    if (!currentGoal) return 'planning';
    
    if (currentGoal.includes('market research') || currentGoal.includes('SEO keywords')) {
      return 'discussion';
    } else if (currentGoal.includes('content strategy') || currentGoal.includes('Generate initial content')) {
      return 'execution';
    } else if (currentGoal.includes('SEO optimization')) {
      return 'review';
    } else if (currentGoal.includes('Refine and finalize')) {
      return 'refinement';
    }
    
    return 'planning';
  }
  
  /**
   * Extract market research from artifacts
   */
  private extractMarketResearch(): any {
    // Find market research artifact
    const artifacts = Object.values(this.state.artifactIndex);
    const marketResearchArtifact = artifacts.find(a => a.type === 'market-research');
    
    if (!marketResearchArtifact || !marketResearchArtifact.parts) return null;
    
    // Extract data from artifact
    const dataParts = marketResearchArtifact.parts.filter(p => p.type === 'data');
    if (dataParts.length > 0 && dataParts[0].type === 'data') {
      return dataParts[0].data;
    }
    
    // Fallback to text
    const textParts = marketResearchArtifact.parts.filter(p => p.type === 'text');
    if (textParts.length > 0 && textParts[0].type === 'text') {
      return { research: textParts[0].text };
    }
    
    return null;
  }
  
  /**
   * Extract SEO keywords from artifacts
   */
  private extractSeoKeywords(): any {
    // Find keyword artifact
    const artifacts = Object.values(this.state.artifactIndex);
    const keywordArtifact = artifacts.find(a => a.type === 'keyword-set');
    
    if (!keywordArtifact || !keywordArtifact.parts) return null;
    
    // Extract data from artifact
    const dataParts = keywordArtifact.parts.filter(p => p.type === 'data');
    if (dataParts.length > 0 && dataParts[0].type === 'data') {
      return dataParts[0].data;
    }
    
    // Fallback to text
    const textParts = keywordArtifact.parts.filter(p => p.type === 'text');
    if (textParts.length > 0 && textParts[0].type === 'text') {
      return { keywords: textParts[0].text };
    }
    
    return null;
  }
  
  /**
   * Extract content strategy from artifacts
   */
  private extractContentStrategy(): any {
    // Find content structure artifact
    const artifacts = Object.values(this.state.artifactIndex);
    const structureArtifact = artifacts.find(a => a.type === 'content-structure');
    
    if (!structureArtifact || !structureArtifact.parts) return null;
    
    // Extract data from artifact
    const dataParts = structureArtifact.parts.filter(p => p.type === 'data');
    if (dataParts.length > 0 && dataParts[0].type === 'data') {
      return dataParts[0].data;
    }
    
    // Fallback to text
    const textParts = structureArtifact.parts.filter(p => p.type === 'text');
    if (textParts.length > 0 && textParts[0].type === 'text') {
      try {
        return JSON.parse(textParts[0].text);
      } catch (e) {
        return { contentStructure: textParts[0].text };
      }
    }
    
    return null;
  }
  
  /**
   * Extract content from artifacts
   */
  private extractContent(): any {
    // Find content artifact (use final if available, otherwise draft)
    const artifacts = Object.values(this.state.artifactIndex);
    const contentArtifact = 
      artifacts.find(a => a.type === 'final-content') || 
      artifacts.find(a => a.type === 'draft-content');
    
    if (!contentArtifact || !contentArtifact.parts) return null;
    
    // Extract data from artifact
    const dataParts = contentArtifact.parts.filter(p => p.type === 'data');
    if (dataParts.length > 0 && dataParts[0].type === 'data') {
      return dataParts[0].data;
    }
    
    // Fallback to text
    const textParts = contentArtifact.parts.filter(p => p.type === 'text');
    if (textParts.length > 0 && textParts[0].type === 'text') {
      return { content: textParts[0].text };
    }
    
    return null;
  }
  
  /**
   * Extract SEO optimization from artifacts
   */
  private extractSeoOptimization(): any {
    // Find SEO analysis artifact
    const artifacts = Object.values(this.state.artifactIndex);
    const seoArtifact = artifacts.find(a => a.type === 'seo-analysis');
    
    if (!seoArtifact || !seoArtifact.parts) return null;
    
    // Extract data from artifact
    const dataParts = seoArtifact.parts.filter(p => p.type === 'data');
    if (dataParts.length > 0 && dataParts[0].type === 'data') {
      return dataParts[0].data;
    }
    
    // Fallback to text
    const textParts = seoArtifact.parts.filter(p => p.type === 'text');
    if (textParts.length > 0 && textParts[0].type === 'text') {
      try {
        return JSON.parse(textParts[0].text);
      } catch (e) {
        return { analysis: textParts[0].text };
      }
    }
    
    return null;
  }
  
  /**
   * Extract legacy conversations format
   */
  private extractLegacyConversations(): Record<string, any[]> {
    const legacyConversations: Record<string, any[]> = {};
    
    // Convert each conversation
    Object.entries(this.conversations).forEach(([conversationId, messageIds]) => {
      legacyConversations[conversationId] = messageIds
        .map(id => this.messageIndex[id])
        .map(message => this.convertToLegacyFormat(message));
    });
    
    return legacyConversations;
  }
  
  /**
   * Merge state updates from agent responses
   */
  private mergeStateUpdates(updates: any): void {
    console.log('Merging state updates:', JSON.stringify(updates, null, 2));
    
    // Process messages first if they exist
    if (updates.messages && Array.isArray(updates.messages)) {
      // Add these messages to the queue for processing
      for (const message of updates.messages) {
        console.log('Adding trigger message to queue:', message.from, 'to', message.to);
        this.messageQueue.push(message);
      }
      // Remove messages from updates to avoid double processing
      delete updates.messages;
    }
    
    // Process goal updates if they exist
    if (updates.goalUpdates && typeof updates.goalUpdates === 'object') {
      console.log('Processing goal updates:', updates.goalUpdates);
      
      // Update each goal with its progress and artifacts
      Object.entries(updates.goalUpdates).forEach(([goalId, goalUpdate]: [string, any]) => {
        const goalIndex = this.state.goals.findIndex(g => g.id === goalId);
        if (goalIndex >= 0) {
          // Update progress
          if (typeof goalUpdate.progress === 'number') {
            this.state.goals[goalIndex].progress = goalUpdate.progress;
          }
          
          // Mark as completed if specified
          if (goalUpdate.progress === 100 || goalUpdate.completed) {
            this.state.goals[goalIndex].status = 'completed';
            this.state.goals[goalIndex].completedAt = new Date().toISOString();
          }
          
          // Add artifacts if provided
          if (goalUpdate.artifacts && Array.isArray(goalUpdate.artifacts)) {
            // Add artifacts to the goal
            this.state.goals[goalIndex].artifacts = [
              ...this.state.goals[goalIndex].artifacts,
              ...goalUpdate.artifacts.map((a: any) => typeof a === 'string' ? a : a.id)
            ];
            
            // Add artifacts to the artifact index if they're not already there
            goalUpdate.artifacts.forEach((artifact: any) => {
              if (typeof artifact !== 'string' && artifact.id) {
                this.state.artifactIndex[artifact.id] = artifact;
              }
            });
          }
        }
      });
      
      // Remove goalUpdates from updates to avoid double processing
      delete updates.goalUpdates;
    }
    
    // Process remaining updates
    Object.entries(updates).forEach(([key, value]) => {
      if (key === 'artifactIndex' && typeof value === 'object') {
        // Merge artifact updates
        this.state.artifactIndex = {
          ...this.state.artifactIndex,
          ...value
        };
      } else if (key === 'decisionIndex' && typeof value === 'object') {
        // Merge decision updates
        this.state.decisionIndex = {
          ...this.state.decisionIndex,
          ...value
        };
      } else if (key === 'goals' && Array.isArray(value)) {
        // Update goals if provided
        this.state.goals = value;
      } else if (key in this.state) {
        // Update other properties
        (this.state as any)[key] = value;
      }
    });
    
    // Update overall progress
    this.updateProgress();
  }
  
  /**
   * Update overall progress based on goals
   */
  private updateProgress(): void {
    if (!this.state.goals || this.state.goals.length === 0) {
      this.state.progress = 0;
      return;
    }
    
    const totalProgress = this.state.goals.reduce((sum, goal) => sum + goal.progress, 0);
    this.state.progress = Math.round(totalProgress / this.state.goals.length);
  }
  
  /**
   * Create a system message
   * @param content The message content
   * @param to The recipient(s) of the message (default: 'all')
   * @param messageType The type of system message (default: 'info')
   * @param reasoning Optional reasoning for the message
   */
  createSystemMessage(
    content: string, 
    to: string | string[] = 'all',
    messageType: string = 'info',
    reasoning?: Reasoning
  ): EnhancedA2AMessage {
    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'system',
      to,
      role: 'system' as const,
      parts: [
        {
          type: 'text' as const,
          text: content
        }
      ],
      conversationId: 'system',
      intentions: ['inform' as const],
      reasoning,
      metadata: {
        systemMessage: true,
        messageType,
        timestamp: new Date().toISOString()
      }
    };
  }
    
    /**
   * Force completion of the collaboration with a final article
   * This is called when the maximum message limit is reached
   */
  private async forceCompletionWithFinalArticle(): Promise<void> {
    console.log('Forcing collaboration completion due to message limit or time exceeded');
    
    // Check if we're already completed to prevent redundant completion attempts
    if (this.state.status === 'completed') {
      console.log('Collaboration already completed, skipping force completion');
      return;
    }
    
    // Update state
    this.state.status = 'completed';
    this.state.endTime = new Date().toISOString();
    
    // Calculate duration
    const startTime = new Date(this.state.startTime).getTime();
    const endTime = new Date(this.state.endTime).getTime();
    const durationMs = endTime - startTime;
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    this.state.duration = `${minutes}m ${seconds}s`;
    
    // Update progress
    this.state.progress = 100;
    
    // Reset all agent message counts to prevent any further processing
    this.agentMessageCounts = {};
    
    // Clear the message queue
    this.messageQueue = [];
    
    // Mark all in-progress goals as completed
    if (this.state.goals) {
      for (const goal of this.state.goals) {
        if (goal.status === 'in-progress' || goal.status === 'pending') {
          goal.status = 'completed';
          goal.progress = 100;
        }
      }
    }
    
    // Find the latest draft content
    let finalContent = '';
    let draftFound = false;
    
    // Look through all messages for draft content from content-generation agent
    const messages = Object.values(this.state.messageIndex || {});
    const contentGenMessages = messages.filter(m => 
      m && typeof m === 'object' && 'from' in m && m.from === 'content-generation'
    );
    
    // Sort by timestamp, newest first
    contentGenMessages.sort((a: any, b: any) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
    
    // Find the latest content
    for (const message of contentGenMessages) {
      if (message.parts && Array.isArray(message.parts)) {
        const textParts = message.parts.filter(p => p && p.type === 'text');
        if (textParts.length > 0) {
          const content = textParts.map(p => (p as TextPart).text).join('\n');
          if (content.length > 200) { // Assuming real content is longer than 200 chars
            finalContent = content;
            draftFound = true;
            break;
          }
        }
      }
    }
    
    if (!draftFound) {
      finalContent = `Final Article for ${this.state.topic || 'Requested Topic'}\n\nThis is an automatically generated article based on the collaboration between agents.\n\nPlease check the artifacts created during the collaboration for more detailed content.`;
    }
    
    // Create a final artifact directly (without going through the message bus)
    if (this.state.artifactIndex) {
      const finalArtifactId = uuidv4();
      const timestamp = new Date().toISOString();
      
      const finalArtifact = {
        id: finalArtifactId,
        name: 'Final Article',
        description: 'Automatically generated final article',
        type: 'final-content' as any,
        creator: 'system',
        timestamp,
        data: {},
        parts: [
          {
            type: 'text' as const,
            text: finalContent
          }
        ],
        metadata: {
          createdAt: timestamp,
          createdBy: 'system',
          forcedCompletion: true
        },
        index: 0,
        version: 1,
        contributors: ['system'],
        feedback: [],
        history: [
          {
            version: 1,
            timestamp,
            agent: 'system',
            changes: 'Force completion - final article generation',
            parts: [
              {
                type: 'text' as const,
                text: finalContent
              }
            ],
            reasoning: {
              thoughts: ['Maximum message limit reached'],
              considerations: ['Need to provide a final output'],
              decision: 'Generate final article from available content',
              confidence: 0.8
            }
          }
        ],
        dependencies: [],
        status: 'published' as const,
        qualityScore: 70
      };
      
      // Add the artifact to the state directly
      this.state.artifactIndex[finalArtifactId] = finalArtifact;
      
      // Find the final goal and add this artifact to it
      const finalGoal = this.state.goals.find(g => 
        g.description === 'Refine and finalize content'
      );
      
      if (finalGoal) {
        finalGoal.artifacts = finalGoal.artifacts || [];
        finalGoal.artifacts.push(finalArtifactId);
      }
      
      console.log(`Created final artifact with ID: ${finalArtifactId}`);
    }
    
    // Create a system message announcing completion without sending it through the normal flow
    const completionMessageId = uuidv4();
    const completionMessage: EnhancedA2AMessage = {
      id: completionMessageId,
      timestamp: new Date().toISOString(),
      from: 'system',
      to: 'all',
      role: 'system' as const,
      parts: [
        {
          type: 'text' as const,
          text: `COLLABORATION COMPLETE: Maximum limit reached. Final article has been generated.`
        }
      ],
      conversationId: 'system',
      intentions: ['inform' as const]
    };
    
    // Add message directly to state without processing it
    this.messageIndex[completionMessageId] = completionMessage;
    if (this.state.messageIndex) {
      this.state.messageIndex[completionMessageId] = completionMessage;
    }
    
    if (!this.conversations['system']) {
      this.conversations['system'] = [];
      if (this.state.conversations) {
        this.state.conversations['system'] = [];
      }
    }
    
    this.conversations['system'].push(completionMessageId);
    if (this.state.conversations && this.state.conversations['system']) {
      this.state.conversations['system'].push(completionMessageId);
    }
    
    // Mark the collaboration as completed
    this.state.status = 'completed';
    this.state.endTime = new Date().toISOString();
    
    console.log('Collaboration has been forcefully completed with final article');
  }
}
