// src/app/(payload)/api/agents/dynamic-collaboration/decision-manager.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  Decision, 
  DecisionOption, 
  DecisionVote, 
  Reasoning 
} from '../a2atypes';

/**
 * Decision Manager
 * 
 * This class manages the collaborative decision-making process between agents.
 * It provides methods for creating, updating, and resolving decisions with
 * voting and reasoning mechanisms.
 */
export class DecisionManager {
  private decisions: Map<string, Decision> = new Map();
  
  /**
   * Get all decisions
   */
  getAllDecisions(): Decision[] {
    return Array.from(this.decisions.values());
  }
  
  /**
   * Get all decisions (alias for getAllDecisions for API compatibility)
   */
  getDecisions(): Decision[] {
    return this.getAllDecisions();
  }
  
  /**
   * Get open decisions
   */
  getOpenDecisions(): Decision[] {
    return this.getAllDecisions().filter(decision => decision.status === 'open');
  }
  
  /**
   * Get decision by ID
   */
  getDecisionById(id: string): Decision | undefined {
    return this.decisions.get(id);
  }
  
  /**
   * Create a new decision
   */
  createDecision(
    topic: string,
    description: string,
    proposer: string,
    initialOptions: Array<{
      description: string;
      rationale: string;
      pros?: string[];
      cons?: string[];
    }>,
    reasoning?: Reasoning
  ): Decision {
    // Create options with IDs
    const options: DecisionOption[] = initialOptions.map(option => ({
      id: uuidv4(),
      description: option.description,
      rationale: option.rationale,
      proposedBy: proposer,
      votes: [],
      pros: option.pros || [],
      cons: option.cons || []
    }));
    
    // Create new decision
    const decision: Decision = {
      id: uuidv4(),
      topic,
      description,
      options,
      status: 'open',
      reasoning
    };
    
    // Store decision
    this.decisions.set(decision.id, decision);
    
    return decision;
  }
  
  /**
   * Add option to an existing decision
   */
  addOption(
    decisionId: string,
    description: string,
    rationale: string,
    proposer: string,
    pros: string[] = [],
    cons: string[] = []
  ): DecisionOption | undefined {
    const decision = this.getDecisionById(decisionId);
    
    if (!decision) {
      console.error(`Decision not found: ${decisionId}`);
      return undefined;
    }
    
    if (decision.status !== 'open') {
      console.error(`Cannot add option to non-open decision: ${decisionId}`);
      return undefined;
    }
    
    // Create new option
    const option: DecisionOption = {
      id: uuidv4(),
      description,
      rationale,
      proposedBy: proposer,
      votes: [],
      pros,
      cons
    };
    
    // Add option to decision
    decision.options.push(option);
    
    // Store updated decision
    this.decisions.set(decisionId, decision);
    
    return option;
  }
  
  /**
   * Cast a vote for an option
   */
  castVote(
    decisionId: string,
    optionId: string,
    agent: string,
    confidence: number,
    reasoning: string
  ): DecisionVote | undefined {
    const decision = this.getDecisionById(decisionId);
    
    if (!decision) {
      console.error(`Decision not found: ${decisionId}`);
      return undefined;
    }
    
    if (decision.status !== 'open') {
      console.error(`Cannot vote on non-open decision: ${decisionId}`);
      return undefined;
    }
    
    // Find option
    const optionIndex = decision.options.findIndex(opt => opt.id === optionId);
    
    if (optionIndex === -1) {
      console.error(`Option not found: ${optionId}`);
      return undefined;
    }
    
    // Check if agent already voted
    const existingVoteIndex = decision.options[optionIndex].votes.findIndex(vote => vote.agent === agent);
    
    // Create vote
    const vote: DecisionVote = {
      agent,
      confidence,
      reasoning,
      timestamp: new Date().toISOString()
    };
    
    // Replace existing vote or add new one
    if (existingVoteIndex !== -1) {
      decision.options[optionIndex].votes[existingVoteIndex] = vote;
    } else {
      // Remove vote from other options
      decision.options.forEach((opt, idx) => {
        if (idx !== optionIndex) {
          opt.votes = opt.votes.filter(v => v.agent !== agent);
        }
      });
      
      // Add vote to selected option
      decision.options[optionIndex].votes.push(vote);
    }
    
    // Store updated decision
    this.decisions.set(decisionId, decision);
    
    return vote;
  }
  
  /**
   * Add pro or con to an option
   */
  addPointToOption(
    decisionId: string,
    optionId: string,
    point: string,
    type: 'pro' | 'con'
  ): boolean {
    const decision = this.getDecisionById(decisionId);
    
    if (!decision) {
      console.error(`Decision not found: ${decisionId}`);
      return false;
    }
    
    if (decision.status !== 'open') {
      console.error(`Cannot modify non-open decision: ${decisionId}`);
      return false;
    }
    
    // Find option
    const optionIndex = decision.options.findIndex(opt => opt.id === optionId);
    
    if (optionIndex === -1) {
      console.error(`Option not found: ${optionId}`);
      return false;
    }
    
    // Add point to pros or cons
    if (type === 'pro') {
      decision.options[optionIndex].pros.push(point);
    } else {
      decision.options[optionIndex].cons.push(point);
    }
    
    // Store updated decision
    this.decisions.set(decisionId, decision);
    
    return true;
  }
  
  /**
   * Resolve a decision based on votes
   */
  resolveDecision(
    decisionId: string,
    decidedBy: string[],
    reasoning?: Reasoning
  ): Decision | undefined {
    const decision = this.getDecisionById(decisionId);
    
    if (!decision) {
      console.error(`Decision not found: ${decisionId}`);
      return undefined;
    }
    
    if (decision.status !== 'open') {
      console.error(`Decision is already resolved: ${decisionId}`);
      return undefined;
    }
    
    // Calculate weighted votes for each option
    const weightedVotes = decision.options.map(option => {
      const totalConfidence = option.votes.reduce((sum, vote) => sum + vote.confidence, 0);
      return {
        optionId: option.id,
        description: option.description,
        totalConfidence,
        voteCount: option.votes.length
      };
    });
    
    // Find option with highest weighted votes
    const winningOption = weightedVotes.reduce(
      (winner, current) => {
        if (current.totalConfidence > winner.totalConfidence) {
          return current;
        }
        if (current.totalConfidence === winner.totalConfidence && 
            current.voteCount > winner.voteCount) {
          return current;
        }
        return winner;
      },
      { optionId: '', description: '', totalConfidence: -1, voteCount: 0 }
    );
    
    // Update decision
    const updatedDecision: Decision = {
      ...decision,
      status: 'decided',
      decision: winningOption.description,
      decidedBy,
      decidedAt: new Date().toISOString()
    };
    
    if (reasoning) {
      updatedDecision.reasoning = reasoning;
    }
    
    // Store updated decision
    this.decisions.set(decisionId, updatedDecision);
    
    return updatedDecision;
  }
  
  /**
   * Mark a decision as implemented
   */
  markAsImplemented(
    decisionId: string,
    artifactIds: string[]
  ): Decision | undefined {
    const decision = this.getDecisionById(decisionId);
    
    if (!decision) {
      console.error(`Decision not found: ${decisionId}`);
      return undefined;
    }
    
    if (decision.status !== 'decided') {
      console.error(`Cannot mark as implemented: decision is not in 'decided' state: ${decisionId}`);
      return undefined;
    }
    
    // Update decision
    const updatedDecision: Decision = {
      ...decision,
      status: 'implemented',
      implementedIn: artifactIds
    };
    
    // Store updated decision
    this.decisions.set(decisionId, updatedDecision);
    
    return updatedDecision;
  }
  
  /**
   * Get decisions related to a specific topic
   */
  getDecisionsByTopic(topic: string): Decision[] {
    return this.getAllDecisions().filter(decision => 
      decision.topic.toLowerCase().includes(topic.toLowerCase())
    );
  }
  
  /**
   * Get decisions that a specific agent has participated in
   */
  getDecisionsByAgent(agent: string): Decision[] {
    return this.getAllDecisions().filter(decision => 
      decision.options.some(option => 
        option.proposedBy === agent || 
        option.votes.some(vote => vote.agent === agent)
      ) ||
      (decision.decidedBy && decision.decidedBy.includes(agent))
    );
  }
  
  /**
   * Export decisions for storage or transmission
   */
  exportDecisions(): Record<string, Decision> {
    const decisionRecord: Record<string, Decision> = {};
    
    this.decisions.forEach((decision, id) => {
      decisionRecord[id] = decision;
    });
    
    return decisionRecord;
  }
  
  /**
   * Import decisions from external source
   */
  importDecisions(decisionRecord: Record<string, Decision>): void {
    Object.entries(decisionRecord).forEach(([id, decision]) => {
      this.decisions.set(id, decision);
    });
  }
}
