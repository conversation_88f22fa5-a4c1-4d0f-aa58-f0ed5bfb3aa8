// src/app/(payload)/api/agents/dynamic-collaboration/goal-progress-monitor.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  EnhancedA2AMessage, 
  Reasoning,
  Artifact,
  ArtifactType,
  EnhancedArtifact,
  Part
} from '../a2atypes';
import { Goal } from './goal-workflow-engine';
import { ArtifactManager } from './artifact-manager';
import { MessageBus } from './enhanced-message-bus';

/**
 * Goal Progress Monitor
 * 
 * This class enhances the goal workflow engine by actively monitoring
 * agent responses and automatically detecting goal completion based on
 * message content and artifacts.
 */
export class GoalProgressMonitor {
  private artifactManager: ArtifactManager;
  private messageBus: MessageBus;
  
  constructor(
    artifactManager: ArtifactManager,
    messageBus: MessageBus
  ) {
    this.artifactManager = artifactManager;
    this.messageBus = messageBus;
  }
  
  // Flag to prevent recursive processing
  private isProcessingCompletion = false;

  /**
   * Process a message to detect goal progress and completion
   */
  async processMessage(message: EnhancedA2AMessage, goals: Goal[]): Promise<{
    goalUpdates: Record<string, { progress: number, completed: boolean }>,
    artifactsCreated: Artifact[]
  }> {
    const goalUpdates: Record<string, { progress: number, completed: boolean }> = {};
    const artifactsCreated: Artifact[] = [];
    
    // Skip system messages
    if (message.from === 'system') {
      return { goalUpdates, artifactsCreated };
    }
    
    // Skip processing if we're already processing a completion
    // This prevents infinite recursive loops
    if (this.isProcessingCompletion) {
      console.log('Skipping message processing during completion handling to prevent recursion');
      return { goalUpdates, artifactsCreated };
    }
    
    // Extract text content from message parts
    const textContent = this.extractTextContent(message);
    
    // Check for explicit completion indicators
    const hasCompletionIndicator = this.hasCompletionIndicator(textContent);
    
    // Check for artifacts in message
    const detectedArtifacts = await this.detectArtifacts(message, textContent);
    artifactsCreated.push(...detectedArtifacts);
    
    // Find relevant goals for this agent
    const relevantGoals = goals.filter(goal => 
      goal.status === 'in-progress' && goal.assignedTo.includes(message.from)
    );
    
    // If no in-progress goals found, check for pending goals that might be starting
    if (relevantGoals.length === 0) {
      const pendingGoals = goals.filter(goal => 
        goal.status === 'pending' && goal.assignedTo.includes(message.from)
      );
      
      if (pendingGoals.length > 0 && this.isStartingWork(textContent)) {
        // Agent is starting work on a pending goal
        pendingGoals.forEach(goal => {
          goalUpdates[goal.id] = { progress: 10, completed: false };
        });
      }
      
      return { goalUpdates, artifactsCreated };
    }
    
    // Process each relevant goal
    for (const goal of relevantGoals) {
      // Calculate progress based on message content and artifacts
      const progress = this.calculateGoalProgress(goal, message, textContent, detectedArtifacts);
      
      // Determine if goal is completed
      const completed = hasCompletionIndicator || 
        this.isGoalCompleted(goal, progress, detectedArtifacts);
      
      // Update goal status
      goalUpdates[goal.id] = {
        progress: completed ? 100 : progress,
        completed
      };
      
      // If goal is completed, create a completion message
      if (completed) {
        try {
          // Set the flag to prevent recursive processing
          this.isProcessingCompletion = true;
          await this.sendGoalCompletionMessage(goal);
        } finally {
          // Reset the flag regardless of outcome
          this.isProcessingCompletion = false;
        }
      }
    }
    
    return { goalUpdates, artifactsCreated };
  }
  
  /**
   * Extract text content from message parts
   */
  private extractTextContent(message: EnhancedA2AMessage): string {
    let content = '';
    
    if (message.parts) {
      for (const part of message.parts) {
        if (part.type === 'text') {
          content += part.text + '\n';
        }
      }
    }
    
    return content;
  }
  
  /**
   * Check if message has explicit completion indicators
   */
  private hasCompletionIndicator(content: string): boolean {
    const completionPhrases = [
      'GOAL_COMPLETE',
      'TASK_COMPLETE',
      'COMPLETED SUCCESSFULLY',
      'FINISHED THE TASK',
      'GOAL ACCOMPLISHED',
      'TASK ACCOMPLISHED',
      'COMPLETED THE ANALYSIS',
      'COMPLETED THE RESEARCH',
      'COMPLETED THE STRATEGY',
      'COMPLETED THE DRAFT',
      'COMPLETED THE OPTIMIZATION',
      'FINALIZED THE CONTENT'
    ];
    
    return completionPhrases.some(phrase => 
      content.toUpperCase().includes(phrase)
    );
  }
  
  /**
   * Check if agent is indicating they're starting work
   */
  private isStartingWork(content: string): boolean {
    const startingPhrases = [
      'STARTING WORK',
      'BEGINNING ANALYSIS',
      'INITIATING RESEARCH',
      'STARTING THE TASK',
      'BEGINNING THE PROCESS',
      'WORKING ON THIS GOAL',
      'PROCEEDING WITH THE TASK'
    ];
    
    return startingPhrases.some(phrase => 
      content.toUpperCase().includes(phrase)
    );
  }
  
  /**
   * Detect artifacts in message content
   */
  private async detectArtifacts(message: EnhancedA2AMessage, content: string): Promise<Artifact[]> {
    const artifacts: Artifact[] = [];
    
    // Check for explicit artifact references
    if (message.artifactReferences && message.artifactReferences.length > 0) {
      for (const artifactId of message.artifactReferences) {
        const artifact = this.artifactManager.getArtifactById(artifactId);
        if (artifact) {
          artifacts.push(artifact);
        }
      }
    }
    
    // If no explicit references, try to detect artifacts in content
    if (artifacts.length === 0) {
      // Detect market research
      if (message.from === 'market-research' && 
          (content.includes('audience analysis') || content.includes('market trends'))) {
        const artifact = await this.createArtifactFromContent(
          'audience-analysis',
          'Audience Analysis',
          content,
          message
        );
        artifacts.push(artifact);
      }
      
      // Detect SEO keywords
      if (message.from === 'seo-keyword' && 
          (content.includes('keyword research') || content.includes('search intent'))) {
        const artifact = await this.createArtifactFromContent(
          'keyword-set',
          'SEO Keywords',
          content,
          message
        );
        artifacts.push(artifact);
      }
      
      // Detect content structure
      if (message.from === 'content-strategy' && 
          (content.includes('content structure') || content.includes('content outline'))) {
        const artifact = await this.createArtifactFromContent(
          'content-structure',
          'Content Structure',
          content,
          message
        );
        artifacts.push(artifact);
      }
      
      // Detect content draft
      if (message.from === 'content-generation' && 
          (content.includes('content draft') || content.includes('initial draft'))) {
        const artifact = await this.createArtifactFromContent(
          'draft-content',
          'Content Draft',
          content,
          message
        );
        artifacts.push(artifact);
      }
      
      // Detect SEO optimization
      if (message.from === 'seo-optimization' && 
          (content.includes('SEO analysis') || content.includes('optimization suggestions'))) {
        const artifact = await this.createArtifactFromContent(
          'seo-analysis',
          'SEO Analysis',
          content,
          message
        );
        artifacts.push(artifact);
      }
      
      // Detect final content
      if (message.from === 'content-generation' && 
          (content.includes('final content') || content.includes('finalized content') ||
           content.includes('GOAL_COMPLETE') || content.includes('TASK_COMPLETE') ||
           content.includes('FINAL ARTICLE') || content.includes('COMPLETED ARTICLE'))) {
        console.log('Final content detected in message from content-generation agent');
        const artifact = await this.createArtifactFromContent(
          'final-content',
          'Final Content',
          content,
          message
        );
        artifacts.push(artifact);
        
        // Explicitly log the creation of the final content artifact
        console.log(`Created final content artifact with ID: ${artifact.id}`);
      }
    }
    
    return artifacts;
  }
  
  /**
   * Create an artifact from message content
   */
  private async createArtifactFromContent(
    type: ArtifactType,
    name: string,
    content: string,
    message: EnhancedA2AMessage
  ): Promise<EnhancedArtifact> {
    // Extract structured data if possible
    let description = `${name} created from ${message.from}'s message`;
    
    // Create parts from content
    const parts: Part[] = [
      {
        type: 'text',
        text: content
      }
    ];
    
    // Try to extract JSON data
    try {
      // Look for JSON blocks in the content
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        const parsedData = JSON.parse(jsonMatch[1]);
        parts.push({
          type: 'data',
          data: parsedData
        });
      }
    } catch (error) {
      console.error('Error parsing JSON from content:', error);
    }
    
    // Create reasoning if available in the message
    const reasoning = message.reasoning;
    
    // Create the artifact
    const artifact = this.artifactManager.createArtifact(
      name,
      description,
      type,
      parts,
      message.from,
      reasoning,
      []
    );
    
    return artifact;
  }
  
  /**
   * Calculate goal progress based on message content and artifacts
   */
  private calculateGoalProgress(
    goal: Goal,
    message: EnhancedA2AMessage,
    content: string,
    artifacts: Artifact[]
  ): number {
    // If there are artifacts, calculate progress based on them
    if (artifacts.length > 0) {
      // Different progress calculation based on goal
      switch (goal.description) {
        case 'Conduct market research and audience analysis':
          return artifacts.some(a => a.type === 'audience-analysis') ? 100 : 50;
          
        case 'Identify SEO keywords and search intent':
          return artifacts.some(a => a.type === 'keyword-set') ? 100 : 50;
          
        case 'Develop content strategy and structure':
          return artifacts.some(a => a.type === 'content-structure') ? 100 : 50;
          
        case 'Generate initial content draft':
          return artifacts.some(a => a.type === 'draft-content') ? 100 : 50;
          
        case 'Perform SEO optimization':
          return artifacts.some(a => a.type === 'seo-analysis') ? 100 : 50;
          
        case 'Refine and finalize content':
          return artifacts.some(a => a.type === 'final-content') ? 100 : 50;
      }
    }
    
    // If no artifacts, estimate progress based on content
    const progressIndicators = {
      'starting': 10,
      'in progress': 30,
      'halfway': 50,
      'almost done': 80,
      'finishing up': 90
    };
    
    for (const [indicator, value] of Object.entries(progressIndicators)) {
      if (content.toLowerCase().includes(indicator)) {
        return value;
      }
    }
    
    // If no indicators found, return a default progress increase
    return Math.min(goal.progress + 10, 90); // Cap at 90% without artifacts
  }
  
  /**
   * Determine if a goal is completed
   */
  private isGoalCompleted(
    goal: Goal,
    progress: number,
    artifacts: Artifact[]
  ): boolean {
    // If progress is 100%, the goal is completed
    if (progress >= 100) {
      return true;
    }
    
    // Check for specific artifact types that indicate completion
    switch (goal.description) {
      case 'Conduct market research and audience analysis':
        return artifacts.some(a => a.type === 'audience-analysis');
        
      case 'Identify SEO keywords and search intent':
        return artifacts.some(a => a.type === 'keyword-set');
        
      case 'Develop content strategy and structure':
        return artifacts.some(a => a.type === 'content-structure');
        
      case 'Generate initial content draft':
        return artifacts.some(a => a.type === 'draft-content');
        
      case 'Perform SEO optimization':
        return artifacts.some(a => a.type === 'seo-analysis');
        
      case 'Refine and finalize content':
        // Check for final content artifact or explicit completion indicators in the message
        const hasFinalContent = artifacts.some(a => a.type === 'final-content');
        
        // If we have a final content artifact, the goal is completed
        if (hasFinalContent) {
          console.log('Final content artifact detected, marking goal as completed');
          return true;
        }
        
        // If progress is high enough and we have a draft, we can consider it complete
        if (progress >= 90) {
          const draftArtifacts = this.artifactManager.getArtifactsByType('draft-content');
          if (draftArtifacts.length > 0) {
            console.log('High progress with draft content detected, marking goal as completed');
            return true;
          }
        }
        
        return false;
    }
    
    return false;
  }
  
  /**
   * Send a goal completion message
   */
  private async sendGoalCompletionMessage(goal: Goal): Promise<void> {
    const completionMessage: EnhancedA2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'system',
      to: 'all',
      role: 'system',
      parts: [
        {
          type: 'text',
          text: `Goal completed: ${goal.description}`
        }
      ],
      conversationId: uuidv4(),
      reasoning: {
        thoughts: [
          'Goal completion detected based on agent output',
          'Artifacts created satisfy the goal criteria',
          'Workflow can now proceed to dependent goals'
        ],
        considerations: [
          'Dependent goals should be unblocked',
          'Progress should be updated for the overall workflow'
        ],
        decision: `Mark goal "${goal.description}" as completed and proceed with workflow`,
        confidence: 0.95
      },
      intentions: ['inform'],
      metadata: {
        systemMessage: true,
        goalCompletion: true,
        goalId: goal.id,
        timestamp: new Date().toISOString()
      }
    };
    
    await this.messageBus.sendMessage(completionMessage);
  }
}
