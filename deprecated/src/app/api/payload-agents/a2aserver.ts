// src/app/(payload)/api/agents/a2aserver.ts

/**
 * Agent2Agent (A2A) Protocol Server Implementation
 * 
 * This file contains a complete server implementation for the A2A protocol,
 * enabling agents to communicate via standardized JSON-RPC endpoints.
 */

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  AgentCard, 
  A2ATask, 
  A2AMessage, 
  TaskState,
  JsonRpcRequest,
  JsonRpcResponse,
  TaskSendParams,
  TaskGetParams,
  TaskCancelParams,
  PushNotificationConfig,
  TaskStatusUpdateEvent,
  TaskArtifactUpdateEvent,
  Artifact
} from './a2atypes';

// A2A Error Codes as defined in the schema
export const A2AErrorCodes = {
  PARSE_ERROR: -32700,
  INVALID_REQUEST: -32600,
  METHOD_NOT_FOUND: -32601,
  INVALID_PARAMS: -32602,
  INTERNAL_ERROR: -32603,
  TASK_NOT_FOUND: -32001,
  TASK_NOT_CANCELABLE: -32002,
  PUSH_NOTIFICATION_NOT_SUPPORTED: -32003,
  UNSUPPORTED_OPERATION: -32004
};

// In-memory task store (could be replaced with a database in production)
const taskStore: Map<string, A2ATask> = new Map();

/**
 * A2A Server base class
 * Provides JSON-RPC endpoint handling for A2A protocol methods
 */
export class A2AServer {
  private agentCard: AgentCard;
  private taskHandler: (task: A2ATask) => Promise<A2ATask>;
  private streamingEnabled: boolean;
  private pushNotificationsEnabled: boolean;
  
  constructor(
    agentCard: AgentCard, 
    taskHandler: (task: A2ATask) => Promise<A2ATask>,
    options: {
      streamingEnabled?: boolean;
      pushNotificationsEnabled?: boolean;
    } = {}
  ) {
    this.agentCard = agentCard;
    this.taskHandler = taskHandler;
    this.streamingEnabled = options.streamingEnabled ?? agentCard.capabilities?.streaming ?? false;
    this.pushNotificationsEnabled = options.pushNotificationsEnabled ?? agentCard.capabilities?.pushNotifications ?? false;
  }
  
  /**
   * Handle agent card discovery request
   */
  async handleAgentCardRequest(): Promise<NextResponse> {
    return NextResponse.json(this.agentCard);
  }
  
  /**
   * Handle JSON-RPC request
   */
  async handleJsonRpcRequest(req: NextRequest): Promise<NextResponse> {
    try {
      const jsonRpcRequest: JsonRpcRequest = await req.json();
      
      // Validate JSON-RPC request
      if (jsonRpcRequest.jsonrpc !== '2.0' || !jsonRpcRequest.method || !jsonRpcRequest.id) {
        return this.createJsonRpcErrorResponse(
          jsonRpcRequest.id || null,
          A2AErrorCodes.INVALID_REQUEST,
          'Request payload validation error'
        );
      }
      
      // Route to appropriate method handler
      switch (jsonRpcRequest.method) {
        case 'tasks/send':
          return await this.handleTaskSend(jsonRpcRequest);
        
        case 'tasks/sendSubscribe':
          if (!this.streamingEnabled) {
            return this.createJsonRpcErrorResponse(
              jsonRpcRequest.id,
              A2AErrorCodes.UNSUPPORTED_OPERATION,
              'This operation is not supported'
            );
          }
          return await this.handleTaskSendSubscribe(jsonRpcRequest);
        
        case 'tasks/get':
          return await this.handleTaskGet(jsonRpcRequest);
        
        case 'tasks/cancel':
          return await this.handleTaskCancel(jsonRpcRequest);
        
        case 'tasks/pushNotification/set':
          if (!this.pushNotificationsEnabled) {
            return this.createJsonRpcErrorResponse(
              jsonRpcRequest.id,
              A2AErrorCodes.PUSH_NOTIFICATION_NOT_SUPPORTED,
              'Push Notification is not supported'
            );
          }
          return await this.handlePushNotificationSet(jsonRpcRequest);
        
        case 'tasks/pushNotification/get':
          if (!this.pushNotificationsEnabled) {
            return this.createJsonRpcErrorResponse(
              jsonRpcRequest.id,
              A2AErrorCodes.PUSH_NOTIFICATION_NOT_SUPPORTED,
              'Push Notification is not supported'
            );
          }
          return await this.handlePushNotificationGet(jsonRpcRequest);
          
        case 'tasks/resubscribe':
          if (!this.streamingEnabled) {
            return this.createJsonRpcErrorResponse(
              jsonRpcRequest.id,
              A2AErrorCodes.UNSUPPORTED_OPERATION,
              'This operation is not supported'
            );
          }
          return await this.handleTaskResubscribe(jsonRpcRequest);
        
        default:
          return this.createJsonRpcErrorResponse(
            jsonRpcRequest.id,
            A2AErrorCodes.METHOD_NOT_FOUND,
            'Method not found'
          );
      }
    } catch (error) {
      console.error('Error processing JSON-RPC request:', error);
      return this.createJsonRpcErrorResponse(
        null,
        A2AErrorCodes.PARSE_ERROR,
        'Invalid JSON payload'
      );
    }
  }
  
  /**
   * Handle tasks/send method
   */
  private async handleTaskSend(request: JsonRpcRequest): Promise<NextResponse> {
    try {
      const params = request.params as TaskSendParams;
      
      // Validate required parameters
      if (!params.id || !params.message) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.INVALID_PARAMS,
          'Invalid parameters'
        );
      }
      
      // Create or update task
      let task: A2ATask = {
        id: params.id,
        sessionId: params.sessionId || uuidv4(),
        status: {
          state: 'submitted',
          timestamp: new Date().toISOString()
        },
        history: params.message ? [params.message] : [],
        artifacts: [],
        metadata: params.metadata || {}
      };
      
      // Store task
      taskStore.set(task.id, task);
      
      // Process task asynchronously
      this.processTask(task);
      
      // Return immediate response
      return this.createJsonRpcSuccessResponse(request.id, { task });
    } catch (error) {
      console.error('Error handling tasks/send:', error);
      return this.createJsonRpcErrorResponse(
        request.id,
        A2AErrorCodes.INTERNAL_ERROR,
        'Internal error'
      );
    }
  }
  
  /**
   * Handle tasks/sendSubscribe method
   */
  private async handleTaskSendSubscribe(request: JsonRpcRequest): Promise<NextResponse> {
    try {
      const params = request.params as TaskSendParams;
      
      // Validate required parameters
      if (!params.id || !params.message) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.INVALID_PARAMS,
          'Invalid parameters'
        );
      }
      
      // Create task (same as in handleTaskSend)
      let task: A2ATask = {
        id: params.id,
        sessionId: params.sessionId || uuidv4(),
        status: {
          state: 'submitted',
          timestamp: new Date().toISOString()
        },
        history: params.message ? [params.message] : [],
        artifacts: [],
        metadata: params.metadata || {}
      };
      
      // Store task
      taskStore.set(task.id, task);
      
      // Set up streaming response
      const stream = new TransformStream();
      const writer = stream.writable.getWriter();
      
      // Process task with streaming updates
      this.processTaskWithStreaming(task, writer);
      
      // Return streaming response
      return new NextResponse(stream.readable, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive'
        }
      });
    } catch (error) {
      console.error('Error handling tasks/sendSubscribe:', error);
      return this.createJsonRpcErrorResponse(
        request.id,
        A2AErrorCodes.INTERNAL_ERROR,
        'Internal error'
      );
    }
  }
  
  /**
   * Handle tasks/get method
   */
  private async handleTaskGet(request: JsonRpcRequest): Promise<NextResponse> {
    try {
      const params = request.params as TaskGetParams;
      
      // Validate required parameters
      if (!params.id) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.INVALID_PARAMS,
          'Invalid parameters'
        );
      }
      
      // Get task
      const task = taskStore.get(params.id);
      
      if (!task) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.TASK_NOT_FOUND,
          'Task not found'
        );
      }
      
      // Apply history length limit if specified
      if (params.historyLength !== undefined && params.historyLength >= 0 && task.history) {
        const limitedTask = { ...task };
        limitedTask.history = task.history.slice(-params.historyLength);
        return this.createJsonRpcSuccessResponse(request.id, { task: limitedTask });
      }
      
      // Return task
      return this.createJsonRpcSuccessResponse(request.id, { task });
    } catch (error) {
      console.error('Error handling tasks/get:', error);
      return this.createJsonRpcErrorResponse(
        request.id,
        A2AErrorCodes.INTERNAL_ERROR,
        'Internal error'
      );
    }
  }
  
  /**
   * Handle tasks/cancel method
   */
  private async handleTaskCancel(request: JsonRpcRequest): Promise<NextResponse> {
    try {
      const params = request.params as TaskCancelParams;
      
      // Validate required parameters
      if (!params.id) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.INVALID_PARAMS,
          'Invalid parameters'
        );
      }
      
      // Get task
      const task = taskStore.get(params.id);
      
      if (!task) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.TASK_NOT_FOUND,
          'Task not found'
        );
      }
      
      // Check if task can be canceled
      const cancelableStates: TaskState[] = ['submitted', 'working', 'input-required'];
      if (!cancelableStates.includes(task.status.state)) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.TASK_NOT_CANCELABLE,
          'Task cannot be canceled'
        );
      }
      
      // Update task status
      task.status = {
        state: 'canceled',
        timestamp: new Date().toISOString()
      };
      
      // Store updated task
      taskStore.set(task.id, task);
      
      // Return updated task
      return this.createJsonRpcSuccessResponse(request.id, { task });
    } catch (error) {
      console.error('Error handling tasks/cancel:', error);
      return this.createJsonRpcErrorResponse(
        request.id,
        A2AErrorCodes.INTERNAL_ERROR,
        'Internal error'
      );
    }
  }
  
  /**
   * Handle tasks/pushNotification/set method
   */
  private async handlePushNotificationSet(request: JsonRpcRequest): Promise<NextResponse> {
    try {
      const params = request.params as { id: string; pushNotificationConfig: PushNotificationConfig };
      
      // Validate required parameters
      if (!params.id || !params.pushNotificationConfig || !params.pushNotificationConfig.url) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.INVALID_PARAMS,
          'Invalid parameters'
        );
      }
      
      // Get task
      const task = taskStore.get(params.id);
      
      if (!task) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.TASK_NOT_FOUND,
          'Task not found'
        );
      }
      
      // Store push notification config in task metadata
      task.metadata = {
        ...task.metadata,
        pushNotificationConfig: params.pushNotificationConfig
      };
      
      // Store updated task
      taskStore.set(task.id, task);
      
      // Return success
      return this.createJsonRpcSuccessResponse(request.id, { 
        pushNotificationConfig: params.pushNotificationConfig 
      });
    } catch (error) {
      console.error('Error handling tasks/pushNotification/set:', error);
      return this.createJsonRpcErrorResponse(
        request.id,
        A2AErrorCodes.INTERNAL_ERROR,
        'Internal error'
      );
    }
  }
  
  /**
   * Handle tasks/pushNotification/get method
   */
  private async handlePushNotificationGet(request: JsonRpcRequest): Promise<NextResponse> {
    try {
      const params = request.params as { id: string };
      
      // Validate required parameters
      if (!params.id) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.INVALID_PARAMS,
          'Invalid parameters'
        );
      }
      
      // Get task
      const task = taskStore.get(params.id);
      
      if (!task) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.TASK_NOT_FOUND,
          'Task not found'
        );
      }
      
      // Get push notification config from task metadata
      const pushNotificationConfig = task.metadata?.pushNotificationConfig as PushNotificationConfig | undefined;
      
      // Return push notification config
      return this.createJsonRpcSuccessResponse(request.id, { 
        pushNotificationConfig: pushNotificationConfig || null 
      });
    } catch (error) {
      console.error('Error handling tasks/pushNotification/get:', error);
      return this.createJsonRpcErrorResponse(
        request.id,
        A2AErrorCodes.INTERNAL_ERROR,
        'Internal error'
      );
    }
  }
  
  /**
   * Handle tasks/resubscribe method
   */
  private async handleTaskResubscribe(request: JsonRpcRequest): Promise<NextResponse> {
    try {
      const params = request.params as TaskGetParams;
      
      // Validate required parameters
      if (!params.id) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.INVALID_PARAMS,
          'Invalid parameters'
        );
      }
      
      // Get task
      const task = taskStore.get(params.id);
      
      if (!task) {
        return this.createJsonRpcErrorResponse(
          request.id,
          A2AErrorCodes.TASK_NOT_FOUND,
          'Task not found'
        );
      }
      
      // Set up streaming response
      const stream = new TransformStream();
      const writer = stream.writable.getWriter();
      
      // Send current state immediately
      await this.sendStreamingUpdate(writer, {
        id: task.id,
        status: task.status,
        final: task.status.state === 'completed' || 
               task.status.state === 'failed' || 
               task.status.state === 'canceled'
      } as TaskStatusUpdateEvent);
      
      // If task is still in progress, continue streaming updates
      if (task.status.state === 'working' || task.status.state === 'input-required') {
        // Continue streaming updates
        this.continueStreamingTask(task, writer);
      } else {
        // Send all artifacts
        if (task.artifacts && task.artifacts.length > 0) {
          for (const artifact of task.artifacts) {
            await this.sendStreamingUpdate(writer, {
              id: task.id,
              artifact,
              final: false
            } as TaskArtifactUpdateEvent);
          }
        }
        
        // Close the stream
        await writer.close();
      }
      
      // Return streaming response
      return new NextResponse(stream.readable, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive'
        }
      });
    } catch (error) {
      console.error('Error handling tasks/resubscribe:', error);
      return this.createJsonRpcErrorResponse(
        request.id,
        A2AErrorCodes.INTERNAL_ERROR,
        'Internal error'
      );
    }
  }
  
  /**
   * Process task asynchronously
   */
  private async processTask(task: A2ATask): Promise<void> {
    try {
      // Update task status to working
      task.status = {
        state: 'working',
        message: {
          role: 'agent',
          parts: [{ type: 'text', text: 'Processing your request...' }]
        },
        timestamp: new Date().toISOString()
      };
      taskStore.set(task.id, task);
      
      // Process task with task handler
      const updatedTask = await this.taskHandler(task);
      
      // Store updated task
      taskStore.set(updatedTask.id, updatedTask);
      
      // Send push notification if configured
      if (this.pushNotificationsEnabled && updatedTask.metadata?.pushNotificationConfig) {
        await this.sendPushNotification(updatedTask);
      }
    } catch (error) {
      console.error(`Error processing task ${task.id}:`, error);
      
      // Update task status to failed
      task.status = {
        state: 'failed',
        message: {
          role: 'agent',
          parts: [{ 
            type: 'text', 
            text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
          }]
        },
        timestamp: new Date().toISOString()
      };
      taskStore.set(task.id, task);
      
      // Send push notification if configured
      if (this.pushNotificationsEnabled && task.metadata?.pushNotificationConfig) {
        await this.sendPushNotification(task);
      }
    }
  }
  
  /**
   * Process task with streaming updates
   */
  private async processTaskWithStreaming(task: A2ATask, writer: WritableStreamDefaultWriter): Promise<void> {
    try {
      // Update task status to working
      task.status = {
        state: 'working',
        message: {
          role: 'agent',
          parts: [{ type: 'text', text: 'Processing your request...' }]
        },
        timestamp: new Date().toISOString()
      };
      taskStore.set(task.id, task);
      
      // Send initial status update
      await this.sendStreamingUpdate(writer, {
        id: task.id,
        status: task.status,
        final: false
      } as TaskStatusUpdateEvent);
      
      // Create a proxy for adding artifacts that will stream them
      const streamingArtifactProxy = {
        addArtifact: async (artifact: Artifact) => {
          // Add artifact to task
          if (!task.artifacts) {
            task.artifacts = [];
          }
          task.artifacts.push(artifact);
          taskStore.set(task.id, task);
          
          // Stream artifact update
          await this.sendStreamingUpdate(writer, {
            id: task.id,
            artifact,
            final: false
          } as TaskArtifactUpdateEvent);
        }
      };
      
      // Process task with task handler (with streaming artifact proxy)
      task.metadata = {
        ...task.metadata,
        streamingArtifactProxy
      };
      
      const updatedTask = await this.taskHandler(task);
      
      // Store updated task
      taskStore.set(updatedTask.id, updatedTask);
      
      // Send final status update
      await this.sendStreamingUpdate(writer, {
        id: updatedTask.id,
        status: updatedTask.status,
        final: true
      } as TaskStatusUpdateEvent);
      
      // Close the stream
      await writer.close();
      
      // Send push notification if configured
      if (this.pushNotificationsEnabled && updatedTask.metadata?.pushNotificationConfig) {
        await this.sendPushNotification(updatedTask);
      }
    } catch (error) {
      console.error(`Error processing task ${task.id} with streaming:`, error);
      
      // Update task status to failed
      task.status = {
        state: 'failed',
        message: {
          role: 'agent',
          parts: [{ 
            type: 'text', 
            text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
          }]
        },
        timestamp: new Date().toISOString()
      };
      taskStore.set(task.id, task);
      
      // Send error status update
      await this.sendStreamingUpdate(writer, {
        id: task.id,
        status: task.status,
        final: true
      } as TaskStatusUpdateEvent);
      
      // Close the stream
      await writer.close();
      
      // Send push notification if configured
      if (this.pushNotificationsEnabled && task.metadata?.pushNotificationConfig) {
        await this.sendPushNotification(task);
      }
    }
  }
  
  /**
   * Continue streaming updates for an existing task
   */
  private async continueStreamingTask(task: A2ATask, writer: WritableStreamDefaultWriter): Promise<void> {
    try {
      // Create a proxy for adding artifacts that will stream them
      const streamingArtifactProxy = {
        addArtifact: async (artifact: Artifact) => {
          // Add artifact to task
          if (!task.artifacts) {
            task.artifacts = [];
          }
          task.artifacts.push(artifact);
          taskStore.set(task.id, task);
          
          // Stream artifact update
          await this.sendStreamingUpdate(writer, {
            id: task.id,
            artifact,
            final: false
          } as TaskArtifactUpdateEvent);
        }
      };
      
      // Update task metadata with streaming proxy
      task.metadata = {
        ...task.metadata,
        streamingArtifactProxy
      };
      
      // Store updated task
      taskStore.set(task.id, task);
      
      // We don't need to do anything else here, as the original task processing
      // will continue and use the streaming proxy
    } catch (error) {
      console.error(`Error continuing streaming for task ${task.id}:`, error);
      
      // Send error status update
      await this.sendStreamingUpdate(writer, {
        id: task.id,
        status: {
          state: 'failed',
          message: {
            role: 'agent',
            parts: [{ 
              type: 'text', 
              text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
            }]
          },
          timestamp: new Date().toISOString()
        },
        final: true
      } as TaskStatusUpdateEvent);
      
      // Close the stream
      await writer.close();
    }
  }
  
  /**
   * Send push notification
   */
  private async sendPushNotification(task: A2ATask): Promise<void> {
    try {
      const config = task.metadata?.pushNotificationConfig as PushNotificationConfig;
      if (!config || !config.url) {
        return;
      }
      
      // Send push notification
      const response = await fetch(config.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(config.token ? { 'Authorization': `Bearer ${config.token}` } : {})
        },
        body: JSON.stringify({
          id: task.id,
          status: task.status,
          artifacts: task.artifacts
        })
      });
      
      if (!response.ok) {
        console.error(`Error sending push notification for task ${task.id}: ${response.statusText}`);
      }
    } catch (error) {
      console.error(`Error sending push notification for task ${task.id}:`, error);
    }
  }
  
  /**
   * Send streaming update
   */
  private async sendStreamingUpdate(writer: WritableStreamDefaultWriter, data: any): Promise<void> {
    const jsonRpcResponse: JsonRpcResponse = {
      jsonrpc: '2.0',
      id: data.id,
      result: data
    };
    
    const event = `data: ${JSON.stringify(jsonRpcResponse)}\n\n`;
    await writer.write(new TextEncoder().encode(event));
  }
  
  /**
   * Create JSON-RPC success response
   */
  private createJsonRpcSuccessResponse(id: string | number, result: any): NextResponse {
    const response: JsonRpcResponse = {
      jsonrpc: '2.0',
      id,
      result
    };
    
    return NextResponse.json(response);
  }
  
  /**
   * Create JSON-RPC error response
   */
  private createJsonRpcErrorResponse(id: string | number | null, code: number, message: string, data?: any): NextResponse {
    const response: JsonRpcResponse = {
      jsonrpc: '2.0',
      id,
      error: {
        code,
        message,
        data
      }
    };
    
    return NextResponse.json(response);
  }
}