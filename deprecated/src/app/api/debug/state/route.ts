import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '@/core/workflow/singleton';

export async function GET(request: NextRequest) {
  try {
    const store = getStateStore();
    
    // Get raw state from Redis
    const rawState = await store.storage.get('system_state');
    
    // Get processed state through the get() method
    const processedState = await store.get();
    
    return NextResponse.json({
      success: true,
      data: {
        rawState,
        processedState,
        hasRawState: !!rawState,
        hasProcessedState: !!processedState,
        rawStateType: typeof rawState,
        processedStateType: typeof processedState,
        rawStateKeys: rawState ? Object.keys(rawState) : [],
        processedStateKeys: processedState ? Object.keys(processedState) : [],
        workflowsInRaw: rawState?.workflows ? Object.keys(rawState.workflows) : [],
        workflowsInProcessed: processedState?.workflows ? Object.keys(processedState.workflows) : [],
        contentInRaw: rawState?.content ? Object.keys(rawState.content) : [],
        contentInProcessed: processedState?.content ? Object.keys(processedState.content) : []
      }
    });
  } catch (error) {
    console.error('Error getting state debug info:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
