import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import {
  dynamicStateStore,
  DynamicCollaborationState,
  DynamicWorkflowPhase,
  GoalStatus,
  GoalType,
  ArtifactStatus,
  DynamicArtifact,
  DynamicMessageType
} from '../../../../(payload)/api/agents/dynamic-collaboration-v2/state/index';
import logger from '../../../../(payload)/api/agents/collaborative-iteration/utils/logger';

/**
 * Complete workflow endpoint
 * This endpoint directly completes all goals and transitions through all phases
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { sessionId } = body;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Get the current state
    const currentState = await dynamicStateStore.getState(sessionId);
    if (!currentState) {
      return NextResponse.json({ error: `Session ${sessionId} not found` }, { status: 404 });
    }

    logger.info(`Starting complete workflow for session ${sessionId}`, {
      sessionId,
      currentPhase: currentState.currentPhase
    });

    // Step 1: Create artifacts for all goal types
    const artifacts: Record<string, DynamicArtifact> = {};
    const generatedArtifacts: string[] = [];
    const goalTypes = [
      GoalType.MARKET_RESEARCH,
      GoalType.KEYWORD_ANALYSIS,
      GoalType.CONTENT_STRATEGY,
      GoalType.CONTENT_CREATION,
      GoalType.SEO_OPTIMIZATION,
      GoalType.QUALITY_ASSESSMENT
    ];

    for (const goalType of goalTypes) {
      const artifactId = uuidv4();
      const artifact: DynamicArtifact = {
        id: artifactId,
        type: goalType,
        title: `${goalType} Artifact`,
        content: {
          message: `This is a test artifact for goal ${goalType}`,
          timestamp: new Date().toISOString()
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system',
        status: ArtifactStatus.DRAFT,
        version: 1
      };

      artifacts[artifactId] = artifact;
      generatedArtifacts.push(artifactId);

      logger.info(`Created artifact for ${goalType}`, {
        sessionId,
        artifactId,
        goalType
      });
    }

    // Step 2: Complete all goals
    const updatedGoals = { ...currentState.goals };
    const completedGoals: string[] = [];

    for (const goalType of goalTypes) {
      // Find a goal of this type
      const goalsOfType = Object.entries(currentState.goals).filter(([_, g]) => g.type === goalType);
      
      if (goalsOfType.length > 0) {
        const [goalId, goal] = goalsOfType[0];
        
        // Find the artifact for this goal type
        const artifactForGoal = Object.values(artifacts).find(a => a.type === goalType);
        
        if (artifactForGoal) {
          // Update the goal
          updatedGoals[goalId] = {
            ...goal,
            status: GoalStatus.COMPLETED,
            progress: 100,
            completedAt: new Date().toISOString(),
            artifactId: artifactForGoal.id
          };
          
          // Add to completed goals
          completedGoals.push(goalType);
          
          logger.info(`Completed goal ${goalId} of type ${goalType}`, {
            sessionId,
            goalId,
            goalType,
            artifactId: artifactForGoal.id
          });
        }
      }
    }

    // Step 3: Update the state with all artifacts and completed goals
    await dynamicStateStore.updateState(sessionId, (state: DynamicCollaborationState | null) => {
      if (!state) return state;
      
      return {
        ...state,
        artifacts: {
          ...state.artifacts,
          ...artifacts
        },
        generatedArtifacts: [
          ...(state.generatedArtifacts || []),
          ...generatedArtifacts
        ],
        goals: updatedGoals,
        completedGoals,
        currentPhase: DynamicWorkflowPhase.FINALIZATION
      };
    });

    // Step 4: Add system messages for all the completed goals
    for (const goalType of goalTypes) {
      const goalsOfType = Object.entries(updatedGoals).filter(([_, g]) => g.type === goalType);
      
      if (goalsOfType.length > 0) {
        const [goalId, goal] = goalsOfType[0];
        
        // Find the artifact for this goal type
        const artifactForGoal = Object.values(artifacts).find(a => a.type === goalType);
        
        if (artifactForGoal) {
          // Add a message to the state
          await dynamicStateStore.updateState(sessionId, (state: DynamicCollaborationState | null) => {
            if (!state) return state;
            
            const messageId = uuidv4();
            const conversationId = uuidv4();
            const timestamp = new Date().toISOString();
            
            const message = {
              id: messageId,
              timestamp,
              from: 'system',
              to: 'all',
              type: DynamicMessageType.GOAL_UPDATE,
              content: {
                action: 'GOAL_COMPLETED',
                goalId,
                description: goal.description,
                artifactId: artifactForGoal.id
              },
              conversationId
            };
            
            // Add to dynamic messages
            const dynamicMessages = {
              ...state.dynamicMessages,
              [messageId]: message
            };
            
            // Add to conversations
            const conversations = { ...state.conversations };
            if (!conversations[conversationId]) {
              conversations[conversationId] = [];
            }
            conversations[conversationId].push(messageId);
            
            // Add to messages array
            const messages = [...state.messages, message];
            
            return {
              ...state,
              dynamicMessages,
              conversations,
              messages
            };
          });
        }
      }
    }

    // Step 5: Verify the final state
    const finalState = await dynamicStateStore.getState(sessionId);
    
    if (!finalState) {
      return NextResponse.json({ error: `Session ${sessionId} not found after update` }, { status: 500 });
    }
    
    const artifactsCount = Object.keys(finalState.artifacts || {}).length;
    const generatedArtifactsCount = finalState.generatedArtifacts?.length || 0;
    
    logger.info(`Completed workflow for session ${sessionId}`, {
      sessionId,
      currentPhase: finalState.currentPhase,
      completedGoals: finalState.completedGoals,
      artifactsCount,
      generatedArtifactsCount
    });

    return NextResponse.json({
      success: true,
      sessionId,
      currentPhase: finalState.currentPhase,
      completedGoals: finalState.completedGoals,
      artifactsCount,
      generatedArtifactsCount,
      artifacts: Object.keys(finalState.artifacts || {})
    });
  } catch (error) {
    console.error('Error in complete-workflow endpoint:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
