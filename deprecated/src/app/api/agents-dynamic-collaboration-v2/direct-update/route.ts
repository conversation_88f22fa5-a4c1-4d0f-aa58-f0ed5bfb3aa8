import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import {
  dynamicStateStore,
  DynamicCollaborationState,
  GoalStatus
} from '../../../../(payload)/api/agents/dynamic-collaboration-v2/state/index';

/**
 * Direct state update endpoint for testing purposes
 * This endpoint directly updates the state without any validation or orchestration
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { sessionId } = body;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Get the current state
    const currentState = await dynamicStateStore.getState(sessionId);
    if (!currentState) {
      return NextResponse.json({ error: `Session ${sessionId} not found` }, { status: 404 });
    }

    // Create a test artifact
    const artifactId = uuidv4();
    const testArtifact = {
      id: artifactId,
      type: 'test_artifact',
      title: 'Test Artifact',
      content: {
        message: 'This is a test artifact',
        timestamp: new Date().toISOString()
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'debug_system',
      status: 'draft',
      version: 1
    };

    // Find a goal that's not completed
    const pendingGoals = Object.entries(currentState.goals).filter(([_, goal]) => goal.status !== 'completed');
    if (pendingGoals.length === 0) {
      return NextResponse.json({ error: 'No pending goals found' }, { status: 400 });
    }

    // Get the first pending goal
    const [goalId, goal] = pendingGoals[0];
    const goalType = goal.type;

    // Directly update the state
    await dynamicStateStore.updateState(sessionId, (state: DynamicCollaborationState | null) => {
      if (!state) return state;

      // Add the artifact
      const artifacts = {
        ...state.artifacts,
        [artifactId]: testArtifact
      };

      // Add to generated artifacts
      const generatedArtifacts = [...(state.generatedArtifacts || [])];
      if (!generatedArtifacts.includes(artifactId)) {
        generatedArtifacts.push(artifactId);
      }

      // Update the goal
      const updatedGoals = { ...state.goals };
      updatedGoals[goalId] = {
        ...updatedGoals[goalId],
        status: GoalStatus.COMPLETED,
        progress: 100,
        completedAt: new Date().toISOString(),
        artifactId
      };

      // Add to completed goals
      const completedGoals = [...(state.completedGoals || [])];
      if (!completedGoals.includes(goalType)) {
        completedGoals.push(goalType);
      }

      // Remove from active goals
      const activeGoals = (state.activeGoals || []).filter(id => id !== goalId);

      return {
        ...state,
        artifacts,
        generatedArtifacts,
        goals: updatedGoals,
        activeGoals,
        completedGoals
      };
    });

    // Get the updated state
    const updatedState = await dynamicStateStore.getState(sessionId);

    return NextResponse.json({
      success: true,
      goalId,
      goalType,
      artifactId,
      state: {
        currentPhase: updatedState?.currentPhase,
        completedGoals: updatedState?.completedGoals,
        artifacts: Object.keys(updatedState?.artifacts || {}).length,
        generatedArtifacts: updatedState?.generatedArtifacts?.length || 0
      }
    });
  } catch (error) {
    console.error('Error in direct-update endpoint:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
