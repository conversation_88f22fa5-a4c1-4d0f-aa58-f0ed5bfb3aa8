import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { DynamicWorkflowOrchestrator } from '../../../../../../(payload)/api/agents/dynamic-collaboration-v2/dynamic-workflow-orchestrator';
import { stateStore } from '../../../../../../(payload)/api/agents/collaborative-iteration/utils/stateStore';
import { DynamicMessageType } from '../../../../../../(payload)/api/agents/dynamic-collaboration-v2/types';

/**
 * API route handler for messages in dynamic collaboration sessions
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Get the session state from the state store
    const sessionState = await stateStore.getState(sessionId);

    if (!sessionState) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Extract messages
    const messages = sessionState.dynamicMessages || {};

    return NextResponse.json({
      success: true,
      messages: Object.values(messages)
    });
  } catch (error) {
    console.error('Error retrieving messages from dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const {
      content,
      type = DynamicMessageType.USER_MESSAGE,
      to = 'system'
    } = body;

    // Validate content
    if (!content) {
      return NextResponse.json(
        { error: 'Missing required parameter: content' },
        { status: 400 }
      );
    }

    // Create a new orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

    // Create the message
    const messageId = uuidv4();
    const conversationId = uuidv4();
    const timestamp = new Date().toISOString();

    // Add the message to the state
    await stateStore.updateState(sessionId, (currentState) => {
      if (!currentState) return currentState;

      const newMessage = {
        id: messageId,
        timestamp,
        from: 'user',
        to,
        type,
        content,
        conversationId
      };

      const updatedDynamicMessages = {
        ...currentState.dynamicMessages,
        [messageId]: newMessage
      };

      const updatedConversations = { ...currentState.conversations };
      if (!updatedConversations[conversationId]) {
        updatedConversations[conversationId] = [];
      }
      updatedConversations[conversationId].push(messageId);

      return {
        ...currentState,
        dynamicMessages: updatedDynamicMessages,
        conversations: updatedConversations
      };
    });

    // Process the message
    await orchestrator.processUserMessage(messageId);

    return NextResponse.json({
      success: true,
      messageId,
      message: 'Message sent successfully'
    });
  } catch (error) {
    console.error('Error sending message in dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}
