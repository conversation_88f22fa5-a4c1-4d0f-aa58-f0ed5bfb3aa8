import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import {
  AgentId,
  IterativeMessageType,
  IterativeCollaborationState,
  IterativeMessage
} from '../../(payload)/api/agents/collaborative-iteration/types';
import { stateStore } from '../../(payload)/api/agents/collaborative-iteration/utils/stateStore';
import { messageBus } from '../../(payload)/api/agents/collaborative-iteration/utils/messageBus';

/**
 * Orchestrated Collaboration API Route
 * Serves as a bridge between the frontend and the collaborative agent system
 */
export async function GET(request: NextRequest) {
  try {
    // Extract sessionId from query params
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({ success: false, error: 'Session ID is required' }, { status: 400 });
    }

    // Get session state
    let state = await stateStore.get(sessionId);
    if (!state) {
      return NextResponse.json({ success: false, error: 'Session not found' }, { status: 404 });
    }

    // Check if we need to trigger essential agents
    const hasMarketResearch = state.artifacts &&
      Object.values(state.artifacts).some(artifact => artifact.type === 'market-research');

    const hasSeoKeywords = state.artifacts &&
      Object.values(state.artifacts).some(artifact => artifact.type === 'seo-keywords');

    const hasContentStrategy = state.artifacts &&
      Object.values(state.artifacts).some(artifact => artifact.type === 'content-strategy');

    const hasSeoOptimization = state.artifacts &&
      Object.values(state.artifacts).some(artifact => artifact.type === 'seo-optimization');

    // If we're missing essential artifacts, trigger the agents
    if (!hasMarketResearch || !hasSeoKeywords || !hasContentStrategy || !hasSeoOptimization) {
      console.log(`Missing essential artifacts in session ${sessionId}, triggering agents`);

      // Extract session parameters
      const { topic, contentType, targetAudience, tone, keywords } = state;

      if (topic && contentType && targetAudience) {
        // Trigger the essential agents
        await triggerEssentialAgents(
          sessionId,
          state,
          topic,
          contentType,
          targetAudience,
          tone || 'professional',
          keywords || []
        );

        // Get the updated state
        state = await stateStore.get(sessionId) || state;
      }
    }

    // Process the state to make it frontend-friendly
    const processedState = processStateForFrontend(state);

    return NextResponse.json({
      success: true,
      sessionId,
      state: processedState,
      finalOutput: extractFinalOutput(state)
    });
  } catch (error: any) {
    console.error('Error in GET orchestrated-collaboration:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { topic, contentType, targetAudience, tone, keywords, additionalInstructions } = body;

    // Validate required fields
    if (!topic || !contentType || !targetAudience) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: topic, contentType, or targetAudience' },
        { status: 400 }
      );
    }

    // Create a new session using the collaborative agent system
    const sessionId = uuidv4();

    // Format the request for the collaborative agent system
    const collaborationRequest = {
      action: 'createSession',
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone: tone || 'professional',
      keywords: keywords || [],
      additionalInstructions: additionalInstructions || ''
    };

    // Call the collaborative agent API directly
    const response = await fetch('http://localhost:3000/api/agents/collaborative-iteration/agents/api', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(collaborationRequest)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create collaboration session');
    }

    const data = await response.json();

    // Wait a moment to ensure session is fully initialized
    // This is necessary because the agent API might take a moment to fully process and save the session
    const waitForSession = async (sid: string, maxRetries = 5, delayMs = 300): Promise<IterativeCollaborationState | null> => {
      for (let i = 0; i < maxRetries; i++) {
        // Wait for a short time before checking
        await new Promise(resolve => setTimeout(resolve, delayMs));

        // Try to get the session state
        const sessionState = await stateStore.getState(sid);
        if (sessionState) {
          console.log(`Found session after ${i + 1} attempt(s)`);
          return sessionState;
        }
        console.log(`Session not found, retry ${i + 1} of ${maxRetries}...`);
      }
      return null;
    };

    // Get the updated state with retries to ensure we have the most current version
    let state = await waitForSession(sessionId);

    if (!state) {
      // If we still can't find the session, use the data returned from the API call
      console.warn(`Session ${sessionId} not found in stateStore, using data from API response`);
      return NextResponse.json({
        success: true,
        sessionId: data.sessionId,
        state: processStateForFrontend(data.state),
        message: data.message
      });
    }

    // Ensure market-research and content-strategy agents are triggered
    await triggerEssentialAgents(sessionId, state, topic, contentType, targetAudience, tone, keywords);

    // Get the updated state after triggering agents
    state = await stateStore.getState(sessionId) || state;

    // Process the state for frontend display
    const processedState = processStateForFrontend(state);

    return NextResponse.json({
      success: true,
      sessionId,
      state: processedState,
      finalOutput: extractFinalOutput(state)
    });
  } catch (error: any) {
    console.error('Error in POST orchestrated-collaboration:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to ensure market-research and content-strategy agents are triggered
 */
async function triggerEssentialAgents(
  sessionId: string,
  state: IterativeCollaborationState,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string = 'professional',
  keywords: string[] = []
): Promise<void> {
  console.log(`Triggering essential agents for session ${sessionId}`);

  // Check if we already have market-research artifacts
  const hasMarketResearch = state.artifacts &&
    Object.values(state.artifacts).some(artifact => artifact.type === 'market-research');

  // Check if we already have SEO keyword artifacts
  const hasSeoKeywords = state.artifacts &&
    Object.values(state.artifacts).some(artifact => artifact.type === 'seo-keywords');

  // Check if we already have content-strategy artifacts
  const hasContentStrategy = state.artifacts &&
    Object.values(state.artifacts).some(artifact => artifact.type === 'content-strategy');

  // Trigger market-research agent if needed
  if (!hasMarketResearch) {
    console.log(`Triggering market-research agent for session ${sessionId}`);
    try {
      const marketResearchRequest = {
        action: 'sendMessage',
        sessionId,
        from: 'orchestrator',
        to: 'market-research',
        type: IterativeMessageType.INITIAL_REQUEST,
        content: {
          topic,
          contentType,
          targetAudience,
          tone,
          keywords
        }
      };

      await fetch('http://localhost:3000/api/agents/collaborative-iteration/agents/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(marketResearchRequest)
      });

      console.log(`Successfully triggered market-research agent for session ${sessionId}`);
    } catch (error) {
      console.error(`Error triggering market-research agent: ${error}`);
    }
  }

  // Trigger SEO keyword agent if needed
  if (!hasSeoKeywords) {
    console.log(`Triggering SEO keyword agent for session ${sessionId}`);
    try {
      const seoKeywordRequest = {
        action: 'sendMessage',
        sessionId,
        from: 'orchestrator',
        to: 'seo-keyword',
        type: IterativeMessageType.INITIAL_REQUEST,
        content: {
          topic,
          contentType,
          targetAudience,
          tone,
          keywords
        }
      };

      await fetch('http://localhost:3000/api/agents/collaborative-iteration/agents/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(seoKeywordRequest)
      });

      console.log(`Successfully triggered SEO keyword agent for session ${sessionId}`);
    } catch (error) {
      console.error(`Error triggering SEO keyword agent: ${error}`);
    }
  }

  // Trigger content-strategy agent if needed
  if (!hasContentStrategy) {
    console.log(`Triggering content-strategy agent for session ${sessionId}`);
    try {
      const contentStrategyRequest = {
        action: 'sendMessage',
        sessionId,
        from: 'orchestrator',
        to: 'content-strategy',
        type: IterativeMessageType.INITIAL_REQUEST,
        content: {
          topic,
          contentType,
          targetAudience,
          tone,
          keywords
        }
      };

      await fetch('http://localhost:3000/api/agents/collaborative-iteration/agents/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contentStrategyRequest)
      });

      console.log(`Successfully triggered content-strategy agent for session ${sessionId}`);
    } catch (error) {
      console.error(`Error triggering content-strategy agent: ${error}`);
    }
  }

  // Trigger SEO optimization agent if needed
  if (!hasSeoOptimization) {
    console.log(`Triggering SEO optimization agent for session ${sessionId}`);
    try {
      const seoOptimizationRequest = {
        action: 'sendMessage',
        sessionId,
        from: 'orchestrator',
        to: 'seo-optimization',
        type: IterativeMessageType.INITIAL_REQUEST,
        content: {
          topic,
          contentType,
          targetAudience,
          tone,
          keywords
        }
      };

      await fetch('http://localhost:3000/api/agents/collaborative-iteration/agents/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(seoOptimizationRequest)
      });

      console.log(`Successfully triggered SEO optimization agent for session ${sessionId}`);
    } catch (error) {
      console.error(`Error triggering SEO optimization agent: ${error}`);
    }
  }

  // Wait a moment for the agents to process the requests
  await new Promise(resolve => setTimeout(resolve, 1000));
}

/**
 * Helper function to process state for frontend
 * Transforms the state to be more suitable for display
 */
function processStateForFrontend(state: IterativeCollaborationState): any {
  if (!state) return null;

  // Calculate progress based on completed goals
  const totalGoals = state.goals?.length || 0;
  const completedGoals = state.goals?.filter(goal => goal.status === 'completed').length || 0;
  const progress = totalGoals > 0 ? Math.round((completedGoals / totalGoals) * 100) : 0;

  // Get current goal
  const currentGoal = state.goals?.find(goal => goal.status === 'active')?.title ||
                     'Collaboration in progress';

  // Calculate duration
  const startTime = new Date(state.startTime).getTime();
  const endTime = state.endTime ? new Date(state.endTime).getTime() : Date.now();
  const durationMs = endTime - startTime;
  const durationMinutes = Math.floor(durationMs / 60000);
  const durationSeconds = Math.floor((durationMs % 60000) / 1000);
  const duration = `${durationMinutes}m ${durationSeconds}s`;

  // Get all artifacts from the state
  let artifacts = Object.values(state.artifacts || {});

  // Check if we have messages that contain artifacts that aren't in the state
  if (state.messages && state.messages.length > 0) {
    // Extract artifacts from messages
    const messageArtifacts = state.messages
      .filter(msg =>
        msg.type === IterativeMessageType.ARTIFACT_DELIVERY &&
        msg.content &&
        msg.content.artifact
      )
      .map(msg => msg.content.artifact);

    // Add any artifacts from messages that aren't already in the state
    if (messageArtifacts.length > 0) {
      const existingArtifactIds = artifacts.map(a => a.id);
      const newArtifacts = messageArtifacts.filter(a => !existingArtifactIds.includes(a.id));

      if (newArtifacts.length > 0) {
        console.log(`Found ${newArtifacts.length} additional artifacts in messages`);
        artifacts = [...artifacts, ...newArtifacts];
      }
    }
  }

  return {
    ...state,
    progress,
    currentGoal,
    duration,
    // Use the enhanced artifacts array
    artifacts,
    // Convert consultations object to array
    consultations: Object.values(state.consultations || {})
  };
}

/**
 * Helper function to extract final output from state
 * Looks for the final content artifact and formats it for the frontend
 */
function extractFinalOutput(state: IterativeCollaborationState): any {
  if (!state || !state.artifacts) return null;

  // Look for the final content artifact
  const finalContentArtifact = Object.values(state.artifacts).find(
    artifact => artifact.type === 'final-content' || artifact.type === 'content'
  );

  // If no final content is found, return null
  if (!finalContentArtifact) return null;

  // Get the latest iteration
  const latestIteration = finalContentArtifact.iterations[finalContentArtifact.currentVersion - 1];

  return {
    title: finalContentArtifact.name || state.topic,
    content: latestIteration.content.text || latestIteration.content,
    seoScore: finalContentArtifact.qualityScore || 75,
    generatedAt: latestIteration.timestamp,
    contributors: Array.from(new Set(
      finalContentArtifact.iterations.map(iteration => iteration.agent)
    )),
    metadata: {
      wordCount: countWords(latestIteration.content.text || latestIteration.content),
      readingTime: calculateReadingTime(latestIteration.content.text || latestIteration.content),
      keywords: state.keywords || [],
      generatedAt: latestIteration.timestamp,
      targetAudience: state.targetAudience
    }
  };
}

/**
 * Helper function to count words in a string
 */
function countWords(text: string): number {
  return text.split(/\s+/).filter(Boolean).length;
}

/**
 * Helper function to calculate reading time in minutes
 * Assumes average reading speed of 200 words per minute
 */
function calculateReadingTime(text: string): number {
  const words = countWords(text);
  return Math.max(1, Math.ceil(words / 200));
}
