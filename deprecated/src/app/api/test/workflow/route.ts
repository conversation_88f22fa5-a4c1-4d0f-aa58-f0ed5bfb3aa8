/**
 * Test API for Workflow System
 * Tests approval flow and infinite loop fixes
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine, getEnhancedTemplateRegistry } from '@/core/workflow/singleton';
import { StepStatus, ExecutionStatus } from '@/core/workflow/types';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const testType = searchParams.get('type') || 'approval-flow';

  try {
    if (testType === 'approval-flow') {
      return await testApprovalFlow();
    } else if (testType === 'infinite-loop') {
      return await testInfiniteLoopFix();
    } else if (testType === 'health') {
      return await testSystemHealth();
    } else {
      return NextResponse.json(
        { error: 'Invalid test type. Use: approval-flow, infinite-loop, or health' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Test API error:', error);
    return NextResponse.json(
      { 
        error: 'Test failed', 
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

async function testSystemHealth() {
  console.log('🏥 Testing System Health...');
  
  try {
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();
    
    // Test 1: Check if singletons are working
    const engineExists = !!engine;
    const registryExists = !!registry;
    
    // Test 2: Check template registry
    const templates = registry.getAllTemplates();
    const templateCount = templates.length;
    
    // Test 3: Check state store
    const stateStore = (engine as any).stateStore;
    const stateExists = !!stateStore;
    
    // Test 4: Try to get state
    let stateAccessible = false;
    try {
      const state = await stateStore.get();
      stateAccessible = true;
    } catch (error) {
      console.error('State access error:', error);
    }
    
    const healthStatus = {
      engine: engineExists,
      registry: registryExists,
      templates: templateCount,
      stateStore: stateExists,
      stateAccessible,
      timestamp: new Date().toISOString()
    };
    
    const allHealthy = engineExists && registryExists && templateCount > 0 && stateExists && stateAccessible;
    
    return NextResponse.json({
      success: allHealthy,
      health: healthStatus,
      message: allHealthy ? 'System is healthy' : 'System has issues'
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Health check failed'
    });
  }
}

async function testApprovalFlow() {
  console.log('🧪 Testing Approval Flow...');
  
  try {
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();
    
    // Step 1: Process template
    const processed = registry.processTemplate('blog-post-seo-approval', 'test-user');
    if (!processed) {
      throw new Error('Failed to process template');
    }
    
    // Step 2: Create workflow and execution
    const workflowId = await engine.createWorkflow(processed.workflow);

    const executionId = await engine.executeWorkflow(
      workflowId,
      {
        topic: 'Test AI Content Generation',
        targetKeywords: ['AI', 'content', 'automation'],
        targetAudience: 'Developers'
      },
      {
        userId: 'test-user',
        source: 'api-test',
        templateId: 'blog-post-seo-approval'
      }
    );
    
    // Step 3: Register approval gates
    for (const gate of processed.approvalGates) {
      await engine.registerApprovalGate(gate);
    }
    
    // Step 4: Check status after 10 seconds (execution already started)
    await new Promise(resolve => setTimeout(resolve, 10000));

    const currentExecution = await engine.getExecution(executionId);
    if (!currentExecution) {
      throw new Error('Execution not found after start');
    }
    
    // Analyze results
    const stepResults = Object.entries(currentExecution.stepResults);
    const waitingSteps = stepResults.filter(([_, result]) => result.status === StepStatus.WAITING_APPROVAL);
    const completedSteps = stepResults.filter(([_, result]) => result.status === StepStatus.COMPLETED);
    const failedSteps = stepResults.filter(([_, result]) => result.status === StepStatus.FAILED);
    
    const isPaused = currentExecution.status === ExecutionStatus.PAUSED;
    const hasWaitingSteps = waitingSteps.length > 0;
    
    // Success criteria: workflow should be paused with waiting approval steps
    const success = isPaused && hasWaitingSteps && failedSteps.length === 0;
    
    return NextResponse.json({
      success,
      executionId,
      status: currentExecution.status,
      stepCounts: {
        total: stepResults.length,
        completed: completedSteps.length,
        waiting: waitingSteps.length,
        failed: failedSteps.length
      },
      waitingSteps: waitingSteps.map(([stepId, result]) => ({
        stepId,
        artifactId: result.artifactId
      })),
      message: success ? 'Approval flow working correctly' : 'Approval flow has issues',
      details: {
        isPaused,
        hasWaitingSteps,
        approvalGatesRegistered: processed.approvalGates.length
      }
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Approval flow test failed'
    });
  }
}

async function testInfiniteLoopFix() {
  console.log('🔄 Testing Infinite Loop Fix...');
  
  try {
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();
    
    // Create execution
    const processed = registry.processTemplate('blog-post-seo-approval', 'test-user');
    if (!processed) {
      throw new Error('Failed to process template');
    }
    
    const workflowId = await engine.createWorkflow(processed.workflow);

    // Register approval gates
    for (const gate of processed.approvalGates) {
      await engine.registerApprovalGate(gate);
    }

    // Start execution and monitor
    const startTime = Date.now();
    const executionId = await engine.executeWorkflow(
      workflowId,
      { topic: 'Infinite Loop Test' },
      { userId: 'test-user', source: 'loop-test' }
    );
    
    // Wait 10 seconds and check if it's properly paused
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const currentExecution = await engine.getExecution(executionId);
    if (!currentExecution) {
      throw new Error('Execution not found');
    }
    
    const isPaused = currentExecution.status === ExecutionStatus.PAUSED;
    const hasWaitingSteps = Object.values(currentExecution.stepResults)
      .some(r => r.status === StepStatus.WAITING_APPROVAL);
    
    // Success criteria:
    // 1. Execution should be paused
    // 2. Should have waiting approval steps
    // 3. Should complete quickly (< 5 seconds indicates no infinite loop)
    const success = isPaused && hasWaitingSteps && duration < 5000;
    
    return NextResponse.json({
      success,
      duration,
      isPaused,
      hasWaitingSteps,
      executionStatus: currentExecution.status,
      message: success ? 'Infinite loop fix verified' : 'Infinite loop may still exist',
      details: {
        startTime,
        endTime,
        durationMs: duration,
        expectedPaused: true,
        actualPaused: isPaused
      }
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Infinite loop test failed'
    });
  }
}
