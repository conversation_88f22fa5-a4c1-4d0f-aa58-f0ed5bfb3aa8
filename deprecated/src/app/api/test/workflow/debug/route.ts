/**
 * Debug API for Workflow System
 * Provides detailed debugging information about workflow execution
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine, getEnhancedTemplateRegistry } from '@/core/workflow/singleton';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const executionId = searchParams.get('executionId');

  try {
    if (executionId) {
      return await debugExecution(executionId);
    } else {
      return await debugSystem();
    }
  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json(
      { 
        error: 'Debug failed', 
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

async function debugSystem() {
  console.log('🔍 Debugging System...');
  
  try {
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();
    
    // Get all executions
    const stateStore = (engine as any).stateStore;
    const allExecutions = await stateStore.getAllExecutions();
    
    // Get template info
    const template = registry.getTemplate('blog-post-seo-approval');
    
    return NextResponse.json({
      system: {
        totalExecutions: allExecutions.length,
        recentExecutions: allExecutions.slice(-5).map(exec => ({
          id: exec.id,
          status: exec.status,
          stepCount: Object.keys(exec.stepResults).length,
          createdAt: exec.createdAt
        }))
      },
      template: template ? {
        id: template.id,
        stepCount: template.workflow.steps.length,
        approvalSteps: template.workflow.steps.filter(s => s.type === 'APPROVAL_GATE').map(s => s.id)
      } : null,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'System debug failed'
    });
  }
}

async function debugExecution(executionId: string) {
  console.log(`🔍 Debugging Execution: ${executionId}`);
  
  try {
    const engine = getWorkflowEngine();
    const execution = await engine.getExecution(executionId);
    
    if (!execution) {
      return NextResponse.json({
        error: 'Execution not found',
        executionId
      }, { status: 404 });
    }
    
    const workflow = await engine.getWorkflow(execution.workflowId);
    
    // Analyze step results
    const stepAnalysis = Object.entries(execution.stepResults).map(([stepId, result]) => {
      const step = workflow?.steps.find(s => s.id === stepId);
      return {
        stepId,
        stepName: step?.name || 'Unknown',
        stepType: step?.type || 'Unknown',
        status: result.status,
        hasArtifact: !!result.artifactId,
        artifactId: result.artifactId,
        error: result.error,
        startedAt: result.startedAt,
        completedAt: result.completedAt,
        duration: result.duration,
        dependencies: step?.dependencies || [],
        inputs: Object.keys(result.inputs),
        outputs: Object.keys(result.outputs)
      };
    });
    
    // Find next step
    const completedSteps = stepAnalysis.filter(s => s.status === 'completed').map(s => s.stepId);
    const waitingSteps = stepAnalysis.filter(s => s.status === 'waiting_approval');
    const failedSteps = stepAnalysis.filter(s => s.status === 'failed');
    const runningSteps = stepAnalysis.filter(s => s.status === 'running');
    
    // Find eligible next steps
    const eligibleSteps = workflow?.steps.filter(step => {
      const isCompleted = completedSteps.includes(step.id);
      const isWaiting = waitingSteps.some(w => w.stepId === step.id);
      const isRunning = runningSteps.some(r => r.stepId === step.id);
      const isFailed = failedSteps.some(f => f.stepId === step.id);
      
      if (isCompleted || isWaiting || isRunning || isFailed) {
        return false;
      }
      
      // Check if dependencies are met
      const dependenciesMet = step.dependencies.every(depId => 
        completedSteps.includes(depId)
      );
      
      return dependenciesMet;
    }) || [];
    
    return NextResponse.json({
      execution: {
        id: execution.id,
        workflowId: execution.workflowId,
        status: execution.status,
        createdAt: execution.createdAt,
        updatedAt: execution.updatedAt,
        error: execution.error
      },
      workflow: workflow ? {
        id: workflow.id,
        name: workflow.name,
        totalSteps: workflow.steps.length,
        approvalSteps: workflow.steps.filter(s => s.type === 'APPROVAL_GATE').length
      } : null,
      stepAnalysis,
      summary: {
        totalSteps: stepAnalysis.length,
        completed: completedSteps.length,
        waiting: waitingSteps.length,
        failed: failedSteps.length,
        running: runningSteps.length,
        eligible: eligibleSteps.length
      },
      nextSteps: eligibleSteps.map(step => ({
        id: step.id,
        name: step.name,
        type: step.type,
        dependencies: step.dependencies
      })),
      waitingApproval: waitingSteps,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Execution debug failed',
      executionId
    });
  }
}
