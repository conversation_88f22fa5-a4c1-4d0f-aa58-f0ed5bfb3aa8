/**
 * Test API for Feedback & Regeneration System
 * Demonstrates the complete feedback processing and artifact regeneration flow
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../core/workflow/singleton';
import { ArtifactStatus, ArtifactType } from '../../../../core/workflow/types';

/**
 * POST /api/test/feedback-regeneration
 * Test the complete feedback and regeneration flow
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Starting rejection handling test...');

    const workflowEngine = getWorkflowEngine();

    // Step 1: Test the new handleReviewCompletion method directly
    console.log('📝 Testing handleReviewCompletion with rejection and feedback...');

    // Create a mock execution ID for testing
    const testExecutionId = 'test-execution-' + Date.now();
    const testStepId = 'human-review';

    // Create a minimal test execution in the state store
    const testExecution = {
      id: testExecutionId,
      workflowId: 'test-workflow',
      status: 'paused',
      inputs: { topic: 'Test Topic' },
      outputs: {},
      stepResults: {
        [testStepId]: {
          stepId: testStepId,
          status: 'waiting_review',
          startedAt: new Date().toISOString(),
          outputs: {
            blog_content: {
              title: 'Test Blog Post',
              content: 'This is a basic test content that needs improvement.',
              keywords: ['test', 'basic']
            }
          },
          metadata: {}
        }
      },
      progress: 50,
      startedAt: new Date().toISOString()
    };

    // Store the test execution
    await workflowEngine.getStore().setExecution(testExecution);
    console.log(`✅ Created test execution: ${testExecutionId}`);

    // Step 2: Test rejection with feedback
    const feedback = 'The content is too basic and lacks depth. Please add more technical details, examples, and explain different types of machine learning algorithms. The writing style should be more engaging and professional.';

    console.log(`📝 Testing rejection with feedback: ${feedback}`);

    // Call our new handleReviewCompletion method
    await workflowEngine.handleReviewCompletion(
      testExecutionId,
      testStepId,
      {
        decision: 'reject',
        feedback: feedback,
        reviewer: 'test-reviewer'
      }
    );

    console.log(`✅ Review completion handled`);

    // Step 3: Wait a moment for processing
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Step 4: Check the results
    const updatedExecution = await workflowEngine.getExecution(testExecutionId);
    const stepResult = updatedExecution?.stepResults[testStepId];

    console.log(`📊 Test Results:`, {
      executionId: testExecutionId,
      stepStatus: stepResult?.status,
      reviewDecision: stepResult?.outputs?.review_decision,
      regenerationApplied: stepResult?.outputs?.regeneration_applied,
      metadata: stepResult?.metadata
    });

    // Step 5: Test approval flow as well
    console.log('📝 Testing approval flow...');

    const approvalTestExecutionId = 'approval-test-' + Date.now();
    const approvalTestExecution = {
      ...testExecution,
      id: approvalTestExecutionId
    };

    await workflowEngine.getStore().setExecution(approvalTestExecution);

    await workflowEngine.handleReviewCompletion(
      approvalTestExecutionId,
      testStepId,
      {
        decision: 'approve',
        feedback: 'Looks good!',
        reviewer: 'test-reviewer'
      }
    );

    const approvedExecution = await workflowEngine.getExecution(approvalTestExecutionId);
    const approvedStepResult = approvedExecution?.stepResults[testStepId];

    return NextResponse.json({
      success: true,
      message: 'Rejection handling test completed',
      data: {
        rejectionTest: {
          executionId: testExecutionId,
          stepStatus: stepResult?.status,
          reviewDecision: stepResult?.outputs?.review_decision,
          regenerationApplied: stepResult?.outputs?.regeneration_applied,
          metadata: stepResult?.metadata
        },
        approvalTest: {
          executionId: approvalTestExecutionId,
          stepStatus: approvedStepResult?.status,
          reviewDecision: approvedStepResult?.outputs?.review_decision
        },
        feedback,
        testPassed: stepResult?.status === 'completed' && approvedStepResult?.status === 'completed'
      }
    });

  } catch (error) {
    console.error('❌ Rejection handling test failed:', error);

    return NextResponse.json(
      {
        error: 'Test failed',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/test/feedback-regeneration
 * Get information about the feedback & regeneration system
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Feedback & Regeneration System Test API',
    description: 'This API tests the complete feedback processing and artifact regeneration flow',
    endpoints: {
      'POST /api/test/feedback-regeneration': 'Run a complete test of the feedback & regeneration system',
      'GET /api/test/feedback-regeneration': 'Get information about this test API'
    },
    testFlow: [
      '1. Create a test workflow execution',
      '2. Create a test artifact with basic content',
      '3. Submit rejection with detailed feedback',
      '4. System processes feedback and regenerates improved content',
      '5. New artifact is created and submitted for approval',
      '6. Return test results showing the regeneration process'
    ]
  });
}
