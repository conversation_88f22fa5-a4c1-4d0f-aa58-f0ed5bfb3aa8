// src/app/(payload)/api/enhanced-content-generation/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { EnhancedCollaborationController } from '../agents/dynamic-collaboration/enhanced-collaboration-controller';
import { ContentGenerationRequest } from '../agents/a2atypes';

// Create a singleton controller to be reused across requests
const controller = new EnhancedCollaborationController();

/**
 * Handler for starting a new enhanced content generation session
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request
    const requestData: ContentGenerationRequest = await request.json();
    
    // Validate request
    if (!requestData.topic) {
      return NextResponse.json(
        { error: 'Missing required field: topic' },
        { status: 400 }
      );
    }
    
    if (!requestData.contentType) {
      return NextResponse.json(
        { error: 'Missing required field: contentType' },
        { status: 400 }
      );
    }
    
    // Start the collaboration process
    const state = await controller.startCollaboration(requestData);
    
    // Format state for UI
    const formattedState = controller.formatStateForUI(state);
    
    // Return the state
    return NextResponse.json({
      success: true,
      sessionId: state.id,
      state: formattedState
    });
  } catch (error) {
    console.error('Error starting enhanced content generation:', error);
    
    return NextResponse.json(
      { error: 'Failed to start content generation', details: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * Handler for getting the current state of a content generation session
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const sessionId = searchParams.get('sessionId');
    
    if (!sessionId) {
      // Return all sessions if no session ID is provided
      const states = controller.getAllSessionStates();
      const formattedStates = states.map(state => controller.formatStateForUI(state));
      
      return NextResponse.json({
        success: true,
        sessionCount: states.length,
        sessions: formattedStates
      });
    }
    
    // Get state for the specified session
    const state = controller.getSessionState(sessionId);
    
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }
    
    // Format state for UI
    const formattedState = controller.formatStateForUI(state);
    
    return NextResponse.json({
      success: true,
      state: formattedState
    });
  } catch (error) {
    console.error('Error getting content generation state:', error);
    
    return NextResponse.json(
      { error: 'Failed to get content generation state', details: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * Handler for sending a message to a content generation session
 */
export async function PUT(request: NextRequest) {
  try {
    const { sessionId, message } = await request.json();
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required field: sessionId' },
        { status: 400 }
      );
    }
    
    if (!message) {
      return NextResponse.json(
        { error: 'Missing required field: message' },
        { status: 400 }
      );
    }
    
    // Check if session exists
    const state = controller.getSessionState(sessionId);
    
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }
    
    // Check if message is in legacy format
    if (message.type && !message.intentions) {
      // Send as legacy message
      await controller.sendLegacyMessage(sessionId, message);
    } else {
      // Send as enhanced message
      await controller.sendMessage(sessionId, message);
    }
    
    // Get updated state
    const updatedState = controller.getSessionState(sessionId);
    
    // Format state for UI
    const formattedState = controller.formatStateForUI(updatedState!);
    
    return NextResponse.json({
      success: true,
      state: formattedState
    });
  } catch (error) {
    console.error('Error sending message to content generation session:', error);
    
    return NextResponse.json(
      { error: 'Failed to send message', details: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * Handler for initializing a goal in a content generation session
 */
export async function PATCH(request: NextRequest) {
  try {
    const { sessionId, goalDescription } = await request.json();
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required field: sessionId' },
        { status: 400 }
      );
    }
    
    if (!goalDescription) {
      return NextResponse.json(
        { error: 'Missing required field: goalDescription' },
        { status: 400 }
      );
    }
    
    // Initialize goal
    const goal = await controller.initializeGoal(sessionId, goalDescription);
    
    if (!goal) {
      return NextResponse.json(
        { error: 'Goal not found' },
        { status: 404 }
      );
    }
    
    // Get updated state
    const updatedState = controller.getSessionState(sessionId);
    
    // Format state for UI
    const formattedState = controller.formatStateForUI(updatedState!);
    
    return NextResponse.json({
      success: true,
      goal,
      state: formattedState
    });
  } catch (error) {
    console.error('Error initializing goal:', error);
    
    return NextResponse.json(
      { error: 'Failed to initialize goal', details: (error as Error).message },
      { status: 500 }
    );
  }
}
