/**
 * Dynamic Collaboration Message API Route
 * 
 * This file handles API requests for sending messages in the dynamic collaboration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowOrchestrator } from '../../agents/dynamic-collaboration-v2';
import { AgentCommunicationHub } from '../../agents/dynamic-collaboration-v2/agent-communication-hub';
import { DynamicAgentMessage } from '../../agents/dynamic-collaboration-v2/types';
import logger from '../../agents/collaborative-iteration/utils/logger';

/**
 * POST handler for sending a message in a dynamic collaboration session
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const { sessionId, message } = body;
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    if (!message) {
      return NextResponse.json(
        { error: 'Missing message parameter' },
        { status: 400 }
      );
    }
    
    // Validate the message
    if (!message.from || !message.to || !message.type || message.content === undefined) {
      return NextResponse.json(
        { 
          error: 'Invalid message format',
          requiredFields: ['from', 'to', 'type', 'content']
        },
        { status: 400 }
      );
    }
    
    // Get the communication hub for this session
    const communicationHub = new AgentCommunicationHub(sessionId);
    
    // Send the message
    const messageId = await communicationHub.sendMessage(message as DynamicAgentMessage);
    
    // Return success
    return NextResponse.json({ 
      success: true,
      sessionId,
      messageId
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error sending message in dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler for retrieving messages from a dynamic collaboration session
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const sessionId = req.nextUrl.searchParams.get('sessionId');
    const conversationId = req.nextUrl.searchParams.get('conversationId');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    // Get the state store
    const { stateStore } = await import('../../agents/collaborative-iteration/utils/stateStore');
    
    // Get the session state
    const state = await stateStore.getState(sessionId);
    
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }
    
    // Get messages
    let messages: Record<string, DynamicAgentMessage> = {};
    
    if (conversationId) {
      // Get messages for a specific conversation
      const conversationMessageIds = state.conversations?.[conversationId] || [];
      
      // Filter dynamic messages to only include those in the conversation
      if (state.dynamicMessages) {
        messages = Object.fromEntries(
          Object.entries(state.dynamicMessages).filter(
            ([id]) => conversationMessageIds.includes(id)
          )
        );
      }
    } else {
      // Get all messages
      messages = state.dynamicMessages || {};
    }
    
    // Return the messages
    return NextResponse.json({ 
      success: true,
      sessionId,
      conversationId,
      messages,
      messageCount: Object.keys(messages).length
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving messages from dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}
