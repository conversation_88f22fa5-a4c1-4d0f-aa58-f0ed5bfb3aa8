// src/lib/goal-orchestrator-client.ts

import { v4 as uuidv4 } from 'uuid';
import {
  WorkflowPhase,
  MessageType,
  GoalType,
  GoalStatus,
  ArtifactStatus,
  SessionStatus
} from '../app/(payload)/api/agents/dynamic-collaboration-v3';

/**
 * Goal Orchestrator Client
 */
export class GoalOrchestratorClient {
  private apiEndpoint: string;
  private jsonrpcEndpoint: string;

  constructor(
    apiEndpoint = '/api/agents/dynamic-collaboration-v3',
    jsonrpcEndpoint = '/api/agents/dynamic-collaboration-v3/jsonrpc'
  ) {
    this.apiEndpoint = apiEndpoint;
    this.jsonrpcEndpoint = jsonrpcEndpoint;
  }

  /**
   * Initialize a new collaboration session
   */
  async initiate(params: any): Promise<{ sessionId: string }> {
    const response = await fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return {
      sessionId: result.sessionId
    };
  }

  /**
   * Get the current state of a session
   */
  async getState(sessionId: string): Promise<any> {
    const response = await fetch(`${this.apiEndpoint}?sessionId=${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result.state;
  }

  /**
   * Send a JSONRPC request
   */
  private async sendJsonRpcRequest(method: string, params: any): Promise<any> {
    const request = {
      jsonrpc: '2.0',
      method,
      params,
      id: uuidv4()
    };

    const response = await fetch(this.jsonrpcEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(`JSONRPC error: ${result.error.message}`);
    }

    return result.result;
  }

  /**
   * Get goals for a session
   */
  async getGoals(sessionId: string): Promise<any> {
    return this.sendJsonRpcRequest('goal.list', { sessionId });
  }

  /**
   * Get artifacts for a session
   */
  async getArtifacts(sessionId: string, type?: string): Promise<any> {
    return this.sendJsonRpcRequest('artifact.list', { sessionId, type });
  }

  /**
   * Get workflow progress
   */
  async getWorkflowProgress(sessionId: string): Promise<any> {
    return this.sendJsonRpcRequest('workflow.getProgress', { sessionId });
  }

  /**
   * Send user feedback
   */
  async sendFeedback(sessionId: string, artifactId: string, feedback: any): Promise<any> {
    return this.sendJsonRpcRequest('feedback.provide', {
      sessionId,
      requestId: uuidv4(),
      fromAgent: 'user',
      toAgent: 'system',
      feedback
    });
  }
}

// Export a singleton instance
export const goalOrchestratorClient = new GoalOrchestratorClient();
export default goalOrchestratorClient;