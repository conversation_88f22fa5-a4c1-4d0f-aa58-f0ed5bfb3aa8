/**
 * Dynamic Collaboration Client
 * 
 * A client-side utility for interacting with the dynamic collaboration JSONRPC API
 */

import { v4 as uuidv4 } from 'uuid';
import { 
  DynamicWorkflowPhase, 
  DynamicMessageType, 
  ContentGenerationParams,
  DynamicAgentMessage,
  DynamicCollaborationState
} from '../app/(payload)/api/agents/dynamic-collaboration-v2/types';

// JSONRPC interface types
interface JsonRpcRequest {
  jsonrpc: string;
  method: string;
  params: any;
  id: string | number;
}

interface JsonRpcSuccessResponse {
  jsonrpc: string;
  result: any;
  id: string | number;
}

interface JsonRpcErrorResponse {
  jsonrpc: string;
  error: {
    code: number;
    message: string;
    data?: any;
  };
  id: string | number | null;
}

type JsonRpcResponse = JsonRpcSuccessResponse | JsonRpcErrorResponse;

/**
 * Dynamic Collaboration Client
 */
export class DynamicCollaborationClient {
  private apiEndpoint: string;

  constructor(apiEndpoint = '/api/agents/dynamic-collaboration-v2/jsonrpc') {
    this.apiEndpoint = apiEndpoint;
  }

  /**
   * Send a JSONRPC request to the API
   */
  private async sendRequest(method: string, params: any): Promise<any> {
    const request: JsonRpcRequest = {
      jsonrpc: '2.0',
      method,
      params,
      id: uuidv4()
    };

    const response = await fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const jsonResponse = await response.json() as JsonRpcResponse;

    if ('error' in jsonResponse) {
      throw new Error(`JSONRPC error ${jsonResponse.error.code}: ${jsonResponse.error.message}`);
    }

    return jsonResponse.result;
  }

  /**
   * Create a new collaboration session
   */
  async createSession(params: ContentGenerationParams): Promise<{ sessionId: string }> {
    return this.sendRequest('session.create', params);
  }

  /**
   * Get a session by ID
   */
  async getSession(sessionId: string): Promise<DynamicCollaborationState> {
    return this.sendRequest('session.get', { sessionId });
  }

  /**
   * List all sessions
   */
  async listSessions(): Promise<{ sessions: Array<{ id: string, topic: string, status: string }> }> {
    return this.sendRequest('session.list', {});
  }

  /**
   * Update a session
   */
  async updateSession(sessionId: string, action: string, phase?: DynamicWorkflowPhase): Promise<any> {
    return this.sendRequest('session.update', { sessionId, action, phase });
  }

  /**
   * Delete a session
   */
  async deleteSession(sessionId: string): Promise<{ success: boolean }> {
    return this.sendRequest('session.delete', { sessionId });
  }

  /**
   * Send a message
   */
  async sendMessage(
    sessionId: string, 
    content: any, 
    type: DynamicMessageType = DynamicMessageType.USER_MESSAGE,
    to: string = 'system'
  ): Promise<{ messageId: string }> {
    return this.sendRequest('message.send', { sessionId, content, type, to });
  }

  /**
   * List messages for a session
   */
  async listMessages(sessionId: string): Promise<{ messages: DynamicAgentMessage[] }> {
    return this.sendRequest('message.list', { sessionId });
  }

  /**
   * Create an artifact
   */
  async createArtifact(
    sessionId: string,
    type: string,
    name: string,
    content: any,
    creator: string = 'user',
    status: string = 'completed',
    data: any = {}
  ): Promise<{ artifactId: string }> {
    return this.sendRequest('artifact.create', { 
      sessionId, type, name, content, creator, status, data 
    });
  }

  /**
   * List artifacts for a session
   */
  async listArtifacts(sessionId: string): Promise<{ artifacts: any[] }> {
    return this.sendRequest('artifact.list', { sessionId });
  }

  /**
   * Publish content
   */
  async publishContent(
    sessionId: string,
    title: string,
    content: string,
    status: string = 'published'
  ): Promise<{ artifactId: string, publishStatus: string }> {
    return this.sendRequest('content.publish', { sessionId, title, content, status });
  }

  /**
   * Transition to a new phase
   */
  async transitionToPhase(sessionId: string, phase: DynamicWorkflowPhase): Promise<any> {
    return this.updateSession(sessionId, 'transition-phase', phase);
  }

  /**
   * Pause a session
   */
  async pauseSession(sessionId: string): Promise<any> {
    return this.updateSession(sessionId, 'pause');
  }

  /**
   * Resume a session
   */
  async resumeSession(sessionId: string): Promise<any> {
    return this.updateSession(sessionId, 'resume');
  }

  /**
   * Cancel a session
   */
  async cancelSession(sessionId: string): Promise<any> {
    return this.updateSession(sessionId, 'cancel');
  }
}

// Export a singleton instance
export const dynamicCollaborationClient = new DynamicCollaborationClient();
export default dynamicCollaborationClient;
