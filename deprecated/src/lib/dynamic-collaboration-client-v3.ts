/**
 * Dynamic Collaboration Client V3
 *
 * A client-side utility for interacting with the dynamic collaboration V3 API
 */

import { v4 as uuidv4 } from 'uuid';
import {
  WorkflowPhase,
  MessageType,
  GoalType,
  GoalStatus,
  ArtifactStatus,
  SessionStatus
} from '../app/(payload)/api/agents/dynamic-collaboration-v3';

/**
 * Content Generation Parameters
 */
export interface ContentGenerationParams {
  topic: string;
  contentType?: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience?: string;
  tone?: string;
  keywords?: string[];
  additionalInstructions?: string;
  referenceUrls?: string[];
}

/**
 * Dynamic Collaboration Client V3
 */
export class DynamicCollaborationClientV3 {
  private apiEndpoint: string;

  constructor(apiEndpoint = '/api/agents/dynamic-collaboration-v3') {
    this.apiEndpoint = apiEndpoint;
  }

  /**
   * Initialize a new collaboration session
   */
  async initiate(params: ContentGenerationParams): Promise<{ sessionId: string, success: boolean }> {
    const response = await fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to initialize session');
    }

    return {
      sessionId: result.sessionId,
      success: result.success
    };
  }

  /**
   * Get the current state of a session
   */
  async getState(sessionId: string): Promise<any> {
    const response = await fetch(`${this.apiEndpoint}?sessionId=${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to get session state');
    }

    // Validate and normalize state structure
    const state = result.state;

    // Ensure goals structure exists
    if (!state.goals) {
      state.goals = { byId: {}, allIds: [], activeIds: [], completedIds: [] };
    } else if (!state.goals.byId) {
      // Handle case where goals is a flat object (old format)
      const goalsById = state.goals;
      state.goals = {
        byId: goalsById,
        allIds: Object.keys(goalsById),
        activeIds: Object.keys(goalsById).filter(id => goalsById[id].status === GoalStatus.ACTIVE || goalsById[id].status === GoalStatus.IN_PROGRESS),
        completedIds: Object.keys(goalsById).filter(id => goalsById[id].status === GoalStatus.COMPLETED)
      };
    }

    // Ensure messages structure exists
    if (!state.messages) {
      state.messages = { byId: {}, allIds: [], byConversation: {} };
    } else if (!state.messages.byId) {
      // Handle case where messages is an array (old format)
      const messagesArray = Array.isArray(state.messages) ? state.messages : Object.values(state.messages);
      const messagesById: Record<string, any> = {};
      const byConversation: Record<string, string[]> = {};

      messagesArray.forEach(message => {
        if (message.id) {
          messagesById[message.id] = message;

          if (message.conversationId) {
            if (!byConversation[message.conversationId]) {
              byConversation[message.conversationId] = [];
            }
            byConversation[message.conversationId].push(message.id);
          }
        }
      });

      state.messages = {
        byId: messagesById,
        allIds: Object.keys(messagesById),
        byConversation
      };
    }

    // Ensure artifacts structure exists
    if (!state.artifacts) {
      state.artifacts = {};
    }

    return state;
  }

  /**
   * Send a message to the session
   */
  async sendMessage(sessionId: string, content: string): Promise<{ messageId: string }> {
    const response = await fetch(`${this.apiEndpoint}/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        content
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to send message');
    }

    return {
      messageId: result.messageId
    };
  }

  /**
   * Get artifacts for a session
   */
  async getArtifacts(sessionId: string): Promise<any> {
    const response = await fetch(`${this.apiEndpoint}/artifacts?sessionId=${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to get artifacts');
    }

    return result.artifacts;
  }

  /**
   * Get a specific artifact
   */
  async getArtifact(sessionId: string, artifactId: string): Promise<any> {
    const response = await fetch(`${this.apiEndpoint}/artifacts/${artifactId}?sessionId=${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to get artifact');
    }

    return result.artifact;
  }

  /**
   * Get goals for a session
   */
  async getGoals(sessionId: string): Promise<any> {
    const response = await fetch(`${this.apiEndpoint}/goals?sessionId=${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to get goals');
    }

    return result.goals;
  }

  /**
   * Complete a session
   */
  async completeSession(sessionId: string): Promise<{ success: boolean }> {
    const response = await fetch(`${this.apiEndpoint}/complete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to complete session');
    }

    return {
      success: result.success
    };
  }

  /**
   * Manually progress a session
   * @param sessionId Session ID
   * @param steps Number of progression steps to attempt (default: 1)
   */
  async progressSession(sessionId: string, steps: number = 1): Promise<{
    success: boolean;
    initialPhase: string;
    currentPhase: string;
    progressMade: boolean;
  }> {
    const response = await fetch(`${this.apiEndpoint}/progress`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        steps
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to progress session');
    }

    return {
      success: result.success,
      initialPhase: result.initialPhase,
      currentPhase: result.currentPhase,
      progressMade: result.progressMade
    };
  }
}

// Export a singleton instance
export const dynamicCollaborationClientV3 = new DynamicCollaborationClientV3();
export default dynamicCollaborationClientV3;
