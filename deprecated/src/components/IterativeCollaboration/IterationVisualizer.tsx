// src/components/IterativeCollaboration/IterationVisualizer.tsx

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent
} from '@mui/lab';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LightbulbOutlinedIcon from '@mui/icons-material/LightbulbOutlined';
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HistoryIcon from '@mui/icons-material/History';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';

// Import types from our iterative collaboration system
import { IterativeArtifact, Iteration } from '../../app/(payload)/api/agents/collaborative-iteration/types';

interface IterationVisualizerProps {
  artifacts: IterativeArtifact[];
}

const IterationVisualizer: React.FC<IterationVisualizerProps> = ({ artifacts }) => {
  const [selectedArtifact, setSelectedArtifact] = useState<string | null>(null);

  // Format timestamp to a readable format
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // Get color based on artifact type
  const getArtifactColor = (type: string) => {
    switch (type) {
      case 'draft-content':
        return 'primary';
      case 'keyword-set':
        return 'secondary';
      case 'content-structure':
        return 'success';
      case 'market-research':
        return 'info';
      case 'optimized-content':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'info';
      case 'in-review':
        return 'warning';
      case 'revised':
        return 'secondary';
      case 'final':
        return 'success';
      default:
        return 'default';
    }
  };

  // Render the list of artifacts
  const renderArtifactList = () => (
    <Box sx={{ mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Artifacts ({artifacts.length})
      </Typography>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {artifacts.map((artifact) => (
          <Box key={`artifact-${artifact.type}-${artifact.id || Math.random().toString(36).substring(2, 9)}`} sx={{ width: { xs: '100%', sm: 'calc(50% - 16px)', md: 'calc(33.33% - 16px)' } }}>
            <Card 
              variant="outlined" 
              sx={{ 
                cursor: 'pointer',
                border: selectedArtifact === artifact.id ? 2 : 1,
                borderColor: selectedArtifact === artifact.id ? 'primary.main' : 'divider'
              }}
              onClick={() => setSelectedArtifact(artifact.id)}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Chip 
                    label={artifact.type} 
                    color={getArtifactColor(artifact.type) as any} 
                    size="small" 
                  />
                  <Chip 
                    label={artifact.status} 
                    color={getStatusColor(artifact.status)} 
                    size="small" 
                  />
                </Box>
                <Typography variant="subtitle1" noWrap>
                  {artifact.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Created by: {artifact.createdBy}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <HistoryIcon fontSize="small" color="action" />
                  <Typography variant="body2" color="text.secondary">
                    {artifact.iterations.length} iterations
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Quality Score: {artifact.qualityScore}/100
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Box>
        ))}
      </Box>
    </Box>
  );

  // Render the iteration timeline for the selected artifact
  const renderIterationTimeline = () => {
    if (!selectedArtifact) return null;
    
    const artifact = artifacts.find(a => a.id === selectedArtifact);
    if (!artifact) return null;
    
    return (
      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Iteration History: {artifact.name}
        </Typography>
        
        <Timeline position="alternate">
          {artifact.iterations.map((iteration, index) => (
            <TimelineItem key={index}>
              <TimelineOppositeContent color="text.secondary">
                {formatTimestamp(iteration.timestamp)}
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineDot color={index === 0 ? 'primary' : index === artifact.iterations.length - 1 ? 'success' : 'grey'}>
                  {index === 0 ? <LightbulbOutlinedIcon /> : 
                   index === artifact.iterations.length - 1 ? <CheckCircleIcon /> : 
                   <AutorenewIcon />}
                </TimelineDot>
                {index < artifact.iterations.length - 1 && <TimelineConnector />}
              </TimelineSeparator>
              <TimelineContent>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1">
                      Version {iteration.version}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {iteration.feedback?.join(', ') || 'No changes recorded'}
                    </Typography>
                    
                    <Divider sx={{ my: 1 }} />
                    
                    <Typography variant="body2">
                      {iteration.feedback?.join(', ') || 'No changes recorded'}
                    </Typography>
                    
                    {iteration.incorporatedConsultations && iteration.incorporatedConsultations.length > 0 && (
                      <>
                        <Typography variant="body2" sx={{ mt: 2, fontWeight: 'bold' }}>
                          Based on {iteration.incorporatedConsultations.length} consultations
                        </Typography>
                        <Box sx={{ ml: 2 }}>
                          {iteration.incorporatedConsultations.map((consultationId: string, idx: number) => (
                            <Chip key={`iteration-visualization-consultation-${consultationId}-${idx}-${Math.random().toString(36).substring(2, 9)}`} label={`Consultation ${idx + 1}`} size="small" variant="outlined" />
                          ))}
                        </Box>
                      </>
                    )}
                    
                    {/* Timestamp information */}
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Created at: {new Date(iteration.timestamp).toLocaleString()}
                      </Typography>
                    </Box>
                    
                    <Accordion sx={{ mt: 1 }}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="body2">Content Preview</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Box sx={{ maxHeight: '300px', overflow: 'auto' }}>
                          {typeof iteration.content === 'object' ? (
                            <pre style={{ whiteSpace: 'pre-wrap' }}>
                              {JSON.stringify(iteration.content, null, 2)}
                            </pre>
                          ) : (
                            <Typography variant="body2">
                              {String(iteration.content)}
                            </Typography>
                          )}
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                  </CardContent>
                </Card>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      </Box>
    );
  };

  return (
    <Box>
      {artifacts.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            No artifacts have been created yet. The agents will create artifacts as they collaborate.
          </Typography>
        </Paper>
      ) : (
        <>
          {renderArtifactList()}
          {renderIterationTimeline()}
        </>
      )}
    </Box>
  );
};

export default IterationVisualizer;
