// src/components/IterativeCollaboration/IterativeCollaborationDashboard.tsx
"use client"
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Button,
  TextField,
  MenuItem,
  Tabs,
  Tab,
  CircularProgress,
  Divider,
  Chip
} from '@mui/material';
import Grid from '@mui/material/Grid';
import IterationVisualizer from './IterationVisualizer';
import ConsultationTracker from './ConsultationTracker';
import ContentPreview from './ContentPreview';
import AgentStateVisualizer from './AgentStateVisualizer';
import ReasoningDisplay from './ReasoningDisplay';

interface IterativeCollaborationDashboardProps {
  sessionId?: string;
}

const IterativeCollaborationDashboard: React.FC<IterativeCollaborationDashboardProps> = ({
  sessionId: initialSessionId
}) => {
  // Form state
  const [topic, setTopic] = useState('');
  const [contentType, setContentType] = useState('blog-article');
  const [targetAudience, setTargetAudience] = useState('');
  const [tone, setTone] = useState('professional');
  const [keywords, setKeywords] = useState('');

  // Session state
  const [sessionId, setSessionId] = useState(initialSessionId || '');
  const [sessionState, setSessionState] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Define interface for session state to improve type safety
  interface SessionState {
    id: string;
    status: string;
    topic: string;
    contentType: string;
    targetAudience: string;
    currentPhase: string;
    keywords: string[];
    iterations: number;
    maxIterations: number;
    artifacts: Record<string, any>;
    consultations: Record<string, any>;
    reasoning: any[];
    workflowProgress?: {
      marketResearchComplete: boolean;
      keywordResearchComplete: boolean;
      contentStrategyComplete: boolean;
      contentGenerationComplete: boolean;
      seoOptimizationComplete: boolean;
    };
    agentStates: Record<string, any>;
  }

  // Fetch session state if sessionId is provided
  useEffect(() => {
    if (sessionId) {
      fetchSessionState();

      // Set up auto-refresh
      const interval = setInterval(() => {
        fetchSessionState();
      }, 5000); // Refresh every 5 seconds

      setRefreshInterval(interval);
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [sessionId]);

  // Fetch the current state of the session
  const fetchSessionState = async () => {
    if (!sessionId) {
      setError('No session ID provided');
      return;
    }
    try {
      setLoading(true);
      const response = await fetch(`/api/collaborative-agents?sessionId=${sessionId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch session: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setSessionState(data.state);
        setError('');
      } else {
        setError(data.error || 'Failed to fetch session state');
      }
    } catch (err) {
      setError(`Error fetching session: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // Start a new collaboration session
  const startCollaboration = async () => {
    // Clear any previous errors
    setError('');
    try {
      setLoading(true);

      // Validate inputs
      if (!topic) {
        setError('Topic is required');
        return;
      }

      if (!targetAudience) {
        setError('Target audience is required');
        return;
      }

      // Parse keywords
      const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);

      // Create request body
      const requestBody = {
        action: 'createArticle', // Add the required action field
        topic: topic.trim(),
        contentType: contentType.trim(),
        targetAudience: targetAudience.trim(),
        tone: tone.trim(),
        keywords: keywordArray
      };

      // Send request
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Failed to start collaboration: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setSessionId(data.sessionId);
        setSessionState(data.state);
        setError('');
      } else {
        setError(data.error || 'Failed to start collaboration');
      }
    } catch (err) {
      setError(`Error starting collaboration: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Render the form to start a new collaboration
  const renderForm = () => (
    <Card variant="outlined" sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Start New Collaboration
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box>
            <TextField
              label="Topic"
              fullWidth
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              required
              error={!topic && error.includes('Topic')}
              helperText={!topic && error.includes('Topic') ? 'Topic is required' : ''}
              disabled={loading}
            />
          </Box>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ flexGrow: 1, flexBasis: '45%', minWidth: '250px' }}>
              <TextField
                select
                label="Content Type"
                fullWidth
                value={contentType}
                onChange={(e) => setContentType(e.target.value)}
                disabled={loading}
              >
                <MenuItem value="blog-article">Blog Article</MenuItem>
                <MenuItem value="product-page">Product Page</MenuItem>
                <MenuItem value="buying-guide">Buying Guide</MenuItem>
              </TextField>
            </Box>
            <Box sx={{ flexGrow: 1, flexBasis: '45%', minWidth: '250px' }}>
              <TextField
                select
                label="Tone"
                fullWidth
                value={tone}
                onChange={(e) => setTone(e.target.value)}
                disabled={loading}
              >
                <MenuItem value="professional">Professional</MenuItem>
                <MenuItem value="conversational">Conversational</MenuItem>
                <MenuItem value="technical">Technical</MenuItem>
                <MenuItem value="persuasive">Persuasive</MenuItem>
              </TextField>
            </Box>
          </Box>
          <Box>
            <TextField
              label="Target Audience"
              fullWidth
              value={targetAudience}
              onChange={(e) => setTargetAudience(e.target.value)}
              required
              error={!targetAudience && error.includes('audience')}
              helperText={!targetAudience && error.includes('audience') ? 'Target audience is required' : ''}
              disabled={loading}
            />
          </Box>
          <Box>
            <TextField
              label="Keywords (comma separated)"
              fullWidth
              value={keywords}
              onChange={(e) => setKeywords(e.target.value)}
              placeholder="keyword1, keyword2, keyword3"
              disabled={loading}
            />
          </Box>
          <Box>
            <Button
              variant="contained"
              color="primary"
              onClick={startCollaboration}
              disabled={loading}
              fullWidth
            >
              {loading ? <CircularProgress size={24} /> : 'Start Collaboration'}
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  // Render session information
  const renderSessionInfo = () => {
    if (!sessionState) return null;

    return (
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              <Box sx={{ flexGrow: 1, flexBasis: '45%', minWidth: '250px' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Session ID
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {sessionState?.id || 'N/A'}
                </Typography>
              </Box>
              <Box sx={{ flexGrow: 1, flexBasis: '45%', minWidth: '250px' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Status
                </Typography>
                <Chip
                  label={sessionState?.status || 'unknown'}
                  color={
                    sessionState?.status === 'active' ? 'success' :
                    sessionState?.status === 'completed' ? 'primary' :
                    sessionState?.status === 'failed' ? 'error' : 'default'
                  }
                  size="small"
                />
              </Box>
            </Box>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              <Box sx={{ flexGrow: 1, flexBasis: '45%', minWidth: '250px' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Topic
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {sessionState?.topic || 'N/A'}
                </Typography>
              </Box>
              <Box sx={{ flexGrow: 1, flexBasis: '45%', minWidth: '250px' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Content Type
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {sessionState?.contentType || 'N/A'}
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              <Box sx={{ flexGrow: 1, flexBasis: '45%', minWidth: '250px' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Target Audience
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {sessionState?.targetAudience || 'N/A'}
                </Typography>
              </Box>
              <Box sx={{ flexGrow: 1, flexBasis: '45%', minWidth: '250px' }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Current Phase
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {sessionState?.currentPhase || 'Not started'}
                </Typography>
              </Box>
            </Box>
            <Box>
              <Typography variant="subtitle2" color="text.secondary">
                Keywords
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {Array.isArray(sessionState?.keywords) && sessionState.keywords.length > 0 ? (
                  sessionState.keywords.map((keyword: string, index: number) => (
                    <Chip key={index} label={keyword} size="small" />
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">No keywords specified</Typography>
                )}
              </Box>
            </Box>
            <Box>
              <Typography variant="subtitle2" color="text.secondary">
                Progress
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Iterations: {sessionState?.iterations || 0} / {sessionState?.maxIterations || 5}
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={fetchSessionState}
                  disabled={loading}
                >
                  Refresh
                </Button>
              </Box>
            </Box>
          </Box>
        </CardContent>
      </Card>
    );
  };

  // Render reasoning data using ReasoningDisplay
  const renderReasoningData = () => {
    console.log("Reasoning Data: ", sessionState.reasoning);
    return (<p>test</p>)
    // if (!sessionState || !Array.isArray(sessionState.reasoning) || sessionState.reasoning.length === 0) {
    //   return (
    //     <Paper sx={{ padding: 2, marginTop: 2 }}>
    //       No reasoning data available yet. As agents make decisions, their reasoning will appear here.
    //     </Paper>
    //   );
    // }

    // // Debugging: Log the reasoning data to ensure it's structured correctly
    // console.log("Reasoning Data: ", sessionState.reasoning);

    // return <ReasoningDisplay reasoning={sessionState.reasoning} />;
  };

  // Render the main content
  const renderContent = () => {
    if (!sessionState) {
      return renderForm();
    }

    return (
      <>
        {renderSessionInfo()}

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="collaboration tabs">
            <Tab label="Iterations" />
            <Tab label="Consultations" />
            <Tab label="Agent States" />
            <Tab label="Content Preview" />
            <Tab label="Agent Reasoning" />
          </Tabs>
        </Box>

        {/* {activeTab === 0 && (
          <IterationVisualizer
            artifacts={sessionState.artifacts && typeof sessionState.artifacts === 'object' ? Object.values(sessionState.artifacts) : []}
          />
        )}

        {activeTab === 1 && (
          <ConsultationTracker
            consultations={sessionState.consultations && typeof sessionState.consultations === 'object' ? Object.values(sessionState.consultations) : []}
            artifacts={sessionState.artifacts && typeof sessionState.artifacts === 'object' ? sessionState.artifacts : {}}
          />
        )}

        {activeTab === 2 && (
          <AgentStateVisualizer
            agentStates={sessionState.agentStates && typeof sessionState.agentStates === 'object' ? Object.values(sessionState.agentStates) : []}
            artifacts={sessionState.artifacts && typeof sessionState.artifacts === 'object' ? sessionState.artifacts : {}}
            consultations={sessionState.consultations && typeof sessionState.consultations === 'object' ? sessionState.consultations : {}}
          />
        )}

        {activeTab === 3 && (
          <ContentPreview
            artifacts={sessionState.artifacts && typeof sessionState.artifacts === 'object' ? Object.values(sessionState.artifacts) : []}
            contentType={sessionState.contentType || 'blog-article'}
          />
        )}

        {activeTab === 4 && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              Agent Reasoning Processes
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              This visualization shows the chain-of-thought reasoning process for each agent, including the Enhanced SEO Agent.
            </Typography>
            {renderReasoningData()}
          </Box>
        )} */}
      </>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Iterative Agent Collaboration Not Working
      </Typography>

      {error && (
        <Paper
          sx={{
            p: 2,
            mb: 3,
            bgcolor: 'error.light',
            color: 'error.contrastText'
          }}
        >
          <Typography variant="body1">{error}</Typography>
        </Paper>
      )}

      {loading && !sessionState && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {renderContent()}
    </Box>
  );
};

export default IterativeCollaborationDashboard;
