'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Chip,
  LinearProgress,
  CircularProgress,
  Tooltip,
  IconButton,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  useTheme
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import InfoIcon from '@mui/icons-material/Info';
import PendingIcon from '@mui/icons-material/Pending';
import ErrorIcon from '@mui/icons-material/Error';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import RefreshIcon from '@mui/icons-material/Refresh';

// Import types from our V3 implementation
import {
  CollaborationState,
  Goal,
  GoalStatus,
  GoalType
} from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface GoalProgressTrackerV3Props {
  sessionId: string;
  state: CollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const GoalProgressTrackerV3: React.FC<GoalProgressTrackerV3Props> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [expandedGoals, setExpandedGoals] = useState<Record<string, boolean>>({});
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Toggle goal expansion
  const toggleGoalExpansion = (goalId: string) => {
    setExpandedGoals({
      ...expandedGoals,
      [goalId]: !expandedGoals[goalId]
    });
  };

  // Open goal details dialog
  const openGoalDetails = (goal: Goal) => {
    setSelectedGoal(goal);
    setDialogOpen(true);
  };

  // Close goal details dialog
  const closeGoalDetails = () => {
    setDialogOpen(false);
  };

  // Get status icon
  const getStatusIcon = (status: GoalStatus) => {
    switch (status) {
      case GoalStatus.COMPLETED:
        return <CheckCircleIcon color="success" />;
      case GoalStatus.IN_PROGRESS:
        return <HourglassEmptyIcon color="primary" />;
      case GoalStatus.BLOCKED:
        return <ErrorIcon color="error" />;
      case GoalStatus.FAILED:
        return <ErrorIcon color="error" />;
      case GoalStatus.PENDING:
      default:
        return <PendingIcon color="disabled" />;
    }
  };

  // Get goal type display name
  const getGoalTypeDisplay = (type: GoalType): string => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Calculate overall progress
  const calculateOverallProgress = (): number => {
    if (!state || !state.goals || !state.goals.byId || Object.keys(state.goals.byId).length === 0) {
      return 0;
    }

    const goals = Object.values(state.goals.byId);
    const totalProgress = goals.reduce((sum, goal) => sum + goal.progress, 0);
    return Math.round(totalProgress / goals.length);
  };

  // Group goals by status
  const groupGoalsByStatus = (): Record<GoalStatus, Goal[]> => {
    const grouped: Record<GoalStatus, Goal[]> = {
      [GoalStatus.COMPLETED]: [],
      [GoalStatus.IN_PROGRESS]: [],
      [GoalStatus.PENDING]: [],
      [GoalStatus.BLOCKED]: [],
      [GoalStatus.FAILED]: []
    };

    if (!state || !state.goals || !state.goals.byId) {
      return grouped;
    }

    Object.values(state.goals.byId).forEach(goal => {
      grouped[goal.status].push(goal);
    });

    return grouped;
  };

  const groupedGoals = groupGoalsByStatus();
  const overallProgress = calculateOverallProgress();

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Goal Progress</Typography>
        {onRefresh && (
          <Tooltip title="Refresh">
            <IconButton onClick={onRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : !state || !state.goals || !state.goals.byId || Object.keys(state.goals.byId).length === 0 ? (
        <Box sx={{ textAlign: 'center', p: 4 }}>
          <Typography variant="body1" color="text.secondary">
            No goals defined yet
          </Typography>
        </Box>
      ) : (
        <>
          {/* Overall progress */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">Overall Progress</Typography>
              <Typography variant="body2" color="text.secondary">{overallProgress}%</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={overallProgress}
              sx={{ height: 10, borderRadius: 5 }}
            />
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* In Progress Goals */}
          {groupedGoals[GoalStatus.IN_PROGRESS].length > 0 && (
            <>
              <Typography variant="subtitle1" gutterBottom>
                In Progress
              </Typography>
              <List dense disablePadding>
                {groupedGoals[GoalStatus.IN_PROGRESS].map(goal => (
                  <React.Fragment key={goal.id}>
                    <ListItem
                      sx={{
                        bgcolor: 'action.hover',
                        borderRadius: 1,
                        mb: 1
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        {getStatusIcon(goal.status)}
                      </ListItemIcon>
                      <ListItemText
                        primary={goal.description}
                        secondary={
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <LinearProgress
                              variant="determinate"
                              value={goal.progress}
                              sx={{ flexGrow: 1, mr: 1, height: 6, borderRadius: 3 }}
                            />
                            <Typography variant="caption">{goal.progress}%</Typography>
                          </Box>
                        }
                      />
                      <Box sx={{ display: 'flex' }}>
                        <Tooltip title="View Details">
                          <IconButton size="small" onClick={() => openGoalDetails(goal)}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <IconButton size="small" onClick={() => toggleGoalExpansion(goal.id)}>
                          {expandedGoals[goal.id] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      </Box>
                    </ListItem>
                    <Collapse in={expandedGoals[goal.id]} timeout="auto" unmountOnExit>
                      <Box sx={{ pl: 4, pr: 2, pb: 1, pt: 0.5 }}>
                        <Typography variant="caption" display="block">
                          <strong>Type:</strong> {getGoalTypeDisplay(goal.type)}
                        </Typography>
                        {goal.assignedTo && (
                          <Typography variant="caption" display="block">
                            <strong>Assigned to:</strong> {goal.assignedTo}
                          </Typography>
                        )}
                        {goal.startTime && (
                          <Typography variant="caption" display="block">
                            <strong>Started:</strong> {new Date(goal.startTime).toLocaleString()}
                          </Typography>
                        )}
                        {goal.criteria && goal.criteria.length > 0 && (
                          <Box sx={{ mt: 0.5 }}>
                            <Typography variant="caption" display="block">
                              <strong>Criteria:</strong>
                            </Typography>
                            <List dense disablePadding>
                              {goal.criteria.map((criterion, index) => (
                                <ListItem key={index} sx={{ py: 0 }}>
                                  <ListItemIcon sx={{ minWidth: 24 }}>
                                    <RadioButtonUncheckedIcon fontSize="small" />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={
                                      <Typography variant="caption">{criterion}</Typography>
                                    }
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        )}
                      </Box>
                    </Collapse>
                  </React.Fragment>
                ))}
              </List>
            </>
          )}

          {/* Completed Goals */}
          {groupedGoals[GoalStatus.COMPLETED].length > 0 && (
            <>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Completed
              </Typography>
              <List dense disablePadding>
                {groupedGoals[GoalStatus.COMPLETED].map(goal => (
                  <ListItem
                    key={goal.id}
                    sx={{
                      bgcolor: 'success.light',
                      borderRadius: 1,
                      mb: 1,
                      opacity: 0.8
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      {getStatusIcon(goal.status)}
                    </ListItemIcon>
                    <ListItemText
                      primary={goal.description}
                      secondary={getGoalTypeDisplay(goal.type)}
                    />
                    <Tooltip title="View Details">
                      <IconButton size="small" onClick={() => openGoalDetails(goal)}>
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </ListItem>
                ))}
              </List>
            </>
          )}

          {/* Pending Goals */}
          {groupedGoals[GoalStatus.PENDING].length > 0 && (
            <>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Pending
              </Typography>
              <List dense disablePadding>
                {groupedGoals[GoalStatus.PENDING].map(goal => (
                  <ListItem
                    key={goal.id}
                    sx={{
                      bgcolor: 'grey.100',
                      borderRadius: 1,
                      mb: 1
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      {getStatusIcon(goal.status)}
                    </ListItemIcon>
                    <ListItemText
                      primary={goal.description}
                      secondary={getGoalTypeDisplay(goal.type)}
                    />
                    <Tooltip title="View Details">
                      <IconButton size="small" onClick={() => openGoalDetails(goal)}>
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </ListItem>
                ))}
              </List>
            </>
          )}

          {/* Blocked/Failed Goals */}
          {(groupedGoals[GoalStatus.BLOCKED].length > 0 || groupedGoals[GoalStatus.FAILED].length > 0) && (
            <>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Issues
              </Typography>
              <List dense disablePadding>
                {[...groupedGoals[GoalStatus.BLOCKED], ...groupedGoals[GoalStatus.FAILED]].map(goal => (
                  <ListItem
                    key={goal.id}
                    sx={{
                      bgcolor: 'error.light',
                      borderRadius: 1,
                      mb: 1
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      {getStatusIcon(goal.status)}
                    </ListItemIcon>
                    <ListItemText
                      primary={goal.description}
                      secondary={`${getGoalTypeDisplay(goal.type)} (${goal.status})`}
                    />
                    <Tooltip title="View Details">
                      <IconButton size="small" onClick={() => openGoalDetails(goal)}>
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </ListItem>
                ))}
              </List>
            </>
          )}
        </>
      )}

      {/* Goal Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={closeGoalDetails}
        maxWidth="sm"
        fullWidth
      >
        {selectedGoal && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ mr: 1 }}>
                  {getStatusIcon(selectedGoal.status)}
                </Box>
                Goal Details
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="h6">{selectedGoal.description}</Typography>
                  <Chip
                    label={getGoalTypeDisplay(selectedGoal.type)}
                    color="primary"
                    size="small"
                    sx={{ mt: 1 }}
                  />
                  <Chip
                    label={selectedGoal.status}
                    color={
                      selectedGoal.status === GoalStatus.COMPLETED ? 'success' :
                      selectedGoal.status === GoalStatus.IN_PROGRESS ? 'primary' :
                      selectedGoal.status === GoalStatus.BLOCKED || selectedGoal.status === GoalStatus.FAILED ? 'error' :
                      'default'
                    }
                    size="small"
                    sx={{ mt: 1, ml: 1 }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2">Progress</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={selectedGoal.progress}
                      sx={{ flexGrow: 1, mr: 1, height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="body2">{selectedGoal.progress}%</Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Created</Typography>
                  <Typography variant="body2">
                    {new Date(selectedGoal.createdAt).toLocaleString()}
                  </Typography>
                </Grid>

                {selectedGoal.startTime && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Started</Typography>
                    <Typography variant="body2">
                      {new Date(selectedGoal.startTime).toLocaleString()}
                    </Typography>
                  </Grid>
                )}

                {selectedGoal.completedAt && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Completed</Typography>
                    <Typography variant="body2">
                      {new Date(selectedGoal.completedAt).toLocaleString()}
                    </Typography>
                  </Grid>
                )}

                {selectedGoal.assignedTo && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Assigned To</Typography>
                    <Typography variant="body2">{selectedGoal.assignedTo}</Typography>
                  </Grid>
                )}

                {selectedGoal.criteria && selectedGoal.criteria.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">Success Criteria</Typography>
                    <List dense>
                      {selectedGoal.criteria.map((criterion, index) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 30 }}>
                            <RadioButtonUncheckedIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText primary={criterion} />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}

                {selectedGoal.dependencies && selectedGoal.dependencies.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">Dependencies</Typography>
                    <Typography variant="body2">
                      {selectedGoal.dependencies.join(', ')}
                    </Typography>
                  </Grid>
                )}

                {selectedGoal.reasoning && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">Reasoning</Typography>
                    <Paper variant="outlined" sx={{ p: 1.5, mt: 0.5, bgcolor: 'grey.50' }}>
                      <Typography variant="body2">
                        <strong>Decision:</strong> {selectedGoal.reasoning.decision}
                      </Typography>
                      {selectedGoal.reasoning.thoughts && (
                        <>
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            <strong>Thoughts:</strong>
                          </Typography>
                          <List dense disablePadding>
                            {selectedGoal.reasoning.thoughts.map((thought, index) => (
                              <ListItem key={index} sx={{ py: 0.5 }}>
                                <ListItemIcon sx={{ minWidth: 24 }}>
                                  <RadioButtonUncheckedIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText
                                  primary={
                                    <Typography variant="body2">{thought}</Typography>
                                  }
                                />
                              </ListItem>
                            ))}
                          </List>
                        </>
                      )}
                      {selectedGoal.reasoning.confidence !== undefined && (
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          <strong>Confidence:</strong> {Math.round(selectedGoal.reasoning.confidence * 100)}%
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={closeGoalDetails}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Paper>
  );
};

export default GoalProgressTrackerV3;
