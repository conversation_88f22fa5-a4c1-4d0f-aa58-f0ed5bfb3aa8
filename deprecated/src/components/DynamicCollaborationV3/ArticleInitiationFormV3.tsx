'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Autocomplete,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  StepContent
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import TemplateSelector from '../GoalBasedCollaboration/TemplateSelector';

// Define content types
type ContentType = 'blog-article' | 'product-page' | 'buying-guide' | 'how-to-guide' | 'listicle' | 'case-study' | 'opinion-piece' | 'technical-tutorial';

// Define tone options
const toneOptions = [
  'professional',
  'conversational',
  'informative',
  'persuasive',
  'enthusiastic',
  'technical',
  'friendly',
  'authoritative'
];

// Define audience options
const audienceOptions = [
  'general',
  'professionals',
  'executives',
  'technical experts',
  'beginners',
  'students',
  'researchers',
  'entrepreneurs'
];

// Form data interface
interface ArticleFormData {
  topic: string;
  contentType: ContentType;
  targetAudience: string;
  tone: string;
  keywords: string[];
  additionalInstructions?: string;
  referenceUrls?: string[];
  templateId?: string;
}

interface ArticleInitiationFormV3Props {
  onSubmit: (formData: ArticleFormData) => Promise<void>;
  loading?: boolean;
}

const ArticleInitiationFormV3: React.FC<ArticleInitiationFormV3Props> = ({
  onSubmit,
  loading = false
}) => {
  // Form state
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<ArticleFormData>({
    topic: '',
    contentType: 'blog-article',
    targetAudience: 'general',
    tone: 'professional',
    keywords: [],
    additionalInstructions: '',
    referenceUrls: [],
    templateId: undefined
  });
  const [formErrors, setFormErrors] = useState<Partial<Record<keyof ArticleFormData, string>>>({});
  const [keywordInput, setKeywordInput] = useState('');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);

  // Handle form field changes
  const handleChange = (field: keyof ArticleFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });

    // Clear error for this field if it exists
    if (formErrors[field]) {
      setFormErrors({
        ...formErrors,
        [field]: undefined
      });
    }
  };

  // Add a keyword
  const handleAddKeyword = () => {
    if (keywordInput.trim() && !formData.keywords.includes(keywordInput.trim())) {
      handleChange('keywords', [...formData.keywords, keywordInput.trim()]);
      setKeywordInput('');
    }
  };

  // Handle keyword input keydown
  const handleKeywordKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddKeyword();
    }
  };

  // Remove a keyword
  const handleRemoveKeyword = (keyword: string) => {
    handleChange('keywords', formData.keywords.filter(k => k !== keyword));
  };

  // Handle template selection
  const handleTemplateSelect = (templateId: string, customParams?: any) => {
    handleChange('templateId', templateId);

    // Update content type based on template
    handleChange('contentType', templateId as ContentType);

    // Apply custom parameters if provided
    if (customParams) {
      if (customParams.additionalInstructions) {
        handleChange('additionalInstructions', customParams.additionalInstructions);
      }
      if (customParams.referenceUrls) {
        handleChange('referenceUrls', customParams.referenceUrls);
      }
    }

    setShowTemplateSelector(false);
  };

  // Validate form data
  const validateForm = (): boolean => {
    const errors: Partial<Record<keyof ArticleFormData, string>> = {};

    if (!formData.templateId) {
      errors.templateId = 'Please select a template';
    }

    if (!formData.topic.trim()) {
      errors.topic = 'Topic is required';
    }

    if (!formData.targetAudience) {
      errors.targetAudience = 'Target audience is required';
    }

    if (formData.keywords.length === 0) {
      errors.keywords = 'At least one keyword is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      await onSubmit(formData);
    }
  };

  // Get template name for display
  const getTemplateName = (templateId: string) => {
    const templateNames: Record<string, string> = {
      'blog-article': 'SEO Blog Article',
      'how-to-guide': 'How-To Guide',
      'listicle': 'Listicle',
      'case-study': 'Case Study',
      'opinion-piece': 'Opinion Piece',
      'technical-tutorial': 'Technical Tutorial',
      'product-page': 'Product Page',
      'buying-guide': 'Buying Guide'
    };
    return templateNames[templateId] || templateId;
  };

  // Steps for the form
  const steps = [
    {
      label: 'Choose Template',
      description: 'Select the type of content you want to create',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box sx={{
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              p: 2,
              backgroundColor: formData.templateId ? 'action.selected' : 'background.paper'
            }}>
              {formData.templateId ? (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Selected Template: {getTemplateName(formData.templateId)}
                  </Typography>
                  <Button
                    variant="outlined"
                    onClick={() => setShowTemplateSelector(true)}
                    disabled={loading}
                  >
                    Change Template
                  </Button>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="body1" gutterBottom>
                    Choose a template to get started
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => setShowTemplateSelector(true)}
                    disabled={loading}
                  >
                    Select Template
                  </Button>
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>
      )
    },
    {
      label: 'Basic Information',
      description: 'Define the core details of your article',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Article Topic"
              value={formData.topic}
              onChange={(e) => handleChange('topic', e.target.value)}
              error={!!formErrors.topic}
              helperText={formErrors.topic || 'Enter the main topic for your article'}
              placeholder="e.g., Benefits of AI in Content Creation"
              required
              disabled={loading}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Content Type</InputLabel>
              <Select
                value={formData.contentType}
                onChange={(e) => handleChange('contentType', e.target.value)}
                label="Content Type"
                disabled={loading}
              >
                <MenuItem value="blog-article">SEO Blog Article</MenuItem>
                <MenuItem value="how-to-guide">How-To Guide</MenuItem>
                <MenuItem value="listicle">Listicle</MenuItem>
                <MenuItem value="case-study">Case Study</MenuItem>
                <MenuItem value="opinion-piece">Opinion Piece</MenuItem>
                <MenuItem value="technical-tutorial">Technical Tutorial</MenuItem>
                <MenuItem value="product-page">Product Page</MenuItem>
                <MenuItem value="buying-guide">Buying Guide</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      )
    },
    {
      label: 'Audience & Tone',
      description: 'Define who you\'re writing for and how you want to sound',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Autocomplete
              freeSolo
              options={audienceOptions}
              value={formData.targetAudience}
              onChange={(_, newValue) => handleChange('targetAudience', newValue || '')}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Target Audience"
                  error={!!formErrors.targetAudience}
                  helperText={formErrors.targetAudience || 'Who is this content for?'}
                  required
                />
              )}
              disabled={loading}
            />
          </Grid>
          <Grid item xs={12}>
            <Autocomplete
              freeSolo
              options={toneOptions}
              value={formData.tone}
              onChange={(_, newValue) => handleChange('tone', newValue || '')}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tone"
                  helperText="How should the content sound?"
                />
              )}
              disabled={loading}
            />
          </Grid>
        </Grid>
      )
    },
    {
      label: 'Keywords & Instructions',
      description: 'Add keywords and any additional instructions',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Add Keyword"
              value={keywordInput}
              onChange={(e) => setKeywordInput(e.target.value)}
              onKeyDown={handleKeywordKeyDown}
              InputProps={{
                endAdornment: (
                  <Button
                    onClick={handleAddKeyword}
                    disabled={!keywordInput.trim() || loading}
                  >
                    Add
                  </Button>
                )
              }}
              error={!!formErrors.keywords}
              helperText={formErrors.keywords || 'Press Enter or click Add to add a keyword'}
              disabled={loading}
            />
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {formData.keywords.map((keyword) => (
                <Chip
                  key={keyword}
                  label={keyword}
                  onDelete={() => handleRemoveKeyword(keyword)}
                  color="primary"
                  variant="outlined"
                  disabled={loading}
                />
              ))}
              {formData.keywords.length === 0 && (
                <Typography variant="body2" color="text.secondary">
                  No keywords added yet
                </Typography>
              )}
            </Box>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Additional Instructions"
              value={formData.additionalInstructions}
              onChange={(e) => handleChange('additionalInstructions', e.target.value)}
              multiline
              rows={4}
              helperText="Any specific requirements or instructions (optional)"
              disabled={loading}
            />
          </Grid>
        </Grid>
      )
    }
  ];

  // Handle next step
  const handleNext = () => {
    if (activeStep === 0 && !formData.templateId) {
      setFormErrors({
        ...formErrors,
        templateId: 'Please select a template'
      });
      return;
    }

    if (activeStep === 1 && !formData.topic.trim()) {
      setFormErrors({
        ...formErrors,
        topic: 'Topic is required'
      });
      return;
    }

    if (activeStep === 2 && !formData.targetAudience) {
      setFormErrors({
        ...formErrors,
        targetAudience: 'Target audience is required'
      });
      return;
    }

    if (activeStep === steps.length - 1) {
      // Submit the form on the last step
      validateForm() && handleSubmit(new Event('submit') as unknown as React.FormEvent);
      return;
    }

    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        Create New Article
      </Typography>

      <Stepper activeStep={activeStep} orientation="vertical">
        {steps.map((step, index) => (
          <Step key={step.label}>
            <StepLabel>
              <Typography variant="subtitle1">{step.label}</Typography>
            </StepLabel>
            <StepContent>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {step.description}
              </Typography>

              {step.content}

              <Box sx={{ mb: 2, mt: 3 }}>
                <div>
                  <Button
                    disabled={activeStep === 0 || loading}
                    onClick={handleBack}
                    sx={{ mr: 1 }}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    disabled={loading}
                    endIcon={activeStep === steps.length - 1 ? <PlayArrowIcon /> : undefined}
                  >
                    {activeStep === steps.length - 1 ? 'Start Collaboration' : 'Continue'}
                  </Button>
                  {loading && (
                    <CircularProgress size={24} sx={{ ml: 2 }} />
                  )}
                </div>
              </Box>
            </StepContent>
          </Step>
        ))}
      </Stepper>

      {activeStep === steps.length && (
        <Paper square elevation={0} sx={{ p: 3 }}>
          <Typography>All steps completed - starting collaboration...</Typography>
          <CircularProgress size={24} sx={{ mt: 2 }} />
        </Paper>
      )}

      <TemplateSelector
        open={showTemplateSelector}
        onClose={() => setShowTemplateSelector(false)}
        onTemplateSelect={handleTemplateSelect}
      />
    </Paper>
  );
};

export default ArticleInitiationFormV3;
