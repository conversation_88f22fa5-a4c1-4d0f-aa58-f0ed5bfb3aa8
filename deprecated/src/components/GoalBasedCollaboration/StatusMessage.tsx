import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Chip, Alert, AlertTitle } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import PendingIcon from '@mui/icons-material/Pending';
import { WorkflowPhase, GoalStatus } from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface StatusMessageProps {
  state: any;
  loading?: boolean;
}

const StatusMessage: React.FC<StatusMessageProps> = ({
  state,
  loading = false
}) => {
  const [message, setMessage] = useState<string>('');
  const [severity, setSeverity] = useState<'info' | 'success' | 'warning' | 'error'>('info');

  useEffect(() => {
    if (loading) {
      setMessage('Loading session data...');
      setSeverity('info');
      return;
    }

    if (!state) {
      setMessage('No session data available.');
      setSeverity('warning');
      return;
    }

    // Determine the current status based on state
    const currentPhase = state.workflowProgress?.currentPhase || WorkflowPhase.PLANNING;
    const overallProgress = state.workflowProgress?.overallProgress || 0;
    const activeGoals = state.goals?.activeIds?.length || 0;
    const completedGoals = state.goals?.completedIds?.length || 0;
    const totalGoals = Object.keys(state.goals?.byId || {}).length;
    const hasErrors = state.errors && state.errors.length > 0;

    if (hasErrors) {
      setMessage(`Errors detected in the workflow. Using fallback processing.`);
      setSeverity('error');
    } else if (overallProgress >= 100) {
      setMessage(`Workflow complete! All goals have been achieved.`);
      setSeverity('success');
    } else if (activeGoals === 0 && totalGoals > 0 && completedGoals < totalGoals) {
      setMessage(`Waiting for the next phase. Click "Progress Session" to continue.`);
      setSeverity('warning');
    } else if (activeGoals > 0) {
      setMessage(`Processing ${activeGoals} active goals in the ${currentPhase} phase. Overall progress: ${overallProgress}%`);
      setSeverity('info');
    } else if (totalGoals === 0) {
      setMessage(`Initializing workflow. Goals will be created soon.`);
      setSeverity('info');
    } else {
      setMessage(`In ${currentPhase} phase. Overall progress: ${overallProgress}%`);
      setSeverity('info');
    }
  }, [state, loading]);

  if (!message) {
    return null;
  }

  return (
    <Alert 
      severity={severity}
      icon={
        severity === 'success' ? <CheckCircleIcon /> :
        severity === 'error' ? <ErrorIcon /> :
        severity === 'warning' ? <PendingIcon /> :
        <InfoIcon />
      }
      sx={{ mb: 2 }}
    >
      <AlertTitle>
        {severity === 'success' ? 'Success' :
         severity === 'error' ? 'Error' :
         severity === 'warning' ? 'Attention Needed' :
         'Status Update'}
      </AlertTitle>
      {message}
      {state && state.workflowProgress && (
        <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip 
            size="small" 
            label={`Phase: ${state.workflowProgress.currentPhase}`} 
            color="primary"
          />
          <Chip 
            size="small" 
            label={`Progress: ${state.workflowProgress.overallProgress}%`} 
            color="secondary"
          />
          {state.goals && (
            <Chip 
              size="small" 
              label={`Goals: ${Object.keys(state.goals.byId || {}).length} total, ${state.goals.completedIds?.length || 0} completed`} 
              color="default"
            />
          )}
        </Box>
      )}
    </Alert>
  );
};

export default StatusMessage;
