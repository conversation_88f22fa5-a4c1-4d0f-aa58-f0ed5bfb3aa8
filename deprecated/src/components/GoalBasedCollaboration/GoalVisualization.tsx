import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Chip, LinearProgress, Grid,
  Card, CardContent, Divider, IconButton, Tooltip,
  Accordion, AccordionSummary, AccordionDetails, Button,
  CircularProgress
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import BlockIcon from '@mui/icons-material/Block';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import RefreshIcon from '@mui/icons-material/Refresh';
import { GoalStatus, GoalType } from '../../app/(payload)/api/agents/dynamic-collaboration-v3';
import { goalOrchestratorClient } from '../../lib/goal-orchestrator-client';

interface GoalVisualizationProps {
  sessionId: string;
  state: any;
  loading?: boolean;
  onRefresh?: () => void;
}

const GoalVisualization: React.FC<GoalVisualizationProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const [goals, setGoals] = useState<any[]>([]);
  const [hierarchicalGoals, setHierarchicalGoals] = useState<any>({});
  const [processingGoal, setProcessingGoal] = useState<string | null>(null);

  useEffect(() => {
    if (state) {
      console.log('Processing goals from state:', state);
      let goalsArray: any[] = [];

      // Handle new state structure
      if (state.goals?.byId) {
        goalsArray = Object.values(state.goals.byId);
        console.log('Found goals in byId structure:', goalsArray.length);
      } else if (state.goals) {
        // Handle old state structure
        goalsArray = Object.values(state.goals);
        console.log('Found goals in old structure:', goalsArray.length);
      }

      if (goalsArray.length === 0) {
        console.log('No goals found in state');
        setGoals([]);
        setHierarchicalGoals({});
        return;
      }

      // Sort by creation date
      goalsArray.sort((a: any, b: any) => {
        const aTime = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const bTime = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return aTime - bTime;
      });

      console.log('Sorted goals:', goalsArray);
      setGoals(goalsArray);

      // Create hierarchical structure
      // First, identify high-level goals
      const highLevelGoals = goalsArray.filter((g: any) =>
        g.type === GoalType.RESEARCH ||
        g.type === GoalType.CONTENT ||
        g.type === GoalType.QUALITY
      );

      console.log('High level goals:', highLevelGoals);

      // If no high-level goals found, create a default hierarchy with all goals
      if (highLevelGoals.length === 0) {
        console.log('No high-level goals found, creating default hierarchy');
        const defaultHierarchy: any = {
          'default': {
            goal: {
              id: 'default',
              description: 'All Goals',
              type: 'default',
              status: 'ACTIVE',
              progress: calculateOverallProgress(goalsArray),
              criteria: ['Complete all goals']
            },
            children: goalsArray
          }
        };
        setHierarchicalGoals(defaultHierarchy);
        return;
      }

      const hierarchy: any = {};

      highLevelGoals.forEach((highLevelGoal: any) => {
        // For each high-level goal, find its children
        // A child is a goal that has this high-level goal in its dependencies
        // If dependencies aren't defined, use a heuristic based on goal type
        const children = goalsArray.filter((g: any) => {
          // Check explicit dependencies first
          if (g.dependencies && Array.isArray(g.dependencies) && g.dependencies.includes(highLevelGoal.id)) {
            return true;
          }

          // If no explicit dependency, use type-based heuristic
          if (highLevelGoal.type === GoalType.RESEARCH) {
            return g.type === GoalType.MARKET_RESEARCH || g.type === GoalType.KEYWORD_ANALYSIS;
          } else if (highLevelGoal.type === GoalType.CONTENT) {
            return g.type === GoalType.CONTENT_STRATEGY || g.type === GoalType.CONTENT_CREATION;
          } else if (highLevelGoal.type === GoalType.QUALITY) {
            return g.type === GoalType.SEO_OPTIMIZATION || g.type === GoalType.QUALITY_ASSESSMENT;
          }

          return false;
        });

        hierarchy[highLevelGoal.id] = {
          goal: highLevelGoal,
          children: children
        };
      });

      console.log('Created hierarchy:', hierarchy);
      setHierarchicalGoals(hierarchy);
    }
  }, [state]);

  // Calculate overall progress for a set of goals
  const calculateOverallProgress = (goals: any[]): number => {
    if (goals.length === 0) return 0;

    const totalProgress = goals.reduce((sum, goal) => sum + (goal.progress || 0), 0);
    return Math.round(totalProgress / goals.length);
  };

  const getStatusColor = (status: GoalStatus) => {
    switch (status) {
      case GoalStatus.COMPLETED:
        return 'success';
      case GoalStatus.ACTIVE:
      case GoalStatus.IN_PROGRESS:
        return 'primary';
      case GoalStatus.PENDING:
        return 'default';
      case GoalStatus.BLOCKED:
      case GoalStatus.FAILED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: GoalStatus) => {
    switch (status) {
      case GoalStatus.COMPLETED:
        return <CheckCircleIcon fontSize="small" />;
      case GoalStatus.ACTIVE:
      case GoalStatus.IN_PROGRESS:
        return <PlayArrowIcon fontSize="small" />;
      case GoalStatus.PENDING:
        return <PendingIcon fontSize="small" />;
      case GoalStatus.BLOCKED:
      case GoalStatus.FAILED:
        return <BlockIcon fontSize="small" />;
      default:
        return null;
    }
  };

  // Process a goal to create an artifact
  const processGoal = async (goalId: string) => {
    if (!sessionId || processingGoal) return;

    setProcessingGoal(goalId);

    try {
      // Get the goal
      const goal = goals.find(g => g.id === goalId);
      if (!goal) {
        console.error('Goal not found:', goalId);
        return;
      }

      // Create an artifact for the goal
      const artifactType = getArtifactTypeForGoal(goal.type);
      const artifactTitle = `${goal.type} for ${state.topic}`;
      const artifactContent = {
        summary: `This is a ${goal.type} artifact for ${state.topic}`,
        details: `Generated based on the goal: ${goal.description}`,
        criteria: goal.criteria,
        timestamp: new Date().toISOString()
      };

      await goalOrchestratorClient.createArtifact(
        sessionId,
        goalId,
        artifactType,
        artifactTitle,
        artifactContent
      );

      // Refresh the data
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error processing goal:', error);
    } finally {
      setProcessingGoal(null);
    }
  };

  // Get artifact type for goal type
  const getArtifactTypeForGoal = (goalType: string): string => {
    switch (goalType) {
      case GoalType.RESEARCH:
        return 'research-summary';
      case GoalType.CONTENT:
        return 'content-draft';
      case GoalType.QUALITY:
        return 'quality-assessment';
      default:
        return 'generic-artifact';
    }
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Goal Hierarchy</Typography>
        {onRefresh && (
          <Tooltip title="Refresh">
            <IconButton onClick={onRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {loading ? (
        <LinearProgress variant="indeterminate" />
      ) : Object.keys(hierarchicalGoals).length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 2 }}>
          <Typography variant="body2" color="text.secondary" paragraph>
            No goals defined yet. The system is initializing or processing goals.
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            If goals don't appear after a few seconds, you can try to fix them or progress the session.
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={onRefresh}
              disabled={loading}
            >
              Refresh
            </Button>
            {state && state.goals && Object.keys(state.goals).length === 0 && (
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => {
                  if (onRefresh) {
                    console.log('Attempting to fix goals');
                    onRefresh();
                  }
                }}
                disabled={loading}
              >
                Fix Goals
              </Button>
            )}
          </Box>
        </Box>
      ) : (
        <Box>
          {Object.values(hierarchicalGoals).map((highLevelGoalData: any) => {
            const highLevelGoal = highLevelGoalData.goal;
            return (
              <Accordion key={highLevelGoal.id} defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                      {highLevelGoal.description}
                    </Typography>
                    <Chip
                      label={highLevelGoal.status}
                      color={getStatusColor(highLevelGoal.status) as any}
                      size="small"
                      icon={getStatusIcon(highLevelGoal.status)}
                      sx={{ ml: 1 }}
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2">Progress</Typography>
                      <Typography variant="body2">{highLevelGoal.progress}%</Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={highLevelGoal.progress}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>
                    Criteria:
                  </Typography>
                  <Box component="ul" sx={{ pl: 2, mt: 0 }}>
                    {highLevelGoal.criteria.map((criterion: string, index: number) => (
                      <Box component="li" key={index}>
                        <Typography variant="body2">
                          {criterion}
                        </Typography>
                      </Box>
                    ))}
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" gutterBottom>
                    Sub-goals:
                  </Typography>

                  {highLevelGoalData.children.length === 0 ? (
                    <Typography variant="body2" color="text.secondary">
                      No sub-goals defined yet
                    </Typography>
                  ) : (
                    <Grid container spacing={2}>
                      {highLevelGoalData.children.map((childGoal: any) => (
                        <Grid item xs={12} md={6} key={childGoal.id}>
                          <Card
                            variant="outlined"
                            sx={{
                              borderColor: getStatusColor(childGoal.status) === 'default'
                                ? 'divider'
                                : `${getStatusColor(childGoal.status)}.main`
                            }}
                          >
                            <CardContent>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="body1">
                                  {childGoal.description}
                                </Typography>
                                <Chip
                                  label={childGoal.status}
                                  color={getStatusColor(childGoal.status) as any}
                                  size="small"
                                  icon={getStatusIcon(childGoal.status)}
                                />
                              </Box>

                              <Box sx={{ mb: 2 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                  <Typography variant="body2">Progress</Typography>
                                  <Typography variant="body2">{childGoal.progress}%</Typography>
                                </Box>
                                <LinearProgress
                                  variant="determinate"
                                  value={childGoal.progress}
                                  sx={{ height: 6, borderRadius: 3 }}
                                />
                              </Box>

                              {childGoal.criteria && childGoal.criteria.length > 0 && (
                                <Box>
                                  <Typography variant="caption" color="text.secondary">
                                    Criteria:
                                  </Typography>
                                  <Box component="ul" sx={{ pl: 2, mt: 0 }}>
                                    {childGoal.criteria.map((criterion: string, index: number) => (
                                      <Box component="li" key={index}>
                                        <Typography variant="caption">
                                          {criterion}
                                        </Typography>
                                      </Box>
                                    ))}
                                  </Box>
                                </Box>
                              )}

                              {childGoal.artifactIds && childGoal.artifactIds.length > 0 && (
                                <Box sx={{ mt: 1 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Associated Artifacts: {childGoal.artifactIds.length}
                                  </Typography>
                                  {state.artifacts && childGoal.artifactIds.map((artifactId: string) => {
                                    const artifact = state.artifacts[artifactId];
                                    if (!artifact) return null;
                                    return (
                                      <Chip
                                        key={artifactId}
                                        label={artifact.status}
                                        size="small"
                                        color={
                                          artifact.status === 'approved' ? 'success' :
                                          artifact.status === 'review' ? 'warning' :
                                          artifact.status === 'rejected' ? 'error' :
                                          'default'
                                        }
                                        sx={{ ml: 1, mt: 0.5 }}
                                      />
                                    );
                                  })}
                                </Box>
                              )}

                              {childGoal.completedAt && (
                                <Box sx={{ mt: 1 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Completed: {new Date(childGoal.completedAt).toLocaleString()}
                                  </Typography>
                                </Box>
                              )}

                              {/* Show dependencies */}
                              {childGoal.dependencies && childGoal.dependencies.length > 0 && (
                                <Box sx={{ mt: 1 }}>
                                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                    Dependencies:
                                  </Typography>
                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                                    {childGoal.dependencies.map((depId: string) => {
                                      const depGoal = goals.find(g => g.id === depId);
                                      if (!depGoal) return null;

                                      const isCompleted = depGoal.status === GoalStatus.COMPLETED;

                                      return (
                                        <Chip
                                          key={depId}
                                          label={depGoal.description}
                                          size="small"
                                          icon={isCompleted ? <CheckCircleIcon fontSize="small" /> : <PendingIcon fontSize="small" />}
                                          color={isCompleted ? 'success' : 'warning'}
                                          variant="outlined"
                                          sx={{ fontSize: '0.7rem' }}
                                        />
                                      );
                                    })}
                                  </Box>
                                </Box>
                              )}

                              {/* Show blocked status if dependencies aren't completed */}
                              {childGoal.status === GoalStatus.PENDING && childGoal.dependencies && childGoal.dependencies.length > 0 && (
                                <Box sx={{ mt: 1 }}>
                                  {childGoal.dependencies.some(depId => {
                                    const depGoal = goals.find(g => g.id === depId);
                                    return depGoal && depGoal.status !== GoalStatus.COMPLETED;
                                  }) && (
                                    <Typography variant="caption" color="error" sx={{ display: 'flex', alignItems: 'center' }}>
                                      <BlockIcon fontSize="small" sx={{ mr: 0.5 }} />
                                      Waiting for dependencies to complete
                                    </Typography>
                                  )}
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  )}
                </AccordionDetails>
              </Accordion>
            );
          })}
        </Box>
      )}
    </Paper>
  );
};

export default GoalVisualization;
