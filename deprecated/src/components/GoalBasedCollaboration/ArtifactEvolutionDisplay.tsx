'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  Button,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  CircularProgress,
  useTheme
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import HistoryIcon from '@mui/icons-material/History';
import FeedbackIcon from '@mui/icons-material/Feedback';
import ChatIcon from '@mui/icons-material/Chat';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import Rating from '@mui/material/Rating';
import ReactMarkdown from 'react-markdown';
import { formatDistanceToNow } from 'date-fns';

interface ArtifactEvolutionDisplayProps {
  artifact: any;
  artifacts: Record<string, any>;
  sessionId: string;
}

/**
 * Component for displaying the evolution history of an artifact
 */
const ArtifactEvolutionDisplay: React.FC<ArtifactEvolutionDisplayProps> = ({
  artifact,
  artifacts,
  sessionId
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);
  const [selectedVersionId, setSelectedVersionId] = useState<string | null>(null);
  const [evolutionData, setEvolutionData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // Fetch evolution data when component mounts or artifact changes
  useEffect(() => {
    if (artifact && sessionId) {
      fetchEvolutionData();
    }
  }, [artifact?.id, sessionId]);

  // Fetch evolution data from the API
  const fetchEvolutionData = async () => {
    try {
      setLoading(true);

      // Import the client
      const { goalOrchestratorClient } = await import('../../lib/goal-orchestrator-client');

      // Fetch evolution data
      const data = await goalOrchestratorClient.getArtifactEvolutionData(sessionId, artifact.id);

      if (data && data.result) {
        setEvolutionData(data.result);
      }
    } catch (error) {
      console.error('Error fetching evolution data:', error);
    } finally {
      setLoading(false);
    }
  };

  // If no artifact, don't render
  if (!artifact) {
    return null;
  }

  // If loading, show loading indicator
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress size={24} />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Loading evolution history...
        </Typography>
      </Box>
    );
  }

  // Get evolution chain from fetched data or fallback to metadata
  const evolutionHistory = evolutionData?.evolutionHistory || [];
  const consultations = evolutionData?.consultations || artifact.metadata?.reasoningProcess?.consultations || [];
  const feedbackCycles = evolutionData?.feedbackCycles || [];
  const evolutionTimeline = evolutionData?.evolutionTimeline || [];

  // If no evolution data and no metadata, show message
  if (evolutionHistory.length === 0 && !artifact.previousVersionId && consultations.length === 0 && feedbackCycles.length === 0) {
    return (
      <Paper variant="outlined" sx={{ p: 2, mt: 2, bgcolor: 'background.default' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <InfoIcon color="info" sx={{ mr: 1 }} />
          <Typography variant="body2">
            This is the first version of this artifact. No evolution history available.
          </Typography>
        </Box>
      </Paper>
    );
  }

  // Build the complete evolution chain including the current artifact
  const artifactIds = evolutionHistory.map((item: any) => item.id);

  // Add the current artifact if not already in the chain
  if (!artifactIds.includes(artifact.id)) {
    artifactIds.unshift(artifact.id);
  }

  // Sort the chain by creation date if possible
  const sortedChain = [...artifactIds];

  // Render the evolution timeline
  const renderEvolutionTimeline = () => {
    if (!evolutionTimeline || evolutionTimeline.length === 0) {
      // Fallback to basic timeline if no evolution timeline data
      if (sortedChain.length === 0) return null;

      return (
        <Timeline position="alternate" sx={{ p: 0 }}>
          {sortedChain.map((artifactId, index) => {
            const versionArtifact = artifacts[artifactId];
            if (!versionArtifact) return null;

            const isSelected = selectedVersionId === artifactId;
            const isLast = index === sortedChain.length - 1;
            const timeAgo = formatDistanceToNow(new Date(versionArtifact.createdAt), { addSuffix: true });

            return (
              <TimelineItem key={artifactId}>
                <TimelineOppositeContent color="text.secondary">
                  <Typography variant="caption">{timeAgo}</Typography>
                  <Typography variant="caption" display="block">
                    Version {index + 1}
                  </Typography>
                </TimelineOppositeContent>
                <TimelineSeparator>
                  <TimelineDot
                    color={isLast ? "primary" : "grey"}
                    variant={isLast ? "filled" : "outlined"}
                  />
                  {!isLast && <TimelineConnector />}
                </TimelineSeparator>
                <TimelineContent>
                  <Card
                    variant="outlined"
                    sx={{
                      mb: 1,
                      cursor: 'pointer',
                      border: isSelected ? `2px solid ${theme.palette.primary.main}` : undefined,
                      '&:hover': {
                        boxShadow: 1
                      }
                    }}
                    onClick={() => setSelectedVersionId(isSelected ? null : artifactId)}
                  >
                    <CardHeader
                      title={
                        <Typography variant="subtitle2" noWrap>
                          {versionArtifact.title}
                        </Typography>
                      }
                      subheader={
                        <Typography variant="caption" color="text.secondary">
                          By: {versionArtifact.createdBy}
                        </Typography>
                      }
                      action={
                        <Chip
                          size="small"
                          label={versionArtifact.status}
                          color={
                            versionArtifact.status === 'approved' ? 'success' :
                            versionArtifact.status === 'rejected' ? 'error' :
                            'default'
                          }
                        />
                      }
                    />
                    {isSelected && (
                      <CardContent>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Content Preview:
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 1, maxHeight: 200, overflow: 'auto' }}>
                          {typeof versionArtifact.content === 'string' ? (
                            <ReactMarkdown>{versionArtifact.content.substring(0, 300)}</ReactMarkdown>
                          ) : (
                            <Typography variant="body2">
                              {JSON.stringify(versionArtifact.content).substring(0, 300)}...
                            </Typography>
                          )}
                        </Paper>

                        {versionArtifact.metadata?.reasoningProcess?.considerations?.length > 0 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Considerations:
                            </Typography>
                            <List dense>
                              {versionArtifact.metadata.reasoningProcess.considerations.map((consideration: string, i: number) => (
                                <ListItem key={i} sx={{ py: 0 }}>
                                  <ListItemIcon sx={{ minWidth: 24 }}>
                                    <InfoIcon fontSize="small" color="info" />
                                  </ListItemIcon>
                                  <ListItemText primary={consideration} />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        )}
                      </CardContent>
                    )}
                  </Card>
                </TimelineContent>
              </TimelineItem>
            );
          })}
        </Timeline>
      );
    }

    // Render comprehensive evolution timeline
    return (
      <Timeline position="alternate" sx={{ p: 0 }}>
        {evolutionTimeline.map((event: any, index: number) => {
          const timeAgo = formatDistanceToNow(new Date(event.timestamp), { addSuffix: true });

          // Determine icon and color based on event type
          let icon = <HistoryIcon />;
          let color: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning' | 'grey' = 'grey';
          let title = '';
          let content = null;

          switch (event.type) {
            case 'artifact_version':
              icon = <HistoryIcon />;
              color = 'primary';
              title = `Artifact Version: ${event.data.title}`;

              const artifactId = event.data.artifactId;
              const isSelected = selectedVersionId === artifactId;
              const versionArtifact = artifacts[artifactId];

              content = (
                <Card
                  variant="outlined"
                  sx={{
                    mb: 1,
                    cursor: 'pointer',
                    border: isSelected ? `2px solid ${theme.palette.primary.main}` : undefined,
                    '&:hover': {
                      boxShadow: 1
                    }
                  }}
                  onClick={() => setSelectedVersionId(isSelected ? null : artifactId)}
                >
                  <CardHeader
                    title={
                      <Typography variant="subtitle2" noWrap>
                        {event.data.title}
                      </Typography>
                    }
                    subheader={
                      <Typography variant="caption" color="text.secondary">
                        By: {event.data.createdBy}
                      </Typography>
                    }
                    action={
                      <Chip
                        size="small"
                        label={event.data.status}
                        color={
                          event.data.status === 'approved' ? 'success' :
                          event.data.status === 'rejected' ? 'error' :
                          'default'
                        }
                      />
                    }
                  />
                  {isSelected && versionArtifact && (
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Content Preview:
                      </Typography>
                      <Paper variant="outlined" sx={{ p: 1, maxHeight: 200, overflow: 'auto' }}>
                        {typeof versionArtifact.content === 'string' ? (
                          <ReactMarkdown>{versionArtifact.content.substring(0, 300)}</ReactMarkdown>
                        ) : (
                          <Typography variant="body2">
                            {JSON.stringify(versionArtifact.content).substring(0, 300)}...
                          </Typography>
                        )}
                      </Paper>
                    </CardContent>
                  )}
                </Card>
              );
              break;

            case 'consultation':
              icon = <ChatIcon />;
              color = 'info';
              title = `Consultation: ${event.data.consultingAgent} consulted ${event.data.consultedAgent}`;
              content = (
                <Card variant="outlined" sx={{ mb: 1 }}>
                  <CardContent>
                    <Typography variant="body2" gutterBottom>
                      <strong>Question:</strong> {event.data.question}
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="body2">
                      <strong>Response:</strong> {event.data.response.substring(0, 200)}
                      {event.data.response.length > 200 && '...'}
                    </Typography>
                  </CardContent>
                </Card>
              );
              break;

            case 'feedback_request':
              icon = <FeedbackIcon />;
              color = 'secondary';
              title = `Feedback Request: ${event.data.fromAgent} requested feedback from ${event.data.toAgent}`;
              content = (
                <Card variant="outlined" sx={{ mb: 1 }}>
                  <CardContent>
                    <Typography variant="body2" gutterBottom>
                      Requested feedback on:
                    </Typography>
                    <List dense>
                      {event.data.specificAreas?.map((area: string, i: number) => (
                        <ListItem key={i} sx={{ py: 0 }}>
                          <ListItemIcon sx={{ minWidth: 24 }}>
                            <InfoIcon fontSize="small" color="info" />
                          </ListItemIcon>
                          <ListItemText primary={area} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              );
              break;

            case 'feedback_response':
              icon = <FeedbackIcon />;
              color = 'warning';
              title = `Feedback Response: ${event.data.fromAgent} provided feedback`;
              content = (
                <Card variant="outlined" sx={{ mb: 1 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ mr: 1 }}>Rating:</Typography>
                      <Rating value={event.data.rating / 20} readOnly size="small" />
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        ({event.data.rating}/100)
                      </Typography>
                    </Box>
                    <Typography variant="body2" gutterBottom>
                      <strong>Summary:</strong> {event.data.summary}
                    </Typography>
                  </CardContent>
                </Card>
              );
              break;

            case 'feedback_incorporation':
              icon = <CheckCircleIcon />;
              color = 'success';
              title = `Feedback Incorporated: ${event.data.fromAgent} incorporated feedback`;
              content = (
                <Card variant="outlined" sx={{ mb: 1 }}>
                  <CardContent>
                    <Typography variant="body2">
                      Created improved artifact: {event.data.improvedArtifactId}
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      sx={{ mt: 1 }}
                      onClick={() => setSelectedVersionId(event.data.improvedArtifactId)}
                    >
                      View Improved Version
                    </Button>
                  </CardContent>
                </Card>
              );
              break;
          }

          return (
            <TimelineItem key={`${event.type}-${index}`}>
              <TimelineOppositeContent color="text.secondary">
                <Typography variant="caption">{timeAgo}</Typography>
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineDot color={color}>
                  {icon}
                </TimelineDot>
                {index < evolutionTimeline.length - 1 && <TimelineConnector />}
              </TimelineSeparator>
              <TimelineContent>
                <Typography variant="subtitle2" gutterBottom>
                  {title}
                </Typography>
                {content}
              </TimelineContent>
            </TimelineItem>
          );
        })}
      </Timeline>
    );
  };

  // Render consultations
  const renderConsultations = () => {
    if (consultations.length === 0) return null;

    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Consultations
        </Typography>
        <List>
          {consultations.map((consultation: any, index: number) => (
            <ListItem key={index} alignItems="flex-start" sx={{ bgcolor: 'background.paper', mb: 1, borderRadius: 1 }}>
              <ListItemIcon>
                <ChatIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={
                  <Typography variant="body2">
                    <strong>{consultation.consultingAgent}</strong> asked <strong>{consultation.consultedAgent}</strong>:
                  </Typography>
                }
                secondary={
                  <>
                    <Typography variant="body2" component="div" sx={{ mt: 1, fontStyle: 'italic' }}>
                      {consultation.question}
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="body2" component="div">
                      <strong>Response:</strong> {consultation.response}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                      {formatDistanceToNow(new Date(consultation.timestamp), { addSuffix: true })}
                    </Typography>
                  </>
                }
              />
            </ListItem>
          ))}
        </List>
      </Box>
    );
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Button
        variant="outlined"
        size="small"
        startIcon={expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        onClick={() => setExpanded(!expanded)}
        sx={{ mb: 1 }}
      >
        {expanded ? 'Hide Evolution History' : 'Show Evolution History'}
      </Button>

      <Collapse in={expanded}>
        <Paper variant="outlined" sx={{ p: 2, mt: 1 }}>
          <Typography variant="subtitle1" gutterBottom>
            <HistoryIcon sx={{ verticalAlign: 'middle', mr: 1 }} />
            Artifact Evolution History
          </Typography>

          {renderEvolutionTimeline()}
          {renderConsultations()}
        </Paper>
      </Collapse>
    </Box>
  );
};

export default ArtifactEvolutionDisplay;
