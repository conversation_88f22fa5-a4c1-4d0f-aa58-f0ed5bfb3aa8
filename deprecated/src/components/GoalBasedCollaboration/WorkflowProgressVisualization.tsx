import React, { useState, useEffect, useMemo } from 'react';
import {
  Box, Typography, Paper, Stepper, Step, StepLabel,
  StepContent, LinearProgress, Chip, Divider,
  Grid, Card, CardContent, Tooltip
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { WorkflowPhase, MilestoneStatus } from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface WorkflowProgressVisualizationProps {
  sessionId: string;
  state: any;
  loading?: boolean;
  onRefresh?: () => void;
}

const WorkflowProgressVisualization: React.FC<WorkflowProgressVisualizationProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const [activePhase, setActivePhase] = useState<WorkflowPhase>(WorkflowPhase.PLANNING);
  const [phaseProgress, setPhaseProgress] = useState<Record<WorkflowPhase, number>>({
    [WorkflowPhase.PLANNING]: 0,
    [WorkflowPhase.RESEARCH]: 0,
    [WorkflowPhase.CREATION]: 0,
    [WorkflowPhase.REVIEW]: 0,
    [WorkflowPhase.FINALIZATION]: 0
  });
  const [overallProgress, setOverallProgress] = useState(0);
  const [milestones, setMilestones] = useState<any[]>([]);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);

  // Process state to ensure consistent structure
  const processedState = useMemo(() => {
    if (!state || !state.workflowProgress) {
      return {
        currentPhase: WorkflowPhase.PLANNING,
        phaseProgress: {
          [WorkflowPhase.PLANNING]: 0,
          [WorkflowPhase.RESEARCH]: 0,
          [WorkflowPhase.CREATION]: 0,
          [WorkflowPhase.REVIEW]: 0,
          [WorkflowPhase.FINALIZATION]: 0
        },
        overallProgress: 0,
        milestones: [],
        estimatedTimeRemaining: null
      };
    }

    return {
      currentPhase: state.workflowProgress.currentPhase || WorkflowPhase.PLANNING,
      phaseProgress: state.workflowProgress.phaseProgress || {
        [WorkflowPhase.PLANNING]: 0,
        [WorkflowPhase.RESEARCH]: 0,
        [WorkflowPhase.CREATION]: 0,
        [WorkflowPhase.REVIEW]: 0,
        [WorkflowPhase.FINALIZATION]: 0
      },
      overallProgress: state.workflowProgress.overallProgress || 0,
      milestones: state.workflowProgress.milestones || [],
      estimatedTimeRemaining: state.workflowProgress.estimatedTimeRemaining || null
    };
  }, [state]);

  useEffect(() => {
    setActivePhase(processedState.currentPhase);
    setPhaseProgress(processedState.phaseProgress);
    setOverallProgress(processedState.overallProgress);
    setMilestones(processedState.milestones);
    setEstimatedTimeRemaining(processedState.estimatedTimeRemaining);
  }, [processedState]);

  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds} seconds`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)} minutes`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours} hours ${minutes} minutes`;
    }
  };

  const getPhaseIndex = (phase: WorkflowPhase): number => {
    const phases = [
      WorkflowPhase.PLANNING,
      WorkflowPhase.RESEARCH,
      WorkflowPhase.CREATION,
      WorkflowPhase.REVIEW,
      WorkflowPhase.FINALIZATION
    ];
    return phases.indexOf(phase);
  };

  const getMilestoneStatusColor = (status: MilestoneStatus) => {
    switch (status) {
      case MilestoneStatus.COMPLETED:
        return 'success';
      case MilestoneStatus.IN_PROGRESS:
        return 'primary';
      case MilestoneStatus.PENDING:
        return 'default';
      case MilestoneStatus.SKIPPED:
        return 'warning';
      default:
        return 'default';
    }
  };

  const getMilestoneStatusIcon = (status: MilestoneStatus) => {
    switch (status) {
      case MilestoneStatus.COMPLETED:
        return <CheckCircleIcon fontSize="small" />;
      case MilestoneStatus.IN_PROGRESS:
        return <PlayArrowIcon fontSize="small" />;
      case MilestoneStatus.PENDING:
        return <PendingIcon fontSize="small" />;
      default:
        return null;
    }
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>Workflow Progress</Typography>

      {loading ? (
        <LinearProgress variant="indeterminate" />
      ) : (
        <Box>
          {/* Overall Progress */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2">Overall Progress</Typography>
              <Typography variant="body2">{overallProgress}%</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={overallProgress}
              sx={{ height: 10, borderRadius: 5 }}
            />

            {estimatedTimeRemaining !== null && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <AccessTimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  Estimated time remaining: {formatTime(estimatedTimeRemaining)}
                </Typography>
              </Box>
            )}
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Phase Progress */}
          <Stepper activeStep={getPhaseIndex(activePhase)} orientation="vertical">
            {Object.values(WorkflowPhase).map((phase) => (
              <Step key={phase}>
                <StepLabel>
                  <Typography variant="subtitle1">{phase}</Typography>
                </StepLabel>
                <StepContent>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2">Phase Progress</Typography>
                      <Typography variant="body2">{phaseProgress[phase]}%</Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={phaseProgress[phase]}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>

                  {/* Milestones for this phase */}
                  <Box sx={{ ml: 2 }}>
                    {milestones
                      .filter(milestone => {
                        // Dynamic mapping of milestones to phases based on milestone ID
                        // This allows for more flexibility as the backend evolves
                        if (phase === WorkflowPhase.PLANNING) {
                          return milestone.id.includes('planning') ||
                                 milestone.id === 'research-started' ||
                                 milestone.id === 'goals-defined';
                        } else if (phase === WorkflowPhase.RESEARCH) {
                          return milestone.id.includes('research') ||
                                 milestone.id === 'market-research-completed' ||
                                 milestone.id === 'keyword-analysis-completed' ||
                                 milestone.id === 'outline-created';
                        } else if (phase === WorkflowPhase.CREATION) {
                          return milestone.id.includes('creation') ||
                                 milestone.id === 'content-strategy-completed' ||
                                 milestone.id === 'draft-created' ||
                                 milestone.id === 'content-created';
                        } else if (phase === WorkflowPhase.REVIEW) {
                          return milestone.id.includes('review') ||
                                 milestone.id === 'seo-optimized' ||
                                 milestone.id === 'quality-assessment-completed' ||
                                 milestone.id === 'final-review';
                        } else if (phase === WorkflowPhase.FINALIZATION) {
                          return milestone.id.includes('final') ||
                                 milestone.id === 'article-completed' ||
                                 milestone.id === 'content-finalized';
                        }
                        return false;
                      })
                      .map(milestone => (
                        <Box key={milestone.id} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Chip
                            size="small"
                            label={milestone.status}
                            color={getMilestoneStatusColor(milestone.status) as any}
                            icon={getMilestoneStatusIcon(milestone.status)}
                            sx={{ mr: 1 }}
                          />
                          <Typography variant="body2">
                            {milestone.description}
                          </Typography>
                        </Box>
                      ))}
                  </Box>
                </StepContent>
              </Step>
            ))}
          </Stepper>

          <Divider sx={{ my: 2 }} />

          {/* Article Progress */}
          {state?.workflowProgress?.articleProgress && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>Article Progress</Typography>

              <Grid container spacing={2}>
                <Grid item xs={6} sm={4}>
                  <Card variant="outlined" sx={{
                    bgcolor: state.workflowProgress.articleProgress.researchComplete ? 'success.light' : 'background.paper'
                  }}>
                    <CardContent>
                      <Typography variant="body2" align="center">
                        Research
                      </Typography>
                      {state.workflowProgress.articleProgress.researchComplete && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                          <CheckCircleIcon color="success" />
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={6} sm={4}>
                  <Card variant="outlined" sx={{
                    bgcolor: state.workflowProgress.articleProgress.outlineComplete ? 'success.light' : 'background.paper'
                  }}>
                    <CardContent>
                      <Typography variant="body2" align="center">
                        Outline
                      </Typography>
                      {state.workflowProgress.articleProgress.outlineComplete && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                          <CheckCircleIcon color="success" />
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={6} sm={4}>
                  <Card variant="outlined" sx={{
                    bgcolor: state.workflowProgress.articleProgress.draftComplete ? 'success.light' : 'background.paper'
                  }}>
                    <CardContent>
                      <Typography variant="body2" align="center">
                        Draft
                      </Typography>
                      {state.workflowProgress.articleProgress.draftComplete && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                          <CheckCircleIcon color="success" />
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={6} sm={4}>
                  <Card variant="outlined" sx={{
                    bgcolor: state.workflowProgress.articleProgress.seoOptimizationComplete ? 'success.light' : 'background.paper'
                  }}>
                    <CardContent>
                      <Typography variant="body2" align="center">
                        SEO Optimization
                      </Typography>
                      {state.workflowProgress.articleProgress.seoOptimizationComplete && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                          <CheckCircleIcon color="success" />
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={6} sm={4}>
                  <Card variant="outlined" sx={{
                    bgcolor: state.workflowProgress.articleProgress.finalReviewComplete ? 'success.light' : 'background.paper'
                  }}>
                    <CardContent>
                      <Typography variant="body2" align="center">
                        Final Review
                      </Typography>
                      {state.workflowProgress.articleProgress.finalReviewComplete && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                          <CheckCircleIcon color="success" />
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={6} sm={4}>
                  <Card variant="outlined" sx={{
                    bgcolor: state.workflowProgress.articleProgress.articleComplete ? 'success.light' : 'background.paper'
                  }}>
                    <CardContent>
                      <Typography variant="body2" align="center">
                        Article Complete
                      </Typography>
                      {state.workflowProgress.articleProgress.articleComplete && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                          <CheckCircleIcon color="success" />
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default WorkflowProgressVisualization;
