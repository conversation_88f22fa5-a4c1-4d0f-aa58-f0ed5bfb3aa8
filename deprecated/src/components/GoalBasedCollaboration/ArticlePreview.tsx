'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Button,
  TextField,
  Divider,
  Chip,
  useTheme
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import dynamic from 'next/dynamic';
import remarkGfm from 'remark-gfm';

// Dynamically import ReactMarkdown to avoid SSR issues
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false });

interface ArticlePreviewProps {
  sessionId: string;
  state: any;
  loading?: boolean;
}

const ArticlePreview: React.FC<ArticlePreviewProps> = ({
  sessionId,
  state,
  loading = false
}) => {
  const theme = useTheme();
  const [articleContent, setArticleContent] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editedContent, setEditedContent] = useState<string>('');

  // Helper function to recursively extract content from complex objects
  const extractContentFromObject = (obj: any): string => {
    if (!obj) return '';

    // If it's a string, return it directly
    if (typeof obj === 'string') return obj;

    // Check for common content fields
    if (obj.content && typeof obj.content === 'string') return obj.content;
    if (obj.text && typeof obj.text === 'string') return obj.text;
    if (obj.body && typeof obj.body === 'string') return obj.body;

    // If it has sections, try to combine them
    if (obj.sections && Array.isArray(obj.sections)) {
      return obj.sections.map((section: any) => {
        if (typeof section === 'string') return section;

        let sectionContent = '';
        if (section.title) sectionContent += `## ${section.title}\n\n`;
        if (section.content) sectionContent += `${section.content}\n\n`;
        return sectionContent;
      }).join('\n');
    }

    // If it has a title and content, format them together
    if (obj.title && (obj.content || obj.text || obj.body)) {
      const content = obj.content || obj.text || obj.body;
      return `# ${obj.title}\n\n${content}`;
    }

    // Last resort: stringify the object
    return JSON.stringify(obj, null, 2);
  };

  // Extract the final article content from the state
  useEffect(() => {
    if (!state || !state.artifacts) {
      setArticleContent('');
      return;
    }

    // First try to find SEO-optimized content
    const seoArtifacts = Object.values(state.artifacts)
      .filter((artifact: any) => artifact.type === 'seo-optimization')
      .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    if (seoArtifacts.length > 0) {
      const content = seoArtifacts[0].content;
      setArticleContent(extractContentFromObject(content));
      return;
    }

    // If no SEO content, try to find content creation artifacts
    const contentArtifacts = Object.values(state.artifacts)
      .filter((artifact: any) => artifact.type === 'content-creation')
      .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    if (contentArtifacts.length > 0) {
      const content = contentArtifacts[0].content;
      setArticleContent(extractContentFromObject(content));
      return;
    }

    setArticleContent('No article content available yet. The system is still working on generating content.');
  }, [state]);

  // Handle edit button click
  const handleEditClick = () => {
    setEditedContent(articleContent);
    setIsEditing(true);
  };

  // Handle save button click
  const handleSaveClick = () => {
    setArticleContent(editedContent);
    setIsEditing(false);
  };

  // Handle copy to clipboard
  const handleCopyClick = () => {
    navigator.clipboard.writeText(articleContent);
    alert('Article content copied to clipboard!');
  };

  // Handle download as markdown
  const handleDownloadClick = () => {
    const blob = new Blob([articleContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `article-${sessionId.substring(0, 8)}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Paper sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Article Preview</Typography>
        <Box>
          {!isEditing ? (
            <>
              <Button
                startIcon={<EditIcon />}
                onClick={handleEditClick}
                size="small"
                sx={{ mr: 1 }}
                disabled={loading || !articleContent}
              >
                Edit
              </Button>
              <Button
                startIcon={<ContentCopyIcon />}
                onClick={handleCopyClick}
                size="small"
                sx={{ mr: 1 }}
                disabled={loading || !articleContent}
              >
                Copy
              </Button>
              <Button
                startIcon={<DownloadIcon />}
                onClick={handleDownloadClick}
                size="small"
                disabled={loading || !articleContent}
              >
                Download
              </Button>
            </>
          ) : (
            <Button
              startIcon={<SaveIcon />}
              onClick={handleSaveClick}
              size="small"
              variant="contained"
              color="primary"
            >
              Save
            </Button>
          )}
        </Box>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <CircularProgress />
        </Box>
      ) : !state ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <Typography variant="body1" color="text.secondary">
            No session data available
          </Typography>
        </Box>
      ) : (
        <>
          <Box sx={{ mb: 2 }}>
            <Chip
              label={`Topic: ${state.topic || 'N/A'}`}
              sx={{ mr: 1, mb: 1 }}
              color="primary"
            />
            <Chip
              label={`Content Type: ${state.contentType || 'N/A'}`}
              sx={{ mr: 1, mb: 1 }}
              color="secondary"
            />
            <Chip
              label={`Target Audience: ${state.targetAudience || 'N/A'}`}
              sx={{ mr: 1, mb: 1 }}
            />
            <Chip
              label={`Tone: ${state.tone || 'N/A'}`}
              sx={{ mr: 1, mb: 1 }}
            />
          </Box>

          <Divider sx={{ mb: 2 }} />

          {isEditing ? (
            <TextField
              fullWidth
              multiline
              minRows={20}
              maxRows={40}
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          ) : (
            <Box
              sx={{
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                p: 2,
                maxHeight: '60vh',
                overflow: 'auto'
              }}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {typeof articleContent === 'string' ? articleContent : JSON.stringify(articleContent, null, 2)}
              </ReactMarkdown>
            </Box>
          )}
        </>
      )}
    </Paper>
  );
};

export default ArticlePreview;
