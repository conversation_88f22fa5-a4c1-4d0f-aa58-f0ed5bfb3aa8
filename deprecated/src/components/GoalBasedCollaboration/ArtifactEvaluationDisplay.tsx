'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Divider,
  Chip,
  Rating,
  List,
  ListItem,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  Card,
  CardContent,
  Grid,
  Button,
  Snackbar,
  Alert,
  useTheme,
  Tab,
  Tabs
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AssessmentIcon from '@mui/icons-material/Assessment';
import { goalOrchestratorClient } from '../../lib/goal-orchestrator-client';
import ArtifactContentDisplay from './ArtifactContentDisplay';

// Import types
import {
  ArtifactStatus,
  GoalType,
  GoalStatus
} from '../../app/(payload)/api/agents/dynamic-collaboration-v3/state/unified-schema';

interface ArtifactEvaluationDisplayProps {
  sessionId: string;
  state: any;
  loading?: boolean;
  onRefresh?: () => void;
}

/**
 * Component for displaying artifact evaluations
 */
const ArtifactEvaluationDisplay: React.FC<ArtifactEvaluationDisplayProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [selectedArtifactId, setSelectedArtifactId] = useState<string | null>(null);
  const [evaluations, setEvaluations] = useState<any[]>([]);
  const [loadingEvaluations, setLoadingEvaluations] = useState<boolean>(false);
  const [requestingEvaluation, setRequestingEvaluation] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [approvingArtifact, setApprovingArtifact] = useState<boolean>(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Process state to ensure consistent structure
  const processedState = useMemo(() => {
    if (!state) return { artifacts: {}, goals: { byId: {} } };

    // Ensure artifacts structure
    const artifacts = state.artifacts || {};

    // Ensure goals structure
    const goals = state.goals?.byId || {};

    return { artifacts, goals };
  }, [state]);

  // Get artifacts from processed state
  const artifacts = processedState.artifacts;
  const artifactIds = Object.keys(artifacts);

  // Get goals from processed state
  const goals = processedState.goals;

  // Show all artifacts instead of filtering to only those with evaluations
  const artifactsWithEvaluations = artifactIds;

  // Load evaluations for the selected artifact
  useEffect(() => {
    if (selectedArtifactId && sessionId) {
      loadArtifactEvaluation(selectedArtifactId);
    }
  }, [selectedArtifactId, sessionId]);

  // Load artifact evaluation
  const loadArtifactEvaluation = async (artifactId: string) => {
    try {
      setLoadingEvaluations(true);
      console.log('Loading evaluation for artifact:', artifactId);

      // Try to get evaluation from the API
      let evaluation;
      try {
        evaluation = await goalOrchestratorClient.getArtifactEvaluation(sessionId, artifactId);
        console.log('Evaluation from API:', evaluation);
      } catch (apiError) {
        console.warn('Error fetching evaluation from API:', apiError);
        evaluation = null;
      }

      if (evaluation) {
        console.log('Setting evaluation from API');
        setEvaluations([evaluation]);

        // Show success message
        setSnackbar({
          open: true,
          message: 'Evaluation loaded successfully',
          severity: 'success'
        });
      } else {
        // Fallback to metadata if API doesn't return evaluation
        const artifact = artifacts[artifactId];
        console.log('Artifact from state:', artifact);

        if (artifact?.metadata?.evaluation) {
          console.log('Using evaluation from artifact metadata:', artifact.metadata.evaluation);
          setEvaluations([artifact.metadata.evaluation]);

          // Show info message
          setSnackbar({
            open: true,
            message: 'Using cached evaluation data',
            severity: 'info'
          });
        } else {
          console.log('No evaluation found in artifact metadata');

          // Check if the artifact has any evaluation data in other formats
          const possibleEvaluationData = extractEvaluationData(artifact);

          if (possibleEvaluationData) {
            console.log('Found evaluation data in alternative format:', possibleEvaluationData);
            setEvaluations([possibleEvaluationData]);

            setSnackbar({
              open: true,
              message: 'Found evaluation data in artifact',
              severity: 'info'
            });
          } else {
            console.log('No evaluation data found at all');
            setEvaluations([]);

            // Show info message
            setSnackbar({
              open: true,
              message: 'No evaluation data available. You can request an evaluation.',
              severity: 'info'
            });
          }
        }
      }
    } catch (error) {
      console.error('Error loading artifact evaluation:', error);

      // Show error message
      setSnackbar({
        open: true,
        message: 'Error loading evaluation: ' + (error instanceof Error ? error.message : String(error)),
        severity: 'error'
      });

      // Fallback to metadata if API call fails
      const artifact = artifacts[artifactId];
      if (artifact?.metadata?.evaluation) {
        setEvaluations([artifact.metadata.evaluation]);
      } else {
        // Try to extract evaluation data from other properties
        const possibleEvaluationData = extractEvaluationData(artifact);
        if (possibleEvaluationData) {
          setEvaluations([possibleEvaluationData]);
        } else {
          setEvaluations([]);
        }
      }
    } finally {
      setLoadingEvaluations(false);
    }
  };

  // Helper function to extract evaluation data from various artifact formats
  const extractEvaluationData = (artifact: any): any => {
    if (!artifact) return null;

    // Check for evaluation in different locations
    if (artifact.metadata?.evaluation) {
      return artifact.metadata.evaluation;
    }

    if (artifact.evaluation) {
      return artifact.evaluation;
    }

    // Check if the artifact content itself contains evaluation data
    if (typeof artifact.content === 'object' && artifact.content !== null) {
      if (artifact.content.evaluation) {
        return artifact.content.evaluation;
      }

      if (artifact.content.criteriaEvaluation || artifact.content.score || artifact.content.meetsRequirements) {
        return {
          artifactId: artifact.id,
          goalId: artifact.goalId,
          meetsRequirements: artifact.content.meetsRequirements || artifact.status === ArtifactStatus.APPROVED,
          score: artifact.content.score || 75,
          criteriaEvaluation: artifact.content.criteriaEvaluation || {},
          overallFeedback: artifact.content.feedback || artifact.content.overallFeedback || 'No detailed feedback available',
          timestamp: artifact.updatedAt
        };
      }
    }

    // If the artifact is approved, create a default positive evaluation
    if (artifact.status === ArtifactStatus.APPROVED) {
      return {
        artifactId: artifact.id,
        goalId: artifact.goalId,
        meetsRequirements: true,
        score: 80,
        criteriaEvaluation: {},
        overallFeedback: 'This artifact has been approved.',
        timestamp: artifact.updatedAt
      };
    }

    return null;
  };

  // Request an evaluation for the selected artifact
  const requestEvaluation = async () => {
    if (!selectedArtifactId || !sessionId) return;

    try {
      setRequestingEvaluation(true);

      const artifact = artifacts[selectedArtifactId];
      const goalId = artifact.goalId;

      if (!goalId) {
        console.error('No goal ID associated with this artifact');
        setSnackbar({
          open: true,
          message: 'No goal associated with this artifact. Cannot request evaluation.',
          severity: 'error'
        });
        return;
      }

      // Show info message that evaluation is being requested
      setSnackbar({
        open: true,
        message: 'Requesting evaluation from the quality assessment agent...',
        severity: 'info'
      });

      await goalOrchestratorClient.requestArtifactEvaluation(sessionId, selectedArtifactId, goalId);

      setSnackbar({
        open: true,
        message: 'Evaluation requested successfully. Processing...',
        severity: 'success'
      });

      // Refresh the evaluation after a short delay to allow the backend to process
      // First attempt after 2 seconds
      setTimeout(() => {
        loadArtifactEvaluation(selectedArtifactId);

        // Second attempt after 5 seconds if the first one doesn't get results
        setTimeout(() => {
          loadArtifactEvaluation(selectedArtifactId);
        }, 3000);
      }, 2000);
    } catch (error) {
      console.error('Error requesting artifact evaluation:', error);
      setSnackbar({
        open: true,
        message: 'Error requesting evaluation: ' + (error instanceof Error ? error.message : String(error)),
        severity: 'error'
      });
    } finally {
      setRequestingEvaluation(false);
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Approve artifact
  const approveArtifact = async () => {
    if (!selectedArtifactId || !sessionId) return;

    try {
      setApprovingArtifact(true);

      // Show info message
      setSnackbar({
        open: true,
        message: 'Approving artifact...',
        severity: 'info'
      });

      // Call the API to approve the artifact
      await goalOrchestratorClient.approveArtifact(sessionId, selectedArtifactId);

      // Show success message
      setSnackbar({
        open: true,
        message: 'Artifact approved successfully',
        severity: 'success'
      });

      // Refresh the data
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error approving artifact:', error);

      // Show error message
      setSnackbar({
        open: true,
        message: 'Error approving artifact: ' + (error instanceof Error ? error.message : String(error)),
        severity: 'error'
      });
    } finally {
      setApprovingArtifact(false);
    }
  };

  // Render criteria evaluation
  const renderCriteriaEvaluation = (criteriaEvaluation: any) => {
    if (!criteriaEvaluation) return null;

    return (
      <List>
        {Object.entries(criteriaEvaluation).map(([criterion, evaluation]: [string, any]) => (
          <Accordion key={criterion} sx={{ mb: 1 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                {evaluation.met ? (
                  <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                ) : (
                  <ErrorIcon color="error" sx={{ mr: 1 }} />
                )}
                <Typography variant="body1" sx={{ flexGrow: 1 }}>
                  {criterion}
                </Typography>
                <Chip
                  label={`${evaluation.score}/100`}
                  color={evaluation.score >= 70 ? "success" : evaluation.score >= 50 ? "warning" : "error"}
                  size="small"
                  sx={{ ml: 1 }}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">{evaluation.feedback}</Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </List>
    );
  };

  return (
    <Paper sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      <Typography variant="h5" gutterBottom>
        Artifact Evaluation
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        View detailed evaluations of artifacts against goal criteria
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : !state ? (
        <Typography variant="body1" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
          No session data available
        </Typography>
      ) : artifactsWithEvaluations.length === 0 ? (
        <Typography variant="body1" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
          No artifact evaluations available yet
        </Typography>
      ) : (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Select an artifact to view its evaluation:
          </Typography>

          <Grid container spacing={2} sx={{ mb: 3 }}>
            {artifactsWithEvaluations.map(artifactId => {
              const artifact = artifacts[artifactId];
              const goalId = artifact.goalId;
              const goal = goalId ? goals[goalId] : null;

              return (
                <Grid item xs={12} sm={6} md={4} key={artifactId}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      border: selectedArtifactId === artifactId ? `2px solid ${theme.palette.primary.main}` : 'none',
                      transition: 'all 0.2s'
                    }}
                    onClick={() => setSelectedArtifactId(artifactId)}
                  >
                    <CardContent>
                      <Typography variant="subtitle1" noWrap>
                        {artifact.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" noWrap>
                        Type: {artifact.type}
                      </Typography>
                      {goal && (
                        <Typography variant="body2" color="text.secondary" noWrap>
                          Goal: {goal.description}
                        </Typography>
                      )}
                      <Chip
                        label={artifact.status}
                        size="small"
                        color={
                          artifact.status === ArtifactStatus.APPROVED ? "success" :
                          artifact.status === ArtifactStatus.REVIEW ? "warning" :
                          artifact.status === ArtifactStatus.REJECTED ? "error" :
                          "default"
                        }
                        sx={{ mt: 1 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>

          <Divider sx={{ my: 2 }} />

          {selectedArtifactId ? (
            <Box>
              {/* Tabs for switching between content and evaluation */}
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Tabs
                  value={activeTab}
                  onChange={handleTabChange}
                  aria-label="artifact tabs"
                >
                  <Tab
                    icon={<VisibilityIcon />}
                    label="Content"
                    id="artifact-tab-0"
                    aria-controls="artifact-tabpanel-0"
                  />
                  <Tab
                    icon={<AssessmentIcon />}
                    label="Evaluation"
                    id="artifact-tab-1"
                    aria-controls="artifact-tabpanel-1"
                  />
                </Tabs>
              </Box>

              {/* Content Tab Panel */}
              {activeTab === 0 && (
                <Box role="tabpanel" id="artifact-tabpanel-0" aria-labelledby="artifact-tab-0">
                  <ArtifactContentDisplay artifact={artifacts[selectedArtifactId]} />
                </Box>
              )}

              {/* Evaluation Tab Panel */}
              {activeTab === 1 && (
                <Box role="tabpanel" id="artifact-tabpanel-1" aria-labelledby="artifact-tab-1">
                  {loadingEvaluations ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                      <CircularProgress />
                    </Box>
                  ) : evaluations.length === 0 ? (
                    <Box sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body1" color="text.secondary" paragraph>
                        No evaluation data available for this artifact
                      </Typography>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={requestEvaluation}
                        disabled={requestingEvaluation}
                      >
                        {requestingEvaluation ? 'Requesting Evaluation...' : 'Request Evaluation'}
                      </Button>
                    </Box>
                  ) : (
                    <Box>
                      {evaluations.map((evaluation, index) => (
                        <Box key={index}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Typography variant="h6" sx={{ flexGrow: 1 }}>
                              Evaluation Results
                            </Typography>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={requestEvaluation}
                              disabled={requestingEvaluation}
                              sx={{ mr: 1 }}
                            >
                              {requestingEvaluation ? 'Refreshing...' : 'Refresh Evaluation'}
                            </Button>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip
                                icon={evaluation.meetsRequirements ? <CheckCircleIcon /> : <ErrorIcon />}
                                label={evaluation.meetsRequirements ? "Meets Requirements" : "Needs Improvement"}
                                color={evaluation.meetsRequirements ? "success" : "error"}
                              />
                              <Chip
                                label={artifacts[selectedArtifactId].status}
                                color={
                                  artifacts[selectedArtifactId].status === ArtifactStatus.APPROVED ? "success" :
                                  artifacts[selectedArtifactId].status === ArtifactStatus.REVIEW ? "warning" :
                                  artifacts[selectedArtifactId].status === ArtifactStatus.REJECTED ? "error" :
                                  "default"
                                }
                                size="small"
                              />
                            </Box>
                          </Box>

                          <Box sx={{ mb: 3 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                              <Typography variant="body2">Overall Score</Typography>
                              <Typography variant="body2">{evaluation.score}/100</Typography>
                            </Box>
                            <LinearProgress
                              variant="determinate"
                              value={evaluation.score}
                              color={
                                evaluation.score >= 70 ? "success" :
                                evaluation.score >= 50 ? "warning" :
                                "error"
                              }
                              sx={{ height: 10, borderRadius: 5 }}
                            />
                          </Box>

                          {/* Approve button - only show if artifact is not already approved */}
                          {artifacts[selectedArtifactId].status !== ArtifactStatus.APPROVED && (
                            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                              <Button
                                variant="contained"
                                color="success"
                                onClick={approveArtifact}
                                disabled={approvingArtifact || !evaluation.meetsRequirements}
                                startIcon={<CheckCircleIcon />}
                                sx={{ minWidth: 200 }}
                              >
                                {approvingArtifact ? 'Approving...' : 'Approve Artifact'}
                              </Button>
                            </Box>
                          )}

                          <Typography variant="subtitle1" gutterBottom>
                            Overall Feedback
                          </Typography>
                          <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
                            <Typography variant="body2">{evaluation.overallFeedback}</Typography>
                          </Paper>

                          <Typography variant="subtitle1" gutterBottom>
                            Criteria Evaluation
                          </Typography>
                          {renderCriteriaEvaluation(evaluation.criteriaEvaluation)}
                        </Box>
                      ))}
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          ) : (
            <Typography variant="body1" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
              Select an artifact to view its content and evaluation
            </Typography>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default ArtifactEvaluationDisplay;
