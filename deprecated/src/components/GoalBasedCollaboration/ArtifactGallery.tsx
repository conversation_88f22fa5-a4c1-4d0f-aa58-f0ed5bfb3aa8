'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Divider,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  useTheme
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ArticleIcon from '@mui/icons-material/Article';
import SearchIcon from '@mui/icons-material/Search';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import SpellcheckIcon from '@mui/icons-material/Spellcheck';
import BiotechIcon from '@mui/icons-material/Biotech';
import dynamic from 'next/dynamic';
import remarkGfm from 'remark-gfm';

// Dynamically import ReactMarkdown to avoid SSR issues
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false });

// Import types from our V3 implementation
import {
  ArtifactStatus
} from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface ArtifactGalleryProps {
  sessionId: string;
  state: any;
  loading?: boolean;
  onRefresh?: () => void;
}

const ArtifactGallery: React.FC<ArtifactGalleryProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [selectedArtifact, setSelectedArtifact] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);

  // Get artifact icon
  const getArtifactIcon = (type: string) => {
    switch (type) {
      case 'market-research':
        return <BiotechIcon />;
      case 'keyword-analysis':
        return <SearchIcon />;
      case 'content-strategy':
        return <LightbulbIcon />;
      case 'content-creation':
        return <ArticleIcon />;
      case 'seo-optimization':
        return <SpellcheckIcon />;
      case 'quality-assessment':
        return <VisibilityIcon />;
      default:
        return <ArticleIcon />;
    }
  };

  // Get artifact type label
  const getArtifactTypeLabel = (type: string): string => {
    switch (type) {
      case 'market-research':
        return 'Market Research';
      case 'keyword-analysis':
        return 'Keyword Analysis';
      case 'content-strategy':
        return 'Content Strategy';
      case 'content-creation':
        return 'Content Creation';
      case 'seo-optimization':
        return 'SEO Optimization';
      case 'quality-assessment':
        return 'Quality Assessment';
      default:
        return type;
    }
  };

  // Get artifact type color
  const getArtifactTypeColor = (type: string): string => {
    switch (type) {
      case 'market-research':
        return theme.palette.info.main;
      case 'keyword-analysis':
        return theme.palette.info.dark;
      case 'content-strategy':
        return theme.palette.primary.light;
      case 'content-creation':
        return theme.palette.primary.main;
      case 'seo-optimization':
        return theme.palette.success.light;
      case 'quality-assessment':
        return theme.palette.success.main;
      default:
        return theme.palette.grey[500];
    }
  };

  // Handle view artifact
  const handleViewArtifact = (artifact: any) => {
    setSelectedArtifact(artifact);
    setDialogOpen(true);
  };

  // Handle close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Format artifact content
  const formatArtifactContent = (content: any): string => {
    if (typeof content === 'string') {
      return content;
    } else if (typeof content === 'object') {
      return JSON.stringify(content, null, 2);
    } else {
      return String(content);
    }
  };

  // Render artifact content
  const renderArtifactContent = () => {
    if (!selectedArtifact) return null;

    const content = selectedArtifact.content;

    if (activeTab === 0) {
      // Formatted view
      if (typeof content === 'string') {
        return (
          <Box sx={{ maxHeight: '60vh', overflow: 'auto', p: 2 }}>
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {content}
            </ReactMarkdown>
          </Box>
        );
      } else if (typeof content === 'object') {
        return (
          <Box sx={{ maxHeight: '60vh', overflow: 'auto', p: 2 }}>
            {Object.entries(content).map(([key, value]) => (
              <Box key={key} sx={{ mb: 2 }}>
                <Typography variant="subtitle1" fontWeight="bold">
                  {key}
                </Typography>
                {typeof value === 'string' ? (
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {value as string}
                  </ReactMarkdown>
                ) : (
                  <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
                    {JSON.stringify(value, null, 2)}
                  </pre>
                )}
              </Box>
            ))}
          </Box>
        );
      }
    } else {
      // Raw view
      return (
        <Box sx={{ maxHeight: '60vh', overflow: 'auto', p: 2 }}>
          <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
            {formatArtifactContent(content)}
          </pre>
        </Box>
      );
    }
  };

  return (
    <>
      <Paper sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Artifacts</Typography>
          {onRefresh && (
            <Tooltip title="Refresh">
              <IconButton onClick={onRefresh} size="small" disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
            <CircularProgress />
          </Box>
        ) : !state || !state.artifacts || Object.keys(state.artifacts).length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
            <Typography variant="body1" color="text.secondary">
              No artifacts available
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {Object.values(state.artifacts)
              .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
              .map((artifact: any) => (
                <Grid item xs={12} sm={6} md={4} key={artifact.id}>
                  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <CardHeader
                      avatar={getArtifactIcon(artifact.type)}
                      title={artifact.title}
                      subheader={new Date(artifact.createdAt).toLocaleString()}
                      titleTypographyProps={{ variant: 'subtitle1', noWrap: true }}
                      subheaderTypographyProps={{ variant: 'caption' }}
                      sx={{ pb: 1 }}
                    />
                    <Divider />
                    <CardContent sx={{ flexGrow: 1, pt: 1, pb: 0 }}>
                      <Box sx={{ display: 'flex', mb: 1 }}>
                        <Chip
                          label={getArtifactTypeLabel(artifact.type)}
                          size="small"
                          sx={{
                            backgroundColor: getArtifactTypeColor(artifact.type),
                            color: '#fff',
                            mr: 1
                          }}
                        />
                        <Chip
                          label={artifact.status || ArtifactStatus.DRAFT}
                          size="small"
                          sx={{
                            backgroundColor: 
                              artifact.status === ArtifactStatus.APPROVED 
                                ? theme.palette.success.main 
                                : artifact.status === ArtifactStatus.REJECTED
                                  ? theme.palette.error.main
                                  : theme.palette.grey[500],
                            color: '#fff'
                          }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ 
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical'
                      }}>
                        {typeof artifact.content === 'string' 
                          ? artifact.content.substring(0, 100) + '...'
                          : 'Complex content (click to view)'}
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        startIcon={<VisibilityIcon />}
                        onClick={() => handleViewArtifact(artifact)}
                      >
                        View
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
          </Grid>
        )}
      </Paper>

      {/* Artifact Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedArtifact && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {getArtifactIcon(selectedArtifact.type)}
                <Typography variant="h6" sx={{ ml: 1 }}>
                  {selectedArtifact.title}
                </Typography>
              </Box>
            </DialogTitle>
            <Divider />
            <Box sx={{ px: 3, pt: 1 }}>
              <Tabs value={activeTab} onChange={handleTabChange}>
                <Tab label="Formatted" />
                <Tab label="Raw" />
              </Tabs>
            </Box>
            <DialogContent sx={{ p: 0 }}>
              {renderArtifactContent()}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </>
  );
};

export default ArtifactGallery;
