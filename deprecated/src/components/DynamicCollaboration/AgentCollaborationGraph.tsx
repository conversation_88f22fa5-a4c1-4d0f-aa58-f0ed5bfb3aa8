'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  Divider,
  Chip,
  useTheme
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import dynamic from 'next/dynamic';
import { DynamicCollaborationState } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';

// Dynamically import ForceGraph2D with no SSR to avoid window is not defined error
const ForceGraph2D = dynamic(() => import('react-force-graph-2d'), {
  ssr: false,
  loading: () => <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
    <CircularProgress />
  </Box>
});

interface GraphNode {
  id: string;
  name: string;
  val: number;
  color: string;
  type: 'agent' | 'artifact' | 'goal';
  x?: number;
  y?: number;
}

interface GraphLink {
  source: string;
  target: string;
  value: number;
  type: string;
  label?: string;
  color?: string;
}

interface AgentCollaborationGraphProps {
  sessionId: string;
  state: DynamicCollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const AgentCollaborationGraph: React.FC<AgentCollaborationGraphProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const graphRef = useRef<any>();
  const [graphData, setGraphData] = useState<{ nodes: GraphNode[], links: GraphLink[] }>({ nodes: [], links: [] });
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [selectedLink, setSelectedLink] = useState<GraphLink | null>(null);
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null);

  // Agent colors and names
  const agentColors: Record<string, string> = {
    'market-research': theme.palette.primary.main,
    'seo-keyword': theme.palette.secondary.main,
    'content-strategy': theme.palette.info.main,
    'content-generation': theme.palette.success.main,
    'seo-optimization': theme.palette.warning.main,
    'system': theme.palette.grey[500],
    'user': theme.palette.error.main
  };

  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Writer',
    'seo-optimization': 'SEO Optimizer',
    'system': 'System',
    'user': 'User'
  };

  // Build graph data from state
  useEffect(() => {
    if (!state) return;

    const nodes: GraphNode[] = [];
    const links: GraphLink[] = [];
    const nodeMap = new Map<string, GraphNode>();
    const linkMap = new Map<string, GraphLink>();

    // Add agent nodes
    const addAgentNode = (agentId: string) => {
      if (!nodeMap.has(agentId)) {
        const node: GraphNode = {
          id: agentId,
          name: agentNames[agentId] || agentId,
          val: 5, // Base size
          color: agentColors[agentId] || theme.palette.grey[500],
          type: 'agent'
        };
        nodes.push(node);
        nodeMap.set(agentId, node);
      }
    };

    // Add artifact nodes
    if (state.artifacts && state.generatedArtifacts) {
      state.generatedArtifacts.forEach(artifactId => {
        const artifact = state.artifacts[artifactId];
        if (artifact) {
          const node: GraphNode = {
            id: artifactId,
            name: artifact.name || `Artifact ${artifactId.substring(0, 6)}`,
            val: 3,
            color: theme.palette.grey[300],
            type: 'artifact'
          };
          nodes.push(node);
          nodeMap.set(artifactId, node);

          // Add link from creator to artifact
          if (artifact.creator) {
            addAgentNode(artifact.creator);
            const linkId = `${artifact.creator}-${artifactId}`;
            if (!linkMap.has(linkId)) {
              const link: GraphLink = {
                source: artifact.creator,
                target: artifactId,
                value: 1,
                type: 'created',
                label: 'created',
                color: theme.palette.success.light
              };
              links.push(link);
              linkMap.set(linkId, link);
            }
          }
        }
      });
    }

    // Add goal nodes
    if (state.goals) {
      Object.entries(state.goals).forEach(([goalId, goal]) => {
        const node: GraphNode = {
          id: goalId,
          name: goal.description || `Goal ${goalId.substring(0, 6)}`,
          val: 4,
          color: theme.palette.info.light,
          type: 'goal'
        };
        nodes.push(node);
        nodeMap.set(goalId, node);

        // Add links from assigned agents to goal
        if (goal.assignedTo) {
          goal.assignedTo.forEach(agentId => {
            addAgentNode(agentId);
            const linkId = `${agentId}-${goalId}`;
            if (!linkMap.has(linkId)) {
              const link: GraphLink = {
                source: agentId,
                target: goalId,
                value: 1,
                type: 'assigned',
                label: 'assigned to',
                color: theme.palette.info.main
              };
              links.push(link);
              linkMap.set(linkId, link);
            }
          });
        }
      });
    }

    // Add communication links
    if (state.dynamicMessages) {
      Object.values(state.dynamicMessages).forEach(message => {
        if (message.from && message.to) {
          // Handle both string and array 'to' field
          const recipients = Array.isArray(message.to) ? message.to : [message.to];

          // Add sender node if it doesn't exist
          addAgentNode(message.from);

          recipients.forEach(recipient => {
            // Add recipient node if it doesn't exist
            addAgentNode(recipient);

            // Create or update link
            const linkId = `${message.from}-${recipient}`;
            if (linkMap.has(linkId)) {
              const existingLink = linkMap.get(linkId)!;
              existingLink.value += 0.5;
            } else {
              const link: GraphLink = {
                source: message.from,
                target: recipient,
                value: 1,
                type: 'message',
                color: theme.palette.grey[400]
              };
              links.push(link);
              linkMap.set(linkId, link);
            }
          });
        }
      });
    }

    // Update node sizes based on activity
    nodes.forEach(node => {
      if (node.type === 'agent') {
        // Count messages sent by this agent
        let messageCount = 0;
        if (state.dynamicMessages) {
          messageCount = Object.values(state.dynamicMessages).filter(
            msg => msg.from === node.id
          ).length;
        }

        // Adjust node size based on activity
        node.val = 5 + Math.min(messageCount / 2, 10);
      }
    });

    setGraphData({ nodes, links });
  }, [state, theme]);

  // Handle node click
  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node === selectedNode ? null : node);
    setSelectedLink(null);
  };

  // Handle link click
  const handleLinkClick = (link: GraphLink) => {
    setSelectedLink(link === selectedLink ? null : link);
    setSelectedNode(null);
  };

  // Zoom controls
  const zoomIn = () => {
    if (graphRef.current) {
      const currentZoom = graphRef.current.zoom();
      graphRef.current.zoom(currentZoom * 1.2, 400);
    }
  };

  const zoomOut = () => {
    if (graphRef.current) {
      const currentZoom = graphRef.current.zoom();
      graphRef.current.zoom(currentZoom / 1.2, 400);
    }
  };

  const resetZoom = () => {
    if (graphRef.current) {
      graphRef.current.zoomToFit(400, 50);
    }
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2, height: '100%', minHeight: '500px' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Agent Collaboration Network</Typography>
        <Box>
          <Tooltip title="Zoom In">
            <IconButton onClick={zoomIn} size="small">
              <ZoomInIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom Out">
            <IconButton onClick={zoomOut} size="small">
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Reset View">
            <IconButton onClick={resetZoom} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', height: 'calc(100% - 50px)', minHeight: '450px' }}>
        <Box sx={{ flexGrow: 1, position: 'relative', border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <CircularProgress />
            </Box>
          ) : graphData.nodes.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography variant="body1" color="text.secondary">
                No collaboration data available
              </Typography>
            </Box>
          ) : (
            <ForceGraph2D
              ref={graphRef}
              graphData={graphData}
              nodeLabel={(node: GraphNode) => `${node.name} (${node.type})`}
              nodeColor={(node: GraphNode) => node.color}
              nodeVal={(node: GraphNode) => node.val}
              linkWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
              linkColor={(link: GraphLink) => link.color || theme.palette.grey[400]}
              linkDirectionalParticles={4}
              linkDirectionalParticleWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
              onNodeClick={handleNodeClick}
              onLinkClick={handleLinkClick}
              onNodeHover={setHoveredNode}
              cooldownTicks={100}
              linkDirectionalParticleSpeed={0.01}
              nodeCanvasObject={(node, ctx, globalScale) => {
                const label = node.name;
                const fontSize = 12/globalScale;
                ctx.font = `${fontSize}px Sans-Serif`;
                const textWidth = ctx.measureText(label).width;
                const bckgDimensions = [textWidth, fontSize].map(n => n + fontSize * 0.2);

                ctx.fillStyle = node.color;
                ctx.beginPath();
                ctx.arc(node.x!, node.y!, node.val, 0, 2 * Math.PI, false);
                ctx.fill();

                // Draw label for hovered or selected node
                if (node === hoveredNode || node === selectedNode) {
                  ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                  ctx.fillRect(
                    node.x! - bckgDimensions[0] / 2,
                    node.y! - node.val - bckgDimensions[1] - 2,
                    bckgDimensions[0],
                    bckgDimensions[1]
                  );

                  ctx.textAlign = 'center';
                  ctx.textBaseline = 'middle';
                  ctx.fillStyle = '#000';
                  ctx.fillText(
                    label,
                    node.x!,
                    node.y! - node.val - fontSize / 2 - 2
                  );
                }
              }}
              linkCanvasObject={(link, ctx, globalScale) => {
                if (link === selectedLink) {
                  const start = link.source as any;
                  const end = link.target as any;

                  // Draw highlighted link
                  ctx.strokeStyle = theme.palette.primary.main;
                  ctx.lineWidth = 2;
                  ctx.beginPath();
                  ctx.moveTo(start.x, start.y);
                  ctx.lineTo(end.x, end.y);
                  ctx.stroke();

                  // Draw label if available
                  if (link.label) {
                    const midX = (start.x + end.x) / 2;
                    const midY = (start.y + end.y) / 2;

                    const fontSize = 12/globalScale;
                    ctx.font = `${fontSize}px Sans-Serif`;
                    const textWidth = ctx.measureText(link.label).width;
                    const bckgDimensions = [textWidth, fontSize].map(n => n + fontSize * 0.2);

                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    ctx.fillRect(
                      midX - bckgDimensions[0] / 2,
                      midY - bckgDimensions[1] / 2,
                      bckgDimensions[0],
                      bckgDimensions[1]
                    );

                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillStyle = '#000';
                    ctx.fillText(link.label, midX, midY);
                  }
                }
              }}
              width={800}
              height={500}
            />
          )}
        </Box>

        {/* Selection details panel */}
        {(selectedNode || selectedLink) && (
          <Card sx={{ width: 250, ml: 2, height: '100%', overflow: 'auto' }}>
            <CardContent>
              {selectedNode && (
                <>
                  <Typography variant="h6" gutterBottom>
                    {selectedNode.name}
                  </Typography>
                  <Chip
                    label={selectedNode.type}
                    size="small"
                    sx={{ mb: 2 }}
                    color={
                      selectedNode.type === 'agent' ? 'primary' :
                      selectedNode.type === 'artifact' ? 'secondary' : 'info'
                    }
                  />
                  <Divider sx={{ my: 1 }} />

                  {selectedNode.type === 'agent' && (
                    <>
                      <Typography variant="subtitle2">
                        Activity Level:
                      </Typography>
                      <Box sx={{
                        width: '100%',
                        height: 10,
                        bgcolor: 'grey.200',
                        borderRadius: 5,
                        mt: 1, mb: 2
                      }}>
                        <Box sx={{
                          width: `${Math.min((selectedNode.val - 5) * 10, 100)}%`,
                          height: '100%',
                          bgcolor: selectedNode.color,
                          borderRadius: 5
                        }} />
                      </Box>

                      <Typography variant="subtitle2">
                        Connections:
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 2 }}>
                        {graphData.links.filter(
                          link => link.source === selectedNode.id || link.target === selectedNode.id
                        ).length}
                      </Typography>
                    </>
                  )}

                  {selectedNode.type === 'artifact' && state?.artifacts && (
                    <>
                      <Typography variant="subtitle2">
                        Type:
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        {Object.values(state.artifacts)
                          .find(a => a.id === selectedNode.id)?.type || 'Unknown'}
                      </Typography>

                      <Typography variant="subtitle2">
                        Created by:
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        {agentNames[
                          Object.values(state.artifacts)
                            .find(a => a.id === selectedNode.id)?.creator || ''
                        ] || 'Unknown'}
                      </Typography>
                    </>
                  )}

                  {selectedNode.type === 'goal' && state?.goals && (
                    <>
                      <Typography variant="subtitle2">
                        Status:
                      </Typography>
                      <Chip
                        label={state.goals[selectedNode.id]?.status || 'Unknown'}
                        size="small"
                        color={
                          state.goals[selectedNode.id]?.status === 'completed' ? 'success' :
                          state.goals[selectedNode.id]?.status === 'in-progress' ? 'primary' :
                          state.goals[selectedNode.id]?.status === 'blocked' ? 'error' : 'default'
                        }
                        sx={{ mb: 1 }}
                      />

                      <Typography variant="subtitle2" sx={{ mt: 1 }}>
                        Assigned to:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                        {state.goals[selectedNode.id]?.assignedTo?.map(agentId => (
                          <Chip
                            key={agentId}
                            label={agentNames[agentId] || agentId}
                            size="small"
                            sx={{ mb: 0.5 }}
                          />
                        ))}
                      </Box>
                    </>
                  )}
                </>
              )}

              {selectedLink && (
                <>
                  <Typography variant="h6" gutterBottom>
                    Connection Details
                  </Typography>
                  <Divider sx={{ my: 1 }} />

                  <Typography variant="subtitle2">
                    Type:
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    {selectedLink.type || 'Connection'}
                  </Typography>

                  <Typography variant="subtitle2">
                    From:
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    {typeof selectedLink.source === 'string'
                      ? selectedLink.source
                      : (selectedLink.source as any).name || (selectedLink.source as any).id}
                  </Typography>

                  <Typography variant="subtitle2">
                    To:
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    {typeof selectedLink.target === 'string'
                      ? selectedLink.target
                      : (selectedLink.target as any).name || (selectedLink.target as any).id}
                  </Typography>

                  <Typography variant="subtitle2">
                    Strength:
                  </Typography>
                  <Box sx={{
                    width: '100%',
                    height: 10,
                    bgcolor: 'grey.200',
                    borderRadius: 5,
                    mt: 1, mb: 2
                  }}>
                    <Box sx={{
                      width: `${Math.min(selectedLink.value * 20, 100)}%`,
                      height: '100%',
                      bgcolor: selectedLink.color || theme.palette.primary.main,
                      borderRadius: 5
                    }} />
                  </Box>
                </>
              )}
            </CardContent>
          </Card>
        )}
      </Box>
    </Paper>
  );
};

export default AgentCollaborationGraph;
