'use client';

import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { DynamicWorkflowPhase } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';

interface PhaseTransition {
  from: DynamicWorkflowPhase | null;
  to: DynamicWorkflowPhase;
  timestamp: string;
  completedGoals?: string[];
}

interface PhaseTransitionVisualizerProps {
  transitions: PhaseTransition[];
  currentPhase: DynamicWorkflowPhase;
  goals: Record<string, any>;
  artifacts: Record<string, any>;
}

const PhaseTransitionVisualizer: React.FC<PhaseTransitionVisualizerProps> = ({
  transitions,
  currentPhase,
  goals,
  artifacts
}) => {
  const theme = useTheme();

  // Phase names
  const phaseNames: Record<string, string> = {
    [DynamicWorkflowPhase.PLANNING]: 'Planning',
    [DynamicWorkflowPhase.RESEARCH]: 'Research',
    [DynamicWorkflowPhase.CREATION]: 'Content Creation',
    [DynamicWorkflowPhase.REVIEW]: 'Review',
    [DynamicWorkflowPhase.FINALIZATION]: 'Finalization'
  };

  // Format time
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Get all phases in order
  const allPhases = [
    DynamicWorkflowPhase.PLANNING,
    DynamicWorkflowPhase.RESEARCH,
    DynamicWorkflowPhase.CREATION,
    DynamicWorkflowPhase.REVIEW,
    DynamicWorkflowPhase.FINALIZATION
  ];

  // Get current phase index
  const currentPhaseIndex = allPhases.indexOf(currentPhase);

  // Get artifacts related to a goal
  const getGoalArtifacts = (goalId: string) => {
    const goal = goals[goalId];
    if (!goal || !goal.artifacts || !artifacts) return [];

    return goal.artifacts
      .map((artifactId: string) => artifacts[artifactId])
      .filter(Boolean);
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>
        Workflow Progress & Phase Transitions
      </Typography>

      <Stepper activeStep={currentPhaseIndex} orientation="vertical">
        {allPhases.map((phase, index) => {
          // Find transitions to this phase
          const transitionsToPhase = transitions.filter(t => t.to === phase);
          const isActive = currentPhase === phase;
          const isCompleted = currentPhaseIndex > index;

          return (
            <Step key={phase} completed={isCompleted}>
              <StepLabel
                StepIconComponent={() =>
                  isCompleted ? (
                    <CheckCircleIcon color="success" />
                  ) : isActive ? (
                    <PlayArrowIcon color="primary" />
                  ) : (
                    <CheckCircleIcon color="disabled" />
                  )
                }
              >
                <Typography variant="subtitle1">
                  {phaseNames[phase]} Phase
                </Typography>
              </StepLabel>
              <StepContent>
                {transitionsToPhase.length > 0 ? (
                  <Box sx={{ ml: 2 }}>
                    {transitionsToPhase.map((transition, idx) => (
                      <Box key={idx} sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Chip
                            label={transition.from ? phaseNames[transition.from] : 'Initial'}
                            size="small"
                            color="default"
                            sx={{ mr: 1 }}
                          />
                          <ArrowForwardIcon fontSize="small" sx={{ mx: 1 }} />
                          <Chip
                            label={phaseNames[transition.to]}
                            size="small"
                            color="primary"
                          />
                          <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                            {formatTime(transition.timestamp)}
                          </Typography>
                        </Box>

                        {transition.completedGoals && transition.completedGoals.length > 0 && (
                          <Box sx={{ ml: 2 }}>
                            <Typography variant="body2" gutterBottom>
                              Completed Goals:
                            </Typography>
                            <List dense>
                              {transition.completedGoals.map(goalId => {
                                const goal = goals[goalId];
                                if (!goal) return null;

                                const goalArtifacts = getGoalArtifacts(goalId);

                                return (
                                  <ListItem key={goalId} sx={{ py: 0.5 }}>
                                    <ListItemIcon sx={{ minWidth: 32 }}>
                                      <CheckCircleIcon color="success" fontSize="small" />
                                    </ListItemIcon>
                                    <ListItemText
                                      primary={goal.description}
                                      secondary={
                                        goalArtifacts.length > 0 ? (
                                          <Box sx={{ mt: 0.5 }}>
                                            <Typography variant="caption" color="text.secondary">
                                              Artifacts:
                                            </Typography>
                                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                                              {goalArtifacts.map(artifact => (
                                                <Chip
                                                  key={artifact.id}
                                                  label={artifact.name || `Artifact ${artifact.id.substring(0, 6)}`}
                                                  size="small"
                                                  variant="outlined"
                                                  sx={{ mb: 0.5 }}
                                                />
                                              ))}
                                            </Box>
                                          </Box>
                                        ) : null
                                      }
                                    />
                                  </ListItem>
                                );
                              })}
                            </List>
                          </Box>
                        )}
                      </Box>
                    ))}
                  </Box>
                ) : isActive ? (
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                    Current active phase
                  </Typography>
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                    No transitions recorded for this phase
                  </Typography>
                )}
              </StepContent>
            </Step>
          );
        })}
      </Stepper>
    </Paper>
  );
};

export default PhaseTransitionVisualizer;
