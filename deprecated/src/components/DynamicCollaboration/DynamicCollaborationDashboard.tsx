'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Snackbar,
  Grid,
  Chip,
  Button,
  Divider,
  useMediaQuery,
  useTheme,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress
} from '@mui/material';

// Icons
import RefreshIcon from '@mui/icons-material/Refresh';
import ArticleIcon from '@mui/icons-material/Article';
import GroupIcon from '@mui/icons-material/Group';
import AssessmentIcon from '@mui/icons-material/Assessment';
import FolderIcon from '@mui/icons-material/Folder';
import ChatIcon from '@mui/icons-material/Chat';
import PsychologyIcon from '@mui/icons-material/Psychology';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import VisibilityIcon from '@mui/icons-material/Visibility';
import TrackChangesIcon from '@mui/icons-material/TrackChanges';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';

// Components
import ArticleInitiationForm from './ArticleInitiationForm';
import AgentCollaborationGraph from './AgentCollaborationGraph';
import GoalProgressTracker from './GoalProgressTracker';
import AgentStatusPanel from './AgentStatusPanel';
import ArtifactGallery from './ArtifactGallery';
import AgentCommunicationPanel from './AgentCommunicationPanel';
import ReasoningVisualizer from './ReasoningVisualizer';
import ArticlePreview from './ArticlePreview';
import PhaseTransitionVisualizer from './PhaseTransitionVisualizer';
import GoalCompletionVisualizer from './GoalCompletionVisualizer';

// Types
import { DynamicCollaborationState, DynamicWorkflowPhase } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';
import { dynamicCollaborationClientV2 } from '../../lib/dynamic-collaboration-client-v2';

interface DynamicCollaborationDashboardProps {
  initialSessionId?: string;
}

const DynamicCollaborationDashboard: React.FC<DynamicCollaborationDashboardProps> = ({
  initialSessionId
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State
  const [sessionId, setSessionId] = useState<string | null>(initialSessionId || null);
  const [collaborationState, setCollaborationState] = useState<DynamicCollaborationState | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(!isMobile);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Fetch session data
  useEffect(() => {
    if (sessionId) {
      fetchSessionData(sessionId);

      // Set up polling for updates - use a shorter interval to catch phase transitions quickly
      const intervalId = setInterval(() => {
        fetchSessionData(sessionId, true);
      }, 3000);

      return () => clearInterval(intervalId);
    }
  }, [sessionId]);

  // Track phase changes to provide feedback to the user
  const [previousPhase, setPreviousPhase] = useState<DynamicWorkflowPhase | null>(null);
  const [phaseTransitions, setPhaseTransitions] = useState<Array<{
    from: DynamicWorkflowPhase | null;
    to: DynamicWorkflowPhase;
    timestamp: string;
    completedGoals?: string[];
  }>>([]);

  useEffect(() => {
    if (collaborationState && collaborationState.currentPhase) {
      // If we have a previous phase and it's different from the current one
      if (previousPhase && previousPhase !== collaborationState.currentPhase) {
        // Find goals completed in the previous phase
        const completedGoals = collaborationState.completedGoals || [];
        const completedGoalIds = completedGoals
          .filter(goalId => {
            const goal = collaborationState.goals?.[goalId];
            return goal && goal.endTime &&
              // Check if the goal was completed recently (in the last minute)
              new Date(goal.endTime).getTime() > Date.now() - 60000;
          });

        // Add the phase transition to our history
        setPhaseTransitions(prev => [
          ...prev,
          {
            from: previousPhase,
            to: collaborationState.currentPhase,
            timestamp: new Date().toISOString(),
            completedGoals: completedGoalIds
          }
        ]);

        // Show a notification about the phase transition
        setSnackbar({
          open: true,
          message: `Workflow transitioned to ${getPhaseLabel(collaborationState.currentPhase)} phase`,
          severity: 'info'
        });
      }

      // Update the previous phase
      setPreviousPhase(collaborationState.currentPhase);
    }
  }, [collaborationState?.currentPhase, collaborationState?.completedGoals]);

  // Fetch session data from API
  const fetchSessionData = async (id: string, silent: boolean = false) => {
    if (!silent) setLoading(true);

    try {
      const result = await dynamicCollaborationClientV2.getState(id);
      setCollaborationState(result.state);

      if (!silent) {
        setSnackbar({
          open: true,
          message: 'Session data updated successfully',
          severity: 'success'
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      if (!silent) {
        setSnackbar({
          open: true,
          message: `Error: ${errorMessage}`,
          severity: 'error'
        });
      }
    } finally {
      if (!silent) setLoading(false);
    }
  };

  // Start a new collaboration session
  const startNewSession = async (formData: any) => {
    setLoading(true);
    setError(null);

    try {
      const result = await dynamicCollaborationClientV2.initiate(formData);
      setSessionId(result.sessionId);
      setActiveTab(1); // Switch to the collaboration view

      setSnackbar({
        open: true,
        message: 'New collaboration session started successfully',
        severity: 'success'
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Toggle drawer
  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Calculate progress percentage
  const calculateProgress = (): number => {
    if (!collaborationState) return 0;

    const { completedGoals, goals } = collaborationState;
    if (!completedGoals || !goals) return 0;

    const totalGoals = Object.keys(goals).length;
    if (totalGoals === 0) return 0;

    return Math.round((completedGoals.length / totalGoals) * 100);
  };

  // Get current phase index
  const getCurrentPhaseIndex = (): number => {
    if (!collaborationState) return 0;

    const phases = [
      DynamicWorkflowPhase.PLANNING,
      DynamicWorkflowPhase.RESEARCH,
      DynamicWorkflowPhase.CREATION,
      DynamicWorkflowPhase.REVIEW,
      DynamicWorkflowPhase.FINALIZATION
    ];

    const currentPhase = collaborationState.currentPhase;
    const index = phases.indexOf(currentPhase);

    return index >= 0 ? index : 0;
  };

  // Get phase label
  const getPhaseLabel = (phase: DynamicWorkflowPhase): string => {
    switch (phase) {
      case DynamicWorkflowPhase.PLANNING:
        return 'Planning';
      case DynamicWorkflowPhase.RESEARCH:
        return 'Research';
      case DynamicWorkflowPhase.CREATION:
        return 'Content Creation';
      case DynamicWorkflowPhase.REVIEW:
        return 'Review';
      case DynamicWorkflowPhase.FINALIZATION:
        return 'Finalization';
      default:
        return 'Unknown';
    }
  };

  // Complete a goal for testing purposes
  const completeGoal = async (goalType: string) => {
    if (!sessionId) return;

    setLoading(true);

    try {
      const result = await dynamicCollaborationClientV2.completeGoal(sessionId, goalType);

      if (result.success) {
        setSnackbar({
          open: true,
          message: `Successfully completed goal of type ${goalType}`,
          severity: 'success'
        });

        // Refresh the state
        await fetchSessionData(sessionId);
      } else {
        setSnackbar({
          open: true,
          message: `Failed to complete goal of type ${goalType}`,
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error completing goal:', error);
      setSnackbar({
        open: true,
        message: `Error completing goal: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Render tab content
  const renderTabContent = () => {
    if (!sessionId && activeTab !== 0) {
      return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            Please start a new collaboration session
          </Typography>
          <Button
            variant="contained"
            color="primary"
            sx={{ mt: 2 }}
            onClick={() => setActiveTab(0)}
          >
            Start New Session
          </Button>
        </Box>
      );
    }

    switch (activeTab) {
      case 0: // New Session
        return (
          <ArticleInitiationForm
            onSubmit={startNewSession}
            loading={loading}
          />
        );
      case 1: // Overview
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <AgentCollaborationGraph
                sessionId={sessionId!}
                state={collaborationState}
                loading={loading}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <GoalProgressTracker
                sessionId={sessionId!}
                state={collaborationState}
                loading={loading}
              />
            </Grid>

            {/* Phase Transition Visualizer */}
            <Grid item xs={12} md={6}>
              <PhaseTransitionVisualizer
                transitions={phaseTransitions}
                currentPhase={collaborationState?.currentPhase || DynamicWorkflowPhase.PLANNING}
                goals={collaborationState?.goals || {}}
                artifacts={collaborationState?.artifacts || {}}
              />
            </Grid>

            {/* Goal Completion Visualizer */}
            <Grid item xs={12} md={6}>
              <GoalCompletionVisualizer
                goals={collaborationState?.goals || {}}
                artifacts={collaborationState?.artifacts || {}}
                completedGoals={collaborationState?.completedGoals || []}
              />
            </Grid>
          </Grid>
        );
      case 2: // Agents
        return (
          <AgentStatusPanel
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
          />
        );
      case 3: // Artifacts
        return (
          <ArtifactGallery
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
            onRefresh={() => fetchSessionData(sessionId!)}
          />
        );
      case 4: // Communication
        return (
          <AgentCommunicationPanel
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
          />
        );
      case 5: // Reasoning
        return (
          <ReasoningVisualizer
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
          />
        );
      case 6: // Article Preview
        return (
          <ArticlePreview
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={toggleDrawer}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Dynamic Agent Collaboration
          </Typography>
          {sessionId && (
            <>
              <Chip
                label={`Session: ${sessionId.substring(0, 8)}...`}
                color="secondary"
                sx={{ mr: 1 }}
              />
              <Chip
                label={`Progress: ${calculateProgress()}%`}
                color="primary"
              />
              <IconButton color="inherit" onClick={() => fetchSessionData(sessionId)}>
                <RefreshIcon />
              </IconButton>
            </>
          )}
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={drawerOpen}
        onClose={toggleDrawer}
        sx={{
          width: 240,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 240,
            boxSizing: 'border-box',
            top: ['48px', '56px', '64px'],
            height: 'auto',
            bottom: 0,
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            <ListItem button onClick={() => setActiveTab(0)}>
              <ListItemIcon>
                <ArticleIcon />
              </ListItemIcon>
              <ListItemText primary="New Session" />
            </ListItem>
            <Divider />
            <ListItem button onClick={() => setActiveTab(1)} disabled={!sessionId}>
              <ListItemIcon>
                <TrackChangesIcon />
              </ListItemIcon>
              <ListItemText primary="Overview" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(2)} disabled={!sessionId}>
              <ListItemIcon>
                <GroupIcon />
              </ListItemIcon>
              <ListItemText primary="Agents" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(3)} disabled={!sessionId}>
              <ListItemIcon>
                <FolderIcon />
              </ListItemIcon>
              <ListItemText primary="Artifacts" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(4)} disabled={!sessionId}>
              <ListItemIcon>
                <ChatIcon />
              </ListItemIcon>
              <ListItemText primary="Communication" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(5)} disabled={!sessionId}>
              <ListItemIcon>
                <PsychologyIcon />
              </ListItemIcon>
              <ListItemText primary="Reasoning" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(6)} disabled={!sessionId}>
              <ListItemIcon>
                <VisibilityIcon />
              </ListItemIcon>
              <ListItemText primary="Article Preview" />
            </ListItem>
          </List>
        </Box>
      </Drawer>

      {/* Main content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3, mt: 8 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Mobile tabs */}
        {isMobile && (
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ mb: 2 }}
          >
            <Tab icon={<ArticleIcon />} label="New" />
            <Tab icon={<TrackChangesIcon />} label="Overview" disabled={!sessionId} />
            <Tab icon={<GroupIcon />} label="Agents" disabled={!sessionId} />
            <Tab icon={<FolderIcon />} label="Artifacts" disabled={!sessionId} />
            <Tab icon={<ChatIcon />} label="Comms" disabled={!sessionId} />
            <Tab icon={<PsychologyIcon />} label="Reasoning" disabled={!sessionId} />
            <Tab icon={<VisibilityIcon />} label="Preview" disabled={!sessionId} />
          </Tabs>
        )}

        {/* Workflow Phase Stepper */}
        {sessionId && collaborationState && activeTab !== 0 && (
          <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              Workflow Progress
            </Typography>
            <Stepper activeStep={getCurrentPhaseIndex()} alternativeLabel sx={{ mb: 2 }}>
              {[
                DynamicWorkflowPhase.PLANNING,
                DynamicWorkflowPhase.RESEARCH,
                DynamicWorkflowPhase.CREATION,
                DynamicWorkflowPhase.REVIEW,
                DynamicWorkflowPhase.FINALIZATION
              ].map((phase, index) => (
                <Step key={phase} completed={index < getCurrentPhaseIndex()}>
                  <StepLabel>{getPhaseLabel(phase)}</StepLabel>
                </Step>
              ))}
            </Stepper>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={calculateProgress()}
                  sx={{ height: 10, borderRadius: 5 }}
                />
              </Box>
              <Box sx={{ minWidth: 35 }}>
                <Typography variant="body2" color="text.secondary">{`${calculateProgress()}%`}</Typography>
              </Box>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Current Phase: <Chip
                label={getPhaseLabel(collaborationState.currentPhase)}
                color="primary"
                size="small"
                sx={{ ml: 1 }}
              />
            </Typography>

            {/* Test Controls (for development only) */}
            {process.env.NODE_ENV === 'development' && (
              <Box sx={{ mt: 2, p: 1, border: '1px dashed #ccc', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Test Controls (Dev Only)
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => completeGoal('market_research')}
                    disabled={loading}
                  >
                    Complete Market Research
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => completeGoal('keyword_analysis')}
                    disabled={loading}
                  >
                    Complete Keyword Analysis
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => completeGoal('content_strategy')}
                    disabled={loading}
                  >
                    Complete Content Strategy
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => completeGoal('content_creation')}
                    disabled={loading}
                  >
                    Complete Content Creation
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => completeGoal('seo_optimization')}
                    disabled={loading}
                  >
                    Complete SEO Optimization
                  </Button>
                </Box>
              </Box>
            )}
          </Paper>
        )}

        {/* Tab content */}
        <Box sx={{ position: 'relative' }}>
          {loading && !collaborationState && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {renderTabContent()}
        </Box>
      </Box>
    </Box>
  );
};

export default DynamicCollaborationDashboard;
