// src/components/ContentGeneration/AgentProgress.tsx
'use client'

import React from 'react'

interface AgentProgressProps {
  progress: number
  phase: string
  stage: string
  message: string
  agentMessages: string[]
  showAgentDiscussion: boolean
  onToggleAgentDiscussion: () => void
}

const AgentProgress: React.FC<AgentProgressProps> = ({
  progress,
  phase,
  stage,
  message,
  agentMessages,
  showAgentDiscussion,
  onToggleAgentDiscussion
}) => {
  // Get phase icon based on current phase
  const getPhaseIcon = () => {
    switch (phase) {
      case 'planning':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        )
      case 'discussion':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            <line x1="9" y1="10" x2="15" y2="10"></line>
            <line x1="12" y1="7" x2="12" y2="13"></line>
          </svg>
        )
      case 'execution':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
        )
      case 'review':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
          </svg>
        )
      default:
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        )
    }
  }

  return (
    <div className="agent-progress">
      <h2>Generating Your Content</h2>
      <p className="description">Our AI content team is working on your request.</p>
      
      <div className="progress-container">
        <div className="progress-bar-container">
          <div 
            className="progress-bar" 
            style={{width: `${progress}%`}}
          ></div>
        </div>
        <div className="progress-percentage">{progress}%</div>
      </div>
      
      <div className="current-phase">
        <div className="phase-icon">{getPhaseIcon()}</div>
        <div className="phase-info">
          <div className="phase-name">
            <span className="phase-label">Phase:</span> 
            <span className="phase-value">{phase.charAt(0).toUpperCase() + phase.slice(1)}</span>
          </div>
          <div className="stage-name">
            <span className="stage-label">Stage:</span> 
            <span className="stage-value">{stage.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}</span>
          </div>
        </div>
      </div>
      
      <div className="progress-message">{message}</div>
      
      <div className="agent-discussion-toggle">
        <button onClick={onToggleAgentDiscussion}>
          {showAgentDiscussion ? 'Hide Agent Discussion' : 'Show Agent Discussion'}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            style={{ transform: showAgentDiscussion ? 'rotate(180deg)' : 'rotate(0deg)' }}
          >
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </button>
      </div>
      
      {showAgentDiscussion && (
        <div className="agent-discussion">
          <h3>Agent Discussion</h3>
          <div className="agent-messages">
            {agentMessages.map((message, index) => (
              <div key={index} className="agent-message">
                {message}
              </div>
            ))}
          </div>
        </div>
      )}
      
      <style jsx>{`
        .agent-progress {
          margin-bottom: 2rem;
          padding: 2rem;
          background-color: var(--theme-elevation-50);
          border-radius: 8px;
          border: 1px solid var(--theme-elevation-100);
        }
        
        h2 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }
        
        .description {
          color: var(--theme-elevation-500);
          margin-bottom: 1.5rem;
        }
        
        .progress-container {
          display: flex;
          align-items: center;
          margin-bottom: 1.5rem;
        }
        
        .progress-bar-container {
          flex-grow: 1;
          height: 8px;
          background-color: var(--theme-elevation-100);
          border-radius: 4px;
          overflow: hidden;
          margin-right: 1rem;
        }
        
        .progress-bar {
          height: 100%;
          background-color: var(--theme-success-500);
          border-radius: 4px;
          transition: width 0.3s ease;
        }
        
        .progress-percentage {
          font-weight: 600;
          min-width: 40px;
          text-align: right;
        }
        
        .current-phase {
          display: flex;
          align-items: center;
          margin-bottom: 1rem;
          padding: 1rem;
          background-color: white;
          border-radius: 8px;
          border: 1px solid var(--theme-elevation-100);
        }
        
        .phase-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          background-color: var(--theme-elevation-100);
          border-radius: 50%;
          margin-right: 1rem;
          color: var(--theme-elevation-800);
        }
        
        .phase-info {
          flex-grow: 1;
        }
        
        .phase-name {
          font-weight: 600;
          margin-bottom: 0.25rem;
        }
        
        .phase-label, .stage-label {
          color: var(--theme-elevation-500);
          margin-right: 0.5rem;
        }
        
        .phase-value, .stage-value {
          color: var(--theme-elevation-800);
        }
        
        .progress-message {
          font-size: 1.1rem;
          margin: 1.5rem 0;
          text-align: center;
          color: var(--theme-elevation-800);
        }
        
        .agent-discussion-toggle {
          display: flex;
          justify-content: center;
          margin: 1rem 0;
        }
        
        .agent-discussion-toggle button {
          display: flex;
          align-items: center;
          background: none;
          border: none;
          color: var(--theme-elevation-800);
          font-size: 0.9rem;
          cursor: pointer;
          padding: 0.5rem 1rem;
          border-radius: 4px;
          transition: background-color 0.2s ease;
        }
        
        .agent-discussion-toggle button:hover {
          background-color: var(--theme-elevation-100);
        }
        
        .agent-discussion-toggle svg {
          margin-left: 0.5rem;
          transition: transform 0.2s ease;
        }
        
        .agent-discussion {
          margin-top: 1rem;
          padding: 1rem;
          background-color: white;
          border-radius: 8px;
          border: 1px solid var(--theme-elevation-100);
        }
        
        .agent-discussion h3 {
          font-size: 1rem;
          font-weight: 600;
          margin-bottom: 1rem;
          color: var(--theme-elevation-800);
        }
        
        .agent-messages {
          max-height: 300px;
          overflow-y: auto;
        }
        
        .agent-message {
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          background-color: var(--theme-elevation-50);
          border-radius: 4px;
          font-size: 0.9rem;
          line-height: 1.4;
        }
        
        .agent-message:last-child {
          margin-bottom: 0;
        }
      `}</style>
    </div>
  )
}

export default AgentProgress