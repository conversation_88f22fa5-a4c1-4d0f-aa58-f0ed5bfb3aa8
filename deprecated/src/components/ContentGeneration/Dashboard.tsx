'use client'

import React, { useState, useRef, useEffect } from 'react'
import ContentTypeSelector from './ContentTypeSelector'
import ContentBriefForm from './ContentBriefForm'
import EnhancedAgentProgress from './EnhancedAgentProgress'
import ContentPreview from './ContentPreview'

// Define the content types
export type ContentType = 'product-page' | 'blog-article' | 'buying-guide'

// Define the content brief form state
export interface ContentBriefState {
  contentType: ContentType
  topicFocus: string
  category: string
  targetAudience: string
  primaryKeywords: string[]
  tonePreference: string
  competitorUrls?: string[]
  internalData?: any
  generalInstructions?: string
  sessionId?: string // Added sessionId for tracking collaboration sessions
}

// Define the generation result interface
export interface ContentGenerationResult {
  contentType: ContentType
  title: string
  metaDescription: string
  sections: Array<{
    id: string
    type: string
    title: string
    content: string | string[] | Array<{question: string, answer: string}>
  }>
  seoScore: {
    overall: number
    [key: string]: number
  }
  internalLinks: Array<{
    anchorText: string
    targetTitle: string
    targetURL: string
    context: string
    relevance: number
  }>
  improvementSuggestions: Array<{
    area: string
    suggestion: string
    priority: 'low' | 'medium' | 'high'
  }>
  agentDiscussion?: {
    planning: string[]
    discussion: string[]
    execution: string[]
    review: string[]
  }
}

// Main dashboard component
const ContentGenerationDashboard: React.FC = () => {
  // State management
  const [step, setStep] = useState<'select-type' | 'create-brief' | 'generating' | 'preview'>('select-type')
  const [contentType, setContentType] = useState<ContentType | null>(null)
  const [briefState, setBriefState] = useState<ContentBriefState | null>(null)
  const [generationResult, setGenerationResult] = useState<ContentGenerationResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showAgentDiscussion, setShowAgentDiscussion] = useState(false)
  
  // Progress tracking state
  const [progress, setProgress] = useState({
    phase: 'planning', // planning, discussion, execution, review
    stage: '',
    progress: 0,
    message: '',
    agentMessages: [] as string[]
  })
  
  // Use refs to track timeouts so we can clear them
  const timeoutRefs = useRef<number[]>([])

  // Handle content type selection
  const handleContentTypeSelect = (type: ContentType) => {
    setContentType(type)
    setStep('create-brief')
  }

  // Handle brief form submission
  const handleBriefSubmit = async (briefData: ContentBriefState) => {
    setIsLoading(true)
    setBriefState(briefData)
    setStep('generating')
    setError(null)
    
    // Clear any existing timeouts and simulate new progress updates
    clearAllTimeouts()
    simulateProgressUpdates()
    
    try {
      // Call the API to generate content
      const response = await fetch('/api/content-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(briefData),
      })
      
      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || 'Content generation failed')
      }
      
      // Set the generation result
      setGenerationResult(data.content)
      setStep('preview')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
      // Go back to brief form on error
      setStep('create-brief')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle content publication
  const handlePublishContent = async (content: ContentGenerationResult, status: 'draft' | 'published') => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Call the API to publish content
      const response = await fetch('/api/content-publication', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content, status }),
      })
      
      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || 'Content publication failed')
      }
      
      // Reset state and go back to content type selection
      setContentType(null)
      setBriefState(null)
      setGenerationResult(null)
      setStep('select-type')
      
      // Show success message
      alert(`Content published successfully as ${status}!`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle content regeneration with feedback
  const handleRegenerateContent = async (feedback: string) => {
    if (!briefState) return
    
    setIsLoading(true)
    setStep('generating')
    setError(null)
    
    // Clear any existing timeouts and simulate new progress updates
    clearAllTimeouts()
    simulateProgressUpdates()
    
    try {
      // Call the API to regenerate content with feedback
      const response = await fetch('/api/content-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...briefState,
          feedback,
          isRetry: true
        }),
      })
      
      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || 'Content regeneration failed')
      }
      
      // Set the regenerated content
      setGenerationResult(data.content)
      setStep('preview')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
      // Go back to preview on error
      setStep('preview')
    } finally {
      setIsLoading(false)
    }
  }

  // Clear all timeouts to prevent memory leaks and infinite loops
  const clearAllTimeouts = () => {
    timeoutRefs.current.forEach(timeoutId => window.clearTimeout(timeoutId))
    timeoutRefs.current = []
  }

  // Clean up timeouts on component unmount
  useEffect(() => {
    return () => clearAllTimeouts()
  }, [])
  
  // Simulate progress updates for better UX
  const simulateProgressUpdates = () => {
    // Clear any existing timeouts to prevent issues
    clearAllTimeouts()
    
    // Planning phase
    setProgress({
      phase: 'planning',
      stage: 'planning',
      progress: 10,
      message: 'Planning content generation approach...',
      agentMessages: [
        'Content Lead: Analyzing brief requirements...',
        'Market Research Agent: Planning research strategy...',
        'SEO Agent: Planning keyword research approach...',
        'Content Agent: Developing content structure...'
      ]
    })
    
    // Discussion phase
    const timeout1 = window.setTimeout(() => {
      setProgress({
        phase: 'discussion',
        stage: 'discussion',
        progress: 25,
        message: 'Agents discussing content strategy...',
        agentMessages: [
          'Content Lead: Coordinating agent discussion...',
          'Market Research Agent: Sharing research plan with team...',
          'SEO Agent: Suggesting primary keyword focus...',
          'Content Agent: Proposing section structure...',
          'Editorial Agent: Reviewing tone and style guidelines...'
        ]
      })
    }, 3000)
    timeoutRefs.current.push(timeout1)
    
    // Execution phase
    const timeout2 = window.setTimeout(() => {
      setProgress({
        phase: 'execution',
        stage: 'research',
        progress: 40,
        message: 'Researching market and competitors...',
        agentMessages: [
          'Market Research Agent: Gathering industry data...',
          'Market Research Agent: Analyzing competitor content...',
          'SEO Agent: Researching keyword opportunities...'
        ]
      })
    }, 6000)
    timeoutRefs.current.push(timeout2)
    
    const timeout3 = window.setTimeout(() => {
      setProgress({
        phase: 'execution',
        stage: 'content-creation',
        progress: 60,
        message: 'Creating content sections...',
        agentMessages: [
          'Content Agent: Drafting introduction section...',
          'Content Agent: Developing main content sections...',
          'Content Agent: Creating supporting elements...'
        ]
      })
    }, 9000)
    timeoutRefs.current.push(timeout3)
    
    const timeout4 = window.setTimeout(() => {
      setProgress({
        phase: 'execution',
        stage: 'editorial',
        progress: 75,
        message: 'Editing and refining content...',
        agentMessages: [
          'Editorial Agent: Reviewing content for tone consistency...',
          'Editorial Agent: Checking grammar and readability...',
          'SEO Agent: Optimizing content for search engines...'
        ]
      })
    }, 12000)
    timeoutRefs.current.push(timeout4)
    
    // Review phase
    const timeout5 = window.setTimeout(() => {
      setProgress({
        phase: 'review',
        stage: 'final-review',
        progress: 90,
        message: 'Performing final content review...',
        agentMessages: [
          'Content Lead: Conducting final quality check...',
          'SEO Agent: Verifying keyword optimization...',
          'Editorial Agent: Confirming brand voice consistency...',
          'Internal Linking Agent: Adding relevant internal links...'
        ]
      })
    }, 15000)
    timeoutRefs.current.push(timeout5)
    
    // Completion
    const timeout6 = window.setTimeout(() => {
      setProgress({
        phase: 'complete',
        stage: 'complete',
        progress: 100,
        message: 'Content generation complete!',
        agentMessages: [
          'Content Lead: Content generation successfully completed!',
          'Content Lead: Preparing content for preview...'
        ]
      })
    }, 18000)
    timeoutRefs.current.push(timeout6)
  }

  // Handle going back to previous step
  const handleBack = () => {
    if (step === 'create-brief') {
      setStep('select-type')
      setContentType(null)
    } else if (step === 'preview') {
      setStep('create-brief')
      setGenerationResult(null)
    }
  }

  // Toggle agent discussion visibility
  const toggleAgentDiscussion = () => {
    setShowAgentDiscussion(!showAgentDiscussion)
  }

  // Render the appropriate step
  return (
    <div className="content-generation-dashboard">
      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={() => setError(null)}>Dismiss</button>
        </div>
      )}
      
      {step === 'select-type' && (
        <ContentTypeSelector onSelect={handleContentTypeSelect} />
      )}
      
      {step === 'create-brief' && (
        <ContentBriefForm 
          contentType={contentType!} 
          initialValues={briefState} 
          onSubmit={handleBriefSubmit} 
          onBack={handleBack}
          isLoading={isLoading}
        />
      )}
      
      {step === 'generating' && (
        <EnhancedAgentProgress 
          sessionId={briefState?.sessionId || 'temp-session-id'}
          progress={progress.progress} 
          phase={progress.phase}
          message={progress.message}
          agentMessages={progress.agentMessages}
          showAgentDiscussion={showAgentDiscussion}
          onToggleAgentDiscussion={toggleAgentDiscussion}
        />
      )}
      
      {step === 'preview' && generationResult && (
        <>
          <ContentPreview 
            content={generationResult} 
            onPublish={handlePublishContent}
            onRegenerate={handleRegenerateContent}
            onBack={handleBack}
            isLoading={isLoading}
          />
        </>
      )}
      
      <style jsx>{`
        .content-generation-dashboard {
          position: relative;
        }
        
        .error-message {
          background-color: #fff0f0;
          border: 1px solid #ffcccc;
          border-radius: 4px;
          padding: 1rem;
          margin-bottom: 1.5rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .error-message p {
          margin: 0;
          color: #cc0000;
        }
        
        .error-message button {
          background: none;
          border: none;
          color: #cc0000;
          text-decoration: underline;
          cursor: pointer;
        }
        
        .preview-extras {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.5rem;
          margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
          .preview-extras {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  )
}

export default ContentGenerationDashboard