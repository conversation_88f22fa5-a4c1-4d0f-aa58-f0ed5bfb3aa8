// src/components/EnhancedCollaboration/CollaborativeWorkflowVisualizer.tsx

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Tabs,
  Tab,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>l,
  Step<PERSON>ontent,
  Chip,
  Avatar,
  IconButton,
  Toolt<PERSON>
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AgentReasoningVisualizer from './AgentReasoningVisualizer';
import { IterativeArtifact } from '../../app/(payload)/api/agents/collaborative-iteration/types';

// Define the props interface
interface CollaborativeWorkflowVisualizerProps {
  sessionId: string;
  state: any; // Collaborative state
  onRefresh?: () => void; // Optional refresh callback
}

// Define the workflow phases
const workflowPhases = [
  {
    label: 'Planning',
    description: 'Agents collaborate to plan content structure and approach'
  },
  {
    label: 'Research',
    description: 'Gathering information and keywords for content creation'
  },
  {
    label: 'Creation',
    description: 'Generating initial content drafts based on research'
  },
  {
    label: 'Review',
    description: 'Analyzing content quality and SEO performance'
  },
  {
    label: 'Refinement',
    description: 'Iterative improvement based on feedback and analysis'
  },
  {
    label: 'Finalization',
    description: 'Preparing the final optimized content for publication'
  }
];

// Map agent IDs to colors and icons
const agentConfig: Record<string, { color: string; name: string }> = {
  'content-generation': {
    color: '#4caf50',
    name: 'Content Generation'
  },
  'seo-optimization': {
    color: '#2196f3',
    name: 'SEO Optimization'
  },
  'market-research': {
    color: '#ff9800',
    name: 'Market Research'
  },
  'content-strategy': {
    color: '#9c27b0',
    name: 'Content Strategy'
  },
  'seo-keyword': {
    color: '#00bcd4',
    name: 'SEO Keyword'
  }
};

/**
 * Component to visualize the collaborative workflow between agents
 */
const CollaborativeWorkflowVisualizer: React.FC<CollaborativeWorkflowVisualizerProps> = ({ 
  sessionId, 
  state, 
  onRefresh 
}) => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedArtifactId, setSelectedArtifactId] = useState<string | null>(null);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  // Get current phase index
  const currentPhaseIndex = workflowPhases.findIndex(
    phase => phase.label.toLowerCase() === state?.currentPhase?.toLowerCase()
  );

  // Get all artifacts as an array
  const artifacts = state?.artifacts ? Object.values(state.artifacts) : [];
  
  // Get all iterations from all artifacts
  const allIterations = artifacts.flatMap((artifact: any) => 
    artifact.iterations.map((iteration: any) => ({
      ...iteration,
      artifactId: artifact.id,
      artifactType: artifact.type,
      artifactName: artifact.name
    }))
  );

  // Sort iterations by timestamp
  const sortedIterations = [...allIterations].sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  // Get selected artifact
  const selectedArtifact = selectedArtifactId 
    ? state?.artifacts?.[selectedArtifactId] 
    : null;

  return (
    <Box sx={{ mb: 4 }}>
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">
            Collaborative Workflow
          </Typography>
          {onRefresh && (
            <IconButton onClick={onRefresh} size="small" color="primary">
              <RefreshIcon />
            </IconButton>
          )}
        </Box>

        <Divider sx={{ mb: 2 }} />
        
        {/* Workflow Phase Stepper */}
        <Stepper activeStep={currentPhaseIndex} orientation="horizontal" sx={{ mb: 4 }}>
          {workflowPhases.map((phase, index) => (
            <Step key={phase.label} completed={index < currentPhaseIndex}>
              <StepLabel>{phase.label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {/* Current Phase Details */}
        {currentPhaseIndex >= 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6">
              Current Phase: {workflowPhases[currentPhaseIndex].label}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {workflowPhases[currentPhaseIndex].description}
            </Typography>
          </Box>
        )}
        
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="workflow tabs">
          <Tab label="Agent Interactions" />
          <Tab label="Artifacts" />
          <Tab label="Agent Reasoning" />
        </Tabs>
        
        {/* Agent Interactions Tab */}
        <Box hidden={selectedTab !== 0} sx={{ mt: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Recent Agent Interactions
              </Typography>
              
              {state?.messages && state.messages.length > 0 ? (
                <Box sx={{ maxHeight: 400, overflow: 'auto', p: 1 }}>
                  {state.messages.slice(-10).map((message: any, index: number) => {
                    const fromAgent = agentConfig[message.from] || { color: '#757575', name: message.from };
                    const toAgent = agentConfig[message.to] || { color: '#757575', name: message.to };
                    
                    return (
                      <Card key={`message-${index}`} sx={{ mb: 1, bgcolor: 'background.default' }}>
                        <CardContent sx={{ py: 1, '&:last-child': { pb: 1 } }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Avatar sx={{ bgcolor: fromAgent.color, width: 24, height: 24, fontSize: '0.75rem', mr: 1 }}>
                                {fromAgent.name.charAt(0)}
                              </Avatar>
                              <Typography variant="body2">
                                {fromAgent.name} → {toAgent.name}
                              </Typography>
                            </Box>
                            <Chip 
                              label={message.type} 
                              size="small" 
                              color={
                                message.type === 'FEEDBACK' ? 'warning' :
                                message.type === 'ERROR' ? 'error' :
                                message.type === 'ARTIFACT_DELIVERY' ? 'success' :
                                'default'
                              }
                            />
                          </Box>
                          
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                            {new Date(message.timestamp).toLocaleString()}
                          </Typography>
                          
                          {message.content && typeof message.content === 'object' && (
                            <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}>
                              {message.content.feedback && (
                                <Typography variant="body2">
                                  {message.content.feedback.length > 100 
                                    ? `${message.content.feedback.substring(0, 100)}...` 
                                    : message.content.feedback}
                                </Typography>
                              )}
                              
                              {message.type === 'ARTIFACT_DELIVERY' && message.content.artifactId && (
                                <Button 
                                  size="small" 
                                  startIcon={<VisibilityIcon />}
                                  onClick={() => setSelectedArtifactId(message.content.artifactId)}
                                  sx={{ mt: 1 }}
                                >
                                  View Artifact
                                </Button>
                              )}
                            </Box>
                          )}
                        </CardContent>
                      </Card>
                    );
                  })}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                  No agent interactions recorded yet
                </Typography>
              )}
            </Grid>
          </Grid>
        </Box>
        
        {/* Artifacts Tab */}
        <Box hidden={selectedTab !== 1} sx={{ mt: 2 }}>
          <Grid container spacing={2}>
            {artifacts.length > 0 ? (
              artifacts.map((artifact: any, index: number) => {
                const agentConf = agentConfig[artifact.createdBy] || { color: '#757575', name: artifact.createdBy };
                const latestIteration = artifact.iterations[artifact.iterations.length - 1];
                
                return (
                  <Grid item xs={12} sm={6} md={4} key={`artifact-${index}`}>
                    <Card 
                      sx={{ 
                        cursor: 'pointer',
                        border: selectedArtifactId === artifact.id ? `2px solid ${agentConf.color}` : 'none'
                      }}
                      onClick={() => setSelectedArtifactId(artifact.id)}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="subtitle1" noWrap sx={{ maxWidth: '70%' }}>
                            {artifact.name}
                          </Typography>
                          <Chip 
                            label={artifact.type} 
                            size="small"
                            color={
                              artifact.type === 'content-draft' ? 'primary' :
                              artifact.type === 'content-outline' ? 'secondary' :
                              artifact.type === 'seo-analysis' ? 'info' :
                              'default'
                            }
                          />
                        </Box>
                        
                        <Typography variant="body2" color="text.secondary">
                          Created by {agentConf.name}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary">
                          {artifact.iterations.length} iterations • Last updated: {new Date(latestIteration.timestamp).toLocaleString()}
                        </Typography>
                        
                        {artifact.type === 'seo-analysis' && latestIteration.content?.score && (
                          <Box sx={{ mt: 1 }}>
                            <Chip 
                              label={`SEO Score: ${latestIteration.content.score}/100`}
                              color={
                                latestIteration.content.score > 70 ? 'success' :
                                latestIteration.content.score > 50 ? 'warning' :
                                'error'
                              }
                              size="small"
                            />
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })
            ) : (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                  No artifacts created yet
                </Typography>
              </Grid>
            )}
          </Grid>
          
          {/* Selected Artifact Details */}
          {selectedArtifact && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                {selectedArtifact.name}
              </Typography>
              
              <Card>
                <CardContent>
                  <Typography variant="subtitle2" gutterBottom>
                    Latest Version (v{selectedArtifact.currentVersion})
                  </Typography>
                  
                  {selectedArtifact.type === 'content-draft' && (
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                      <Typography variant="body1">
                        {typeof selectedArtifact.iterations[selectedArtifact.iterations.length - 1].content === 'string' 
                          ? selectedArtifact.iterations[selectedArtifact.iterations.length - 1].content
                          : selectedArtifact.iterations[selectedArtifact.iterations.length - 1].content?.content || 
                            JSON.stringify(selectedArtifact.iterations[selectedArtifact.iterations.length - 1].content, null, 2)}
                      </Typography>
                    </Box>
                  )}
                  
                  {selectedArtifact.type === 'seo-analysis' && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        SEO Score: {selectedArtifact.iterations[selectedArtifact.iterations.length - 1].content?.score || 'N/A'}/100
                      </Typography>
                      
                      <Typography variant="subtitle2" gutterBottom>
                        Recommendations:
                      </Typography>
                      
                      {selectedArtifact.iterations[selectedArtifact.iterations.length - 1].content?.recommendations?.map((rec: any, i: number) => (
                        <Box key={`rec-${i}`} sx={{ mb: 1 }}>
                          <Typography variant="body2">
                            {i + 1}. {rec.recommendation || rec}
                            {rec.priority && <span> (Priority: {rec.priority})</span>}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Box>
          )}
        </Box>
        
        {/* Agent Reasoning Tab */}
        <Box hidden={selectedTab !== 2} sx={{ mt: 2 }}>
          <AgentReasoningVisualizer iterations={sortedIterations} />
        </Box>
      </Paper>
    </Box>
  );
};

export default CollaborativeWorkflowVisualizer;
