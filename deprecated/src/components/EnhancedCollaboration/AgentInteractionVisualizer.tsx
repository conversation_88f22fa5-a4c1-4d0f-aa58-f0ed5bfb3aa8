// src/components/EnhancedCollaboration/AgentInteractionVisualizer.tsx

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Avatar,
  Chip,
  Card,
  CardContent,
  IconButton,
  Button,
  Tooltip,
  Badge,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  CircularProgress
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import FeedbackIcon from '@mui/icons-material/Feedback';
import AutorenewIcon from '@mui/icons-material/Autorenew';

// Define message type
interface AgentMessage {
  id: string;
  from: string;
  to: string;
  type: string;
  content: any;
  timestamp: string;
  conversationId: string;
  replyTo?: string;
}

// Define props interface
interface AgentInteractionVisualizerProps {
  messages: AgentMessage[];
  artifacts?: Record<string, any>;
  loading?: boolean;
  onRefresh?: () => void;
  onViewArtifact?: (artifactId: string) => void;
}

// Map agent IDs to colors and display names
const agentConfig: Record<string, { color: string; name: string }> = {
  'content-generation': {
    color: '#4caf50',
    name: 'Content Generation'
  },
  'seo-optimization': {
    color: '#2196f3',
    name: 'SEO Optimization'
  },
  'market-research': {
    color: '#ff9800',
    name: 'Market Research'
  },
  'content-strategy': {
    color: '#9c27b0',
    name: 'Content Strategy'
  },
  'seo-keyword': {
    color: '#00bcd4',
    name: 'SEO Keyword'
  }
};

// Helper function to get agent config with fallback
const getAgentConfig = (agentId: string) => {
  return agentConfig[agentId] || {
    color: '#757575',
    name: agentId
  };
};

// Helper function to get message type icon
const getMessageTypeIcon = (type: string) => {
  switch (type) {
    case 'ARTIFACT_DELIVERY':
      return <CheckCircleIcon />;
    case 'ARTIFACT_REQUEST':
      return <InfoIcon />;
    case 'FEEDBACK':
      return <FeedbackIcon />;
    case 'ERROR':
      return <ErrorIcon />;
    case 'ACKNOWLEDGMENT':
      return <CheckCircleIcon />;
    case 'CONSULTATION_REQUEST':
      return <InfoIcon />;
    case 'CONSULTATION_RESPONSE':
      return <InfoIcon />;
    default:
      return <ArrowForwardIcon />;
  }
};

// Helper function to get message type color
const getMessageTypeColor = (type: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
  switch (type) {
    case 'ARTIFACT_DELIVERY':
      return 'success';
    case 'ARTIFACT_REQUEST':
      return 'info';
    case 'FEEDBACK':
      return 'warning';
    case 'ERROR':
      return 'error';
    case 'ACKNOWLEDGMENT':
      return 'success';
    case 'CONSULTATION_REQUEST':
      return 'info';
    case 'CONSULTATION_RESPONSE':
      return 'secondary';
    default:
      return 'default';
  }
};

/**
 * Component to visualize real-time interactions between agents
 */
const AgentInteractionVisualizer: React.FC<AgentInteractionVisualizerProps> = ({
  messages,
  artifacts,
  loading = false,
  onRefresh,
  onViewArtifact
}) => {
  const [expandedMessage, setExpandedMessage] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Toggle message expansion
  const toggleMessageExpansion = (messageId: string) => {
    if (expandedMessage === messageId) {
      setExpandedMessage(null);
    } else {
      setExpandedMessage(messageId);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleTimeString();
    } catch (e) {
      return timestamp;
    }
  };

  // Render message content based on type
  const renderMessageContent = (message: AgentMessage) => {
    const { content, type } = message;

    // Handle different message types
    switch (type) {
      case 'ARTIFACT_DELIVERY':
        return (
          <Box>
            <Typography variant="body2" gutterBottom>
              Delivered artifact: {content.artifactId}
            </Typography>
            {artifacts && artifacts[content.artifactId] && (
              <Typography variant="body2" color="text.secondary">
                {artifacts[content.artifactId].name}
              </Typography>
            )}
            {onViewArtifact && (
              <Button
                size="small"
                startIcon={<VisibilityIcon />}
                onClick={() => onViewArtifact(content.artifactId)}
                sx={{ mt: 1 }}
              >
                View Artifact
              </Button>
            )}
          </Box>
        );

      case 'ARTIFACT_REQUEST':
        return (
          <Box>
            <Typography variant="body2">
              Requesting artifact: {content.artifactType}
            </Typography>
            {content.description && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {content.description}
              </Typography>
            )}
          </Box>
        );

      case 'FEEDBACK':
        return (
          <Box>
            <Typography variant="body2" gutterBottom>
              Feedback on artifact: {content.artifactId}
            </Typography>
            <Paper variant="outlined" sx={{ p: 1, mt: 1, bgcolor: 'background.default' }}>
              <Typography variant="body2">
                {content.feedback}
              </Typography>
            </Paper>
          </Box>
        );

      case 'CONSULTATION_REQUEST':
        return (
          <Box>
            <Typography variant="body2" gutterBottom>
              Consultation request:
            </Typography>
            <Paper variant="outlined" sx={{ p: 1, mt: 1, bgcolor: 'background.default' }}>
              <Typography variant="body2">
                {content.question}
              </Typography>
            </Paper>
          </Box>
        );

      case 'CONSULTATION_RESPONSE':
        return (
          <Box>
            <Typography variant="body2" gutterBottom>
              Consultation response:
            </Typography>
            <Paper variant="outlined" sx={{ p: 1, mt: 1, bgcolor: 'background.default' }}>
              <Typography variant="body2">
                {content.response}
              </Typography>
            </Paper>
          </Box>
        );

      case 'ERROR':
        return (
          <Box>
            <Typography variant="body2" color="error">
              Error: {content.error || JSON.stringify(content)}
            </Typography>
          </Box>
        );

      case 'ACKNOWLEDGMENT':
        return (
          <Box>
            <Typography variant="body2">
              {content.message || 'Acknowledged'}
            </Typography>
          </Box>
        );

      default:
        // For other message types or when content is a string
        if (typeof content === 'string') {
          return (
            <Typography variant="body2">{content}</Typography>
          );
        }
        
        // For complex content objects
        return (
          <Box>
            <Typography variant="body2">
              {content.message || JSON.stringify(content).substring(0, 100) + (JSON.stringify(content).length > 100 ? '...' : '')}
            </Typography>
            {expandedMessage === message.id && (
              <Paper variant="outlined" sx={{ p: 1, mt: 1, bgcolor: 'background.default' }}>
                <pre style={{ margin: 0, overflow: 'auto', maxHeight: '200px', fontSize: '0.75rem' }}>
                  {JSON.stringify(content, null, 2)}
                </pre>
              </Paper>
            )}
            {JSON.stringify(content).length > 100 && (
              <Button 
                size="small" 
                onClick={() => toggleMessageExpansion(message.id)}
                sx={{ mt: 1 }}
              >
                {expandedMessage === message.id ? 'Show Less' : 'Show More'}
              </Button>
            )}
          </Box>
        );
    }
  };

  return (
    <Paper elevation={2} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">Agent Interactions</Typography>
        {onRefresh && (
          <Tooltip title="Refresh">
            <IconButton onClick={onRefresh} disabled={loading}>
              {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
        )}
      </Box>
      
      <Divider />
      
      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
        {messages.length > 0 ? (
          <List>
            {messages.map((message, index) => {
              const fromAgent = getAgentConfig(message.from);
              const toAgent = getAgentConfig(message.to);
              
              return (
                <ListItem 
                  key={message.id || index}
                  alignItems="flex-start"
                  sx={{ 
                    mb: 1,
                    p: 0,
                    '&:last-child': { mb: 0 }
                  }}
                >
                  <Card 
                    variant="outlined" 
                    sx={{ 
                      width: '100%',
                      borderLeft: `4px solid ${fromAgent.color}`
                    }}
                  >
                    <CardContent sx={{ py: 1, '&:last-child': { pb: 1 } }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar 
                            sx={{ 
                              bgcolor: fromAgent.color, 
                              width: 24, 
                              height: 24, 
                              fontSize: '0.75rem', 
                              mr: 1 
                            }}
                          >
                            {fromAgent.name.charAt(0)}
                          </Avatar>
                          <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                            {fromAgent.name}
                            <ArrowForwardIcon sx={{ mx: 0.5, fontSize: '1rem' }} />
                            <Avatar 
                              sx={{ 
                                bgcolor: toAgent.color, 
                                width: 24, 
                                height: 24, 
                                fontSize: '0.75rem', 
                                mr: 1 
                              }}
                            >
                              {toAgent.name.charAt(0)}
                            </Avatar>
                            {toAgent.name}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Chip 
                            icon={getMessageTypeIcon(message.type)}
                            label={message.type} 
                            size="small" 
                            color={getMessageTypeColor(message.type)}
                            sx={{ height: 24 }}
                          />
                        </Box>
                      </Box>
                      
                      <Box sx={{ ml: 4 }}>
                        {renderMessageContent(message)}
                      </Box>
                      
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1, ml: 4 }}>
                        <Typography variant="caption" color="text.secondary">
                          {formatTimestamp(message.timestamp)}
                        </Typography>
                        
                        {message.replyTo && (
                          <Typography variant="caption" color="text.secondary">
                            Reply to: {message.replyTo.substring(0, 8)}...
                          </Typography>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </ListItem>
              );
            })}
          </List>
        ) : (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Typography variant="body2" color="text.secondary">
              No agent interactions yet
            </Typography>
          </Box>
        )}
        <div ref={messagesEndRef} />
      </Box>
      
      {messages.length > 0 && (
        <Box sx={{ p: 1, display: 'flex', justifyContent: 'center' }}>
          <Button
            startIcon={<ArrowDownwardIcon />}
            size="small"
            onClick={() => messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })}
          >
            Scroll to Latest
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default AgentInteractionVisualizer;
