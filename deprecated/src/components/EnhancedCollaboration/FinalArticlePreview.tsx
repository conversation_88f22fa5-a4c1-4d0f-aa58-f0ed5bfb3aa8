'use client';

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON>ton,
  TextField,
  Divider,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import { IterativeArtifact } from '../../app/(payload)/api/agents/collaborative-iteration/types';
import ReactMarkdown from 'react-markdown';

interface FinalArticlePreviewProps {
  sessionId: string;
  artifacts: Record<string, IterativeArtifact>;
  generatedArtifacts: string[];
  loading?: boolean;
  onSaveArticle?: (articleContent: string) => Promise<void>;
}

/**
 * Component to display the final generated article with preview and editing capabilities
 */
const FinalArticlePreview: React.FC<FinalArticlePreviewProps> = ({
  sessionId,
  artifacts,
  generatedArtifacts,
  loading = false,
  onSaveArticle
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Find the final article artifact (should be from content-generation or seo-optimization)
  const finalArticleArtifact = generatedArtifacts
    .map(id => artifacts[id])
    .filter(artifact =>
      artifact?.type === 'generated-content' ||
      artifact?.type === 'seo-optimized-content' ||
      artifact?.type === 'blog-content' ||
      artifact?.type === 'blog-post' ||
      artifact?.type === 'content-draft'
    )
    .sort((a, b) => {
      // Sort by creation time, newest first
      const timeA = new Date(a?.createdAt || 0).getTime();
      const timeB = new Date(b?.createdAt || 0).getTime();
      return timeB - timeA;
    })[0];

  // Log available artifacts for debugging
  console.log('Available artifacts for article preview:',
    generatedArtifacts.map(id => ({
      id,
      type: artifacts[id]?.type,
      createdAt: artifacts[id]?.createdAt
    }))
  );

  // Extract article content with better handling of different content formats
  const extractContent = (artifact: any): string => {
    if (!artifact) return '';

    // Handle string content directly
    if (typeof artifact.content === 'string') {
      return artifact.content;
    }

    // Handle object content with text property
    if (artifact.content && typeof artifact.content === 'object') {
      // Check for common content properties
      if (artifact.content.text) return artifact.content.text;
      if (artifact.content.content) return artifact.content.content;
      if (artifact.content.body) return artifact.content.body;

      // If there's a title and sections, try to reconstruct the content
      if (artifact.content.title && artifact.content.sections) {
        return `# ${artifact.content.title}\n\n` +
          artifact.content.sections.map((section: any) =>
            `## ${section.title || 'Section'}\n\n${section.content || ''}`
          ).join('\n\n');
      }

      // Last resort: stringify the object
      return JSON.stringify(artifact.content, null, 2);
    }

    // Handle content in data property (some artifacts store content here)
    if (artifact.data && typeof artifact.data === 'object') {
      if (typeof artifact.data.content === 'string') return artifact.data.content;
      if (typeof artifact.data.text === 'string') return artifact.data.text;
      if (typeof artifact.data === 'string') return artifact.data;
    }

    // Handle iterations (some artifacts store content in iterations)
    if (artifact.iterations && artifact.iterations.length > 0) {
      const latestIteration = artifact.iterations[artifact.iterations.length - 1];
      if (typeof latestIteration.content === 'string') return latestIteration.content;
      if (latestIteration.content && typeof latestIteration.content === 'object') {
        if (latestIteration.content.text) return latestIteration.content.text;
        if (latestIteration.content.content) return latestIteration.content.content;
        if (latestIteration.content.body) return latestIteration.content.body;
        return JSON.stringify(latestIteration.content, null, 2);
      }
    }

    // Fallback
    return '';
  };

  const articleContent = extractContent(finalArticleArtifact);
  console.log('Extracted article content length:', articleContent.length);

  // Extract title from content or use artifact name
  const extractTitle = (artifact: any, content: string): string => {
    if (!artifact) return 'Generated Article';

    // Try to extract from content first line if it's a markdown heading
    if (content && typeof content === 'string') {
      const firstLine = content.split('\n')[0];
      if (firstLine.startsWith('# ')) {
        return firstLine.replace(/^#\s+/, '');
      }
    }

    // Try to get from artifact content object
    if (artifact.content && typeof artifact.content === 'object' && artifact.content.title) {
      return artifact.content.title;
    }

    // Try to get from artifact data
    if (artifact.data && artifact.data.title) {
      return artifact.data.title;
    }

    // Try to get from artifact name
    if (artifact.name) {
      return artifact.name;
    }

    // Fallback
    return 'Generated Article';
  };

  const articleTitle = extractTitle(finalArticleArtifact, articleContent);

  // Handle edit mode toggle
  const handleEditToggle = () => {
    if (isEditing) {
      setIsEditing(false);
    } else {
      setEditedContent(articleContent);
      setIsEditing(true);
    }
  };

  // Handle save edited content
  const handleSave = async () => {
    if (!onSaveArticle) return;

    setIsSaving(true);
    try {
      await onSaveArticle(editedContent);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving article:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle copy to clipboard
  const handleCopy = () => {
    navigator.clipboard.writeText(articleContent).then(() => {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    });
  };

  // Handle download as markdown
  const handleDownload = () => {
    const element = document.createElement('a');
    const file = new Blob([articleContent], {type: 'text/markdown'});
    element.href = URL.createObjectURL(file);
    element.download = `${articleTitle.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.md`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  if (loading) {
    return (
      <Paper elevation={2} sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 5 }}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Generating article...
          </Typography>
        </Box>
      </Paper>
    );
  }

  if (!finalArticleArtifact) {
    return (
      <Paper elevation={2} sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Final Article
        </Typography>
        <Box sx={{ py: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No article has been generated yet. The article will appear here once the content generation phase is complete.
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2, mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Final Article
        </Typography>
        <Box>
          <Tooltip title={copySuccess ? "Copied!" : "Copy to clipboard"}>
            <IconButton onClick={handleCopy} color={copySuccess ? "success" : "default"}>
              <ContentCopyIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Download as Markdown">
            <IconButton onClick={handleDownload}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title={isEditing ? "Cancel editing" : "Edit article"}>
            <IconButton onClick={handleEditToggle} color={isEditing ? "secondary" : "primary"}>
              <EditIcon />
            </IconButton>
          </Tooltip>
          {isEditing && (
            <Tooltip title="Save changes">
              <IconButton
                onClick={handleSave}
                color="primary"
                disabled={isSaving}
              >
                {isSaving ? <CircularProgress size={24} /> : <SaveIcon />}
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {isEditing ? (
        <TextField
          fullWidth
          multiline
          minRows={20}
          value={editedContent}
          onChange={(e) => setEditedContent(e.target.value)}
          variant="outlined"
          sx={{ mb: 2, fontFamily: 'monospace' }}
        />
      ) : (
        <Box sx={{
          p: 2,
          bgcolor: 'background.paper',
          borderRadius: 1,
          border: '1px solid',
          borderColor: 'divider',
          maxHeight: '600px',
          overflow: 'auto'
        }}>
          <ReactMarkdown>{articleContent}</ReactMarkdown>
        </Box>
      )}

      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Chip
          label={finalArticleArtifact.type === 'seo-optimized-content' ? 'SEO Optimized' : 'Generated Content'}
          color={finalArticleArtifact.type === 'seo-optimized-content' ? 'success' : 'primary'}
          size="small"
        />
        <Typography variant="caption" color="text.secondary">
          Generated: {new Date(finalArticleArtifact.createdAt).toLocaleString()}
        </Typography>
      </Box>
    </Paper>
  );
};

export default FinalArticlePreview;
