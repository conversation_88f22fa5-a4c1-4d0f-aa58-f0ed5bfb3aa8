'use client'

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Alert,
  Snackbar,
  <PERSON>bs,
  <PERSON>b,
  Icon<PERSON>utton,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useTheme,
  use<PERSON><PERSON><PERSON><PERSON>y,
  <PERSON>per,
  <PERSON>,
  StepLabel,
  Chip,
  Tooltip
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import ArticleIcon from '@mui/icons-material/Article';
import ChatIcon from '@mui/icons-material/Chat';
import PsychologyIcon from '@mui/icons-material/Psychology';
import ManageSearchIcon from '@mui/icons-material/ManageSearch';
import AssessmentIcon from '@mui/icons-material/Assessment';
import SpeedIcon from '@mui/icons-material/Speed';
import SearchIcon from '@mui/icons-material/Search';
import SettingsIcon from '@mui/icons-material/Settings';
import VisibilityIcon from '@mui/icons-material/Visibility';

// Import components
import AgentDiscussionPanel from './AgentDiscussionPanel';
import ChainOfThoughtVisualizer from './ChainOfThoughtVisualizer';
import ArticleViewer from './ArticleViewer';
import ArtifactGallery from './ArtifactGallery';
import AgentWorkflowVisualizer from './AgentWorkflowVisualizer';
import QualityMetricsPanel from './QualityMetricsPanel';
import SEOOptimizationPanel from './SEOOptimizationPanel';
import ContentQualityPanel from './ContentQualityPanel';
import WorkflowMonitoringDashboard from './WorkflowMonitoringDashboard';
import FinalArticlePreview from './FinalArticlePreview';
import AgentCollaborationNetwork from './AgentCollaborationNetwork';

// Define the props interface
interface CohesiveDashboardProps {
  sessionId: string;
  state: any;
  onRefresh: () => void;
  onSendFeedback?: (feedback: string) => void;
  loading?: boolean;
}

/**
 * A cohesive dashboard that integrates all components in a more user-friendly layout
 */
const CohesiveDashboard: React.FC<CohesiveDashboardProps> = ({
  sessionId,
  state,
  onRefresh,
  onSendFeedback,
  loading = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mainView, setMainView] = useState<string>('article');
  const [sideView, setSideView] = useState<string>('discussion');
  const [drawerOpen, setDrawerOpen] = useState<boolean>(!isMobile);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Get the final article from artifacts if available
  const finalArticle = (() => {
    // Handle different artifact data structures
    if (!state?.artifacts) {
      return null;
    }

    // If artifacts is an array, use find directly
    if (Array.isArray(state.artifacts)) {
      return state.artifacts.find((artifact: any) =>
        artifact.type === 'final-article' ||
        artifact.type === 'seo-optimized-content' ||
        artifact.type === 'blog-content' ||
        artifact.type === 'generated-content'
      );
    }

    // If artifacts is an object with generatedArtifacts array
    if (state.generatedArtifacts && Array.isArray(state.generatedArtifacts)) {
      const artifactId = state.generatedArtifacts.find((id: string) =>
        state.artifacts[id]?.type === 'final-article' ||
        state.artifacts[id]?.type === 'seo-optimized-content' ||
        state.artifacts[id]?.type === 'blog-content' ||
        state.artifacts[id]?.type === 'generated-content'
      );

      return artifactId ? state.artifacts[artifactId] : null;
    }

    // If artifacts is an object, convert to array and find
    return Object.values(state.artifacts).find((artifact: any) =>
      artifact.type === 'final-article' ||
      artifact.type === 'seo-optimized-content' ||
      artifact.type === 'blog-content' ||
      artifact.type === 'generated-content'
    );
  })();

  // Get quality assessment if available
  const qualityAssessment = state?.qualityAssessment || {
    overallScore: 0.75,
    meetsStandards: true,
    dimensions: {
      content: 0.8,
      structure: 0.7,
      seo: 0.75,
      readability: 0.8,
      engagement: 0.7
    },
    strengths: ['Well-structured content', 'Good readability', 'Effective keyword usage'],
    weaknesses: ['Could improve engagement', 'Some sections need more detail'],
    suggestions: ['Add more subheadings', 'Include more examples', 'Consider adding a FAQ section'],
    timestamp: new Date().toISOString(),
    id: 'mock-quality-assessment'
  };

  // Get content metrics if available
  const contentMetrics = state?.contentMetrics || {
    overallScore: 0.75,
    readability: {
      fleschKincaidScore: 65,
      readingTimeMinutes: 5,
      readabilityScore: 0.8
    },
    structure: {
      headingCount: 5,
      paragraphCount: 12,
      listCount: 2,
      structureScore: 0.7
    },
    engagement: {
      engagementScore: 0.7,
      questionCount: 3,
      callToActionCount: 2
    },
    coherence: {
      coherenceScore: 0.8
    },
    content: {
      contentScore: 0.75,
      wordCount: 1200,
      sentenceCount: 60,
      averageSentenceLength: 20
    },
    seo: {
      seoScore: 0.75
    }
  };

  // Get SEO optimization if available
  const seoOptimization = state?.seoOptimization || {
    overallScore: 0.75,
    onPageSeo: {
      titleTag: { score: 0.8, original: 'Sample Title', optimized: 'Optimized Title', suggestions: [] },
      metaDescription: { score: 0.7, original: 'Sample description', optimized: 'Optimized description', suggestions: [] },
      headings: { score: 0.8, original: [], optimized: [], suggestions: [] },
      content: { score: 0.7, keywordDensity: { 'sample': 1.2 }, suggestions: [] },
      internalLinking: { score: 0.6, suggestions: [] }
    },
    semanticSeo: {
      score: 0.7,
      topicClusters: ['Topic 1', 'Topic 2'],
      relatedEntities: ['Entity 1', 'Entity 2'],
      suggestions: []
    },
    serpFeatures: {
      score: 0.8,
      featuredSnippetPotential: 0.7,
      faqSchemaPotential: 0.8,
      howToSchemaPotential: 0.6,
      suggestions: []
    },
    structuredData: {
      score: 0.7,
      recommendedSchemas: ['Article', 'FAQPage'],
      schemaExamples: {},
      suggestions: []
    },
    suggestions: []
  };

  // Handle snackbar close
  const handleCloseSnackbar = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  // Toggle drawer
  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Determine the current workflow phase with improved mapping
  const rawCurrentPhase = state?.currentPhase || state?.workflowProgress?.currentPhase || 'initialization';

  // Map the raw phase to a standardized phase for the stepper
  let currentPhase = rawCurrentPhase;

  // Handle different phase naming conventions
  if (rawCurrentPhase === 'creation') {
    currentPhase = 'content-generation';
  } else if (rawCurrentPhase === 'finalization') {
    currentPhase = 'review';
  } else if (rawCurrentPhase === 'complete') {
    currentPhase = 'completed';
  }

  // Define workflow phases
  const workflowPhases = [
    { label: 'Initialization', value: 'initialization' },
    { label: 'Market Research', value: 'market-research' },
    { label: 'Keyword Research', value: 'keyword-research' },
    { label: 'Content Strategy', value: 'content-strategy' },
    { label: 'Content Generation', value: 'content-generation' },
    { label: 'SEO Optimization', value: 'seo-optimization' },
    { label: 'Review', value: 'review' },
    { label: 'Completed', value: 'completed' }
  ];

  // Get current phase index with fallback
  const currentPhaseIndex = workflowPhases.findIndex(phase => phase.value === currentPhase);

  // Determine the active step based on workflow progress
  let activeStep = currentPhaseIndex >= 0 ? currentPhaseIndex : 0;

  // Use workflow progress to determine the active step more accurately
  if (state?.workflowProgress) {
    const progress = state.workflowProgress;

    if (progress.contentGenerationComplete) {
      // If content generation is complete, we're at least at the review phase
      activeStep = Math.max(activeStep, workflowPhases.findIndex(phase => phase.value === 'review'));
    } else if (progress.contentStrategyComplete) {
      // If content strategy is complete, we're at least at the content generation phase
      activeStep = Math.max(activeStep, workflowPhases.findIndex(phase => phase.value === 'content-generation'));
    } else if (progress.keywordResearchComplete) {
      // If keyword research is complete, we're at least at the content strategy phase
      activeStep = Math.max(activeStep, workflowPhases.findIndex(phase => phase.value === 'content-strategy'));
    } else if (progress.marketResearchComplete) {
      // If market research is complete, we're at least at the keyword research phase
      activeStep = Math.max(activeStep, workflowPhases.findIndex(phase => phase.value === 'keyword-research'));
    }

    if (progress.seoOptimizationComplete) {
      // If SEO optimization is complete, we're at the completed phase
      activeStep = Math.max(activeStep, workflowPhases.findIndex(phase => phase.value === 'completed'));
    }
  }

  // Calculate progress percentage
  const progressPercentage = Math.round(((activeStep + 1) / workflowPhases.length) * 100);

  // Render main content based on selected view
  const renderMainContent = () => {
    switch (mainView) {
      case 'article':
        // Use FinalArticlePreview component for better article display
        if (state && state.artifacts && state.generatedArtifacts) {
          return (
            <FinalArticlePreview
              sessionId={sessionId}
              artifacts={state.artifacts}
              generatedArtifacts={state.generatedArtifacts}
              loading={loading}
              onSaveArticle={async (content) => {
                // Handle saving article if needed
                setSnackbar({
                  open: true,
                  message: 'Article saved successfully',
                  severity: 'success'
                });
              }}
            />
          );
        } else if (finalArticle) {
          // Fallback to ArticleViewer if needed
          return (
            <ArticleViewer
              article={{
                title: finalArticle.title || finalArticle.name || 'Generated Article',
                content: finalArticle.content || finalArticle.text || '',
                seoScore: finalArticle.seoScore || state?.seoOptimization?.overallScore ? Math.round((state.seoOptimization.overallScore || 0) * 100) : undefined,
                generatedAt: finalArticle.createdAt || finalArticle.timestamp || new Date().toISOString(),
                contributors: finalArticle.contributors || finalArticle.createdBy ? [finalArticle.createdBy] : ['content-generation'],
                metadata: {
                  wordCount: finalArticle.wordCount || (finalArticle.content || '').split(/\s+/).length,
                  readingTime: finalArticle.readingTime || state?.contentMetrics?.readability?.readingTimeMinutes || Math.ceil((finalArticle.content || '').split(/\s+/).length / 200),
                  keywords: finalArticle.keywords || state?.keywords || [],
                  targetAudience: finalArticle.targetAudience || state?.targetAudience || 'General Audience',
                  contentType: finalArticle.contentType || state?.contentType || 'blog-article'
                }
              }}
              onEdit={() => {}}
              readOnly={true}
              sessionId={sessionId}
            />
          );
        } else {
          return (
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary">
                Article is being generated...
              </Typography>
              {loading && <CircularProgress sx={{ mt: 2 }} />}
            </Paper>
          );
        }
      case 'quality':
        return (
          <Box>
            <QualityMetricsPanel
              qualityAssessment={qualityAssessment}
              contentMetrics={contentMetrics}
              seoOptimization={seoOptimization}
            />
            <ContentQualityPanel contentMetrics={contentMetrics} />
          </Box>
        );
      case 'seo':
        return (
          <SEOOptimizationPanel
            seoOptimization={seoOptimization}
            analysis={state?.seoAnalysis}
            recommendations={state?.seoRecommendations}
            prioritizedTasks={state?.seoPrioritizedTasks}
            keywordSuggestions={state?.keywordSuggestions}
          />
        );
      case 'artifacts':
        return (
          <ArtifactGallery
            artifacts={(() => {
              // Handle different artifact data structures
              if (!state?.artifacts) {
                return [];
              }

              // If artifacts is an array, return it directly
              if (Array.isArray(state.artifacts)) {
                return state.artifacts;
              }

              // If artifacts is an object with generatedArtifacts array
              if (state.generatedArtifacts && Array.isArray(state.generatedArtifacts)) {
                return state.generatedArtifacts
                  .map(id => state.artifacts[id])
                  .filter(Boolean);
              }

              // If artifacts is an object, convert to array
              return Object.values(state.artifacts);
            })()}
            onSendFeedback={onSendFeedback}
          />
        );
      case 'workflow':
        return (
          <WorkflowMonitoringDashboard
            sessionId={sessionId}
            state={{
              ...state,
              // Ensure artifacts is in the expected format
              artifacts: (() => {
                if (!state?.artifacts) {
                  return [];
                }

                if (Array.isArray(state.artifacts)) {
                  return state.artifacts;
                }

                if (state.generatedArtifacts && Array.isArray(state.generatedArtifacts)) {
                  return state.generatedArtifacts
                    .map(id => state.artifacts[id])
                    .filter(Boolean);
                }

                return Object.values(state.artifacts);
              })(),
              // Ensure messages is in the expected format
              messages: state?.messages || []
            }}
            onRefresh={onRefresh}
          />
        );
      case 'network':
        // Add the Agent Collaboration Network visualization
        return (
          <Box sx={{ height: '600px' }}>
            <AgentCollaborationNetwork
              messages={state?.messages || []}
              artifacts={(() => {
                if (!state?.artifacts) {
                  return [];
                }

                if (Array.isArray(state.artifacts)) {
                  return state.artifacts;
                }

                if (state.generatedArtifacts && Array.isArray(state.generatedArtifacts)) {
                  return state.generatedArtifacts
                    .map(id => state.artifacts[id])
                    .filter(Boolean);
                }

                return Object.values(state.artifacts);
              })()}
              onRefresh={onRefresh}
              loading={loading}
            />
          </Box>
        );
      default:
        return null;
    }
  };

  // Render side content based on selected view
  const renderSideContent = () => {
    switch (sideView) {
      case 'discussion':
        return (
          <AgentDiscussionPanel
            messages={state?.messages || []}
            sessionId={sessionId}
            showInput={false}
          />
        );
      case 'reasoning':
        return (
          <ChainOfThoughtVisualizer
            messages={state?.messages || []}
            artifacts={(() => {
              // Handle different artifact data structures
              if (!state?.artifacts) {
                return [];
              }

              // If artifacts is an array, return it directly
              if (Array.isArray(state.artifacts)) {
                return state.artifacts;
              }

              // If artifacts is an object with generatedArtifacts array
              if (state.generatedArtifacts && Array.isArray(state.generatedArtifacts)) {
                return state.generatedArtifacts
                  .map(id => state.artifacts[id])
                  .filter(Boolean);
              }

              // If artifacts is an object, convert to array
              return Object.values(state.artifacts);
            })()}
            consultations={(() => {
              // Handle different consultation data structures
              if (!state?.consultations) {
                return [];
              }

              // If consultations is an array, return it directly
              if (Array.isArray(state.consultations)) {
                return state.consultations;
              }

              // If consultations is an object, convert to array
              return Object.values(state.consultations);
            })()}
          />
        );
      case 'workflow':
        return (
          <AgentWorkflowVisualizer
            sessionId={sessionId}
            sessionState={{
              ...state,
              // Ensure artifacts is in the expected format
              artifacts: (() => {
                if (!state?.artifacts) {
                  return [];
                }

                if (Array.isArray(state.artifacts)) {
                  return state.artifacts;
                }

                if (state.generatedArtifacts && Array.isArray(state.generatedArtifacts)) {
                  return state.generatedArtifacts
                    .map(id => state.artifacts[id])
                    .filter(Boolean);
                }

                return Object.values(state.artifacts);
              })(),
              // Ensure messages is in the expected format
              messages: state?.messages || []
            }}
            loading={loading}
          />
        );
      default:
        return null;
    }
  };

  // Drawer width
  const drawerWidth = 400;

  return (
    <Box sx={{ display: 'flex', height: '100%' }}>
      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 2,
          width: { sm: `calc(100% - ${drawerOpen ? drawerWidth : 0}px)` },
          ml: { sm: drawerOpen ? `${drawerWidth}px` : 0 },
          transition: theme.transitions.create(['margin', 'width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        {/* Header */}
        <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={toggleDrawer}
                sx={{ mr: 2, display: { sm: 'none' } }}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h5" noWrap component="div">
                {state?.topic || 'Collaborative Content Generation'}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Chip
                label={`Phase: ${workflowPhases[currentPhaseIndex]?.label || 'Unknown'}`}
                color={currentPhase === 'completed' ? 'success' : 'primary'}
                sx={{ mr: 2 }}
              />
              <Chip
                label={`Progress: ${progressPercentage}%`}
                color="secondary"
                sx={{ mr: 2 }}
              />
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={onRefresh}
                disabled={loading}
                size="small"
              >
                Refresh
              </Button>
            </Box>
          </Box>

          {/* Workflow progress */}
          <Box sx={{ mt: 2 }}>
            <Stepper activeStep={activeStep} alternativeLabel>
              {workflowPhases.map((phase, index) => (
                <Step
                  key={phase.value}
                  completed={index < activeStep || (state?.workflowProgress &&
                    ((index === workflowPhases.findIndex(p => p.value === 'market-research') && state.workflowProgress.marketResearchComplete) ||
                     (index === workflowPhases.findIndex(p => p.value === 'keyword-research') && state.workflowProgress.keywordResearchComplete) ||
                     (index === workflowPhases.findIndex(p => p.value === 'content-strategy') && state.workflowProgress.contentStrategyComplete) ||
                     (index === workflowPhases.findIndex(p => p.value === 'content-generation') && state.workflowProgress.contentGenerationComplete) ||
                     (index === workflowPhases.findIndex(p => p.value === 'seo-optimization') && state.workflowProgress.seoOptimizationComplete)))}
                >
                  <StepLabel>{phase.label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
        </Paper>

        {/* Main view tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs
            value={mainView}
            onChange={(e, newValue) => setMainView(newValue)}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<ArticleIcon />} label="Article" value="article" />
            <Tab icon={<SpeedIcon />} label="Quality Metrics" value="quality" />
            <Tab icon={<SearchIcon />} label="SEO Analysis" value="seo" />
            <Tab icon={<ManageSearchIcon />} label="Artifacts" value="artifacts" />
            <Tab icon={<AssessmentIcon />} label="Workflow Metrics" value="workflow" />
            <Tab icon={<VisibilityIcon />} label="Agent Network" value="network" />
          </Tabs>
        </Box>

        {/* Main content area */}
        <Box sx={{ mb: 2 }}>
          {renderMainContent()}
        </Box>
      </Box>

      {/* Side drawer */}
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
        variant={isMobile ? 'temporary' : 'persistent'}
        anchor="left"
        open={drawerOpen}
        onClose={toggleDrawer}
      >
        <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Agent Activity</Typography>
          <IconButton onClick={toggleDrawer}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />

        {/* Side view tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={sideView}
            onChange={(e, newValue) => setSideView(newValue)}
            variant="fullWidth"
          >
            <Tab icon={<ChatIcon />} label="Discussion" value="discussion" />
            <Tab icon={<PsychologyIcon />} label="Reasoning" value="reasoning" />
            <Tab icon={<VisibilityIcon />} label="Workflow" value="workflow" />
          </Tabs>
        </Box>

        {/* Side content area */}
        <Box sx={{ p: 1, height: 'calc(100% - 112px)', overflow: 'auto' }}>
          {renderSideContent()}
        </Box>
      </Drawer>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CohesiveDashboard;
