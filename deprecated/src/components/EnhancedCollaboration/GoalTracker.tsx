// src/components/EnhancedCollaboration/GoalTracker.tsx

import React from 'react';
import { 
  <PERSON>, 
  Typography, 
  Stepper, 
  Step, 
  StepL<PERSON>l, 
  StepContent,
  Button,
  Paper,
  Chip,
  LinearProgress,
  Grid,
  Divider,
  Card,
  CardContent
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

interface Goal {
  id: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  dependencies: string[];
  assignedAgents: string[];
  startTime?: string;
  endTime?: string;
  artifacts: string[];
  subgoals?: Goal[];
}

interface GoalTrackerProps {
  goals: Goal[];
  currentGoal: string;
  onInitializeGoal: (goalDescription: string) => void;
}

const GoalTracker: React.FC<GoalTrackerProps> = ({ goals, currentGoal, onInitializeGoal }) => {
  const formatTime = (timestamp?: string) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'primary';
      case 'blocked':
        return 'error';
      default:
        return 'default';
    }
  };

  // Find the active step index based on the current goal
  const activeStep = goals.findIndex(goal => goal.description === currentGoal);

  const renderDetailedGoalView = (goal: Goal) => {
    const isActive = goal.description === currentGoal;
    
    return (
      <Card 
        variant="outlined" 
        sx={{ 
          mb: 2,
          border: isActive ? '2px solid #1976d2' : undefined,
          bgcolor: isActive ? 'rgba(25, 118, 210, 0.05)' : undefined
        }}
      >
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {goal.description}
          </Typography>
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Status
                </Typography>
                <Chip 
                  label={goal.status.charAt(0).toUpperCase() + goal.status.slice(1)} 
                  color={getStatusColor(goal.status)}
                  size="small"
                />
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Progress
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: '100%', mr: 1 }}>
                    <LinearProgress 
                      variant="determinate" 
                      value={goal.progress} 
                      color={
                        goal.progress === 100 ? 'success' : 'primary'
                      }
                    />
                  </Box>
                  <Box sx={{ minWidth: 35 }}>
                    <Typography variant="body2" color="text.secondary">
                      {Math.round(goal.progress)}%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Timing
                </Typography>
                <Typography variant="body2">
                  Start: {formatTime(goal.startTime)}
                </Typography>
                {goal.endTime && (
                  <Typography variant="body2">
                    End: {formatTime(goal.endTime)}
                  </Typography>
                )}
              </Box>
              
              {goal.assignedAgents && goal.assignedAgents.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Assigned Agents
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {goal.assignedAgents.map((agent, index) => (
                      <Chip 
                        key={index}
                        label={agent} 
                        size="small" 
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Grid>
          </Grid>
          
          {isActive && goal.status === 'pending' && (
            <Box sx={{ mt: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<PlayArrowIcon />}
                onClick={() => onInitializeGoal(goal.description)}
              >
                Start this goal
              </Button>
            </Box>
          )}
          
          {goal.subgoals && goal.subgoals.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Sub-goals
              </Typography>
              <Box sx={{ pl: 2 }}>
                {goal.subgoals.map((subgoal, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    {subgoal.status === 'completed' ? (
                      <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 1 }} />
                    ) : (
                      <RadioButtonUncheckedIcon color="disabled" fontSize="small" sx={{ mr: 1 }} />
                    )}
                    <Typography variant="body2">
                      {subgoal.description}
                    </Typography>
                    <Chip 
                      label={`${Math.round(subgoal.progress)}%`} 
                      size="small" 
                      sx={{ ml: 1 }}
                      variant="outlined"
                    />
                  </Box>
                ))}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      {goals.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No goals have been defined yet. Goals will appear here when the collaboration begins.
          </Typography>
        </Paper>
      ) : (
        <Box>
          <Typography variant="h6" gutterBottom>
            Workflow Progress
          </Typography>
          
          <Stepper activeStep={activeStep} orientation="vertical">
            {goals.map((goal, index) => (
              <Step key={index} completed={goal.status === 'completed'}>
                <StepLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle1">
                      {goal.description}
                    </Typography>
                    <Chip 
                      label={`${Math.round(goal.progress)}%`} 
                      size="small" 
                      color={
                        goal.status === 'completed' ? 'success' : 
                        goal.status === 'in-progress' ? 'primary' : 'default'
                      }
                    />
                  </Box>
                </StepLabel>
                <StepContent>
                  {renderDetailedGoalView(goal)}
                </StepContent>
              </Step>
            ))}
          </Stepper>
          
          <Divider sx={{ my: 3 }} />
          
          <Typography variant="h6" gutterBottom>
            All Goals
          </Typography>
          
          <Grid container spacing={2}>
            {goals.map((goal, index) => (
              <Grid item xs={12} key={index}>
                {renderDetailedGoalView(goal)}
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default GoalTracker;
