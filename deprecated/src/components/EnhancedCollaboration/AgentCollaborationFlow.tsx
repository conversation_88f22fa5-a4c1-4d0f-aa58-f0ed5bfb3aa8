// src/components/EnhancedCollaboration/AgentCollaborationFlow.tsx

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>nt,
  CircularProg<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AgentInteractionVisualizer from './AgentInteractionVisualizer';
import AgentReasoningPanel from './AgentReasoningPanel';

// Define the props interface
interface AgentCollaborationFlowProps {
  sessionId: string;
  state: any;
  onRefresh?: () => void;
  onViewArtifact?: (artifactId: string) => void;
}

// Define the workflow phases
const workflowPhases = [
  {
    label: 'Planning',
    description: 'Agents collaborate to plan content structure and approach',
    agents: ['content-strategy', 'market-research', 'seo-keyword']
  },
  {
    label: 'Research',
    description: 'Gathering information and keywords for content creation',
    agents: ['market-research', 'seo-keyword']
  },
  {
    label: 'Creation',
    description: 'Generating initial content drafts based on research',
    agents: ['content-generation']
  },
  {
    label: 'Review',
    description: 'Analyzing content quality and SEO performance',
    agents: ['seo-optimization', 'content-strategy']
  },
  {
    label: 'Refinement',
    description: 'Iterative improvement based on feedback and analysis',
    agents: ['content-generation', 'seo-optimization']
  },
  {
    label: 'Finalization',
    description: 'Preparing the final optimized content for publication',
    agents: ['content-generation', 'content-strategy']
  }
];

// Map agent IDs to display names
const agentNames: Record<string, string> = {
  'content-generation': 'Content Generation Agent',
  'seo-optimization': 'SEO Optimization Agent',
  'market-research': 'Market Research Agent',
  'content-strategy': 'Content Strategy Agent',
  'seo-keyword': 'SEO Keyword Agent'
};

/**
 * Component to visualize the collaborative workflow between agents
 */
const AgentCollaborationFlow: React.FC<AgentCollaborationFlowProps> = ({
  sessionId,
  state,
  onRefresh,
  onViewArtifact
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);

  // Get current phase index
  const currentPhaseIndex = workflowPhases.findIndex(
    phase => phase.label.toLowerCase() === state?.currentPhase?.toLowerCase()
  );

  // Get all artifacts as an array
  const artifacts = state?.artifacts ? Object.values(state.artifacts) : [];

  // Get active agents in the current phase
  const activeAgents = currentPhaseIndex >= 0 
    ? workflowPhases[currentPhaseIndex].agents
    : [];

  // Handle refresh
  const handleRefresh = async () => {
    if (!onRefresh) return;
    
    setLoading(true);
    try {
      await onRefresh();
      setError(null);
    } catch (err) {
      console.error('Error refreshing state:', err);
      setError('Failed to refresh collaboration state');
    } finally {
      setLoading(false);
    }
  };

  // Extract agent reasoning from artifacts
  const getAgentReasoning = (agentId: string) => {
    // Find the most recent artifact created by this agent
    const agentArtifacts = artifacts
      .filter((artifact: any) => artifact.createdBy === agentId)
      .sort((a: any, b: any) => {
        const aTimestamp = a.iterations[a.iterations.length - 1].timestamp;
        const bTimestamp = b.iterations[b.iterations.length - 1].timestamp;
        return new Date(bTimestamp).getTime() - new Date(aTimestamp).getTime();
      });
    
    if (agentArtifacts.length === 0) return null;
    
    const latestArtifact = agentArtifacts[0];
    const latestIteration = latestArtifact.iterations[latestArtifact.iterations.length - 1];
    
    return latestIteration.reasoning || null;
  };

  // Filter messages for the current phase
  const getCurrentPhaseMessages = () => {
    if (!state?.messages || !state.currentPhase) return [];
    
    // Get the timestamp when the current phase started
    const phaseStartMessages = state.messages.filter(
      (msg: any) => msg.type === 'PHASE_CHANGE' && msg.content.phase === state.currentPhase
    );
    
    if (phaseStartMessages.length === 0) return state.messages;
    
    const phaseStartTime = new Date(phaseStartMessages[0].timestamp).getTime();
    
    return state.messages.filter(
      (msg: any) => new Date(msg.timestamp).getTime() >= phaseStartTime
    );
  };

  const currentPhaseMessages = getCurrentPhaseMessages();

  return (
    <Box sx={{ mb: 4 }}>
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">
            Agent Collaboration Flow
          </Typography>
          <IconButton onClick={handleRefresh} disabled={loading}>
            {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
          </IconButton>
        </Box>

        <Divider sx={{ mb: 2 }} />
        
        {/* Workflow Phase Stepper */}
        <Stepper activeStep={currentPhaseIndex} orientation="horizontal" sx={{ mb: 4 }}>
          {workflowPhases.map((phase, index) => (
            <Step key={phase.label} completed={index < currentPhaseIndex}>
              <StepLabel>{phase.label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {/* Current Phase Details */}
        {currentPhaseIndex >= 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6">
              Current Phase: {workflowPhases[currentPhaseIndex].label}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {workflowPhases[currentPhaseIndex].description}
            </Typography>
            
            <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {workflowPhases[currentPhaseIndex].agents.map(agentId => (
                <Tooltip key={agentId} title={`${agentNames[agentId] || agentId} is active in this phase`}>
                  <Button
                    variant={selectedAgentId === agentId ? "contained" : "outlined"}
                    size="small"
                    onClick={() => setSelectedAgentId(agentId === selectedAgentId ? null : agentId)}
                  >
                    {agentNames[agentId] || agentId}
                  </Button>
                </Tooltip>
              ))}
            </Box>
          </Box>
        )}
        
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        <Grid container spacing={3}>
          {/* Agent Reasoning */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Agent Reasoning
            </Typography>
            
            {selectedAgentId ? (
              <>
                {getAgentReasoning(selectedAgentId) ? (
                  <AgentReasoningPanel
                    agentId={selectedAgentId}
                    agentName={agentNames[selectedAgentId] || selectedAgentId}
                    reasoning={getAgentReasoning(selectedAgentId)}
                    expanded={true}
                  />
                ) : (
                  <Paper sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      No reasoning data available for {agentNames[selectedAgentId] || selectedAgentId}
                    </Typography>
                  </Paper>
                )}
              </>
            ) : (
              <>
                {activeAgents.map(agentId => {
                  const reasoning = getAgentReasoning(agentId);
                  return reasoning ? (
                    <AgentReasoningPanel
                      key={agentId}
                      agentId={agentId}
                      agentName={agentNames[agentId] || agentId}
                      reasoning={reasoning}
                      expanded={false}
                      onShowMore={() => setSelectedAgentId(agentId)}
                    />
                  ) : null;
                })}
                
                {activeAgents.every(agentId => !getAgentReasoning(agentId)) && (
                  <Paper sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      No reasoning data available for active agents
                    </Typography>
                  </Paper>
                )}
              </>
            )}
          </Grid>
          
          {/* Agent Interactions */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Agent Interactions
            </Typography>
            
            <Box sx={{ height: 500 }}>
              <AgentInteractionVisualizer
                messages={selectedAgentId 
                  ? currentPhaseMessages.filter((msg: any) => 
                      msg.from === selectedAgentId || msg.to === selectedAgentId
                    )
                  : currentPhaseMessages
                }
                artifacts={state.artifacts}
                loading={loading}
                onRefresh={handleRefresh}
                onViewArtifact={onViewArtifact}
              />
            </Box>
          </Grid>
        </Grid>
        
        {/* Recent Artifacts */}
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            Recent Artifacts
          </Typography>
          
          <Grid container spacing={2}>
            {artifacts.length > 0 ? (
              artifacts
                .sort((a: any, b: any) => {
                  const aTimestamp = a.iterations[a.iterations.length - 1].timestamp;
                  const bTimestamp = b.iterations[b.iterations.length - 1].timestamp;
                  return new Date(bTimestamp).getTime() - new Date(aTimestamp).getTime();
                })
                .slice(0, 4)
                .map((artifact: any, index: number) => (
                  <Grid item xs={12} sm={6} md={3} key={`artifact-${index}`}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle2" noWrap>
                          {artifact.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" noWrap>
                          Created by: {agentNames[artifact.createdBy] || artifact.createdBy}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Version: {artifact.currentVersion}
                        </Typography>
                        
                        {onViewArtifact && (
                          <Button
                            size="small"
                            startIcon={<VisibilityIcon />}
                            onClick={() => onViewArtifact(artifact.id)}
                            sx={{ mt: 1 }}
                          >
                            View
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))
            ) : (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', p: 2 }}>
                  No artifacts created yet
                </Typography>
              </Grid>
            )}
          </Grid>
        </Box>
      </Paper>
    </Box>
  );
};

export default AgentCollaborationFlow;
