import { useState, useEffect, useCallback } from 'react';
import { AgentLogEntry } from './AgentActivityLogger';

/**
 * Hook to fetch and manage agent logs
 * @param sessionId The current session ID
 * @param pollingInterval How often to poll for new logs (in ms)
 * @param maxLogs Maximum number of logs to keep in memory
 */
const useAgentLogs = (sessionId: string, pollingInterval = 3000, maxLogs = 200) => {
  const [logs, setLogs] = useState<AgentLogEntry[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastLogId, setLastLogId] = useState<string | null>(null);

  // Function to fetch logs from the API
  const fetchLogs = useCallback(async () => {
    if (!sessionId) return;

    try {
      setIsLoading(true);
      setError(null);

      // Build query string for fetching only new logs
      const queryParams = new URLSearchParams();
      queryParams.append('sessionId', sessionId);
      if (lastLogId) {
        queryParams.append('after', lastLogId);
      }

      // Add a larger limit to ensure we get all logs
      queryParams.append('limit', '100');

      console.log(`Fetching logs for session ${sessionId} with params: ${queryParams.toString()}`);
      const response = await fetch(`/api/agents/collaborative-iteration/logs?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch logs: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`Received ${data.logs?.length || 0} logs from server`);

      if (data.logs && data.logs.length > 0) {
        // Convert ISO strings to Date objects and improve compatibility
        const formattedLogs = data.logs.map((log: any) => {
          // Ensure each log has agent information
          const processedLog = {
            ...log,
            timestamp: new Date(log.timestamp),
            // Make sure agent field is always set - this helps with UI filtering
            agent: log.agent || log.from || 'system',
            // Create a message format compatible with the discussion panel
            from: log.from || log.agent || 'system',
            to: log.to || 'system',
            // Add ID if missing
            id: log.id || `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            // Ensure level is set
            level: log.level || 'info',
            // Ensure message is set
            message: log.message || (log.content?.text || JSON.stringify(log.content || {}))
          };

          // Format content field to ensure compatibility with message display
          if (log.message && !log.content) {
            processedLog.content = { text: log.message };
          }

          // Extract artifact information if present
          if (log.context?.artifactsCount > 0) {
            processedLog.message = `${processedLog.message} (Artifacts: ${log.context.artifactsCount})`;

            // Add a special log entry for artifact creation
            if (log.context.artifactsCount > 0 && !log.message.includes('artifact')) {
              addLog({
                level: 'success',
                message: `${log.context.artifactsCount} artifact(s) created successfully`,
                agent: processedLog.agent,
                phase: log.phase || 'artifact-creation'
              });
            }
          }

          console.log(`Processed log for agent: ${processedLog.agent} - ${processedLog.message.substring(0, 50)}...`);
          return processedLog;
        });

        // Add new logs to existing logs, but limit to maxLogs
        setLogs(prevLogs => {
          const updatedLogs = [...prevLogs, ...formattedLogs];
          return updatedLogs.slice(-maxLogs);
        });

        // Update last log ID for pagination
        if (data.logs.length > 0) {
          setLastLogId(data.logs[data.logs.length - 1].id);
        }

        // Check for artifact creation in the logs
        const artifactLogs = formattedLogs.filter(log =>
          log.message.includes('artifact') ||
          (log.context && log.context.artifactsCount > 0)
        );

        if (artifactLogs.length > 0) {
          console.log(`Found ${artifactLogs.length} logs related to artifacts`);

          // Add a special log entry for artifact creation
          addLog({
            level: 'info',
            message: `Artifacts detected in backend. Refresh the Artifacts tab to view them.`,
            agent: 'system',
            phase: 'artifact-detection'
          });
        }
      }
    } catch (err) {
      console.error('Error fetching agent logs:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch logs');
    } finally {
      setIsLoading(false);
    }
  }, [sessionId, lastLogId, maxLogs]);

  // Add a single log entry manually (useful for frontend-generated logs)
  const addLog = useCallback((log: Omit<AgentLogEntry, 'timestamp'>) => {
    const newLog: AgentLogEntry = {
      ...log,
      timestamp: new Date()
    };

    setLogs(prevLogs => {
      const updatedLogs = [...prevLogs, newLog];
      return updatedLogs.slice(-maxLogs);
    });
  }, [maxLogs]);

  // Clear all logs
  const clearLogs = useCallback(() => {
    setLogs([]);
    setLastLogId(null);
  }, []);

  // Set up polling for logs
  useEffect(() => {
    if (!sessionId) return;

    // Add an initial "session started" log entry
    addLog({
      level: 'info',
      message: 'Agent collaboration session initialized',
      agent: 'system',
      phase: 'initialization'
    });

    // Fetch logs immediately
    fetchLogs();

    // Set up polling interval
    const intervalId = setInterval(fetchLogs, pollingInterval);

    // Clean up on unmount or when session changes
    return () => {
      clearInterval(intervalId);
    };
  }, [sessionId, pollingInterval, fetchLogs, addLog]);

  return {
    logs,
    isLoading,
    error,
    addLog,
    clearLogs,
    refetch: fetchLogs
  };
};

export default useAgentLogs;
