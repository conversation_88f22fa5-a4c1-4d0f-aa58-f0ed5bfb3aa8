// src/components/EnhancedCollaboration/MessagePanel.tsx
import React, { useEffect, useRef } from 'react';

interface Message {
  id?: string;
  sender?: string;
  content?: string;
  timestamp?: string;
  type?: string;
  [key: string]: any;
}

interface MessagePanelProps {
  messages: Message[] | null | undefined;
  messageInput: string;
  setMessageInput: (input: string) => void;
  sendMessage: () => void;
  loading: boolean;
}

const MessagePanel: React.FC<MessagePanelProps> = ({
  messages,
  messageInput,
  setMessageInput,
  sendMessage,
  loading
}) => {
  // Create a ref for the messages container to auto-scroll to bottom
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Ensure messages is an array
  const safeMessages = Array.isArray(messages) ? messages : [];
  
  // Debug the incoming messages array
  useEffect(() => {
    console.log('MessagePanel received:', {
      messagesLength: safeMessages.length,
      messagesIsArray: Array.isArray(messages),
      firstMessageKeys: safeMessages.length > 0 ? Object.keys(safeMessages[0]) : []
    });
  }, [messages, safeMessages]);
  
  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [safeMessages]);
  
  // Function to handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && messageInput.trim()) {
      e.preventDefault();
      sendMessage();
    }
  };
  
  // Function to format message content
  const formatMessageContent = (message: Message) => {
    // If content is missing, try to find it in other properties
    let content = message.content;
    
    if (!content) {
      // Try other common properties
      if (message.text) {
        content = message.text;
      } else if (message.body) {
        content = message.body;
      } else if (message.message) {
        content = message.message;
      } else if (typeof message.data === 'string') {
        content = message.data;
      } else if (message.data?.content) {
        content = message.data.content;
      } else if (message.data?.text) {
        content = message.data.text;
      }
    }
    
    // If still no content, return a placeholder
    if (!content) {
      return '[No message content]';
    }
    
    // If content is not a string, convert it
    if (typeof content !== 'string') {
      return JSON.stringify(content);
    }
    
    return content;
  };
  
  // Function to get sender name
  const getSenderName = (message: Message) => {
    const sender = message.sender || message.from || message.author || message.agent || 'Unknown';
    return sender === 'user' ? 'You' : sender;
  };
  
  // Function to get message timestamp
  const getMessageTime = (message: Message) => {
    const timestamp = message.timestamp || message.time || message.createdAt || message.date;
    
    if (!timestamp) {
      return 'Unknown time';
    }
    
    try {
      return new Date(timestamp).toLocaleString();
    } catch (error) {
      return 'Invalid time';
    }
  };
  
  // Function to determine if a message is from the user
  const isUserMessage = (message: Message) => {
    const sender = message.sender || message.from || message.author || message.agent || '';
    return sender.toLowerCase() === 'user';
  };

  return (
    <div className="message-panel">
      <h2 className="panel-title">Agent Messages</h2>
      
      <div className="messages-container">
        {safeMessages.length > 0 ? (
          <div className="message-list">
            {safeMessages.map((message, index) => (
              <div 
                key={`message-panel-${message.id || 'unknown'}-${index}-${Math.random().toString(36).substring(2, 9)}`} 
                className={`message-item ${isUserMessage(message) ? 'user-message' : 'agent-message'}`}
              >
                <div className="message-header">
                  <span className="sender">{getSenderName(message)}</span>
                  <span className="timestamp">{getMessageTime(message)}</span>
                </div>
                <div className="message-content">
                  {formatMessageContent(message)}
                </div>
                {message.type && (
                  <div className="message-type">
                    Type: {message.type}
                  </div>
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        ) : (
          <div className="no-messages">
            <p>No messages yet. Start the conversation by sending a message to the agents.</p>
          </div>
        )}
      </div>
      
      <div className="message-input-container">
        <input
          type="text"
          placeholder="Type a message to the agents..."
          value={messageInput}
          onChange={(e) => setMessageInput(e.target.value)}
          onKeyPress={handleKeyPress}
          className="message-input"
          disabled={loading}
        />
        <button
          className="send-button"
          onClick={sendMessage}
          disabled={!messageInput.trim() || loading}
        >
          {loading ? 'Sending...' : 'Send'}
        </button>
      </div>
      
      <style jsx>{`
        .message-panel {
          display: flex;
          flex-direction: column;
          height: 100%;
          min-height: 400px;
        }
        
        .panel-title {
          font-size: 1.5rem;
          margin-bottom: 1rem;
          color: #111827;
        }
        
        .messages-container {
          flex-grow: 1;
          overflow-y: auto;
          background-color: #f9fafb;
          border-radius: 8px;
          margin-bottom: 1rem;
          padding: 1rem;
          min-height: 300px;
          max-height: 500px;
        }
        
        .message-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }
        
        .message-item {
          display: flex;
          flex-direction: column;
          padding: 0.75rem;
          border-radius: 8px;
          max-width: 80%;
        }
        
        .user-message {
          align-self: flex-end;
          background-color: #eff6ff;
          border: 1px solid #dbeafe;
        }
        
        .agent-message {
          align-self: flex-start;
          background-color: #ffffff;
          border: 1px solid #e5e7eb;
        }
        
        .message-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.5rem;
          font-size: 0.75rem;
        }
        
        .sender {
          font-weight: 600;
          color: #4b5563;
        }
        
        .timestamp {
          color: #9ca3af;
        }
        
        .message-content {
          font-size: 0.875rem;
          color: #111827;
          line-height: 1.5;
          white-space: pre-wrap;
          word-break: break-word;
        }
        
        .message-type {
          margin-top: 0.5rem;
          font-size: 0.75rem;
          color: #6b7280;
          padding: 0.25rem 0.5rem;
          background-color: #f3f4f6;
          border-radius: 4px;
          align-self: flex-start;
        }
        
        .no-messages {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #6b7280;
          font-size: 0.875rem;
          text-align: center;
          padding: 2rem;
        }
        
        .message-input-container {
          display: flex;
          gap: 0.5rem;
          margin-top: auto;
        }
        
        .message-input {
          flex-grow: 1;
          padding: 0.75rem;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          font-size: 0.875rem;
          outline: none;
          transition: border-color 0.2s;
        }
        
        .message-input:focus {
          border-color: #3b82f6;
        }
        
        .send-button {
          padding: 0.75rem 1.5rem;
          background-color: #3b82f6;
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        
        .send-button:hover:not(:disabled) {
          background-color: #2563eb;
        }
        
        .send-button:disabled {
          background-color: #9ca3af;
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
};

export default MessagePanel;
