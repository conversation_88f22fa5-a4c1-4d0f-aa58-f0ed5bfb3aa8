'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Grid,
  Button,
  Tooltip,
  IconButton
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoIcon from '@mui/icons-material/Info';
import SearchIcon from '@mui/icons-material/Search';
import KeyboardIcon from '@mui/icons-material/Keyboard';
import DescriptionIcon from '@mui/icons-material/Description';
import CreateIcon from '@mui/icons-material/Create';
import RateReviewIcon from '@mui/icons-material/RateReview';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

// Define the phase information
const phaseInfo = {
  'research': {
    title: 'Research Phase',
    description: 'In this phase, agents gather market research, analyze SEO keywords, and develop a content strategy.',
    icon: <SearchIcon />,
    color: '#2196f3',
    agents: [
      { id: 'market-research', name: 'Market Research Agent', color: '#2196f3', description: 'Analyzes market trends, audience demographics, and competitive landscape' },
      { id: 'seo-keyword', name: 'SEO Keyword Agent', color: '#ff9800', description: 'Identifies high-value keywords and analyzes search volume and competition' },
      { id: 'content-strategy', name: 'Content Strategy Agent', color: '#3f51b5', description: 'Develops content structure and strategy based on research findings' }
    ],
    outputs: [
      { type: 'market-research-report', name: 'Market Research Report', description: 'Analysis of target audience, market trends, and competition' },
      { type: 'keyword-analysis', name: 'SEO Keyword Analysis', description: 'Prioritized list of keywords with search volume and competition metrics' },
      { type: 'content-strategy', name: 'Content Strategy', description: 'Comprehensive plan for content structure, tone, and approach' }
    ],
    validationCriteria: [
      'Market research quality score ≥ 0.75',
      'Keyword analysis quality score ≥ 0.75',
      'Content strategy quality score ≥ 0.80'
    ]
  },
  'content-generation': {
    title: 'Content Generation Phase',
    description: 'In this phase, the content generation agent creates a draft based on the content strategy and research findings.',
    icon: <CreateIcon />,
    color: '#009688',
    agents: [
      { id: 'content-generation', name: 'Content Generation Agent', color: '#009688', description: 'Creates high-quality content following the content strategy' },
      { id: 'content-strategy', name: 'Content Strategy Agent', color: '#3f51b5', description: 'Provides feedback on content alignment with strategy' },
      { id: 'seo-keyword', name: 'SEO Keyword Agent', color: '#ff9800', description: 'Provides feedback on keyword usage and optimization' }
    ],
    outputs: [
      { type: 'content-draft', name: 'Content Draft', description: 'Initial draft of the content following the content strategy' }
    ],
    validationCriteria: [
      'Content draft quality score ≥ 0.85',
      'Proper implementation of content strategy',
      'Effective use of target keywords'
    ]
  },
  'review': {
    title: 'Review & Optimization Phase',
    description: 'In this phase, the SEO optimization agent reviews and enhances the content for search engine visibility.',
    icon: <RateReviewIcon />,
    color: '#ff9800',
    agents: [
      { id: 'seo-optimization', name: 'SEO Optimization Agent', color: '#f44336', description: 'Optimizes content for search engines while maintaining readability' },
      { id: 'content-strategy', name: 'Content Strategy Agent', color: '#3f51b5', description: 'Ensures optimized content still aligns with strategy' }
    ],
    outputs: [
      { type: 'seo-optimized-content', name: 'SEO Optimized Content', description: 'Final content with SEO enhancements applied' }
    ],
    validationCriteria: [
      'SEO optimized content quality score ≥ 0.85',
      'Proper keyword placement and density',
      'Optimized meta tags and structure'
    ]
  },
  'finalization': {
    title: 'Finalization Phase',
    description: 'In this phase, the final content is prepared for publication with all optimizations applied.',
    icon: <CheckCircleIcon />,
    color: '#4caf50',
    agents: [
      { id: 'system', name: 'System', color: '#9e9e9e', description: 'Finalizes the content and prepares it for publication' }
    ],
    outputs: [
      { type: 'final-content', name: 'Final Content', description: 'Publication-ready content with all optimizations applied' }
    ],
    validationCriteria: [
      'All previous phase validations passed',
      'Final content meets quality standards'
    ]
  }
};

interface WorkflowPhaseExplainerProps {
  currentPhase: string;
  workflowProgress?: any;
  artifacts?: Record<string, any>;
  generatedArtifacts?: string[];
}

/**
 * Component to explain the workflow phases, their purposes, and outputs
 */
const WorkflowPhaseExplainer: React.FC<WorkflowPhaseExplainerProps> = ({
  currentPhase,
  workflowProgress,
  artifacts = {},
  generatedArtifacts = []
}) => {
  const [expandedPhase, setExpandedPhase] = useState<string | false>(currentPhase);

  // Map API phase names to our phase info keys
  const getPhaseKey = (phase: string): string => {
    if (phase === 'creation') return 'content-generation';
    return phase;
  };

  // Handle accordion expansion
  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedPhase(isExpanded ? panel : false);
  };

  // Check if a phase is complete based on workflow progress
  const isPhaseComplete = (phase: string): boolean => {
    if (!workflowProgress) return false;
    
    switch (phase) {
      case 'research':
        return workflowProgress.marketResearchComplete && 
               workflowProgress.keywordResearchComplete && 
               workflowProgress.contentStrategyComplete;
      case 'content-generation':
        return workflowProgress.contentGenerationComplete;
      case 'review':
        return workflowProgress.seoOptimizationComplete;
      case 'finalization':
        return currentPhase === 'completed';
      default:
        return false;
    }
  };

  // Check if a phase is active
  const isPhaseActive = (phase: string): boolean => {
    return getPhaseKey(currentPhase) === phase;
  };

  // Find artifacts of a specific type
  const findArtifactsByType = (type: string): any[] => {
    if (!artifacts || !generatedArtifacts) return [];
    
    return generatedArtifacts
      .map(id => artifacts[id])
      .filter(artifact => artifact?.type === type);
  };

  // Render the phase accordions
  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          Workflow Phases Explained
        </Typography>
        <Tooltip title="Understanding the workflow phases helps you track progress and understand the collaborative content creation process.">
          <IconButton>
            <HelpOutlineIcon />
          </IconButton>
        </Tooltip>
      </Box>
      
      <Typography variant="body2" color="text.secondary" paragraph>
        Our AI agents collaborate through a structured workflow to create high-quality content. Each phase has specific goals, outputs, and quality criteria.
      </Typography>
      
      <Divider sx={{ mb: 3 }} />
      
      {Object.entries(phaseInfo).map(([phase, info]) => (
        <Accordion
          key={phase}
          expanded={expandedPhase === phase}
          onChange={handleChange(phase)}
          sx={{
            mb: 2,
            borderLeft: '4px solid',
            borderColor: isPhaseActive(phase) ? info.color : 'transparent',
            bgcolor: isPhaseActive(phase) ? `${info.color}10` : 'background.paper'
          }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <Avatar sx={{ bgcolor: info.color, mr: 2 }}>
                {info.icon}
              </Avatar>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="subtitle1" fontWeight={isPhaseActive(phase) ? 'bold' : 'normal'}>
                  {info.title}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {info.description}
                </Typography>
              </Box>
              <Box>
                {isPhaseComplete(phase) ? (
                  <Chip label="Complete" color="success" size="small" />
                ) : isPhaseActive(phase) ? (
                  <Chip label="In Progress" color="primary" size="small" />
                ) : (
                  <Chip label="Pending" color="default" size="small" />
                )}
              </Box>
            </Box>
          </AccordionSummary>
          
          <AccordionDetails>
            <Grid container spacing={3}>
              {/* Agents involved */}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Agents Involved
                </Typography>
                <List dense>
                  {info.agents.map(agent => (
                    <ListItem key={agent.id}>
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: agent.color, width: 28, height: 28, fontSize: '0.875rem' }}>
                          {agent.name.charAt(0)}
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={agent.name}
                        secondary={agent.description}
                      />
                    </ListItem>
                  ))}
                </List>
              </Grid>
              
              {/* Expected outputs */}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Expected Outputs
                </Typography>
                <List dense>
                  {info.outputs.map(output => {
                    const artifacts = findArtifactsByType(output.type);
                    const hasArtifact = artifacts.length > 0;
                    
                    return (
                      <ListItem key={output.type}>
                        <ListItemIcon>
                          <Avatar sx={{ 
                            bgcolor: hasArtifact ? '#4caf50' : 'grey.300', 
                            width: 28, 
                            height: 28, 
                            fontSize: '0.875rem' 
                          }}>
                            {hasArtifact ? <CheckCircleIcon fontSize="small" /> : <DescriptionIcon fontSize="small" />}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {output.name}
                              {hasArtifact && (
                                <Chip 
                                  label="Generated" 
                                  color="success" 
                                  size="small" 
                                  sx={{ ml: 1, height: 20 }} 
                                />
                              )}
                            </Box>
                          }
                          secondary={output.description}
                        />
                      </ListItem>
                    );
                  })}
                </List>
              </Grid>
              
              {/* Validation criteria */}
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Validation Criteria
                </Typography>
                <Box sx={{ pl: 2, borderLeft: '2px solid', borderColor: 'divider' }}>
                  {info.validationCriteria.map((criterion, index) => (
                    <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                      • {criterion}
                    </Typography>
                  ))}
                </Box>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      ))}
    </Paper>
  );
};

export default WorkflowPhaseExplainer;
