// src/components/EnhancedCollaboration/ReasoningPanel.tsx
import React, { useState, useEffect, useMemo } from 'react';
import { Box, Typography, Paper, Accordion, AccordionSummary, AccordionDetails, Divider, Chip, LinearProgress, Avatar, Tabs, Tab } from '@mui/material';
import { Timeline, TimelineItem, TimelineSeparator, TimelineConnector, TimelineContent, TimelineDot, TimelineOppositeContent } from '@mui/lab';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { format, parseISO } from 'date-fns';
// Agent icons
import PsychologyIcon from '@mui/icons-material/Psychology';
import SearchIcon from '@mui/icons-material/Search';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import EditNoteIcon from '@mui/icons-material/EditNote';
import SpeedIcon from '@mui/icons-material/Speed';

interface ReasoningStep {
  id?: string;
  name?: string;
  content?: string;
  evidence?: string;
}

interface Reasoning {
  process?: string;
  steps?: string[];
  thoughts?: string[];
  considerations?: string[];
  decision?: string;
  confidence?: number;
  agentId?: string;
  timestamp?: string;
  conclusion?: string;
  supportingEvidence?: string[];
  insights?: string[];
}

interface Decision {
  id?: string;
  agent?: string;
  creator?: string;
  author?: string;
  source?: string;
  timestamp?: string;
  time?: string;
  createdAt?: string;
  date?: string;
  context?: string;
  situation?: string;
  problem?: string;
  input?: string;
  reasoning?: string | Reasoning;
  rationale?: string;
  thought?: string;
  thinking?: string;
  process?: string;
  reasoningSteps?: ReasoningStep[];
  outcome?: string;
  result?: string;
  decision?: string;
  output?: string;
  conclusion?: string;
  confidence?: number;
  certainty?: number;
  probability?: number;
  iterations?: any[];
  content?: any;
  metadata?: any;
  [key: string]: any;
}

interface ReasoningPanelProps {
  decisions: Decision[] | null | undefined;
  artifacts?: any[] | Record<string, any>;
}

const ReasoningPanel: React.FC<ReasoningPanelProps> = ({ decisions, artifacts }) => {
  // State
  const [expandedDecision, setExpandedDecision] = useState<string | null>(null);
  const [expandedAgent, setExpandedAgent] = useState<string | false>('market-research');
  const [processedDecisions, setProcessedDecisions] = useState<Decision[]>([]);

  // Enhanced recursive function to find reasoning data in nested objects
  const findReasoningData = (obj: any): Reasoning | null => {
    if (!obj) return null;

    // If it's already a reasoning object with expected fields, return it
    if (obj.thoughts || obj.steps || obj.considerations || obj.process) {
      return obj as Reasoning;
    }

    // Check common reasoning fields
    const possibleReasoningFields = ['reasoning', 'rationale', 'thought', 'thinking', 'process'];
    for (const field of possibleReasoningFields) {
      if (obj[field] && typeof obj[field] === 'object') {
        return obj[field] as Reasoning;
      }
    }

    // Check metadata for reasoning
    if (obj.metadata && typeof obj.metadata === 'object') {
      if (obj.metadata.reasoning) {
        return obj.metadata.reasoning as Reasoning;
      }
    }

    // Check iterations for reasoning
    if (Array.isArray(obj.iterations) && obj.iterations.length > 0) {
      // Get the latest iteration
      const latestIteration = obj.iterations[obj.iterations.length - 1];
      if (latestIteration && latestIteration.reasoning) {
        return latestIteration.reasoning as Reasoning;
      }
    }

    // Recursively search all object properties
    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        const nestedReasoning = findReasoningData(obj[key]);
        if (nestedReasoning) return nestedReasoning;
      }
    }

    return null;
  };

  // Process artifacts to extract reasoning data
  const extractReasoningFromArtifacts = useMemo(() => {
    return () => {
      if (!artifacts) return [];

      let artifactsArray: any[] = [];

      // Convert object format to array if needed
      if (Array.isArray(artifacts)) {
        artifactsArray = artifacts;
      } else if (typeof artifacts === 'object') {
        artifactsArray = Object.values(artifacts);
      }

      // Extract reasoning data from artifacts
      const extractedDecisions: Decision[] = [];

      artifactsArray.forEach((artifact) => {
        if (!artifact) return;

        const reasoningData = findReasoningData(artifact);
        if (reasoningData) {
          // Create a decision object from the reasoning data
          const decision: Decision = {
            id: artifact.id || `artifact-${Math.random().toString(36).substring(7)}`,
            agent: artifact.createdBy || artifact.agent || artifact.creator || 'unknown',
            timestamp: artifact.createdAt || artifact.timestamp || artifact.created || new Date().toISOString(),
            context: artifact.name || artifact.title || 'Artifact reasoning',
            reasoning: reasoningData,
            outcome: reasoningData.decision || reasoningData.conclusion || '',
            confidence: reasoningData.confidence
          };

          extractedDecisions.push(decision);
        }
      });

      return extractedDecisions;
    };
  }, [artifacts]);

  // Combine decisions from props and extracted from artifacts
  useEffect(() => {
    // Start with decisions from props
    let allDecisions: Decision[] = Array.isArray(decisions) ? [...decisions] : [];

    // Add decisions extracted from artifacts
    const artifactDecisions = extractReasoningFromArtifacts();

    // Combine and deduplicate by ID
    const combinedDecisions = [...allDecisions, ...artifactDecisions];
    const uniqueDecisions = Array.from(
      new Map(combinedDecisions.map(decision => [decision.id, decision])).values()
    );

    setProcessedDecisions(uniqueDecisions);

    console.log('ReasoningPanel processed:', {
      decisionsFromProps: allDecisions.length,
      decisionsFromArtifacts: artifactDecisions.length,
      combinedDecisions: uniqueDecisions.length
    });
  }, [decisions, extractReasoningFromArtifacts]);

  // Debug the incoming decisions array
  useEffect(() => {
    console.log('ReasoningPanel received:', {
      decisionsLength: processedDecisions.length,
      decisionsIsArray: Array.isArray(processedDecisions),
      firstDecisionKeys: processedDecisions.length > 0 ? Object.keys(processedDecisions[0]) : [],
      hasReasoningSteps: processedDecisions.length > 0 && processedDecisions[0].reasoningSteps !== undefined,
    });
  }, [processedDecisions]);

  // Function to get agent name
  const getAgentName = (decision: Decision): string => {
    const agentId = getAgentId(decision);
    return getAgentDisplayName(agentId);
  };

  // Function to get agent display name from ID
  const getAgentDisplayName = (agentId: string): string => {
    // Format the agent name for display
    const agentMap: {[key: string]: string} = {
      'market-research': 'Market Research',
      'seo-keyword': 'SEO Keywords',
      'content-strategy': 'Content Strategy',
      'content-generation': 'Content Generation',
      'seo-optimization': 'SEO Optimization',
      'unknown': 'Unknown Agent'
    };

    return agentMap[agentId] || agentId;
  };

  // Get the agent ID for a decision (normalized)
  const getAgentId = (decision: Decision): string => {
    const agentId = (decision.agent ||
                    decision.creator ||
                    decision.author ||
                    decision.source ||
                    'unknown').toLowerCase();

    // Normalize agent IDs
    if (agentId.includes('market') || agentId.includes('research')) return 'market-research';
    if (agentId.includes('seo') && (agentId.includes('keyword') || agentId.includes('key'))) return 'seo-keyword';
    if (agentId.includes('strategy') || agentId.includes('plan')) return 'content-strategy';
    if (agentId.includes('content') || agentId.includes('generation') || agentId.includes('writer')) return 'content-generation';
    if (agentId.includes('optim') || agentId.includes('review')) return 'seo-optimization';

    return agentId;
  };

  // Get agent icon for timeline
  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'market-research':
        return <SearchIcon />;
      case 'seo-keyword':
        return <TrendingUpIcon />;
      case 'content-strategy':
        return <PsychologyIcon />;
      case 'content-generation':
        return <EditNoteIcon />;
      case 'seo-optimization':
        return <SpeedIcon />;
      default:
        return <PsychologyIcon />;
    }
  };

  // Get color for agent
  const getAgentColor = (agentId: string): string => {
    // Agent-specific colors
    switch (agentId) {
      case 'market-research':
        return 'primary';
      case 'seo-keyword':
        return 'secondary';
      case 'content-strategy':
        return 'info';
      case 'content-generation':
        return 'success';
      case 'seo-optimization':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Function to get decision timestamp
  const getDecisionTime = (decision: Decision): string => {
    const timestamp = decision.timestamp || decision.time || decision.createdAt || decision.date;

    if (!timestamp) {
      return 'Unknown time';
    }

    try {
      // Try to format the date nicely
      return format(parseISO(timestamp), 'MMM d, yyyy h:mm a');
    } catch (error) {
      // Fallback to basic formatting
      try {
        return new Date(timestamp).toLocaleString();
      } catch (err) {
        return 'Invalid time';
      }
    }
  };

  // Function to get decision context
  const getContext = (decision: Decision): string => {
    return decision.context || decision.situation || decision.problem || decision.input || 'No context provided';
  };

  // Function to get decision reasoning
  const getReasoning = (decision: Decision): string => {
    return decision.reasoning || decision.rationale || decision.thought || decision.thinking || decision.process || 'No reasoning provided';
  };

  // Check if decision has reasoning steps
  const hasReasoningSteps = (decision: Decision): boolean => {
    return !!decision.reasoningSteps && Array.isArray(decision.reasoningSteps) && decision.reasoningSteps.length > 0;
  };

  // Function to get decision outcome
  const getOutcome = (decision: Decision): string => {
    return decision.outcome || decision.result || decision.decision || decision.output || decision.conclusion || 'No outcome provided';
  };

  // Function to get confidence level (0-1)
  const getConfidence = (decision: Decision): number | undefined => {
    if (decision.confidence !== undefined) {
      // If confidence is already between 0-1, use it directly
      if (decision.confidence >= 0 && decision.confidence <= 1) {
        return decision.confidence;
      }

      // If confidence is on a different scale (e.g., 0-100), normalize it
      if (decision.confidence >= 0 && decision.confidence <= 100) {
        return decision.confidence / 100;
      }
    }

    // Check for other possible confidence fields
    if (decision.certainty !== undefined) {
      return decision.certainty >= 0 && decision.certainty <= 1 ? decision.certainty : decision.certainty / 100;
    }

    if (decision.probability !== undefined) {
      return decision.probability >= 0 && decision.probability <= 1 ? decision.probability : decision.probability / 100;
    }

    // Default confidence
    return undefined;
  };

  // Group decisions by agent
  const groupDecisionsByAgent = (): {[key: string]: Decision[]} => {
    const groups: {[key: string]: Decision[]} = {};

    // Sort decisions by timestamp if possible (newest first)
    const sortedDecisions = [...processedDecisions].sort((a, b) => {
      const timeA = a.timestamp || a.createdAt || a.created || a.date || '';
      const timeB = b.timestamp || b.createdAt || b.created || b.date || '';

      if (!timeA || !timeB) return 0;
      try {
        return new Date(timeB).getTime() - new Date(timeA).getTime();
      } catch (error) {
        return 0;
      }
    });

    // Group by agent ID
    sortedDecisions.forEach(decision => {
      const agentId = getAgentId(decision);
      if (!groups[agentId]) groups[agentId] = [];
      groups[agentId].push(decision);
    });

    return groups;
  };

  // Group decisions for display
  const groupedDecisions = groupDecisionsByAgent();

  // Sort agent IDs for display
  const agentIds = Object.keys(groupedDecisions);

  // Define the order of agent types
  const agentOrder = {
    'market-research': 1,
    'seo-keyword': 2,
    'content-strategy': 3,
    'content-generation': 4,
    'seo-optimization': 5
  };

  // Sort agent IDs by defined order
  agentIds.sort((a, b) => {
    const orderA = agentOrder[a as keyof typeof agentOrder] || 999;
    const orderB = agentOrder[b as keyof typeof agentOrder] || 999;
    return orderA - orderB;
  });

  // Render the timeline view for all decisions across agents
  const renderTimelineView = () => {
    // Combine all decisions from all agents and sort by timestamp
    const allDecisions = [...processedDecisions].sort((a, b) => {
      const timeA = a.timestamp || a.createdAt || a.created || a.date || '';
      const timeB = b.timestamp || b.createdAt || b.created || b.date || '';

      if (!timeA || !timeB) return 0;
      try {
        return new Date(timeA).getTime() - new Date(timeB).getTime();
      } catch (error) {
        return 0;
      }
    });

    return (
      <Timeline position="alternate">
        {allDecisions.map((decision, index) => {
          const agentId = getAgentId(decision);
          const agentColor = getAgentColor(agentId);

          return (
            <TimelineItem key={decision.id || index}>
              <TimelineOppositeContent color="text.secondary">
                {getDecisionTime(decision)}
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineDot color={agentColor as any}>
                  {getAgentIcon(agentId)}
                </TimelineDot>
                {index < allDecisions.length - 1 && <TimelineConnector />}
              </TimelineSeparator>
              <TimelineContent>
                <Paper
                  elevation={3}
                  sx={{
                    p: 2,
                    cursor: 'pointer'
                  }}
                  onClick={() => handleDecisionExpand(decision.id || `decision-${index}`)}
                >
                  <Typography variant="h6" component="div">
                    {getAgentName(decision)}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    {getContext(decision).substring(0, 100)}{getContext(decision).length > 100 ? '...' : ''}
                  </Typography>

                  {expandedDecision === (decision.id || `decision-${index}`) && (
                    <Box sx={{ mt: 2 }}>
                      <Divider sx={{ my: 1 }} />
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Reasoning:
                      </Typography>

                      {hasReasoningSteps(decision) ? (
                        <Box sx={{ ml: 2 }}>
                          {decision.reasoningSteps?.map((step, stepIndex) => (
                            <Box key={step.id || `step-${stepIndex}`} sx={{ mb: 2 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Step {stepIndex + 1}: {step.name || 'Reasoning'}
                              </Typography>
                              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                                {step.content}
                              </Typography>
                              {step.evidence && (
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
                                  Evidence: {step.evidence}
                                </Typography>
                              )}
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {getReasoning(decision)}
                        </ReactMarkdown>
                      )}

                      <Divider sx={{ my: 1 }} />
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Outcome:
                      </Typography>
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {getOutcome(decision)}
                      </ReactMarkdown>

                      {getConfidence(decision) !== undefined && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="caption" display="block" gutterBottom>
                            Confidence: {Math.round(getConfidence(decision)! * 100)}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={getConfidence(decision)! * 100}
                            color={getConfidence(decision)! > 0.8 ? 'success' : getConfidence(decision)! > 0.5 ? 'warning' : 'error'}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                        </Box>
                      )}
                    </Box>
                  )}
                </Paper>
              </TimelineContent>
            </TimelineItem>
          );
        })}
      </Timeline>
    );
  };

  // Render the grouped view (by agent)
  const renderGroupedView = () => {
    return (
      <Box>
        {agentIds.map((agentId) => (
          <Accordion
            key={agentId}
            expanded={expandedAgent === agentId}
            onChange={handleAgentAccordionChange(agentId)}
            sx={{ mb: 1 }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                bgcolor: (getAgentColor(agentId) === 'default') ? 'grey.200' : `${getAgentColor(agentId)}.light`,
                '& .MuiAccordionSummary-content': {
                  alignItems: 'center'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ mr: 2, bgcolor: `${getAgentColor(agentId)}.main` }}>
                  {getAgentIcon(agentId)}
                </Avatar>
                <Typography variant="subtitle1">
                  {getAgentName({ agent: agentId })} ({groupedDecisions[agentId].length})
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ ml: 1 }}>
                {groupedDecisions[agentId].map((decision, index) => (
                  <Paper
                    key={decision.id || index}
                    elevation={3}
                    sx={{
                      p: 2,
                      mb: 2,
                      borderLeft: `4px solid`,
                      borderLeftColor: `${getAgentColor(agentId)}.main`
                    }}
                  >

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle1">
                        {decision.id || `Decision ${index + 1}`}
                      </Typography>
                      <Chip
                        size="small"
                        label={getDecisionTime(decision)}
                        color="default"
                        variant="outlined"
                      />
                    </Box>

                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle2" gutterBottom color="text.secondary">
                      Context:
                    </Typography>
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {getContext(decision)}
                    </ReactMarkdown>

                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle2" gutterBottom color="text.secondary">
                      Reasoning:
                    </Typography>

                    {hasReasoningSteps(decision) ? (
                      <Box sx={{ ml: 2 }}>
                        {decision.reasoningSteps?.map((step, stepIndex) => (
                          <Box key={step.id || `step-${stepIndex}`} sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Step {stepIndex + 1}: {step.name || 'Reasoning'}
                            </Typography>
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>
                              {step.content || ''}
                            </ReactMarkdown>
                            {step.evidence && (
                              <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
                                Evidence: {step.evidence}
                              </Typography>
                            )}
                          </Box>
                        ))}
                      </Box>
                    ) : (
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {getReasoning(decision)}
                      </ReactMarkdown>
                    )}

                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle2" gutterBottom color="text.secondary">
                      Outcome:
                    </Typography>
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {getOutcome(decision)}
                    </ReactMarkdown>

                    {getConfidence(decision) !== undefined && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="caption" display="block" gutterBottom>
                          Confidence: {Math.round(getConfidence(decision)! * 100)}%
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={getConfidence(decision)! * 100}
                          color={getConfidence(decision)! > 0.8 ? 'success' : getConfidence(decision)! > 0.5 ? 'warning' : 'error'}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>
                    )}
                  </Paper>
                ))}
              </Box>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    );
  };

  // Handle expanding/collapsing agent accordions
  const handleAgentAccordionChange = (agentId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAgent(isExpanded ? agentId : false);
  };

  // Handle expanding/collapsing decision details
  const handleDecisionExpand = (decisionId: string) => {
    setExpandedDecision(expandedDecision === decisionId ? null : decisionId);
  };

  // Main component render
  return (
    <Box className="reasoning-panel" sx={{ p: 2 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Agent Reasoning & Chain-of-Thought
      </Typography>

      {processedDecisions.length === 0 ? (
        <Paper elevation={0} sx={{ p: 3, textAlign: 'center', bgcolor: 'grey.100' }}>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            No reasoning data available yet.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            As the AI agents make decisions during the collaboration process, their reasoning will appear here.
          </Typography>
        </Paper>
      ) : (
        <Box>
          <Tabs
            value={expandedAgent ? 'grouped' : 'timeline'}
            onChange={(_: React.SyntheticEvent, value: string) => value === 'timeline' ? setExpandedAgent(false) : setExpandedAgent('market-research')}
            sx={{ mb: 2, borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Timeline View" value="timeline" />
            <Tab label="Agent View" value="grouped" />
          </Tabs>

          {expandedAgent ? renderGroupedView() : renderTimelineView()}
        </Box>
      )}
    </Box>

        .agent-name {
          font-size: 1rem;
          font-weight: 600;
          color: #111827;
        }

        .confidence-indicator {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .confidence-bar {
          width: 80px;
          height: 6px;
          background-color: #e5e7eb;
          border-radius: 3px;
          overflow: hidden;
        }

        .confidence-fill {
          height: 100%;
          border-radius: 3px;
          transition: width 0.3s ease;
        }

        .confidence-text {
          font-size: 0.75rem;
          color: #6b7280;
        }

        .decision-time {
          font-size: 0.75rem;
          color: #6b7280;
        }

        .decision-body {
          padding: 1rem;
        }

        .decision-section {
          margin-bottom: 1.25rem;
        }

        .decision-section:last-child {
          margin-bottom: 0;
        }

        .decision-section h3 {
          font-size: 0.875rem;
          font-weight: 600;
          color: #4b5563;
          margin-bottom: 0.5rem;
        }

        .decision-section p {
          font-size: 0.875rem;
          color: #1f2937;
          line-height: 1.5;
          white-space: pre-wrap;
          word-break: break-word;
        }

        .no-decisions {
          background-color: #f9fafb;
          border-radius: 8px;
          padding: 3rem;
          text-align: center;
        }

        .no-decisions p:first-child {
          font-size: 1rem;
          font-weight: 500;
          color: #4b5563;
          margin-bottom: 0.5rem;
        }

        .no-decisions p:last-child {
          color: #6b7280;
          font-size: 0.875rem;
        }
      `}</style>
    </div>
  );
};

export default ReasoningPanel;
