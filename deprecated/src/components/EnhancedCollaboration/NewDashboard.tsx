// src/components/EnhancedCollaboration/NewDashboard.tsx
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import ArtifactPanel from './ArtifactPanel';
import MessagePanel from './MessagePanel';
import ReasoningPanel from './ReasoningPanel';
import OverviewPanel from './OverviewPanel';
import ContentViewer from './ContentViewer';

interface Artifact {
  id: string;
  type: string;
  name: string;
  content?: string;
  text?: string;
  data?: any;
  createdAt: string;
  [key: string]: any;
}

interface Message {
  id: string;
  sender: string;
  content?: string;
  text?: string;
  timestamp: string;
  [key: string]: any;
}

interface Decision {
  id: string;
  agent: string;
  context?: string;
  reasoning?: string;
  outcome?: string;
  timestamp: string;
  [key: string]: any;
}

interface Goal {
  id: string;
  title: string;
  description?: string;
  status: string;
  progress?: number;
  [key: string]: any;
}

interface FinalOutput {
  title?: string;
  content?: string;
  seoScore?: number;
  marketResearch?: any;
  keywords?: any;
  metadata?: any;
  [key: string]: any;
}

interface CollaborationState {
  id?: string;
  topic?: string;
  contentType?: string;
  targetAudience?: string;
  tone?: string;
  keywords?: string[] | string;
  progress?: number;
  currentGoal?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
  duration?: string;
  goals?: Goal[];
  artifacts?: Artifact[];
  messages?: Message[];
  decisions?: Decision[];
  finalOutput?: FinalOutput;
  [key: string]: any;
}

const NewDashboard: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get('id');

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [collaborationState, setCollaborationState] = useState<CollaborationState | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Form state for new collaboration
  const [topic, setTopic] = useState<string>('');
  const [contentType, setContentType] = useState<string>('blog-article');
  const [targetAudience, setTargetAudience] = useState<string>('');
  const [tone, setTone] = useState<string>('professional');
  const [keywords, setKeywords] = useState<string>('');
  const [formError, setFormError] = useState<string | null>(null);
  const [formLoading, setFormLoading] = useState<boolean>(false);

  // State for feedback and content improvement
  const [feedbackInput, setFeedbackInput] = useState<string>('');
  const [showFeedbackForm, setShowFeedbackForm] = useState<boolean>(false);
  const [improvementOptions, setImprovementOptions] = useState<string[]>([]);

  // Debug the query params
  useEffect(() => {
    console.log('Dashboard query params:', {
      id,
      searchParams: Object.fromEntries(searchParams.entries()),
      pathname: window.location.pathname
    });
  }, [searchParams, id]);

  // Fetch collaboration state
  const fetchCollaborationState = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      console.log(`Fetching collaboration state for ID: ${id}`);

      // Updated to use sessionId parameter instead of id
      const response = await fetch(`/api/orchestrated-collaboration?sessionId=${id}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch collaboration state: ${response.status} ${response.statusText}`);
      }

      // Get the raw response text for debugging
      const responseText = await response.text();
      console.log(`Raw API response (first 500 chars): ${responseText.substring(0, 500)}...`);

      // Parse the response text
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (err) {
        console.error('Error parsing JSON response:', err);
        throw new Error('Failed to parse API response');
      }

      // Check if data is in the expected structure
      if (data.success && data.state) {
        // API returns data in a nested 'state' object
        data = data.state;
        console.log('Using nested state object from API response');
      }

      // If finalOutput is at the top level, add it to the data object
      if (data.success && data.finalOutput && !data.finalOutput) {
        data.finalOutput = data.finalOutput;
        console.log('Moving finalOutput from top level to data object');
      }

      console.log('Processed API response:', {
        hasData: !!data,
        dataKeys: Object.keys(data),
        topic: data.topic || 'Not found',
        status: data.status || 'Not found',
        hasArtifacts: !!(data.artifacts),
        artifactsLength: data.artifacts?.length || 0,
        hasMessages: !!(data.messages),
        messagesLength: data.messages?.length || 0,
        hasDecisions: !!(data.decisions),
        decisionsLength: data.decisions?.length || 0,
        hasFinalOutput: !!(data.finalOutput),
        finalOutputKeys: data.finalOutput ? Object.keys(data.finalOutput) : []
      });

      // Ensure arrays are initialized even if missing in the API response
      const normalizedData: CollaborationState = {
        ...data,
        // Ensure required fields have default values
        id: data.id || id,
        topic: data.topic || 'Untitled Topic',
        contentType: data.contentType || 'blog-article',
        targetAudience: data.targetAudience || 'General Audience',
        tone: data.tone || 'professional',
        keywords: data.keywords || [],
        progress: data.progress || 0,
        // Only set default status if it's missing, preserve original status
        status: data.status || 'pending',
        // Ensure arrays exist
        artifacts: data.artifacts || [],
        messages: data.messages || [],
        decisions: data.decisions || [],
        goals: data.goals || []
      };

      console.log('Normalized data:', {
        id: normalizedData.id,
        topic: normalizedData.topic,
        contentType: normalizedData.contentType,
        status: normalizedData.status,
        artifactsLength: normalizedData.artifacts.length,
        messagesLength: normalizedData.messages.length
      });

      setCollaborationState(normalizedData);
    } catch (err) {
      console.error('Error fetching collaboration state:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Start a new collaboration session
  const startCollaboration = async () => {
    try {
      setFormLoading(true);
      setFormError(null);

      // Validate inputs
      if (!topic.trim()) {
        throw new Error('Topic is required');
      }

      if (!targetAudience.trim()) {
        throw new Error('Target audience is required');
      }

      if (!keywords.trim()) {
        throw new Error('At least one keyword is required');
      }

      // Create request payload
      const keywordsArray = keywords.split(',').map(k => k.trim()).filter(k => k.length > 0);

      // Enhanced payload with more detailed instructions for content generation
      const payload = {
        topic: topic.trim(),
        contentType,
        targetAudience: targetAudience.trim(),
        tone,
        keywords: keywordsArray,
        // Add specific instructions for content generation
        instructions: `Create a comprehensive ${contentType.replace('-', ' ')} about "${topic.trim()}" for ${targetAudience.trim()} audience.
        Use a ${tone} tone and incorporate the following keywords: ${keywordsArray.join(', ')}.
        The content should be well-structured with proper headings, paragraphs, and include relevant information.`,
        // Specify expected output format
        outputFormat: {
          title: "string",
          content: "markdown",
          seoScore: "number",
          metadata: {
            wordCount: "number",
            readingTime: "string"
          }
        }
      };

      console.log('Starting new collaboration session with enhanced payload:', payload);

      // Call the API to start a new session
      const response = await fetch('/api/orchestrated-collaboration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);
        throw new Error(`Failed to start collaboration: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      console.log('API response for new collaboration:', data);

      if (data.success && data.sessionId) {
        console.log('Collaboration session started:', data.sessionId);

        // Redirect to the session page
        router.push(`/admin/enhanced-collaboration?id=${data.sessionId}`);
      } else {
        throw new Error(data.error || 'Failed to start collaboration session');
      }
    } catch (err) {
      console.error('Error starting collaboration:', err);
      setFormError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setFormLoading(false);
    }
  };

  // Function to manually trigger content generation if needed
  const triggerContentGeneration = async () => {
    if (!id || !collaborationState) return;

    try {
      setLoading(true);

      console.log('Manually triggering content generation for session:', id);

      // Call the API to generate content
      const response = await fetch('/api/orchestrated-collaboration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: id,
          action: 'generateContent',
          force: true
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate content: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      console.log('Content generation triggered:', data);

      // Refresh the dashboard to show the new content
      fetchCollaborationState();

    } catch (err) {
      console.error('Error triggering content generation:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Function to send feedback to the agents
  const sendFeedback = async () => {
    if (!id || !collaborationState || !feedbackInput.trim()) return;

    try {
      setLoading(true);

      console.log('Sending feedback for session:', id);

      // Call the API to send feedback
      const response = await fetch('/api/orchestrated-collaboration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: id,
          action: 'sendFeedback',
          feedback: feedbackInput.trim(),
          requestImprovements: improvementOptions
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to send feedback: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      console.log('Feedback sent:', data);

      // Reset feedback form
      setFeedbackInput('');
      setShowFeedbackForm(false);
      setImprovementOptions([]);

      // Refresh the dashboard to show updates
      fetchCollaborationState();

    } catch (err) {
      console.error('Error sending feedback:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Function to toggle improvement options
  const toggleImprovementOption = (option: string) => {
    if (improvementOptions.includes(option)) {
      setImprovementOptions(improvementOptions.filter(o => o !== option));
    } else {
      setImprovementOptions([...improvementOptions, option]);
    }
  };

  // Initial fetch
  useEffect(() => {
    if (id) {
      fetchCollaborationState();
    } else {
      // If no ID is provided, we're not loading an existing session
      setLoading(false);
    }
  }, [id]);

  // Handle refresh
  const handleRefresh = () => {
    fetchCollaborationState();
  };

  // Add auto-refresh for active sessions
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout | null = null;

    // Only set up auto-refresh for active sessions
    if (id && collaborationState?.status === 'active') {
      console.log('Setting up auto-refresh for active session');
      refreshInterval = setInterval(() => {
        console.log('Auto-refreshing active session');
        fetchCollaborationState();
      }, 10000); // Refresh every 10 seconds
    }

    // Clean up interval on unmount or when status changes
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [id, collaborationState?.status]);

  // If loading, show loading state
  if (loading && !collaborationState && id) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading collaboration dashboard...</p>
      </div>
    );
  }

  // If error, show error state
  if (error && id) {
    return (
      <div className="error-container">
        <h2>Error Loading Dashboard</h2>
        <p>{error}</p>
        <button
          className="retry-button"
          onClick={handleRefresh}
        >
          Try Again
        </button>
      </div>
    );
  }

  // If no ID, show the form to start a new collaboration
  if (!id) {
    return (
      <div className="start-collaboration-container">
        <div className="start-collaboration-panel">
          <h2>Start New Collaboration</h2>
          <div className="form-container">
            <div className="form-group">
              <label htmlFor="topic">Topic</label>
              <input
                id="topic"
                type="text"
                placeholder="Enter the main topic for your content"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="contentType">Content Type</label>
              <select
                id="contentType"
                value={contentType}
                onChange={(e) => setContentType(e.target.value)}
                className="form-select"
              >
                <option value="blog-article">Blog Article</option>
                <option value="product-page">Product Page</option>
                <option value="buying-guide">Buying Guide</option>
                <option value="case-study">Case Study</option>
                <option value="white-paper">White Paper</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="targetAudience">Target Audience</label>
              <input
                id="targetAudience"
                type="text"
                placeholder="Who is this content for?"
                value={targetAudience}
                onChange={(e) => setTargetAudience(e.target.value)}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="tone">Tone</label>
              <select
                id="tone"
                value={tone}
                onChange={(e) => setTone(e.target.value)}
                className="form-select"
              >
                <option value="professional">Professional</option>
                <option value="conversational">Conversational</option>
                <option value="technical">Technical</option>
                <option value="friendly">Friendly</option>
                <option value="authoritative">Authoritative</option>
                <option value="persuasive">Persuasive</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="keywords">Keywords</label>
              <input
                id="keywords"
                type="text"
                placeholder="Enter keywords separated by commas"
                value={keywords}
                onChange={(e) => setKeywords(e.target.value)}
                className="form-input"
              />
              <small className="form-help">Enter keywords separated by commas</small>
            </div>

            <button
              className="start-button"
              onClick={startCollaboration}
              disabled={formLoading}
            >
              {formLoading ? 'Starting...' : 'Start Collaboration'}
            </button>

            {formError && (
              <div className="form-error">
                <p>{formError}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>{collaborationState?.topic || 'Collaboration Dashboard'}</h1>
        <div className="dashboard-actions">
          <button
            className="refresh-button"
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh Dashboard'}
          </button>

          {collaborationState?.status === 'active' && (
            <button
              className="generate-content-button"
              onClick={triggerContentGeneration}
              disabled={loading}
            >
              {loading ? 'Generating...' : 'Generate Content Now'}
            </button>
          )}
        </div>
      </div>

      <div className="dashboard-status">
        <div className="status-item">
          <span className="status-label">Status:</span>
          <span className={`status-value status-${collaborationState?.status || 'unknown'}`}>
            {collaborationState?.status || 'Unknown'}
          </span>
        </div>

        <div className="status-item">
          <span className="status-label">Progress:</span>
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${collaborationState?.progress || 0}%` }}
            ></div>
          </div>
          <span className="progress-text">{collaborationState?.progress || 0}%</span>
        </div>
      </div>

      <div className="dashboard-tabs">
        <button
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab-button ${activeTab === 'messages' ? 'active' : ''}`}
          onClick={() => setActiveTab('messages')}
        >
          Messages
        </button>
        <button
          className={`tab-button ${activeTab === 'artifacts' ? 'active' : ''}`}
          onClick={() => setActiveTab('artifacts')}
        >
          Artifacts
        </button>
        <button
          className={`tab-button ${activeTab === 'reasoning' ? 'active' : ''}`}
          onClick={() => setActiveTab('reasoning')}
        >
          Reasoning
        </button>
        <button
          className={`tab-button ${activeTab === 'content' ? 'active' : ''}`}
          onClick={() => setActiveTab('content')}
        >
          Content
        </button>
      </div>

      <div className="dashboard-content">
        {activeTab === 'overview' && (
          <OverviewPanel
            collaborationState={collaborationState}
            refreshContent={handleRefresh}
            loading={loading}
          />
        )}

        {activeTab === 'messages' && (
          <MessagePanel
            messages={collaborationState?.messages}
            messageInput={''}
            setMessageInput={(input) => {}}
            sendMessage={(message) => {}}
            loading={loading}
          />
        )}

        {activeTab === 'artifacts' && (
          <ArtifactPanel
            artifacts={collaborationState?.artifacts}
            refreshContent={handleRefresh}
            loading={loading}
          />
        )}

        {activeTab === 'reasoning' && (
          <ReasoningPanel
            decisions={collaborationState?.decisions}
            artifacts={collaborationState?.artifacts}
            refreshContent={handleRefresh}
            loading={loading}
          />
        )}

        {activeTab === 'content' && (
          <div className="content-tab">
            <ContentViewer
              finalOutput={collaborationState?.finalOutput}
              topic={collaborationState?.topic}
              refreshContent={handleRefresh}
              loading={loading}
            />

            {collaborationState?.status === 'completed' && (
              <div className="content-actions">
                <button
                  className="feedback-button"
                  onClick={() => setShowFeedbackForm(!showFeedbackForm)}
                >
                  {showFeedbackForm ? 'Hide Feedback Form' : 'Provide Feedback on Content'}
                </button>

                {showFeedbackForm && (
                  <div className="feedback-form">
                    <h3>Provide Feedback to Improve Content</h3>

                    <div className="improvement-options">
                      <p>What aspects need improvement?</p>
                      <div className="options-grid">
                        {['Content Quality', 'SEO Optimization', 'Structure', 'Tone', 'Accuracy', 'Depth'].map(option => (
                          <label key={option} className="option-label">
                            <input
                              type="checkbox"
                              checked={improvementOptions.includes(option)}
                              onChange={() => toggleImprovementOption(option)}
                            />
                            {option}
                          </label>
                        ))}
                      </div>
                    </div>

                    <textarea
                      placeholder="Provide specific feedback to improve the content..."
                      value={feedbackInput}
                      onChange={(e) => setFeedbackInput(e.target.value)}
                      className="feedback-textarea"
                      rows={4}
                    />

                    <div className="feedback-actions">
                      <button
                        className="cancel-button"
                        onClick={() => {
                          setShowFeedbackForm(false);
                          setFeedbackInput('');
                          setImprovementOptions([]);
                        }}
                      >
                        Cancel
                      </button>
                      <button
                        className="send-feedback-button"
                        onClick={sendFeedback}
                        disabled={!feedbackInput.trim() || loading}
                      >
                        {loading ? 'Sending...' : 'Send Feedback & Improve Content'}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {activeTab === 'messages' && (
          <MessagePanel
            messages={collaborationState?.messages}
            messageInput={''}
            setMessageInput={(input) => {}}
            sendMessage={(message) => {}}
            loading={loading}
          />
        )}

        {activeTab === 'artifacts' && (
          <ArtifactPanel
            artifacts={collaborationState?.artifacts}
            refreshContent={handleRefresh}
            loading={loading}
          />
        )}

        {activeTab === 'reasoning' && (
          <ReasoningPanel
            decisions={collaborationState?.decisions}
            artifacts={collaborationState?.artifacts}
            refreshContent={handleRefresh}
            loading={loading}
          />
        )}
      </div>

      <style jsx>{`
        .dashboard-container {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
          padding: 1.5rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .dashboard-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 1rem;
          border-bottom: 1px solid #e5e7eb;
        }

        .dashboard-header h1 {
          font-size: 1.875rem;
          color: #111827;
          margin: 0;
        }

        .dashboard-actions {
          display: flex;
          gap: 0.75rem;
        }

        .refresh-button {
          padding: 0.5rem 1rem;
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          font-size: 0.875rem;
          color: #374151;
          cursor: pointer;
          transition: all 0.2s;
        }

        .refresh-button:hover:not(:disabled) {
          background-color: #f3f4f6;
          border-color: #d1d5db;
        }

        .refresh-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .dashboard-tabs {
          display: flex;
          gap: 0.25rem;
          border-bottom: 1px solid #e5e7eb;
        }

        .tab-button {
          padding: 0.75rem 1.25rem;
          background-color: transparent;
          border: none;
          border-bottom: 2px solid transparent;
          font-size: 0.875rem;
          font-weight: 500;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        }

        .tab-button:hover {
          color: #111827;
        }

        .tab-button.active {
          color: #0070f3;
          border-bottom-color: #0070f3;
        }

        .dashboard-content {
          padding: 1.5rem 0;
        }

        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 4rem;
          text-align: center;
        }

        .loading-spinner {
          border: 4px solid #f3f3f3;
          border-top: 4px solid #0070f3;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          animation: spin 1s linear infinite;
          margin-bottom: 1.5rem;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .error-container {
          padding: 2rem;
          text-align: center;
          background-color: #fef2f2;
          border: 1px solid #fee2e2;
          border-radius: 8px;
          margin: 2rem auto;
          max-width: 600px;
        }

        .error-container h2 {
          color: #b91c1c;
          margin-bottom: 1rem;
        }

        .error-container p {
          color: #7f1d1d;
          margin-bottom: 1.5rem;
        }

        .retry-button {
          padding: 0.5rem 1rem;
          background-color: #ef4444;
          color: white;
          border: none;
          border-radius: 4px;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .retry-button:hover {
          background-color: #dc2626;
        }

        .start-collaboration-container {
          max-width: 800px;
          margin: 2rem auto;
          padding: 0 1rem;
        }

        .start-collaboration-panel {
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
          padding: 2rem;
        }

        .start-collaboration-panel h2 {
          font-size: 1.5rem;
          color: #111827;
          margin-bottom: 1.5rem;
          text-align: center;
        }

        .form-container {
          display: flex;
          flex-direction: column;
          gap: 1.25rem;
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .form-group label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
        }

        .form-input, .form-select {
          padding: 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 0.875rem;
          color: #111827;
          background-color: #ffffff;
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus, .form-select:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .form-help {
          font-size: 0.75rem;
          color: #6b7280;
        }

        .start-button {
          margin-top: 1rem;
          padding: 0.75rem 1.5rem;
          background-color: #0070f3;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
          align-self: center;
        }

        .start-button:hover:not(:disabled) {
          background-color: #0051cc;
        }

        .start-button:disabled {
          background-color: #9ca3af;
          cursor: not-allowed;
        }

        .form-error {
          margin-top: 1rem;
          padding: 0.75rem;
          background-color: #fef2f2;
          border: 1px solid #fee2e2;
          border-radius: 6px;
          color: #b91c1c;
          font-size: 0.875rem;
        }

        .no-id-container {
          padding: 2rem;
          text-align: center;
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          margin: 2rem auto;
          max-width: 600px;
        }

        .no-id-container h2 {
          color: #374151;
          margin-bottom: 1rem;
        }

        .no-id-container p {
          color: #6b7280;
        }

        .dashboard-status {
          display: flex;
          gap: 1.5rem;
          padding: 1rem;
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
        }

        .status-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .status-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #6b7280;
        }

        .status-value {
          font-size: 0.875rem;
          font-weight: 500;
          color: #111827;
        }

        .status-active {
          color: #34c759;
        }

        .status-pending {
          color: #f59e0b;
        }

        .status-failed {
          color: #ef4444;
        }

        .progress-bar {
          width: 100px;
          height: 8px;
          background-color: #e5e7eb;
          border-radius: 4px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background-color: #34c759;
          border-radius: 4px 0 0 4px;
        }

        .progress-text {
          font-size: 0.875rem;
          font-weight: 500;
          color: #6b7280;
        }

        .content-tab {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .content-actions {
          margin-top: 1rem;
          padding-top: 1rem;
          border-top: 1px solid #e5e7eb;
        }

        .feedback-button {
          background-color: #f3f4f6;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
          font-weight: 500;
          color: #4b5563;
          cursor: pointer;
          transition: all 0.2s;
        }

        .feedback-button:hover {
          background-color: #e5e7eb;
        }

        .feedback-form {
          margin-top: 1rem;
          padding: 1rem;
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
        }

        .feedback-form h3 {
          margin-top: 0;
          margin-bottom: 1rem;
          font-size: 1rem;
          color: #111827;
        }

        .improvement-options {
          margin-bottom: 1rem;
        }

        .improvement-options p {
          margin-bottom: 0.5rem;
          font-size: 0.875rem;
          color: #4b5563;
        }

        .options-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 0.5rem;
        }

        .option-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
          color: #4b5563;
          cursor: pointer;
        }

        .feedback-textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          resize: vertical;
        }

        .feedback-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
          margin-top: 1rem;
        }

        .cancel-button {
          background-color: #f3f4f6;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
          font-weight: 500;
          color: #4b5563;
          cursor: pointer;
        }

        .send-feedback-button {
          background-color: #4f46e5;
          border: 1px solid #4338ca;
          border-radius: 0.375rem;
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
          font-weight: 500;
          color: white;
          cursor: pointer;
        }

        .send-feedback-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
};

export default NewDashboard;
