'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  IconButton,
  Tooltip,
  Avatar
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import PsychologyIcon from '@mui/icons-material/Psychology';
import TimelineIcon from '@mui/icons-material/Timeline';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import RefreshIcon from '@mui/icons-material/Refresh';
import { IterativeMessage } from '../../app/(payload)/api/agents/collaborative-iteration/types';

interface EnhancedReasoningVisualizerProps {
  messages: IterativeMessage[];
  decisions?: any[];
  onRefresh?: () => void;
  loading?: boolean;
  workflowProgress?: any;
  currentPhase?: string;
}

interface AgentReasoning {
  agentId: string;
  agentName: string;
  reasoning: any[];
  color: string;
}

/**
 * Component to visualize agent reasoning processes in a clear, understandable format
 */
const EnhancedReasoningVisualizer: React.FC<EnhancedReasoningVisualizerProps> = ({
  messages,
  decisions = [],
  onRefresh,
  loading = false,
  workflowProgress,
  currentPhase
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [agentReasoning, setAgentReasoning] = useState<AgentReasoning[]>([]);
  const [expandedAgent, setExpandedAgent] = useState<string | false>(false);

  // Agent colors
  const agentColors: Record<string, string> = {
    'market-research': '#2196f3',
    'seo-keyword': '#ff9800',
    'content-strategy': '#3f51b5',
    'content-generation': '#009688',
    'seo-optimization': '#f44336',
    'system': '#9e9e9e',
    'user': '#4caf50'
  };

  // Agent display names
  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Generation',
    'seo-optimization': 'SEO Optimization',
    'system': 'System',
    'user': 'User'
  };

  // Extract reasoning from messages
  useEffect(() => {
    if (!messages || messages.length === 0) return;

    // Group reasoning by agent
    const reasoningByAgent: Record<string, any[]> = {};

    messages.forEach(message => {
      if (message.reasoning) {
        if (!reasoningByAgent[message.from]) {
          reasoningByAgent[message.from] = [];
        }

        reasoningByAgent[message.from].push({
          messageId: message.id,
          timestamp: message.timestamp,
          reasoning: message.reasoning,
          to: message.to,
          type: message.type,
          content: message.content
        });
      }
    });

    // Convert to array format
    const agentReasoningArray: AgentReasoning[] = Object.entries(reasoningByAgent)
      .map(([agentId, reasoning]) => ({
        agentId,
        agentName: agentNames[agentId] || agentId,
        reasoning,
        color: agentColors[agentId] || '#999'
      }))
      .sort((a, b) => b.reasoning.length - a.reasoning.length); // Sort by most reasoning first

    setAgentReasoning(agentReasoningArray);

    // Auto-expand the agent with the most reasoning
    if (agentReasoningArray.length > 0 && !expandedAgent) {
      setExpandedAgent(agentReasoningArray[0].agentId);
    }
  }, [messages]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle accordion change
  const handleAccordionChange = (agentId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAgent(isExpanded ? agentId : false);
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleTimeString();
    } catch (e) {
      return timestamp;
    }
  };

  // Render reasoning steps
  const renderReasoningSteps = (reasoning: any) => {
    if (!reasoning) return null;

    // Handle different reasoning formats
    const steps = reasoning.steps || reasoning.process || reasoning.thoughts || [];

    if (Array.isArray(steps) && steps.length > 0) {
      return (
        <List dense disablePadding>
          {steps.map((step, index) => (
            <ListItem key={index} sx={{ py: 0.5 }}>
              <ListItemIcon sx={{ minWidth: 32 }}>
                <ArrowRightIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary={typeof step === 'string' ? step : JSON.stringify(step)}
                primaryTypographyProps={{ variant: 'body2' }}
              />
            </ListItem>
          ))}
        </List>
      );
    }

    // If steps is a string, display it directly
    if (typeof steps === 'string') {
      return (
        <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
          {steps}
        </Typography>
      );
    }

    // Fallback for other formats
    return (
      <Typography variant="body2" color="text.secondary">
        No detailed reasoning steps available
      </Typography>
    );
  };

  // Render considerations
  const renderConsiderations = (reasoning: any) => {
    if (!reasoning) return null;

    const considerations = reasoning.considerations || reasoning.alternatives || [];

    if (Array.isArray(considerations) && considerations.length > 0) {
      return (
        <List dense disablePadding>
          {considerations.map((consideration, index) => (
            <ListItem key={index} sx={{ py: 0.5 }}>
              <ListItemIcon sx={{ minWidth: 32 }}>
                <CompareArrowsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary={typeof consideration === 'string' ? consideration : JSON.stringify(consideration)}
                primaryTypographyProps={{ variant: 'body2' }}
              />
            </ListItem>
          ))}
        </List>
      );
    }

    return (
      <Typography variant="body2" color="text.secondary">
        No considerations available
      </Typography>
    );
  };

  // Render agent reasoning cards
  const renderAgentReasoningCards = () => {
    return agentReasoning.map(agent => (
      <Accordion
        key={agent.agentId}
        expanded={expandedAgent === agent.agentId}
        onChange={handleAccordionChange(agent.agentId)}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Avatar sx={{ bgcolor: agent.color, mr: 2 }}>
              <PsychologyIcon />
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="subtitle1">
                {agent.agentName}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {agent.reasoning.length} reasoning processes
              </Typography>
            </Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <List disablePadding>
            {agent.reasoning.map((item, index) => (
              <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="subtitle2">
                      {item.type}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatTimestamp(item.timestamp)}
                    </Typography>
                  </Box>

                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" fontWeight="medium" gutterBottom>
                        <TimelineIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                        Reasoning Process
                      </Typography>
                      {renderReasoningSteps(item.reasoning)}
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" fontWeight="medium" gutterBottom>
                        <CompareArrowsIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                        Considerations
                      </Typography>
                      {renderConsiderations(item.reasoning)}
                    </Grid>

                    <Grid item xs={12}>
                      <Typography variant="body2" fontWeight="medium" gutterBottom>
                        <CheckCircleIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                        Conclusion
                      </Typography>
                      <Typography variant="body2" sx={{
                        p: 1.5,
                        bgcolor: 'background.default',
                        borderRadius: 1,
                        whiteSpace: 'pre-wrap'
                      }}>
                        {typeof item.content === 'string'
                          ? item.content
                          : JSON.stringify(item.content, null, 2)}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>
    ));
  };

  // Render decision timeline
  const renderDecisionTimeline = () => {
    if (!decisions || decisions.length === 0) {
      return (
        <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
          No decisions recorded yet
        </Typography>
      );
    }

    // Sort decisions by timestamp
    const sortedDecisions = [...decisions].sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    return (
      <Box sx={{ position: 'relative', ml: 4, mt: 2 }}>
        {/* Timeline line */}
        <Box sx={{
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: 2,
          bgcolor: 'divider',
          zIndex: 0
        }} />

        {/* Decision points */}
        {sortedDecisions.map((decision, index) => (
          <Box key={index} sx={{ position: 'relative', mb: 4, pl: 3 }}>
            {/* Timeline dot */}
            <Box sx={{
              position: 'absolute',
              left: -4,
              top: 0,
              width: 10,
              height: 10,
              borderRadius: '50%',
              bgcolor: agentColors[decision.agent] || '#999',
              zIndex: 1
            }} />

            <Box sx={{ mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                {formatTimestamp(decision.timestamp)}
              </Typography>
              <Typography variant="subtitle2">
                {agentNames[decision.agent] || decision.agent}
              </Typography>
            </Box>

            <Paper variant="outlined" sx={{ p: 2 }}>
              {decision.context && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Context: {decision.context}
                </Typography>
              )}

              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                {decision.reasoning || decision.outcome || 'No reasoning provided'}
              </Typography>

              {decision.confidence !== undefined && (
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                    Confidence:
                  </Typography>
                  <Chip
                    size="small"
                    label={`${Math.round(decision.confidence * 100)}%`}
                    color={
                      decision.confidence > 0.8 ? 'success' :
                      decision.confidence > 0.5 ? 'primary' : 'warning'
                    }
                  />
                </Box>
              )}
            </Paper>
          </Box>
        ))}
      </Box>
    );
  };

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          Agent Reasoning
        </Typography>

        {onRefresh && (
          <IconButton onClick={onRefresh} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        )}
      </Box>

      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab
          label="Agent Reasoning"
          icon={<PsychologyIcon />}
          iconPosition="start"
        />
        <Tab
          label="Decision Timeline"
          icon={<TimelineIcon />}
          iconPosition="start"
        />
        <Tab
          label="Workflow Progress"
          icon={<LightbulbIcon />}
          iconPosition="start"
        />
      </Tabs>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Agent Reasoning Tab */}
          <Box hidden={activeTab !== 0}>
            {agentReasoning.length === 0 ? (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                No reasoning data available yet
              </Typography>
            ) : (
              renderAgentReasoningCards()
            )}
          </Box>

          {/* Decision Timeline Tab */}
          <Box hidden={activeTab !== 1}>
            {renderDecisionTimeline()}
          </Box>

          {/* Workflow Progress Tab */}
          <Box hidden={activeTab !== 2}>
            {renderWorkflowProgress()}
          </Box>
        </>
      )}
    </Paper>
  );

  // Render workflow progress
  function renderWorkflowProgress() {
    if (!workflowProgress) {
      return (
        <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
          No workflow progress data available
        </Typography>
      );
    }

    const phases = [
      { id: 'initialization', label: 'Initialization', color: '#3f51b5' },
      { id: 'research', label: 'Research Phase', color: '#2196f3' },
      { id: 'creation', label: 'Content Generation', color: '#009688' }, // Changed from "Content Creation" to "Content Generation" for consistency
      { id: 'review', label: 'Review & Optimization', color: '#ff9800' },
      { id: 'finalization', label: 'Finalization', color: '#4caf50' },
      { id: 'complete', label: 'Complete', color: '#4caf50' }
    ];

    const currentPhaseIndex = phases.findIndex(phase => phase.id === currentPhase);

    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Current Workflow Status
        </Typography>

        <Paper variant="outlined" sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Current Phase
              </Typography>
              <Chip
                label={phases.find(p => p.id === currentPhase)?.label || currentPhase || 'Unknown'}
                color="primary"
                sx={{ mb: 2 }}
              />
              {/* Display the internal phase ID for debugging */}
              <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 2 }}>
                Internal phase ID: {currentPhase || 'unknown'}
              </Typography>

              <Typography variant="subtitle2" gutterBottom>
                Research Progress
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">Market Research</Typography>
                      <Chip
                        size="small"
                        label={workflowProgress.marketResearchComplete ? "Complete" : "Pending"}
                        color={workflowProgress.marketResearchComplete ? "success" : "default"}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">Keyword Research</Typography>
                      <Chip
                        size="small"
                        label={workflowProgress.keywordResearchComplete ? "Complete" : "Pending"}
                        color={workflowProgress.keywordResearchComplete ? "success" : "default"}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">Content Strategy</Typography>
                      <Chip
                        size="small"
                        label={workflowProgress.contentStrategyComplete ? "Complete" : "Pending"}
                        color={workflowProgress.contentStrategyComplete ? "success" : "default"}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Content Progress
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">Content Generation</Typography>
                      <Chip
                        size="small"
                        label={workflowProgress.contentGenerationComplete ? "Complete" : "Pending"}
                        color={workflowProgress.contentGenerationComplete ? "success" : "default"}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">SEO Optimization</Typography>
                      <Chip
                        size="small"
                        label={workflowProgress.seoOptimizationComplete ? "Complete" : "Pending"}
                        color={workflowProgress.seoOptimizationComplete ? "success" : "default"}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </Box>

              <Typography variant="subtitle2" gutterBottom>
                Phase Timeline
              </Typography>
              <Box sx={{ position: 'relative', ml: 4, mt: 2 }}>
                {/* Timeline line */}
                <Box sx={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  bottom: 0,
                  width: 2,
                  bgcolor: 'divider',
                  zIndex: 0
                }} />

                {/* Phase points */}
                {phases.map((phase, index) => (
                  <Box key={phase.id} sx={{ position: 'relative', mb: 3, pl: 3 }}>
                    {/* Timeline dot */}
                    <Box sx={{
                      position: 'absolute',
                      left: -4,
                      top: 0,
                      width: 10,
                      height: 10,
                      borderRadius: '50%',
                      bgcolor: index <= currentPhaseIndex ? phase.color : 'grey.400',
                      zIndex: 1
                    }} />

                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: currentPhase === phase.id ? 'bold' : 'normal',
                        color: index <= currentPhaseIndex ? 'text.primary' : 'text.secondary'
                      }}
                    >
                      {phase.label}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>
    );
  }
};

export default EnhancedReasoningVisualizer;
