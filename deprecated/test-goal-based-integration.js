/**
 * Goal-Based Integration Test Script
 * 
 * This script helps test the integration between the frontend and the goal-based backend.
 * Run this script in the browser console while on the goal-based dashboard page.
 */

// Test the goal orchestrator client
async function testGoalOrchestratorClient() {
  console.log('Testing Goal Orchestrator Client...');
  
  try {
    // Get the client from the window object
    const client = window.goalOrchestratorClient;
    
    if (!client) {
      console.error('Goal Orchestrator Client not found on window object');
      return false;
    }
    
    console.log('Goal Orchestrator Client found:', client);
    return true;
  } catch (error) {
    console.error('Error testing Goal Orchestrator Client:', error);
    return false;
  }
}

// Test session creation
async function testSessionCreation() {
  console.log('Testing Session Creation...');
  
  try {
    // Get the client from the window object
    const client = window.goalOrchestratorClient;
    
    if (!client) {
      console.error('Goal Orchestrator Client not found on window object');
      return false;
    }
    
    // Create a test session
    const params = {
      topic: 'Test Topic ' + new Date().toISOString(),
      contentType: 'blog-article',
      targetAudience: 'Test Audience',
      tone: 'Informative',
      keywords: ['test', 'integration', 'goal-based']
    };
    
    console.log('Creating test session with params:', params);
    const result = await client.initiate(params);
    
    if (!result || !result.sessionId) {
      console.error('Failed to create session');
      return false;
    }
    
    console.log('Session created successfully:', result);
    return result.sessionId;
  } catch (error) {
    console.error('Error creating session:', error);
    return false;
  }
}

// Test session state retrieval
async function testSessionState(sessionId) {
  console.log('Testing Session State Retrieval...');
  
  try {
    // Get the client from the window object
    const client = window.goalOrchestratorClient;
    
    if (!client) {
      console.error('Goal Orchestrator Client not found on window object');
      return false;
    }
    
    // Get session state
    console.log('Getting state for session:', sessionId);
    const state = await client.getState(sessionId);
    
    if (!state) {
      console.error('Failed to get session state');
      return false;
    }
    
    console.log('Session state retrieved successfully:', state);
    return state;
  } catch (error) {
    console.error('Error getting session state:', error);
    return false;
  }
}

// Test session progression
async function testSessionProgression(sessionId) {
  console.log('Testing Session Progression...');
  
  try {
    // Get the client from the window object
    const client = window.goalOrchestratorClient;
    
    if (!client) {
      console.error('Goal Orchestrator Client not found on window object');
      return false;
    }
    
    // Progress the session
    console.log('Progressing session:', sessionId);
    const result = await client.progressSession(sessionId);
    
    if (!result) {
      console.error('Failed to progress session');
      return false;
    }
    
    console.log('Session progressed successfully:', result);
    return true;
  } catch (error) {
    console.error('Error progressing session:', error);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('Running Goal-Based Integration Tests...');
  
  // Test client
  const clientResult = await testGoalOrchestratorClient();
  if (!clientResult) {
    console.error('Client test failed');
    return;
  }
  
  // Test session creation
  const sessionId = await testSessionCreation();
  if (!sessionId) {
    console.error('Session creation test failed');
    return;
  }
  
  // Test session state
  const state = await testSessionState(sessionId);
  if (!state) {
    console.error('Session state test failed');
    return;
  }
  
  // Test session progression
  const progressResult = await testSessionProgression(sessionId);
  if (!progressResult) {
    console.error('Session progression test failed');
    return;
  }
  
  console.log('All tests completed successfully!');
  console.log('Test session ID:', sessionId);
}

// Export functions for use in the browser console
window.testGoalBasedIntegration = {
  testGoalOrchestratorClient,
  testSessionCreation,
  testSessionState,
  testSessionProgression,
  runTests
};

console.log('Goal-Based Integration Test Script loaded. Run window.testGoalBasedIntegration.runTests() to start tests.');
