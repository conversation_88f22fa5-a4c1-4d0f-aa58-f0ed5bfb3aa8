/**
 * Test Runner for Approval Flow
 * Simple Node.js script to test the workflow system
 */

const path = require('path');

// Set up environment
process.env.NODE_ENV = 'test';

async function runTests() {
  console.log('🧪 Starting Workflow Tests...');
  
  try {
    // Import the test functions
    const { testApprovalFlow, testInfiniteLoopFix } = require('./src/core/workflow/test-approval-flow.ts');
    
    console.log('\n=== Test 1: Approval Flow ===');
    const approvalResult = await testApprovalFlow();
    console.log('Result:', approvalResult);
    
    console.log('\n=== Test 2: Infinite Loop Fix ===');
    const loopResult = await testInfiniteLoopFix();
    console.log('Result:', loopResult);
    
    console.log('\n=== Test Summary ===');
    console.log(`Approval Flow: ${approvalResult.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Infinite Loop Fix: ${loopResult.success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (approvalResult.success && loopResult.success) {
      console.log('\n🎉 All tests passed! Ready to move to review system implementation.');
      process.exit(0);
    } else {
      console.log('\n❌ Some tests failed. Please check the issues above.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

runTests();
