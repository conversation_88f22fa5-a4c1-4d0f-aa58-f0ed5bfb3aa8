# Deprecated Code Archive

This folder contains old/experimental code that has been moved out of the main codebase to keep it clean and focused on the unified workflow system.

## What's in here

### Old Workflow Implementations
- `/workflow/enhanced` - Old enhanced workflow system
- `/workflow/agent-enhanced` - Old agent-enhanced workflow system  
- `/workflow/results/[id]` - Old results pages
- `/workflow/test` - Test workflow pages

### Experimental Agent Systems
- `/goal-based-collaboration` - Goal-based orchestration system
- `/dynamic-collaboration-v3` - Dynamic collaboration v3
- `/agents` - Old agent implementations
- Various experimental collaboration systems

### Old API Routes
- Old agent collaboration APIs
- Experimental endpoints
- Test/debug APIs

### Unused Components
- Old workflow components not used by unified system
- Experimental UI components
- Test components

## Why moved to deprecated

1. **Code Cleanup**: Remove clutter from main codebase
2. **Build Performance**: Exclude from build process
3. **Maintainability**: Focus on unified workflow system
4. **Historical Reference**: Keep for reference but not active

## Build Exclusion

This folder is excluded from the Next.js build process via:
- `next.config.mjs` webpack configuration
- `ignore-loader` for webpack

## Current Active System

The active system is the **Unified Workflow System** located at:
- `/workflow/unified` - Main workflow interface
- Core workflow engine in `/src/core/workflow/`
- Unified workflow components in `/src/components/Workflow/`

## Migration Notes

If you need to reference or restore any code from this folder:
1. Check if the functionality exists in the unified system
2. If needed, adapt the code to work with the unified architecture
3. Ensure proper integration with the current state management
4. Test thoroughly before moving back to active codebase
