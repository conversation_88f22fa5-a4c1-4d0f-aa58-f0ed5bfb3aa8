/**
 * Unified Workflow Experience
 * 
 * Single-page workflow experience with integrated agent collaboration
 * and real-time backend integration
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import ExecutionDashboard from './ExecutionDashboard';
import HumanReviewInterface from './HumanReviewInterface';
import ResultsDashboard from './ResultsDashboard';
import ErrorBoundary from './ErrorBoundary';
import WorkflowNavigationSystem, { useWorkflowNavigation } from './WorkflowNavigationSystem';

// Types
interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  difficulty: string;
  estimatedTime: number;
  consultationEnabled: boolean;
  agentCount: number;
  steps: Array<{
    id: string;
    name: string;
    type: string;
    dependencies?: string[];
  }>;
}

interface WorkflowExecution {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed' | 'waiting_review';
  progress: number;
  currentStep?: string;
  steps: Array<{
    id: string;
    name: string;
    status: string;
    outputs?: any;
    artifactId?: string;
  }>;
}

interface WorkflowResults {
  execution: {
    id: string;
    workflowId: string;
    status: string;
    progress: number;
    startedAt: string;
    completedAt?: string;
    inputs: Record<string, any>;
    currentStep?: string;
    metadata?: Record<string, any>;
  };
  workflow: {
    id: string;
    name: string;
    description: string;
  } | null;
  steps: Array<{
    stepId: string;
    status: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    inputs: Record<string, any>;
    outputs: Record<string, any>;
    error?: string;
    stepType?: string;
    approvalRequired?: boolean;
    artifactId?: string;
  }>;
  content: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    createdAt: string;
    metadata?: Record<string, any>;
  }>;
  artifacts: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    executionId: string;
    createdAt: string;
    approvedBy?: string;
    approvedAt?: string;
    metadata?: Record<string, any>;
  }>;
}

interface AgentActivity {
  agentId: string;
  status: 'idle' | 'analyzing' | 'responding' | 'waiting' | 'completed';
  lastSeen: string;
}

type WorkflowStep = 'template' | 'configure' | 'executing' | 'collaboration' | 'review' | 'results';

interface Props {
  onComplete?: (executionId: string) => void;
  onBack?: () => void;
  initialStep?: WorkflowStep;
  initialExecutionId?: string;
  initialReviewId?: string;
}

export default function UnifiedWorkflowExperience({
  onComplete,
  onBack,
  initialStep = 'template',
  initialExecutionId,
  initialReviewId
}: Props) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Core state
  const [currentStep, setCurrentStep] = useState<WorkflowStep>(initialStep);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [workflowInputs, setWorkflowInputs] = useState<Record<string, any>>({});
  const [execution, setExecution] = useState<WorkflowExecution | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Results state
  const [results, setResults] = useState<WorkflowResults | null>(null);
  const [publishingStatus, setPublishingStatus] = useState<Record<string, 'idle' | 'publishing' | 'published' | 'failed'>>({});
  const [publishResults, setPublishResults] = useState<Record<string, { url?: string; error?: string }>>({});

  // Review state
  const [reviewData, setReviewData] = useState<any>(null);
  const [reviewVersions, setReviewVersions] = useState<any[]>([]);
  const [showVersionComparison, setShowVersionComparison] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');

  // Navigation state
  const navigationState = useWorkflowNavigation(currentStep, execution?.id || initialExecutionId);

  // Agent collaboration state
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([]);
  const [isCollaborationActive, setIsCollaborationActive] = useState(false);
  const [collaborationResult, setCollaborationResult] = useState<any>(null);

  // UI state
  const [notifications, setNotifications] = useState<string[]>([]);
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [isCompletingWorkflow, setIsCompletingWorkflow] = useState(false);

  // Initialize from URL parameters (only on mount)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const step = searchParams.get('step') as WorkflowStep;
    const executionId = searchParams.get('executionId') || initialExecutionId;
    const reviewId = searchParams.get('reviewId') || initialReviewId;

    console.log('🔍 URL Parameters on mount:', { step, executionId, reviewId, currentStep });

    // Only set step from URL on initial load, not on every URL change
    if (step && step !== 'null' && currentStep === initialStep) {
      console.log('🔄 Setting initial step from URL to:', step);
      setCurrentStep(step);
    } else if (!step || (step as string) === 'null') {
      // If no step is specified or step is null, default to template
      if (currentStep === initialStep) {
        console.log('🔄 Setting default step to template');
        setCurrentStep('template');
      }
    }

    if (executionId && (step === 'results' || initialStep === 'results')) {
      loadResults(executionId).catch(error => {
        console.error('Failed to load results on mount:', error);
      });
    }

    if (reviewId && (step === 'review' || initialStep === 'review')) {
      loadReviewData(reviewId).catch(error => {
        console.error('Failed to load review data on mount:', error);
      });
    }
  }, []); // Only run on mount - intentionally ignoring dependencies

  // Load templates on mount
  useEffect(() => {
    console.log('🔄 Loading templates on mount...');
    loadTemplates();
  }, [loadTemplates]);

  // Auto-refresh execution status
  useEffect(() => {
    if (execution?.id && (currentStep === 'executing' || currentStep === 'collaboration')) {
      const interval = setInterval(() => {
        refreshExecutionStatus();
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [execution?.id, currentStep, refreshExecutionStatus]);

  // Update URL when step changes
  useEffect(() => {
    const params = new URLSearchParams();
    params.set('step', currentStep);

    if (execution?.id) {
      params.set('executionId', execution.id);
    }

    if (reviewData?.id) {
      params.set('reviewId', reviewData.id);
    }

    const newUrl = `/workflow/unified?${params.toString()}`;
    console.log('🔗 Updating URL to:', newUrl);
    router.replace(newUrl, { scroll: false });
  }, [currentStep, execution?.id, reviewData?.id, router]);

  const addNotification = useCallback((message: string) => {
    setNotifications(prev => [...prev.slice(-2), message]);
    setTimeout(() => {
      setNotifications(prev => prev.slice(1));
    }, 5000);
  }, []);

  const loadResults = useCallback(async (executionId: string) => {
    if (!executionId) {
      setError('No execution ID provided');
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch(`/api/workflow/results/${executionId}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.data) {
        const execution = result.data.execution;
        const workflow = result.data.workflow;
        const artifacts = result.data.artifacts || [];
        const steps = result.data.steps || [];

        const transformedResults: WorkflowResults = {
          execution: {
            id: execution?.id || executionId,
            workflowId: execution?.workflowId || 'unknown',
            status: execution?.status || 'unknown',
            progress: execution?.progress || 0,
            startedAt: execution?.startedAt || new Date().toISOString(),
            completedAt: execution?.completedAt,
            inputs: execution?.inputs || {},
            currentStep: execution?.currentStep
          },
          workflow: workflow ? {
            id: workflow.id || 'unknown',
            name: workflow.name || 'Unknown Workflow',
            description: workflow.description || 'No description available'
          } : null,
          steps: steps.length > 0 ? steps : Object.values(execution?.stepResults || {}).map((step: any) => ({
            stepId: step?.stepId || 'unknown',
            status: step?.status || 'unknown',
            startedAt: step?.startedAt || new Date().toISOString(),
            completedAt: step?.completedAt,
            duration: step?.duration,
            inputs: step?.inputs || {},
            outputs: step?.outputs || {},
            error: step?.error,
            stepType: step?.stepType,
            approvalRequired: step?.approvalRequired,
            artifactId: step?.artifactId
          })),
          content: [],
          artifacts: artifacts.map((artifact: any) => ({
            ...artifact,
            id: artifact.id || `artifact-${Date.now()}`,
            title: artifact.title || 'Untitled Artifact',
            content: artifact.content || '',
            status: artifact.status || 'draft',
            stepId: artifact.stepId || 'unknown',
            executionId: artifact.executionId || executionId,
            createdAt: artifact.createdAt || new Date().toISOString()
          }))
        };

        setResults(transformedResults);
        setCurrentStep('results');
        addNotification('✅ Results loaded successfully');
      } else {
        setError(result.error || 'Failed to load results');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load results';
      setError(errorMessage);
      console.error('Load results error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [addNotification]);

  const loadReviewData = useCallback(async (reviewId: string) => {
    try {
      setIsLoading(true);
      setError(null); // Clear any previous errors

      console.log(`🔍 Loading review data for ID: ${reviewId}`);

      const response = await fetch(`/api/review/${reviewId}`);
      console.log(`📡 Review API response status: ${response.status}`);

      const result = await response.json();
      console.log(`📡 Review API response:`, result);

      if (result.success) {
        console.log(`✅ Review data loaded successfully:`, result.data);
        setReviewData(result.data);
        setCurrentStep('review');
        addNotification('✅ Review data loaded');

        // Load versions (optional, don't fail if this doesn't work)
        try {
          const versionsResponse = await fetch(`/api/review/${reviewId}/versions`);
          const versionsResult = await versionsResponse.json();
          if (versionsResult.success) {
            setReviewVersions(versionsResult.data.versions || []);
            console.log(`📋 Loaded ${versionsResult.data.versions?.length || 0} review versions`);
          }
        } catch (versionError) {
          console.warn('⚠️ Failed to load review versions (non-critical):', versionError);
          // Don't fail the whole review loading if versions fail
        }
      } else {
        const errorMessage = result.error || 'Failed to load review';
        console.error(`❌ Review API error:`, errorMessage);
        setError(errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load review';
      console.error('❌ Review loading error:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [addNotification]);

  const loadTemplates = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/workflow/templates');
      const result = await response.json();

      console.log('📋 Templates API response:', result);

      if (result.success) {
        // Handle both possible response formats
        const templatesData = result.data.templates || result.data;
        console.log('📋 Templates data:', templatesData);

        if (templatesData && Array.isArray(templatesData) && templatesData.length > 0) {
          console.log('📋 First template structure:', templatesData[0]);
        }

        setTemplates(templatesData || []);
        console.log('📋 Templates set to state:', templatesData);
        addNotification(`✅ Loaded ${(templatesData || []).length} templates`);
      } else {
        // Fallback to enhanced template selector data
        const fallbackTemplates: WorkflowTemplate[] = [
          {
            id: 'blog-post-seo',
            name: 'SEO Blog Post',
            description: 'Complete SEO-optimized blog post generation with keyword research and agent consultation',
            category: 'blog',
            tags: ['seo', 'blog', 'content-marketing'],
            difficulty: 'easy',
            estimatedTime: 45,
            consultationEnabled: true,
            agentCount: 3,
            steps: [
              { id: 'topic-input', name: 'Topic Input', type: 'TEXT_INPUT' },
              { id: 'keyword-research', name: 'Keyword Research', type: 'AI_GENERATION' },
              { id: 'content-creation', name: 'Content Creation', type: 'AI_GENERATION' },
              { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW' },
              { id: 'seo-optimization', name: 'SEO Optimization', type: 'AI_GENERATION' }
            ]
          },
          {
            id: 'product-descriptions',
            name: 'Bulk Product Descriptions',
            description: 'Generate product descriptions in bulk with brand voice consistency',
            category: 'ecommerce',
            tags: ['ecommerce', 'bulk', 'product-descriptions'],
            difficulty: 'medium',
            estimatedTime: 120,
            consultationEnabled: true,
            agentCount: 2,
            steps: [
              { id: 'csv-import', name: 'Import Product Data', type: 'CSV_IMPORT' },
              { id: 'brand-voice-input', name: 'Brand Voice Guidelines', type: 'TEXT_INPUT' },
              { id: 'bulk-generation', name: 'Generate Descriptions', type: 'LOOP' }
            ]
          }
        ];
        setTemplates(fallbackTemplates);
        addNotification(`⚠️ Using fallback templates (${fallbackTemplates.length})`);
      }
    } catch (error) {
      console.error('❌ Failed to load templates:', error);
      addNotification('❌ Failed to load templates, using fallback');

      // Use fallback templates on error
      const fallbackTemplates: WorkflowTemplate[] = [
        {
          id: 'blog-post-seo',
          name: 'SEO Blog Post',
          description: 'Complete SEO-optimized blog post generation with keyword research and agent consultation',
          category: 'blog',
          tags: ['seo', 'blog', 'content-marketing'],
          difficulty: 'easy',
          estimatedTime: 45,
          consultationEnabled: true,
          agentCount: 3,
          steps: [
            { id: 'topic-input', name: 'Topic Input', type: 'TEXT_INPUT' },
            { id: 'keyword-research', name: 'Keyword Research', type: 'AI_GENERATION' },
            { id: 'content-creation', name: 'Content Creation', type: 'AI_GENERATION' },
            { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW' },
            { id: 'seo-optimization', name: 'SEO Optimization', type: 'AI_GENERATION' }
          ]
        }
      ];
      setTemplates(fallbackTemplates);
    } finally {
      setIsLoading(false);
    }
  }, [addNotification]);

  const refreshExecutionStatus = useCallback(async () => {
    if (!execution?.id) return;

    try {
      const response = await fetch(`/api/workflow/execution/${execution.id}`);
      const result = await response.json();
      
      if (result.success) {
        const updatedExecution = result.data;

        setExecution(prev => prev ? {
          ...prev,
          status: updatedExecution.status || prev.status,
          progress: updatedExecution.progress !== undefined ? updatedExecution.progress : prev.progress,
          currentStep: updatedExecution.currentStep || prev.currentStep,
          steps: updatedExecution.steps ? updatedExecution.steps.map((updatedStep: any) => ({
            id: updatedStep.id,
            name: updatedStep.name,
            status: updatedStep.status || 'pending',
            outputs: updatedStep.outputs
          })) : prev.steps
        } : null);

        // Handle status changes
        if (updatedExecution.status === 'waiting_review' && currentStep !== 'review') {
          setCurrentStep('review');
          addNotification('Workflow paused for human review');
        } else if (updatedExecution.status === 'completed' && currentStep !== 'results') {
          setCurrentStep('results');
          addNotification('Workflow completed successfully!');
          if (onComplete) {
            onComplete(execution.id);
          }
        } else if (updatedExecution.status === 'failed') {
          setError('Workflow execution failed');
          addNotification('Workflow execution failed');
        }

        // Update agent activities based on execution status
        if (updatedExecution.status === 'running') {
          setAgentActivities([
            { agentId: 'seo-keyword', status: 'analyzing', lastSeen: new Date().toISOString() },
            { agentId: 'market-research', status: 'analyzing', lastSeen: new Date().toISOString() },
            { agentId: 'content-strategy', status: 'analyzing', lastSeen: new Date().toISOString() }
          ]);
        }
      }
    } catch (error) {
      console.error('Failed to refresh execution status:', error);
    }
  }, [execution?.id, currentStep, addNotification, onComplete]);

  const startStatusPolling = (executionId: string) => {
    console.log('📊 Starting status polling for execution:', executionId);

    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/workflow/execution/${executionId}`);
        const result = await response.json();

        if (result.success) {
          const updatedExecution = result.data;
          console.log('📊 Status update:', {
            status: updatedExecution.status,
            progress: updatedExecution.progress,
            currentStep: updatedExecution.currentStep,
            fullData: updatedExecution
          });

          setExecution(prev => prev ? {
            ...prev,
            status: updatedExecution.status || prev.status,
            progress: updatedExecution.progress !== undefined ? updatedExecution.progress : prev.progress,
            currentStep: updatedExecution.currentStep || prev.currentStep,
            steps: updatedExecution.steps ? updatedExecution.steps.map((updatedStep: any) => ({
              id: updatedStep.id,
              name: updatedStep.name,
              status: updatedStep.status || 'pending',
              outputs: updatedStep.outputs
            })) : prev.steps
          } : null);

          // Handle status changes
          if (updatedExecution.status === 'waiting_review' && currentStep !== 'review') {
            setCurrentStep('review');
            addNotification('⏸️ Workflow paused for human review');
            clearInterval(pollInterval);
          } else if (updatedExecution.status === 'completed' && currentStep !== 'results') {
            setCurrentStep('results');
            addNotification('🎉 Workflow completed successfully! Loading results...');
            clearInterval(pollInterval);

            // Automatically load results when workflow completes
            setTimeout(async () => {
              try {
                await loadResults(executionId);
                addNotification('✅ Results loaded successfully!');
              } catch (error) {
                console.error('Failed to load results:', error);
                addNotification('⚠️ Workflow completed but failed to load results. Please refresh.');
              }
            }, 1000);

            if (onComplete) {
              onComplete(executionId);
            }
          } else if (updatedExecution.status === 'failed') {
            setError('Workflow execution failed');
            addNotification('❌ Workflow execution failed');
            clearInterval(pollInterval);
          }
        }
      } catch (error) {
        console.error('❌ Status polling error:', error);
      }
    }, 2000); // Poll every 2 seconds

    // Clean up interval after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      console.log('📊 Status polling stopped after timeout');
    }, 300000);
  };

  const handleTemplateSelect = (template: WorkflowTemplate) => {
    console.log('🎯 Template selected:', template);
    setSelectedTemplate(template);

    // Initialize inputs based on template
    const initialInputs: Record<string, any> = {};
    if (template.id === 'blog-post-seo') {
      initialInputs.topic = '';
      initialInputs.target_audience = 'startups';
      initialInputs.primary_keyword = '';
    } else if (template.id === 'product-descriptions') {
      initialInputs.brand_voice = '';
      initialInputs.product_category = '';
    }

    console.log('📝 Initial inputs:', initialInputs);
    setWorkflowInputs(initialInputs);

    console.log('🔄 Setting current step to configure');
    setCurrentStep('configure');
    addNotification(`Selected template: ${template.name}`);
  };

  const handleInputChange = (key: string, value: string) => {
    setWorkflowInputs(prev => ({ ...prev, [key]: value }));
  };

  const startWorkflow = async () => {
    if (!selectedTemplate) return;

    try {
      setIsLoading(true);
      setError(null); // Clear any previous errors
      setCurrentStep('executing');
      addNotification('🚀 Starting workflow execution...');

      console.log('🚀 Starting workflow with template:', selectedTemplate.id);
      console.log('📋 Workflow inputs:', workflowInputs);
      console.log('🔄 Current step set to:', 'executing');

      // Create and execute workflow
      const response = await fetch('/api/workflow/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: selectedTemplate.id,
          inputs: workflowInputs,
          userId: 'user-123' // TODO: Get from auth
        })
      });

      console.log('📡 API Response status:', response.status);
      console.log('📡 API Response headers:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('📡 API Response body:', result);

      if (result.success) {
        console.log('✅ Workflow creation successful:', result.data);

        const executionData: WorkflowExecution = {
          id: result.data.executionId,
          status: 'running',
          progress: 0,
          steps: (selectedTemplate.steps || []).map(step => ({
            id: step.id,
            name: step.name,
            status: 'pending'
          }))
        };

        setExecution(executionData);
        addNotification(`✅ Workflow started: ${result.data.executionId ? result.data.executionId.slice(-8) : 'Unknown'}`);

        // Start real-time status monitoring
        startStatusPolling(result.data.executionId);

        // Auto-start agent collaboration for agent-enhanced templates
        if (selectedTemplate.consultationEnabled) {
          console.log('🤖 Agent consultation enabled, starting collaboration monitoring');
          setTimeout(() => {
            setCurrentStep('collaboration');
            setIsCollaborationActive(true);
            addNotification('🤖 Agent collaboration started automatically');
          }, 1000);
        }
      } else {
        console.error('❌ Workflow creation failed:', result);
        const errorMessage = result.error || result.message || 'Failed to start workflow';
        setError(errorMessage);
        addNotification(`❌ ${errorMessage}`);
        setCurrentStep('configure');
      }
    } catch (error) {
      console.error('❌ Failed to start workflow:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to start workflow';
      setError(errorMessage);
      addNotification(`❌ ${errorMessage}`);
      setCurrentStep('configure');
    } finally {
      setIsLoading(false);
    }
  };

  const submitReview = async (decision: 'approved' | 'rejected', feedback?: string) => {
    if (!execution?.id) return;

    try {
      setIsLoading(true);
      const response = await fetch('/api/workflow/execution/review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          executionId: execution.id,
          stepId: 'human-review',
          decision,
          feedback: feedback || '',
          reviewer: 'user-123' // TODO: Get from auth
        })
      });

      const result = await response.json();

      if (result.success) {
        if (decision === 'approved') {
          addNotification(`✅ Content approved! Workflow completing...`);
          setCurrentStep('executing');
          setIsCompletingWorkflow(true);

          // Start monitoring for workflow completion
          const completionInterval = setInterval(async () => {
            try {
              const statusResponse = await fetch(`/api/workflow/execution/${execution.id}`);
              const statusResult = await statusResponse.json();

              if (statusResult.success && statusResult.data.status === 'completed') {
                clearInterval(completionInterval);
                setIsCompletingWorkflow(false);
                addNotification(`🎉 Workflow completed successfully! Loading results...`);

                // Load results and navigate
                setTimeout(async () => {
                  try {
                    await loadResults(execution.id);
                    setCurrentStep('results');
                  } catch (error) {
                    console.error('Failed to load results:', error);
                    // Fallback: still navigate to results step
                    setCurrentStep('results');
                  }
                }, 500);
              } else if (statusResult.success && statusResult.data.status === 'failed') {
                clearInterval(completionInterval);
                setIsCompletingWorkflow(false);
                setError('Workflow execution failed after approval');
                addNotification('❌ Workflow execution failed');
              }
            } catch (error) {
              console.error('Error checking workflow completion:', error);
            }
          }, 2000);

          // Clear interval after 5 minutes to prevent infinite polling
          setTimeout(() => {
            clearInterval(completionInterval);
            setIsCompletingWorkflow(false);
          }, 300000);
        } else {
          addNotification(`❌ Content rejected. Workflow will regenerate based on feedback.`);
          setCurrentStep('executing');
        }
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Failed to submit review:', error);
      setError('Failed to submit review');
    } finally {
      setIsLoading(false);
    }
  };

  const publishToCMS = async (artifactId: string, artifact: any) => {
    setPublishingStatus(prev => ({ ...prev, [artifactId]: 'publishing' }));

    try {
      const response = await fetch(`/api/cms/publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId,
          title: artifact.title,
          content: artifact.content,
          type: artifact.type,
          executionId: artifact.executionId,
          stepId: artifact.stepId,
          metadata: {
            workflowGenerated: true,
            originalArtifactId: artifactId,
            generatedAt: artifact.createdAt,
            approvedBy: artifact.approvedBy,
            approvedAt: artifact.approvedAt
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        setPublishingStatus(prev => ({ ...prev, [artifactId]: 'published' }));
        setPublishResults(prev => ({
          ...prev,
          [artifactId]: { url: result.data?.url || result.data?.id }
        }));
        addNotification('✅ Published to CMS successfully');
      } else {
        setPublishingStatus(prev => ({ ...prev, [artifactId]: 'failed' }));
        setPublishResults(prev => ({
          ...prev,
          [artifactId]: { error: result.error || 'Failed to publish' }
        }));
        addNotification('❌ Failed to publish to CMS');
      }
    } catch (err) {
      setPublishingStatus(prev => ({ ...prev, [artifactId]: 'failed' }));
      setPublishResults(prev => ({
        ...prev,
        [artifactId]: { error: 'Failed to publish to CMS' }
      }));
      console.error('CMS publish error:', err);
      addNotification('❌ Publishing error occurred');
    }
  };

  const downloadArtifact = (artifact: any) => {
    const content = typeof artifact.content === 'string'
      ? artifact.content
      : JSON.stringify(artifact.content, null, 2);

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportAllArtifacts = (format: 'json' | 'markdown' | 'html' | 'txt') => {
    if (!results?.artifacts || results.artifacts.length === 0) {
      addNotification('❌ No artifacts to export');
      return;
    }

    let content = '';
    let mimeType = 'text/plain';
    let fileExtension = format;

    switch (format) {
      case 'json':
        content = JSON.stringify(results.artifacts, null, 2);
        mimeType = 'application/json';
        break;

      case 'markdown':
        content = results.artifacts.map(artifact => {
          const artifactContent = typeof artifact.content === 'string' ? artifact.content : JSON.stringify(artifact.content, null, 2);
          return `# ${artifact.title}\n\n**Type:** ${artifact.type}\n**Status:** ${artifact.status}\n**Created:** ${artifact.createdAt}\n\n---\n\n${artifactContent}\n\n`;
        }).join('\n---\n\n');
        mimeType = 'text/markdown';
        fileExtension = 'md';
        break;

      case 'html':
        content = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow Results - ${results.workflow?.name || 'AI Workflow'}</title>
    <style>
        body { font-family: system-ui, -apple-system, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        .artifact { margin-bottom: 40px; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px; }
        .artifact-header { margin-bottom: 15px; }
        .artifact-title { font-size: 1.5em; font-weight: bold; margin-bottom: 5px; }
        .artifact-meta { color: #6b7280; font-size: 0.9em; }
        .artifact-content { background: #f9fafb; padding: 15px; border-radius: 5px; white-space: pre-wrap; }
        .status { padding: 2px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .status.approved { background: #d1fae5; color: #065f46; }
        .status.pending { background: #fef3c7; color: #92400e; }
        .status.rejected { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <h1>Workflow Results: ${results.workflow?.name || 'AI Workflow'}</h1>
    <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
    <p><strong>Total Artifacts:</strong> ${results.artifacts.length}</p>
    <hr>
    ${results.artifacts.map(artifact => {
      const artifactContent = typeof artifact.content === 'string' ? artifact.content : JSON.stringify(artifact.content, null, 2);
      return `
    <div class="artifact">
        <div class="artifact-header">
            <div class="artifact-title">${artifact.title}</div>
            <div class="artifact-meta">
                Type: ${artifact.type} |
                Status: <span class="status ${artifact.status}">${artifact.status}</span> |
                Created: ${artifact.createdAt}
            </div>
        </div>
        <div class="artifact-content">${artifactContent}</div>
    </div>`;
    }).join('')}
</body>
</html>`;
        mimeType = 'text/html';
        break;

      case 'txt':
      default:
        content = results.artifacts.map(artifact => {
          const artifactContent = typeof artifact.content === 'string' ? artifact.content : JSON.stringify(artifact.content, null, 2);
          return `${artifact.title}\n${'='.repeat(artifact.title.length)}\nType: ${artifact.type}\nStatus: ${artifact.status}\nCreated: ${artifact.createdAt}\n\n${artifactContent}`;
        }).join('\n\n' + '='.repeat(80) + '\n\n');
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-results-${new Date().toISOString().split('T')[0]}.${fileExtension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    addNotification(`📥 Exported ${results.artifacts.length} artifacts as ${format.toUpperCase()}`);
  };

  const submitReviewDecision = async (reviewId: string, decision: 'approve' | 'reject', feedback?: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/review/${reviewId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          decision,
          edits: feedback || undefined,
          reviewer: 'current-user',
          metadata: {
            submittedAt: new Date().toISOString(),
            reviewType: reviewData?.type
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        setReviewData((prev: any) => prev ? { ...prev, status: 'completed' } : null);

        if (decision === 'reject') {
          addNotification(`❌ Content rejected with feedback. Workflow will regenerate content based on your feedback.`);
        } else {
          addNotification(`✅ Content approved successfully! Redirecting to results...`);
        }

        // If approved, redirect to results page
        if (decision === 'approve') {
          // The API returns workflowExecutionId directly in the response
          const executionId = result.workflowExecutionId || result.data?.workflowExecutionId || execution?.id;
          console.log('🔍 Redirection debug:', {
            decision,
            executionId,
            fullResult: result,
            resultData: result.data,
            resultWorkflowExecutionId: result.workflowExecutionId,
            executionIdFromState: execution?.id
          });

          if (executionId) {
            addNotification('✅ Content approved! Redirecting to results...');

            // Start monitoring for workflow completion
            setIsCompletingWorkflow(true);
            const completionInterval = setInterval(async () => {
              try {
                const statusResponse = await fetch(`/api/workflow/execution/${executionId}`);
                const statusResult = await statusResponse.json();

                if (statusResult.success && statusResult.data.status === 'completed') {
                  clearInterval(completionInterval);
                  setIsCompletingWorkflow(false);

                  // Load results and navigate
                  try {
                    await loadResults(executionId);
                    setCurrentStep('results');
                    const resultsUrl = `/workflow/unified?step=results&executionId=${executionId}`;
                    router.push(resultsUrl);
                    addNotification('🎉 Workflow completed! Results loaded successfully.');
                  } catch (error) {
                    console.error('Failed to load results:', error);
                    // Fallback: still navigate to results step
                    setCurrentStep('results');
                    const resultsUrl = `/workflow/unified?step=results&executionId=${executionId}`;
                    router.push(resultsUrl);
                  }
                } else if (statusResult.success && statusResult.data.status === 'failed') {
                  clearInterval(completionInterval);
                  setIsCompletingWorkflow(false);
                  setError('Workflow execution failed after approval');
                  addNotification('❌ Workflow execution failed');
                }
              } catch (error) {
                console.error('Error checking workflow completion:', error);
              }
            }, 2000);

            // Clear interval after 5 minutes to prevent infinite polling
            setTimeout(() => {
              clearInterval(completionInterval);
              setIsCompletingWorkflow(false);
            }, 300000);
          } else {
            console.error('No execution ID found for redirection');
            addNotification('⚠️ Content approved but unable to redirect to results');
          }
        } else if (result.workflowExecutionId || result.data?.workflowExecutionId) {
          // For rejection, still try to load results if available
          const executionId = result.workflowExecutionId || result.data?.workflowExecutionId;
          await loadResults(executionId);
        }
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (err) {
      setError('Failed to submit review');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const resetWorkflow = () => {
    setCurrentStep('template');
    setSelectedTemplate(null);
    setWorkflowInputs({});
    setExecution(null);
    setResults(null);
    setReviewData(null);
    setReviewVersions([]);
    setError(null);
    setIsCollaborationActive(false);
    setCollaborationResult(null);
    setAgentActivities([]);
    setPublishingStatus({});
    setPublishResults({});
    addNotification('Workflow reset');

    // Clear URL parameters
    router.replace('/workflow/unified', { scroll: false });
  };

  // Debug logging for render
  console.log('🎨 Rendering UnifiedWorkflowExperience:', {
    currentStep,
    selectedTemplate: selectedTemplate?.name,
    hasTemplates: templates.length > 0,
    isLoading
  });

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Workflow UI Error:', error, errorInfo);
        addNotification('❌ UI Error: Please refresh the page');
      }}
    >
      <div className="min-h-screen bg-gray-50">
      {/* Navigation System */}
      <WorkflowNavigationSystem
        navigationState={navigationState}
        workflowName={selectedTemplate?.name || results?.workflow?.name || 'AI Workflow Studio'}
        showBreadcrumbs={true}
        showStatusBadge={true}
        showActionButtons={true}
      />

      {/* Header with Progress */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              {onBack && (
                <button
                  onClick={onBack}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <div>
                <h1 className="text-xl font-semibold text-gray-900">AI Workflow Studio</h1>
                <p className="text-sm text-gray-600">Create content with intelligent agent collaboration</p>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="flex items-center space-x-2">
              {['Template', 'Configure', 'Execute', 'Collaborate', 'Review', 'Results'].map((stepName, index) => {
                const stepKeys: WorkflowStep[] = ['template', 'configure', 'executing', 'collaboration', 'review', 'results'];
                const currentStepIndex = stepKeys.indexOf(currentStep);
                const isActive = index === currentStepIndex;
                const isCompleted = index < currentStepIndex;
                const isAccessible = index <= currentStepIndex + 1;
                
                return (
                  <div key={stepName} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                      isActive ? 'bg-blue-600 text-white' :
                      isCompleted ? 'bg-green-600 text-white' :
                      isAccessible ? 'bg-gray-200 text-gray-600' :
                      'bg-gray-100 text-gray-400'
                    }`}>
                      {isCompleted ? '✓' : index + 1}
                    </div>
                    {index < 5 && (
                      <div className={`w-8 h-0.5 mx-1 ${
                        isCompleted ? 'bg-green-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="space-y-2">
            {notifications.map((notification, index) => (
              <div
                key={index}
                className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 animate-fade-in"
              >
                🔔 {notification}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-sm text-red-800">
            ❌ {error}
            <button
              onClick={() => setError(null)}
              className="ml-4 text-red-600 hover:text-red-800 underline"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentStep === 'template' && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Workflow Template</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Select a template that matches your content goals. Agent-enhanced templates include intelligent collaboration for better results.
              </p>
            </div>

            {/* Debug Information */}
            {process.env.NODE_ENV === 'development' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <h3 className="text-sm font-medium text-blue-800 mb-2">Template Debug Info</h3>
                <div className="text-xs text-blue-700 space-y-1">
                  <div>Current Step: {currentStep}</div>
                  <div>Is Loading: {isLoading ? 'Yes' : 'No'}</div>
                  <div>Templates Count: {templates.length}</div>
                  <div>Templates Array: {JSON.stringify(templates.map(t => ({ id: t.id, name: t.name })), null, 2)}</div>
                </div>
              </div>
            )}

            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading templates...</span>
              </div>
            ) : templates.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 mb-4">No templates available</div>
                <button
                  onClick={loadTemplates}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Retry Loading Templates
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {templates.map((template) => (
                  <div
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all cursor-pointer group"
                  >
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-900">
                            {template.name}
                          </h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${
                              template.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                              template.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {template.difficulty}
                            </span>
                            <span className="text-xs text-gray-500">~{template.estimatedTime}min</span>
                          </div>
                        </div>

                        {template.consultationEnabled && (
                          <div className="flex items-center space-x-1">
                            <span className="text-sm">🤖</span>
                            <span className="text-xs text-blue-600 font-medium">{template.agentCount}</span>
                          </div>
                        )}
                      </div>

                      <p className="text-gray-600 text-sm mb-4">{template.description}</p>

                      {template.consultationEnabled && (
                        <div className="mb-4">
                          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span className="mr-1">🤖</span>
                            Agent-Enhanced
                          </div>
                        </div>
                      )}

                      <div className="space-y-2">
                        <div className="text-xs text-gray-600 font-medium">Workflow Steps:</div>
                        <div className="space-y-1">
                          {(template.steps || []).slice(0, 3).map((step, index) => (
                            <div key={step.id} className="flex items-center space-x-2 text-xs">
                              <span className="w-4 h-4 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-medium">
                                {index + 1}
                              </span>
                              <span className="text-gray-700">{step.name}</span>
                            </div>
                          ))}
                          {(template.steps || []).length > 3 && (
                            <div className="text-xs text-gray-500 ml-6">
                              +{(template.steps || []).length - 3} more steps
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="mt-4 pt-3 border-t border-gray-100">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-500">{template.category}</span>
                          <span className="text-blue-600 font-medium">Select Template →</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {currentStep === 'configure' && selectedTemplate && (
          <div className="max-w-2xl mx-auto space-y-6">
            {/* Debug Information */}
            {process.env.NODE_ENV === 'development' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <h3 className="text-sm font-medium text-green-800 mb-2">Configure Debug Info</h3>
                <div className="text-xs text-green-700 space-y-1">
                  <div>Current Step: {currentStep}</div>
                  <div>Selected Template: {selectedTemplate?.name || 'None'}</div>
                  <div>Template ID: {selectedTemplate?.id || 'None'}</div>
                  <div>Workflow Inputs: {JSON.stringify(workflowInputs, null, 2)}</div>
                </div>
              </div>
            )}

            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedTemplate.name}</h2>
              <p className="text-gray-600">{selectedTemplate.description}</p>
              <button
                onClick={() => setCurrentStep('template')}
                className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                ← Change Template
              </button>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Configure Your Workflow</h3>

              <div className="space-y-4">
                {Object.entries(workflowInputs).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </label>
                    {key.includes('description') || key.includes('voice') ? (
                      <textarea
                        value={value}
                        onChange={(e) => handleInputChange(key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows={3}
                        placeholder={`Enter ${key.replace(/_/g, ' ')}`}
                      />
                    ) : (
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => handleInputChange(key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={`Enter ${key.replace(/_/g, ' ')}`}
                      />
                    )}
                  </div>
                ))}
              </div>

              {selectedTemplate.consultationEnabled && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">🤖 Agent Collaboration Enabled</h4>
                  <p className="text-sm text-blue-800 mb-2">
                    This workflow includes intelligent agent consultation with {selectedTemplate.agentCount} specialized agents.
                  </p>
                  <div className="text-xs text-blue-700">
                    <strong>Agents:</strong> SEO Specialist, Market Research, Content Strategy
                  </div>
                </div>
              )}

              <div className="mt-6 flex justify-between">
                <button
                  onClick={() => setCurrentStep('template')}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Back
                </button>
                <button
                  onClick={startWorkflow}
                  disabled={isLoading || Object.values(workflowInputs).some(v => !v)}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Starting...' : 'Start Workflow'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Debug Information */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">Debug Info</h3>
            <div className="text-xs text-yellow-700 space-y-1">
              <div>Current Step: {currentStep}</div>
              <div>Execution: {execution ? 'Present' : 'Null'}</div>
              <div>Execution ID: {execution?.id || 'N/A'}</div>
              <div>Execution Status: {execution?.status || 'N/A'}</div>
              <div>Is Loading: {isLoading ? 'Yes' : 'No'}</div>
              <div>Error: {error || 'None'}</div>
            </div>
          </div>
        )}

        {(currentStep === 'executing' || currentStep === 'collaboration') && execution && (
          <ExecutionDashboard
            execution={execution}
            currentStep={currentStep}
            onRefresh={refreshExecutionStatus}
            onPause={() => {
              // TODO: Implement pause functionality
              addNotification('⏸️ Pause functionality coming soon');
            }}
            onCancel={() => {
              // TODO: Implement cancel functionality
              if (confirm('Are you sure you want to cancel this workflow?')) {
                resetWorkflow();
                addNotification('❌ Workflow cancelled');
              }
            }}
          />
        )}

        {/* Fallback for executing state without execution object */}
        {currentStep === 'executing' && !execution && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {isCompletingWorkflow ? 'Completing Workflow...' : 'Starting Workflow...'}
              </h2>
              <p className="text-gray-600">
                {isCompletingWorkflow
                  ? 'Processing your approval and finalizing content...'
                  : 'Initializing workflow execution...'
                }
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-center space-x-3">
                <div className={`animate-spin rounded-full h-8 w-8 border-b-2 ${isCompletingWorkflow ? 'border-green-600' : 'border-blue-600'}`}></div>
                <span className="text-gray-700">
                  {isCompletingWorkflow ? 'Finalizing workflow after approval...' : 'Setting up workflow execution...'}
                </span>
              </div>

              {isCompletingWorkflow && (
                <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-center space-x-2">
                      <span className="text-green-800 text-sm">✅ Content approved - generating final results...</span>
                    </div>

                    {/* Post-Approval Progress Steps */}
                    <div className="space-y-2">
                      <div className="text-xs text-green-700 font-medium">Remaining Steps:</div>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2 text-xs">
                          <div className="animate-pulse w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-green-700">Finalizing content generation</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                          <span className="text-gray-600">SEO optimization</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                          <span className="text-gray-600">Quality assurance</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                          <span className="text-gray-600">Preparing results</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {isLoading && (
                <div className="mt-4 text-center text-sm text-gray-500">
                  This may take a few moments...
                </div>
              )}

              {error && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="text-red-800 text-sm">{error}</div>
                  <button
                    onClick={() => setCurrentStep('configure')}
                    className="mt-2 text-red-600 hover:text-red-800 text-sm underline"
                  >
                    Go back to configuration
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {currentStep === 'review' && (
          <div className="space-y-6">
            {/* Loading State */}
            {isLoading && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-center space-x-3">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="text-gray-700">Loading review data...</span>
                </div>
              </div>
            )}

            {/* Error State */}
            {error && !isLoading && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Review Not Available</h2>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p className="text-red-800">{error}</p>
                  </div>
                  <div className="flex gap-3 justify-center">
                    <button
                      onClick={() => {
                        setError(null);
                        const reviewId = searchParams?.get('reviewId');
                        if (reviewId) {
                          loadReviewData(reviewId);
                        }
                      }}
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      🔄 Retry
                    </button>
                    <button
                      onClick={() => setCurrentStep('template')}
                      className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                    >
                      ← Back to Templates
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Review Interface */}
            {reviewData && !isLoading && !error ? (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Content Review</h2>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>Type: {reviewData.type}</span>
                    <span>Status: {reviewData.status}</span>
                    {reviewData.deadline && (
                      <span className={new Date() > new Date(reviewData.deadline) ? 'text-red-600' : ''}>
                        Deadline: {new Date(reviewData.deadline).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>

                {/* Context */}
                {reviewData.content?.context && (
                  <div className="bg-gray-50 p-4 rounded-lg mb-6">
                    <h3 className="font-medium mb-2">Context</h3>
                    {reviewData.content.context.workflowName && (
                      <p className="text-sm text-gray-600">Workflow: {reviewData.content.context.workflowName}</p>
                    )}
                    {reviewData.content.context.stepName && (
                      <p className="text-sm text-gray-600">Step: {reviewData.content.context.stepName}</p>
                    )}
                  </div>
                )}

                {/* Instructions */}
                <div className="bg-blue-50 p-4 rounded-lg mb-6">
                  <h3 className="font-medium mb-2">Instructions</h3>
                  <p className="text-gray-700">{reviewData.instructions}</p>
                </div>

                {/* Content to Review */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium">Content: {reviewData.content?.title}</h3>
                    {reviewVersions.length > 1 && (
                      <button
                        onClick={() => setShowVersionComparison(!showVersionComparison)}
                        className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                      >
                        {showVersionComparison ? 'Hide' : 'Show'} Version History ({reviewVersions.length} versions)
                      </button>
                    )}
                  </div>

                  <div className="border rounded-lg p-4 bg-white">
                    <pre className="whitespace-pre-wrap text-sm">
                      {typeof reviewData.content?.data === 'string'
                        ? reviewData.content.data
                        : JSON.stringify(reviewData.content?.data, null, 2)
                      }
                    </pre>
                  </div>
                </div>

                {/* Version Comparison */}
                {showVersionComparison && reviewVersions.length > 1 && (
                  <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-yellow-900 mb-4">📋 Version History & Comparison</h3>
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {reviewVersions.map((version, index) => (
                        <div key={version.version} className={`border rounded-lg p-4 ${
                          index === 0 ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200'
                        }`}>
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <span className={`px-2 py-1 rounded text-sm font-medium ${
                                index === 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                              }`}>
                                {index === 0 ? 'Current Version' : `Version ${version.versionNumber}`}
                              </span>
                              <span className="text-sm text-gray-600">
                                {new Date(version.timestamp).toLocaleString()}
                              </span>
                            </div>
                          </div>
                          {version.userFeedback && (
                            <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded">
                              <h5 className="font-medium text-red-900 mb-1">User Feedback:</h5>
                              <p className="text-sm text-red-800">{version.userFeedback}</p>
                            </div>
                          )}
                          <div className="bg-gray-50 p-3 rounded border max-h-32 overflow-y-auto">
                            <pre className="whitespace-pre-wrap text-xs text-gray-700">
                              {typeof version.content === 'string' ? version.content : JSON.stringify(version.content, null, 2)}
                            </pre>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Review Actions */}
                {reviewData.status !== 'completed' && (
                  <div className="space-y-4">
                    <div className="flex gap-4">
                      <button
                        onClick={() => submitReviewDecision(reviewData.id, 'approve')}
                        disabled={isLoading}
                        className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                      >
                        ✅ Approve
                      </button>
                      <button
                        onClick={() => {
                          setFeedbackText('');
                          setShowFeedbackModal(true);
                        }}
                        disabled={isLoading}
                        className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                      >
                        ❌ Reject
                      </button>
                    </div>

                    {/* Review Information */}
                    <div className="pt-2 border-t border-gray-200">
                      <p className="text-sm text-gray-600">Use the approve/reject buttons above to complete your review.</p>
                    </div>
                  </div>
                )}
              </div>
            ) : execution && (
              <HumanReviewInterface
                execution={execution}
                onApprove={() => submitReview('approved')}
                onReject={(feedback) => submitReview('rejected', feedback)}
                isSubmitting={isLoading}
              />
            )}
          </div>
        )}

        {currentStep === 'results' && (results || (execution && selectedTemplate)) && (
          <div className="space-y-6">
            {/* Success Banner */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <span className="text-green-600 mr-3 text-2xl">🎉</span>
                <div>
                  <h2 className="text-lg font-medium text-green-800">Workflow Completed Successfully!</h2>
                  <p className="text-sm text-green-700 mt-1">
                    Your AI-powered content workflow has finished. Review the results below and publish to your CMS.
                  </p>
                </div>
              </div>
            </div>

            {results ? (
              <div className="space-y-6">
                {/* Workflow Info */}
                {results.workflow && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">Workflow: {results.workflow.name}</h3>
                    <p className="text-sm text-gray-600">{results.workflow.description}</p>
                  </div>
                )}

                {/* Execution Summary */}
                <div className="bg-white border rounded-lg p-4">
                  <h3 className="font-medium mb-3">Execution Summary</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Started:</span>
                      <p>{new Date(results.execution.startedAt).toLocaleString()}</p>
                    </div>
                    {results.execution.completedAt && (
                      <div>
                        <span className="text-gray-500">Completed:</span>
                        <p>{new Date(results.execution.completedAt).toLocaleString()}</p>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-500">Total Steps:</span>
                      <p>{results.steps?.length || 0}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Artifacts:</span>
                      <p>{results.artifacts?.length || 0}</p>
                    </div>
                  </div>
                </div>

                {/* Publishing Status Summary */}
                {results.artifacts.length > 0 && (
                  <div className="bg-white border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium">Publishing Status</h3>
                      {results.artifacts.filter(a => a.status === 'approved' && !publishingStatus[a.id]).length > 1 && (
                        <button
                          onClick={() => {
                            const approvedArtifacts = results.artifacts.filter(a => a.status === 'approved' && !publishingStatus[a.id]);
                            addNotification(`📤 Publishing ${approvedArtifacts.length} approved artifacts to CMS...`);
                            approvedArtifacts.forEach(artifact => {
                              publishToCMS(artifact.id, artifact);
                            });
                          }}
                          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                        >
                          📤 Publish All Approved
                        </button>
                      )}
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Ready to Publish:</span>
                        <p className="text-blue-600 font-medium">
                          {results.artifacts.filter(a => a.status === 'approved' && !publishingStatus[a.id]).length}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Published:</span>
                        <p className="text-green-600 font-medium">
                          {Object.values(publishingStatus).filter(status => status === 'published').length}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Failed:</span>
                        <p className="text-red-600 font-medium">
                          {Object.values(publishingStatus).filter(status => status === 'failed').length}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Total Artifacts:</span>
                        <p className="font-medium">{results.artifacts.length}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Agent Collaboration Timeline */}
                {results.steps && results.steps.some(step => step.outputs?.consultationSummary) && (
                  <div className="space-y-4 mb-6">
                    <h3 className="text-xl font-semibold">⏱️ Agent Collaboration Timeline</h3>
                    <div className="bg-white border rounded-lg p-4">
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="text-sm text-gray-600">Collaboration Flow:</div>
                        <div className="flex items-center space-x-2">
                          {results.steps
                            .filter(step => step.outputs?.consultationSummary)
                            .map((step, index, array) => (
                              <div key={step.stepId} className="flex items-center">
                                <div className="flex items-center space-x-1 bg-blue-50 px-3 py-1 rounded-full">
                                  <span className="text-xs font-medium text-blue-800">{step.name || step.stepId}</span>
                                  <span className="text-xs text-blue-600">({step.outputs.consultationSummary.totalConsultations} agents)</span>
                                </div>
                                {index < array.length - 1 && (
                                  <div className="mx-2 text-gray-400">→</div>
                                )}
                              </div>
                            ))}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500">
                        Total consultation time: {results.steps
                          .filter(step => step.outputs?.consultationSummary)
                          .reduce((total, step) => total + (step.outputs.consultationSummary.totalProcessingTime || 0), 0)}s
                      </div>
                    </div>
                  </div>
                )}

                {/* Agent Consultation Results & Insights */}
                {results.steps && results.steps.some(step => step.outputs?.consultationSummary) && (
                  <div className="space-y-4 mb-6">
                    <h3 className="text-xl font-semibold">🤖 Agent Collaboration & Insights</h3>
                    {results.steps
                      .filter(step => step.outputs?.consultationSummary)
                      .map((step, index) => {
                        const summary = step.outputs.consultationSummary;
                        const insights = step.outputs.agentInsights || {};

                        return (
                          <div key={`consultation-${step.stepId}-${index}`} className="border border-blue-200 rounded-lg p-6 bg-blue-50">
                            <div className="flex items-center justify-between mb-4">
                              <h4 className="text-lg font-medium text-blue-900">{step.name || step.stepId}</h4>
                              <div className="flex items-center space-x-3 text-sm text-blue-700">
                                <span className="bg-blue-100 px-2 py-1 rounded">🤖 {summary.totalConsultations} consultations</span>
                                <span className="bg-green-100 px-2 py-1 rounded">📊 {Math.round(summary.averageConfidence * 100)}% confidence</span>
                                <span className="bg-purple-100 px-2 py-1 rounded">⏱️ {summary.totalProcessingTime}s</span>
                              </div>
                            </div>

                            {/* Agent Artifacts Display */}
                            <div className="space-y-4">
                              {Object.entries(insights).map(([agentId, agentInsight]: [string, any]) => (
                                <div key={agentId} className="bg-white rounded-lg p-4 border border-blue-200">
                                  <div className="flex items-center justify-between mb-3">
                                    <h5 className="font-medium text-blue-900 capitalize flex items-center">
                                      {agentId === 'seo-keyword' && '🔍'}
                                      {agentId === 'content-strategy' && '📋'}
                                      {agentId === 'market-research' && '📊'}
                                      <span className="ml-2">{agentId.replace('-', ' ')} Agent</span>
                                    </h5>
                                    <div className="flex items-center space-x-2 text-sm">
                                      {agentInsight.confidence && (
                                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                                          {Math.round(agentInsight.confidence * 100)}% confidence
                                        </span>
                                      )}
                                      {agentInsight.processingTime && (
                                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                          {agentInsight.processingTime}s
                                        </span>
                                      )}
                                    </div>
                                  </div>

                                  {/* Agent-Specific Real Data Display */}
                                  {agentId === 'seo-keyword' && (agentInsight.primaryKeywords || agentInsight.keywordAnalysis?.primaryKeywords) && (
                                    <div className="space-y-3">
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        <div className="bg-gray-50 p-3 rounded">
                                          <h6 className="font-medium text-gray-900 mb-2">🎯 Primary Keywords</h6>
                                          <div className="flex flex-wrap gap-1">
                                            {(agentInsight.primaryKeywords || agentInsight.keywordAnalysis?.primaryKeywords || []).map((keyword: string, idx: number) => (
                                              <span key={idx} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                                {keyword}
                                              </span>
                                            ))}
                                          </div>
                                        </div>

                                        <div className="bg-gray-50 p-3 rounded">
                                          <h6 className="font-medium text-gray-900 mb-2">🔗 Long-tail Keywords</h6>
                                          <div className="space-y-1 text-xs text-gray-700">
                                            {(agentInsight.longTailKeywords || agentInsight.keywordAnalysis?.longTailKeywords || []).length > 0 ?
                                              (agentInsight.longTailKeywords || agentInsight.keywordAnalysis?.longTailKeywords || []).map((keyword: string, idx: number) => (
                                                <div key={idx} className="flex items-center">
                                                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
                                                  {keyword}
                                                </div>
                                              )) : <div className="text-gray-500">No long-tail keywords available</div>}
                                          </div>
                                        </div>
                                      </div>

                                      {agentInsight.seoMetrics && (
                                        <div className="bg-gray-50 p-3 rounded">
                                          <h6 className="font-medium text-gray-900 mb-2">📈 SEO Metrics</h6>
                                          <div className="grid grid-cols-4 gap-3 text-xs">
                                            <div className="text-center">
                                              <div className="text-gray-600">Search Volume</div>
                                              <div className="font-medium">{agentInsight.seoMetrics.searchVolume || 'N/A'}</div>
                                            </div>
                                            <div className="text-center">
                                              <div className="text-gray-600">Difficulty</div>
                                              <div className="font-medium text-orange-600">{agentInsight.seoMetrics.difficulty || 'N/A'}</div>
                                            </div>
                                            <div className="text-center">
                                              <div className="text-gray-600">Competition</div>
                                              <div className="font-medium text-red-600">{agentInsight.seoMetrics.competition || 'N/A'}</div>
                                            </div>
                                            <div className="text-center">
                                              <div className="text-gray-600">Opportunity</div>
                                              <div className="font-medium text-green-600">{agentInsight.seoMetrics.opportunity || 'N/A'}</div>
                                            </div>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  {agentId === 'content-strategy' && (agentInsight.contentStructure || agentInsight.strategyAnalysis?.contentOutline) && (
                                    <div className="space-y-3">
                                      <div className="bg-gray-50 p-3 rounded">
                                        <h6 className="font-medium text-gray-900 mb-2">📋 Content Structure</h6>
                                        <div className="space-y-1 text-xs">
                                          {(agentInsight.contentStructure || agentInsight.strategyAnalysis?.contentOutline || []).map((item: any, idx: number) => (
                                            <div key={idx} className="flex justify-between items-center p-2 bg-white rounded border">
                                              <span className="font-medium">{item.section}</span>
                                              <span className="text-gray-600">{item.words || item.wordCount || 'N/A'}</span>
                                              <span className="text-blue-600 text-xs">{item.focus || item.purpose || 'N/A'}</span>
                                            </div>
                                          ))}
                                        </div>
                                      </div>

                                      {(agentInsight.contentPillars || agentInsight.strategyAnalysis?.contentPillars) && (
                                        <div className="bg-gray-50 p-3 rounded">
                                          <h6 className="font-medium text-gray-900 mb-2">🎯 Content Pillars</h6>
                                          <div className="grid grid-cols-3 gap-2">
                                            {(agentInsight.contentPillars || agentInsight.strategyAnalysis?.contentPillars || []).map((item: any, idx: number) => (
                                              <div key={idx} className="bg-white p-2 rounded border text-center">
                                                <div className="font-medium text-gray-900 text-xs">{typeof item === 'string' ? item : item.pillar}</div>
                                                <div className="text-xs text-gray-600">{typeof item === 'string' ? 'Strategic focus' : item.focus}</div>
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  {agentId === 'market-research' && (agentInsight.marketInsights || agentInsight.marketAnalysis) && (
                                    <div className="space-y-3">
                                      <div className="bg-gray-50 p-3 rounded">
                                        <h6 className="font-medium text-gray-900 mb-2">📊 Market Insights</h6>
                                        <div className="grid grid-cols-2 gap-3">
                                          <div className="space-y-2">
                                            <div className="bg-white p-2 rounded border">
                                              <div className="text-xs text-gray-600">Market Size</div>
                                              <div className="font-medium">{(agentInsight.marketInsights?.marketSize || agentInsight.marketAnalysis?.marketSize) || 'N/A'}</div>
                                              <div className="text-xs text-green-600">{(agentInsight.marketInsights?.growthRate || agentInsight.marketAnalysis?.growthRate) || 'N/A'}</div>
                                            </div>
                                            <div className="bg-white p-2 rounded border">
                                              <div className="text-xs text-gray-600">Top Industries</div>
                                              <div className="font-medium text-xs">{(agentInsight.marketInsights?.topIndustries || agentInsight.marketAnalysis?.topIndustries) || 'N/A'}</div>
                                            </div>
                                          </div>
                                          <div className="space-y-2">
                                            <div className="bg-white p-2 rounded border">
                                              <div className="text-xs text-gray-600">Key Drivers</div>
                                              <div className="font-medium text-xs">{(agentInsight.marketInsights?.keyDrivers || agentInsight.marketAnalysis?.keyDrivers) || 'N/A'}</div>
                                            </div>
                                            <div className="bg-white p-2 rounded border">
                                              <div className="text-xs text-gray-600">Main Barriers</div>
                                              <div className="font-medium text-xs">{(agentInsight.marketInsights?.mainBarriers || agentInsight.marketAnalysis?.mainBarriers) || 'N/A'}</div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  )}

                                  {/* Key Recommendations */}
                                  {agentInsight.keyRecommendations && (
                                    <div className="mt-3 bg-yellow-50 p-3 rounded border border-yellow-200">
                                      <h6 className="font-medium text-yellow-900 mb-2">💡 Key Recommendations</h6>
                                      <ul className="space-y-1">
                                        {agentInsight.keyRecommendations.map((rec: string, index: number) => (
                                          <li key={index} className="flex items-start text-xs text-yellow-800">
                                            <span className="text-yellow-600 mr-2 mt-1">▶</span>
                                            {rec}
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}

                                  {/* Agent Reasoning Display */}
                                  {agentInsight.reasoning && (
                                    <div className="mt-3 bg-gray-50 p-3 rounded border">
                                      <h6 className="font-medium text-gray-900 mb-2">🧠 Agent Reasoning</h6>
                                      <div className="text-xs text-gray-700">
                                        {agentInsight.reasoning}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>

                            {/* Consultation Summary */}
                            <div className="mt-4 bg-white rounded-lg p-4 border border-blue-200">
                              <div className="flex items-center justify-between mb-2">
                                <h6 className="font-medium text-blue-900">📊 Consultation Summary</h6>
                                <button
                                  onClick={() => {
                                    const reportData = {
                                      stepName: step.name || step.stepId,
                                      timestamp: new Date().toISOString(),
                                      summary: summary,
                                      agentInsights: insights,
                                      consultationDetails: {
                                        totalConsultations: summary.totalConsultations,
                                        averageConfidence: summary.averageConfidence,
                                        totalProcessingTime: summary.totalProcessingTime,
                                        consultedAgents: summary.consultedAgents || [],
                                        qualityScore: summary.qualityScore || 'N/A'
                                      }
                                    };

                                    const reportContent = `# Agent Consultation Report
## ${step.name || step.stepId}
**Generated:** ${new Date().toLocaleString()}

### Summary
- **Total Consultations:** ${summary.totalConsultations}
- **Average Confidence:** ${Math.round(summary.averageConfidence * 100)}%
- **Total Processing Time:** ${summary.totalProcessingTime}s
- **Agents Consulted:** ${summary.consultedAgents?.length || 0}

### Agent Insights
${Object.entries(insights).map(([agentId, insight]: [string, any]) => `
#### ${agentId.replace('-', ' ').toUpperCase()} Agent
- **Confidence:** ${Math.round(insight.confidence * 100)}%
- **Processing Time:** ${insight.processingTime}s
- **Reasoning:** ${insight.reasoning || 'N/A'}
${insight.keyRecommendations ? `- **Recommendations:**\n${insight.keyRecommendations.map((rec: string) => `  - ${rec}`).join('\n')}` : ''}
${agentId === 'seo-keyword' && insight.primaryKeywords ? `- **Primary Keywords:** ${insight.primaryKeywords.join(', ')}` : ''}
${agentId === 'content-strategy' && insight.contentStructure ? `- **Content Sections:** ${insight.contentStructure.length}` : ''}
${agentId === 'market-research' && insight.marketInsights ? `- **Market Size:** ${insight.marketInsights.marketSize || 'N/A'}` : ''}
`).join('\n')}

### Raw Data
\`\`\`json
${JSON.stringify(reportData, null, 2)}
\`\`\`
`;

                                    const blob = new Blob([reportContent], { type: 'text/markdown' });
                                    const url = URL.createObjectURL(blob);
                                    const a = document.createElement('a');
                                    a.href = url;
                                    a.download = `agent-consultation-report-${step.stepId}-${Date.now()}.md`;
                                    document.body.appendChild(a);
                                    a.click();
                                    document.body.removeChild(a);
                                    URL.revokeObjectURL(url);
                                    addNotification('📄 Agent consultation report downloaded');
                                  }}
                                  className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                                >
                                  📄 Download Report
                                </button>
                              </div>
                              <div className="grid grid-cols-4 gap-4 text-sm">
                                <div className="text-center p-2 bg-blue-50 rounded">
                                  <div className="text-lg font-bold text-blue-600">{summary.totalConsultations}</div>
                                  <div className="text-xs text-gray-600">Consultations</div>
                                </div>
                                <div className="text-center p-2 bg-green-50 rounded">
                                  <div className="text-lg font-bold text-green-600">{Math.round(summary.averageConfidence * 100)}%</div>
                                  <div className="text-xs text-gray-600">Avg Confidence</div>
                                </div>
                                <div className="text-center p-2 bg-purple-50 rounded">
                                  <div className="text-lg font-bold text-purple-600">{summary.totalProcessingTime}s</div>
                                  <div className="text-xs text-gray-600">Total Time</div>
                                </div>
                                <div className="text-center p-2 bg-orange-50 rounded">
                                  <div className="text-lg font-bold text-orange-600">{summary.consultedAgents?.length || 0}</div>
                                  <div className="text-xs text-gray-600">Agents Used</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                )}

                {/* Generated Artifacts */}
                {results.artifacts.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold">📝 Generated Content & Artifacts</h3>
                    {results.artifacts.map((artifact) => (
                      <div key={artifact.id} className="bg-white border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <h4 className="font-medium">{artifact.title}</h4>
                          </div>
                          <div className="flex gap-2 text-xs">
                            <span className="bg-gray-100 px-2 py-1 rounded">{artifact.type}</span>
                            <span className={`px-2 py-1 rounded ${
                              artifact.status === 'approved' ? 'bg-green-100 text-green-700' :
                              artifact.status === 'rejected' ? 'bg-red-100 text-red-700' :
                              artifact.status === 'pending_approval' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-gray-100 text-gray-700'
                            }`}>
                              {artifact.status}
                            </span>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-3 rounded border">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium text-gray-900">Content Preview</h5>
                            <span className="text-xs text-gray-600">
                              {(() => {
                                if (typeof artifact.content === 'string') {
                                  return `${artifact.content.length} characters`;
                                } else if (artifact.content && typeof artifact.content === 'object') {
                                  const contentStr = JSON.stringify(artifact.content);
                                  return `${contentStr.length} characters (JSON)`;
                                } else {
                                  return 'No content';
                                }
                              })()}
                            </span>
                          </div>
                          <div className="max-h-40 overflow-y-auto">
                            <pre className="whitespace-pre-wrap text-sm">
                              {(() => {
                                if (typeof artifact.content === 'string') {
                                  return artifact.content;
                                } else if (artifact.content && typeof artifact.content === 'object') {
                                  // Handle structured content objects
                                  if (artifact.content.title && artifact.content.content) {
                                    return `${artifact.content.title}\n\n${artifact.content.content}`;
                                  } else if (artifact.content.content) {
                                    return artifact.content.content;
                                  } else {
                                    return JSON.stringify(artifact.content, null, 2);
                                  }
                                } else {
                                  return 'No content available';
                                }
                              })()}
                            </pre>
                          </div>
                        </div>

                        {/* Action Buttons - Always Available */}
                        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded">
                          <h5 className="font-medium text-gray-800 mb-3">Actions</h5>
                          <div className="flex flex-wrap gap-2">
                            <button
                              onClick={() => downloadArtifact(artifact)}
                              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center gap-2"
                            >
                              📥 Download
                            </button>
                            <button
                              onClick={() => {
                                const content = typeof artifact.content === 'string' ? artifact.content : JSON.stringify(artifact.content, null, 2);
                                navigator.clipboard.writeText(content);
                                addNotification('📋 Content copied to clipboard!');
                              }}
                              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center gap-2"
                            >
                              📋 Copy
                            </button>
                            <button
                              onClick={() => {
                                const content = typeof artifact.content === 'string' ? artifact.content : JSON.stringify(artifact.content, null, 2);
                                const newWindow = window.open('', '_blank');
                                if (newWindow) {
                                  newWindow.document.write(`
                                    <html>
                                      <head>
                                        <title>${artifact.title}</title>
                                        <style>
                                          body { font-family: system-ui; padding: 20px; line-height: 1.6; max-width: 800px; margin: 0 auto; }
                                          pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
                                        </style>
                                      </head>
                                      <body>
                                        <h1>${artifact.title}</h1>
                                        <pre>${content}</pre>
                                      </body>
                                    </html>
                                  `);
                                  newWindow.document.close();
                                }
                              }}
                              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 flex items-center gap-2"
                            >
                              🔗 Open Full View
                            </button>
                          </div>
                        </div>

                        {/* Publishing Actions */}
                        {artifact.status === 'approved' && (
                          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
                            <h5 className="font-medium text-blue-800 mb-3">Ready for Publishing</h5>
                            <div className="flex gap-2">
                              <button
                                onClick={() => publishToCMS(artifact.id, artifact)}
                                disabled={publishingStatus[artifact.id] === 'publishing'}
                                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                              >
                                {publishingStatus[artifact.id] === 'publishing' ? 'Publishing...' : '📤 Publish to CMS'}
                              </button>
                            </div>

                            {publishResults[artifact.id]?.url && (
                              <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded">
                                <span className="text-sm text-green-700">Published at: </span>
                                <a
                                  href={publishResults[artifact.id].url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-sm text-blue-600 hover:underline"
                                >
                                  {publishResults[artifact.id].url}
                                </a>
                              </div>
                            )}

                            {publishResults[artifact.id]?.error && (
                              <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
                                <span className="text-sm text-red-700">Error: {publishResults[artifact.id].error}</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* Bulk Export Options */}
                {results.artifacts.length > 0 && (
                  <div className="bg-white border rounded-lg p-4">
                    <h3 className="font-medium mb-3">📦 Bulk Export Options</h3>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                      <button
                        onClick={() => exportAllArtifacts('json')}
                        className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm flex items-center gap-2"
                      >
                        📊 Export JSON
                      </button>
                      <button
                        onClick={() => exportAllArtifacts('markdown')}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm flex items-center gap-2"
                      >
                        📝 Export Markdown
                      </button>
                      <button
                        onClick={() => exportAllArtifacts('html')}
                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm flex items-center gap-2"
                      >
                        🌐 Export HTML
                      </button>
                      <button
                        onClick={() => exportAllArtifacts('txt')}
                        className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 text-sm flex items-center gap-2"
                      >
                        📄 Export Text
                      </button>
                      <button
                        onClick={() => {
                          const allContent = results.artifacts.map(artifact => {
                            const content = typeof artifact.content === 'string' ? artifact.content : JSON.stringify(artifact.content, null, 2);
                            return `${artifact.title}\n${'='.repeat(artifact.title.length)}\n\n${content}`;
                          }).join('\n\n---\n\n');
                          navigator.clipboard.writeText(allContent);
                          addNotification('📋 All content copied to clipboard!');
                        }}
                        className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 text-sm flex items-center gap-2"
                      >
                        📋 Copy All
                      </button>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <button
                    onClick={resetWorkflow}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    🚀 Start New Workflow
                  </button>
                  <button
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    🔄 Refresh
                  </button>
                  {onBack && (
                    <button
                      onClick={onBack}
                      className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                    >
                      ← Back to Dashboard
                    </button>
                  )}
                </div>
              </div>
            ) : execution && selectedTemplate && (
              <ResultsDashboard
                execution={execution}
                selectedTemplate={selectedTemplate}
                onReset={resetWorkflow}
                onBack={onBack}
              />
            )}
          </div>
        )}
      </div>

      {/* Feedback Modal */}
      {showFeedbackModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Provide Feedback for Rejection</h3>
            <textarea
              value={feedbackText}
              onChange={(e) => setFeedbackText(e.target.value)}
              placeholder="Please explain what needs to be improved..."
              className="w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="flex gap-3 mt-4">
              <button
                onClick={() => {
                  if (feedbackText.trim() && reviewData?.id) {
                    submitReviewDecision(reviewData.id, 'reject', feedbackText);
                    setShowFeedbackModal(false);
                    setFeedbackText('');
                  }
                }}
                disabled={!feedbackText.trim() || isLoading}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                Submit Rejection
              </button>
              <button
                onClick={() => {
                  setShowFeedbackModal(false);
                  setFeedbackText('');
                }}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
    </ErrorBoundary>
  );
}
