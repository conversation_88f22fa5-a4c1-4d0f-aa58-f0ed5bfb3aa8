/**
 * Unified Feedback Processor
 * 
 * Bridges human feedback from approval workflows with AI feedback systems
 * to create intelligent artifact regeneration workflows.
 */

import { v4 as uuidv4 } from 'uuid';
// import { FeedbackLoopSystem } from '../../app/(payload)/api/agents/dynamic-collaboration-v3/utils/feedback-loop-system'; // DEPRECATED
import { getWorkflowEngine } from '../workflow/singleton';
import { ArtifactStatus } from '../workflow/types';
import { FeedbackAnalytics } from './analytics';

export interface HumanFeedback {
  artifactId: string;
  feedback: string;
  approver: string;
  timestamp: string;
  feedbackType: 'rejection' | 'improvement_request';
  specificAreas?: string[];
}

export interface RegenerationRequest {
  id: string;
  originalArtifactId: string;
  humanFeedback: HumanFeedback;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  completedAt?: string;
  newArtifactId?: string;
  error?: string;
}

export class UnifiedFeedbackProcessor {
  private sessionId: string;
  private feedbackLoopSystem: FeedbackLoopSystem;
  private workflowEngine: any;
  private analytics: FeedbackAnalytics;

  constructor(sessionId?: string) {
    this.sessionId = sessionId || 'default-session';
    this.feedbackLoopSystem = new FeedbackLoopSystem(this.sessionId);
    this.workflowEngine = getWorkflowEngine();
    this.analytics = new FeedbackAnalytics();
  }

  /**
   * Process human feedback and determine if regeneration is needed
   */
  async processHumanFeedback(
    artifactId: string, 
    feedback: string, 
    approver: string,
    isRejection: boolean = false
  ): Promise<RegenerationRequest | null> {
    try {
      // Validate feedback quality
      if (!this.isActionableFeedback(feedback)) {
        console.log('Feedback not actionable, skipping regeneration');
        return null;
      }

      const humanFeedback: HumanFeedback = {
        artifactId,
        feedback,
        approver,
        timestamp: new Date().toISOString(),
        feedbackType: isRejection ? 'rejection' : 'improvement_request',
        specificAreas: this.extractSpecificAreas(feedback)
      };

      // Create regeneration request
      const regenerationRequest: RegenerationRequest = {
        id: uuidv4(),
        originalArtifactId: artifactId,
        humanFeedback,
        status: 'pending',
        createdAt: new Date().toISOString()
      };

      // Track feedback for analytics
      this.analytics.trackFeedback({
        artifactId,
        feedback,
        approver,
        isRejection,
        specificAreas: humanFeedback.specificAreas || [],
        timestamp: humanFeedback.timestamp
      });

      // Store the regeneration request first
      // In a real implementation, this would be stored in a database
      console.log('Regeneration request created:', regenerationRequest.id);

      // Trigger regeneration workflow asynchronously
      this.triggerRegenerationWorkflow(regenerationRequest).catch(error => {
        console.error('Regeneration workflow failed:', error);
        regenerationRequest.status = 'failed';
        regenerationRequest.error = error instanceof Error ? error.message : String(error);
      });

      return regenerationRequest;

    } catch (error) {
      console.error('Error processing human feedback:', error);
      throw error;
    }
  }

  /**
   * Trigger the regeneration workflow using existing FeedbackLoopSystem
   */
  private async triggerRegenerationWorkflow(request: RegenerationRequest): Promise<void> {
    try {
      // Update request status
      request.status = 'processing';

      // Get the original artifact
      const artifact = await this.workflowEngine.getArtifact(request.originalArtifactId);
      if (!artifact) {
        throw new Error(`Artifact ${request.originalArtifactId} not found`);
      }

      // Convert human feedback to AI feedback format
      const aiFeedback = await this.convertHumanToAIFeedback(
        request.humanFeedback,
        artifact
      );

      // Use FeedbackLoopSystem to generate improved content
      const improvedContent = await this.generateImprovedContent(
        artifact,
        aiFeedback,
        request.humanFeedback
      );

      // Create new artifact with improved content
      const newArtifactId = await this.createImprovedArtifact(
        artifact,
        improvedContent,
        request
      );

      // Update request with completion
      request.status = 'completed';
      request.completedAt = new Date().toISOString();
      request.newArtifactId = newArtifactId;

      // Automatically resubmit for approval
      await this.resubmitForApproval(newArtifactId, request);

    } catch (error) {
      request.status = 'failed';
      request.error = error instanceof Error ? error.message : String(error);
      console.error('Regeneration workflow failed:', error);
      throw error;
    }
  }

  /**
   * Convert human feedback to AI feedback format
   */
  private async convertHumanToAIFeedback(humanFeedback: HumanFeedback, artifact: any) {
    // Use the existing generateFeedback method but with human context
    return await this.feedbackLoopSystem.generateFeedback(
      artifact.id,
      'human-reviewer',
      humanFeedback.specificAreas || ['content quality', 'accuracy', 'clarity']
    );
  }

  /**
   * Generate improved content using existing FeedbackLoopSystem
   */
  private async generateImprovedContent(
    artifact: any,
    aiFeedback: any,
    humanFeedback: HumanFeedback
  ): Promise<string> {
    // Create a feedback response that incorporates human feedback
    const responseId = uuidv4();
    
    // Provide the AI feedback through the system
    await this.feedbackLoopSystem.provideFeedback(
      'human-reviewer',
      'content-improver',
      responseId,
      aiFeedback
    );

    // Use the incorporate feedback method to generate improved content
    const improvedArtifactId = await this.feedbackLoopSystem.incorporateFeedback(
      artifact.id,
      [responseId],
      'content-improver'
    );

    // Get the improved content
    // Note: This would need to be adapted based on how the FeedbackLoopSystem stores results
    return improvedArtifactId; // Placeholder - would need actual content extraction
  }

  /**
   * Create new artifact with improved content
   */
  private async createImprovedArtifact(
    originalArtifact: any,
    improvedContent: string,
    request: RegenerationRequest
  ): Promise<string> {
    const newArtifactId = uuidv4();
    
    const improvedArtifact = {
      ...originalArtifact,
      id: newArtifactId,
      content: improvedContent,
      title: `${originalArtifact.title} (Improved)`,
      status: ArtifactStatus.PENDING_APPROVAL,
      version: (originalArtifact.version || 1) + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'feedback-regeneration-system',
      metadata: {
        ...originalArtifact.metadata,
        regeneratedFrom: originalArtifact.id,
        humanFeedback: request.humanFeedback,
        regenerationRequestId: request.id
      }
    };

    // Store the new artifact
    await this.workflowEngine.storeArtifact(improvedArtifact);
    
    return newArtifactId;
  }

  /**
   * Automatically resubmit improved artifact for approval
   */
  private async resubmitForApproval(artifactId: string, request: RegenerationRequest): Promise<void> {
    // Update artifact status to pending approval
    await this.workflowEngine.updateArtifact(artifactId, {
      status: ArtifactStatus.PENDING_APPROVAL
    });

    // Log the resubmission
    console.log(`Artifact ${artifactId} automatically resubmitted for approval after regeneration`);
  }

  /**
   * Check if feedback is actionable (has sufficient detail for improvement)
   */
  private isActionableFeedback(feedback: string): boolean {
    if (!feedback || feedback.trim().length < 10) {
      return false;
    }

    // Check for actionable keywords
    const actionableKeywords = [
      'improve', 'change', 'add', 'remove', 'clarify', 'expand', 'reduce',
      'better', 'more', 'less', 'different', 'specific', 'detailed'
    ];

    const lowerFeedback = feedback.toLowerCase();
    return actionableKeywords.some(keyword => lowerFeedback.includes(keyword));
  }

  /**
   * Extract specific areas for improvement from feedback text
   */
  private extractSpecificAreas(feedback: string): string[] {
    const areas: string[] = [];
    const lowerFeedback = feedback.toLowerCase();

    // Common improvement areas
    const areaKeywords = {
      'content quality': ['quality', 'content', 'writing', 'poor'],
      'clarity': ['clear', 'clarity', 'understand', 'confusing', 'unclear'],
      'accuracy': ['accurate', 'correct', 'wrong', 'error', 'inaccurate'],
      'completeness': ['complete', 'missing', 'incomplete', 'add'],
      'style': ['style', 'tone', 'voice', 'format'],
      'structure': ['structure', 'organize', 'flow', 'order', 'bad']
    };

    for (const [area, keywords] of Object.entries(areaKeywords)) {
      if (keywords.some(keyword => lowerFeedback.includes(keyword))) {
        areas.push(area);
      }
    }

    return areas.length > 0 ? areas : ['content quality', 'clarity'];
  }

  /**
   * Track feedback cycle for analytics
   */
  async trackFeedbackCycle(
    originalId: string,
    feedback: string,
    newId: string
  ): Promise<void> {
    // Implementation for tracking feedback cycles
    console.log(`Feedback cycle tracked: ${originalId} -> ${newId}`);
  }

  /**
   * Get feedback suggestions based on analytics
   */
  getFeedbackSuggestions(artifactType: string = 'general'): string[] {
    return this.analytics.getFeedbackSuggestions(artifactType);
  }

  /**
   * Get feedback analytics and metrics
   */
  getAnalytics() {
    return this.analytics.exportAnalytics();
  }

  /**
   * Get feedback quality score for given feedback text
   */
  getFeedbackQuality(feedback: string): number {
    return this.analytics['calculateFeedbackQuality'](feedback);
  }
}
